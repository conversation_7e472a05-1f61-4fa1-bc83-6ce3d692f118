export const pythonInstallCommands = [
  'yum -y install mariadb-devel mysql',
  'python3 -m pip install --compile -r requirements.txt -t .',
  'find . -type f -name "*.so" | xargs -r strip',
  'find . -type f -name "*.pyc" | xargs -r rm',
  'find . -type f -name "*.pyo" | xargs -r rm',
  'find . -type d -name "__pycache__*" | xargs -r rm -r',
  'find . -type d -name "*.dist-info*" | xargs -r rm -r',
  // TODO: Look at explicitly skipping pendulum testing library
  //'find . -type d -name "test*" | xargs -r rm -rf',
];
