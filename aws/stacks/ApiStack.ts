import {IVpc, SubnetSelection, SubnetType, Vpc} from 'aws-cdk-lib/aws-ec2';
import {isProduction} from './helpers/isProduction';
import {
  Api,
  Function,
  RDS,
  StackContext,
  Cron,
  WebSocketApi,
  Table,
  Script,
  Stack,
  Bucket,
} from 'sst/constructs';
import {use, Queue} from 'sst/constructs';
import {RdsStack} from './RdsStack';
import {EventBusStack} from './EventBusStack';
import {WebsocketStack} from './WebsocketStack';
import {EmailTemplates} from './constructs/EmailTemplates';
import {getAmplitudeKey} from './helpers/getAmplitudeKey';
import {isLocal as runningLocal} from './helpers/isRunningLocal';
import {isDevelopment as runningDevelopment} from './helpers/isDevelopment';
import {DOMAIN} from './constants/domain';
import {getDomainProps} from './helpers/createEndpoints';
import {getPermissions} from './helpers/getPermissions';
import {RemovalPolicy} from 'aws-cdk-lib/core';
import {QueueStack} from './QueueStack';
import {getPollyPolicy, getEmailPolicy, getKMSPolicy} from './helpers/policies';

const DEV_ACCOUNT_IDS = ['************', '************'];

export function ApiStack({app, stack}: StackContext) {
  const deleteDayLimit: number = 90;
  const isLocal = runningLocal(app);
  const isDevelopment = runningDevelopment(app);
  const {nextDomainProps, apiDomainProps} = getDomainProps(app);
  const localAuthUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const authUrl =
    isLocal || isDevelopment
      ? localAuthUrl
      : `https://${nextDomainProps.domainName}`;

  const amplitudeKey = getAmplitudeKey(app);

  // Stack context
  const {eventBus} = use(EventBusStack);
  const {websocket, socketTable, socketTicketsTable} = use(WebsocketStack);
  const {
    hubspotEventQueue,
    amplitudeEventQueue,
    elasticDocumentBlockEventQueue,
    elasticDocumentEventQueue,
  } = use(QueueStack);

  // TODO: Environment variable keys need to be in set in a global place but beyond scope for this change
  const requireEnvVars = ['LICENSE_URL', 'PRODUCT_NAME', 'COLLINS_API_KEY'];

  requireEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
      throw Error(`Error: ${envVar} needs to be defined in .env`);
    }
  });

  if ((isLocal || isDevelopment) && !DEV_ACCOUNT_IDS.includes(stack.account)) {
    throw Error(
      `You need to run this stack within the appropriate development accounts ${DEV_ACCOUNT_IDS.join(
        ', ',
      )}`,
    );
  }
  if (app.mode === 'remove') {
    console.log(`\n\nRemoving ${app.stage} stack\n\n`);
  } else {
    console.log(`\n\nDeploying ${isLocal ? 'local' : app.stage} stack\n\n`);
  }

  const emailSource =
    isLocal || isDevelopment ? '<EMAIL>' : `hello@${DOMAIN}`;

  const feedbackEmail =
    isLocal || isDevelopment ? '<EMAIL>' : `feedback@${DOMAIN}`; // TODO: Change this to the correct email when decided on

  const emailSender = isLocal || isDevelopment ? 'Devs @ Tailo' : 'Ben @ Tailo';

  let EnvVars: Record<string, string> = {
    ROLE_ACCESS_ONLY: 'access-only',
    ROLE_GUEST_USER: 'guest-user',
    ROLE_GENERAL_USER: 'general-user',
    ROLE_ADMIN_USER: 'admin-user',
    TOS_KEY: 'terms-of-service',
    PRIVACY_KEY: 'privacy-policy',
    EXAMPLE_DOCUMENT_KEY: 'example_document.json',
    HELLO_EMAIL_SOURCE: `${emailSender} <${emailSource}>`,
    FEEDBACK_EMAIL_INBOX: feedbackEmail,
    LOGO_IMG_KEY: 'index.png',
    LICENSE_URL: process.env.LICENSE_URL!,
    PRODUCT_NAME: process.env.PRODUCT_NAME!,
    COLLINS_API_KEY: process.env.COLLINS_API_KEY!,
    AMPLITUDE_KEY: amplitudeKey,
    KMS_ARN: process.env.KMS_ARN!,
  };

  if (isLocal) {
    // For typescripts sake put a nested if otherwise it complains that the env vars could be undefined
    if (
      !process.env.DATABASE_HOST ||
      !process.env.DATABASE_PORT ||
      !process.env.DATABASE_USER ||
      !process.env.DATABASE_NAME
    ) {
      throw Error(
        'Error: DATABASE_HOST, DATABASE_PORT, DATABASE_USER, and DATABASE_NAME need to be defined in .env',
      );
    }
    EnvVars = {
      ...EnvVars,
      LOCAL_DB_HOST: process.env.DATABASE_HOST,
      LOCAL_DB_PORT: process.env.DATABASE_PORT,
      LOCAL_DB_USER: process.env.DATABASE_USER,
      LOCAL_DB_NAME: process.env.DATABASE_NAME,
    };
  } else if (!process.env.SENTRY_DNS) {
    throw Error('Error: Cannot deploy until SENTRY_DNS is provided in .env');
  }

  if (!process.env.DICTIONARY_API_KEY)
    throw Error(
      'Error: Cannot deploy until DICTIONARY_API_KEY is provided in .env',
    );
  EnvVars = {
    ...EnvVars,
    DICTIONARY_API_KEY: process.env.DICTIONARY_API_KEY,
  };

  if (!isLocal && process.env.SENTRY_DNS)
    EnvVars = {
      ...EnvVars,
      SENTRY_DNS: process.env.SENTRY_DNS,
    };

  // Attach the pusher variables.
  if (
    process.env.PUSHER_APP_ID &&
    process.env.PUSHER_KEY &&
    process.env.PUSHER_SECRET &&
    process.env.PUSHER_CLUSTER
  ) {
    EnvVars = {
      ...EnvVars,
      PUSHER_APP_ID: process.env.PUSHER_APP_ID,
      PUSHER_KEY: process.env.PUSHER_KEY,
      PUSHER_SECRET: process.env.PUSHER_SECRET,
      PUSHER_CLUSTER: process.env.PUSHER_CLUSTER,
    };
  }

  stack.setDefaultFunctionProps({
    runtime: 'python3.9',
    timeout: '30 seconds',
  });

  if (!process.env.OPENAI_API_KEY) {
    throw Error('Error: OPENAI_API_KEY needs to be defined in .env');
  }

  EnvVars = {
    ...EnvVars,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    ELASTIC_API_KEY: process.env.ELASTIC_API_KEY as string,
    ELASTIC_ENDPOINT: process.env.ELASTIC_ENDPOINT as string,
  };

  let vpc: IVpc | undefined = undefined;
  let usingRds = !isLocal; // if not isLocal then should be using RDS

  // If we are running local then we don't need to generate the VPC or associated nat gateways
  if (!isLocal) {
    const {tailoVpc} = use(RdsStack);
    vpc = tailoVpc;
    EnvVars = {
      ...EnvVars,
      RDS_HOST: process.env.CLUSTER_ENDPOINT!,
      RDS_USERNAME: 'admin', // might not need to be sent in, could be extracted from secret?
      RDS_DB_NAME: process.env.CLUSTER_DB_NAME!,
      RDS_DB_PORT: process.env.CLUSTER_PORT!,
      SECRET_NAME: process.env.CLUSTER_SECRET_ARN!,
    };
  }

  const perms = [...getPermissions(usingRds), getKMSPolicy()];

  const vpcSubnet: SubnetSelection | undefined = vpc
    ? {subnetType: SubnetType.PRIVATE_WITH_EGRESS}
    : undefined;

  const bucket = new Bucket(stack, 'TailoDocumentBucket', {
    blockPublicACLs: true,
    cdk: {
      bucket: {
        versioned: true,
        autoDeleteObjects: !isProduction(app), // If prod then don't allow for auto delete
        removalPolicy: RemovalPolicy.DESTROY,
      },
    },
  });

  const textractBucket = new Bucket(stack, 'TextractBucket', {});

  EnvVars['BUCKET_NAME'] = bucket.bucketName;
  EnvVars['TEXTRACT_BUCKET_NAME'] = textractBucket.bucketName;

  EnvVars = {
    ...EnvVars,
    HUBSPOT_EVENT_QUEUE_URL: hubspotEventQueue.queueUrl,
    AMPLITUDE_EVENT_QUEUE_URL: amplitudeEventQueue.queueUrl,
    ELASTIC_DOCUMENT_EVENT_QUEUE_URL: elasticDocumentEventQueue.queueUrl,
    ELASTIC_DOCUMENT_BLOCK_EVENT_QUEUE_URL:
      elasticDocumentBlockEventQueue.queueUrl,
  };

  const emailTemplates = EmailTemplates(stack);

  EnvVars = {
    ...EnvVars,
    MAGIC_LINK_LOGIN: emailTemplates.MagicLinkLogin,
    MAGIC_LINK_REGISTRATION: emailTemplates.MagicLinkRegistration,
    WAIT_LIST_EMAIL: emailTemplates.WaitListEmail,
    WELCOME_EMAIL: emailTemplates.WelcomeEmail,
  };

  EnvVars = {
    ...EnvVars,
    GOOGLE_APPLICATION_CREDENTIALS:
      process.env.GOOGLE_APPLICATION_CREDENTIALS ?? '',
  };

  const documentIndexScheduler = new Cron(stack, 'DocumentIndexSchedule', {
    job: {
      function: new Function(stack, 'DocumentIndexScheduler', {
        runtime: 'python3.9',
        handler: 'aws/lambda/jobs/index_scheduler.handler',
        description:
          'Cron job to schedule the indexing of documents on a daily basis.',
        environment: EnvVars,
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
    schedule: 'rate(1 hour)',
  });

  const deleteChecker = new Cron(stack, 'DocumentDeleteChecker', {
    job: {
      function: new Function(stack, 'DocumentDeleteJob', {
        runtime: 'python3.9',
        handler: 'aws/lambda/jobs/deleted_checker.handler',
        description:
          'Cron job to delete files that have been marked as deleted for longer than the limit set in DELETE_DAY_LIMIT',
        environment: {DELETE_DAY_LIMIT: deleteDayLimit.toString(), ...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
    schedule: 'rate(1 day)',
  });

  const magicLinkTicketTable = new Table(stack, 'MagicLinkTickets', {
    fields: {
      ticketId: 'string',
      userId: 'string',
      ttl: 'number',
    },
    timeToLiveAttribute: 'ttl',
    primaryIndex: {partitionKey: 'ticketId', sortKey: 'userId'},
  });

  const legalsTable = new Table(stack, 'Legals', {
    fields: {
      title: 'string',
      version: 'number',
      content: 'string',
    },
    primaryIndex: {partitionKey: 'title', sortKey: 'version'},
  });

  const api = new Api(stack, 'TailoAPI', {
    defaults: {
      throttle: {
        burst: 100,
        rate: 100,
      },
    },
    customDomain: isLocal || isDevelopment ? undefined : apiDomainProps,
    authorizers: {
      GuestAuthorizer: {
        type: 'lambda',
        function: new Function(stack, 'GuestAuthorizer', {
          runtime: 'python3.9',
          handler: 'aws/lambda/authorizers/guest_auth.handler',
          description: 'Authorisation for guest login',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
        }),
      },
      UserAuthorizer: {
        type: 'lambda',
        function: new Function(stack, 'UserAuthorizer', {
          runtime: 'python3.9',
          handler: 'aws/lambda/authorizers/user_auth.handler',
          description: 'Authorisation for users',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
        }),
      },
      AdminAuthorizer: {
        type: 'lambda',
        function: new Function(stack, 'AdminAuthorizer', {
          runtime: 'python3.9',
          handler: 'aws/lambda/authorizers/admin_auth.handler',
          description: 'Authorisation for admin users',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
        }),
      },
      DocumentAuthorizer: {
        type: 'lambda',
        function: new Function(stack, 'DocumentAuthorizer', {
          runtime: 'python3.9',
          handler: 'aws/lambda/authorizers/document_auth.handler',
          description: 'Authorisation for document routes',
          environment: {...EnvVars},
        }),
      },
    },
    routes: {
      'POST /auth/refresh': new Function(stack, 'RefreshToken', {
        runtime: 'python3.9',
        handler: 'aws/lambda/refresh.handler',
        description:
          'Generate a new access token using the users refresh token',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
      'POST /auth/magic-link/email': new Function(stack, 'MagicLinkEmail', {
        runtime: 'python3.9',
        handler: 'aws/lambda/magic_link/email.handler',
        description: 'Generate a new magic link url',
        environment: {
          ...EnvVars,
          TICKETS_TABLE: magicLinkTicketTable.tableName,
          // Replace the default local host url with the tailo domain
          APP_URL: authUrl,
        },
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: [...perms, 'dynamodb', getEmailPolicy(emailSource)],
      }),
      'POST /auth/magic-link/auth': new Function(
        stack,
        'MagicLinkAuthorization',
        {
          runtime: 'python3.9',
          handler: 'aws/lambda/magic_link/auth.handler',
          description:
            "Login the user to the app based on the magic link's ticket",
          environment: {
            ...EnvVars,
            TICKETS_TABLE: magicLinkTicketTable.tableName,
            APP_URL: authUrl,
            PRELOADED_STUDENT_DOCUMENTS:
              process.env.PRELOADED_STUDENT_DOCUMENTS ?? '',
            PRELOADED_ASSESSOR_DOCUMENTS:
              process.env.PRELOADED_ASSESSOR_DOCUMENTS ?? '',
          },
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: [...perms, 'dynamodb', getEmailPolicy(emailSource)],
        },
      ),
      'GET /auth/register/details/{id}': new Function(
        stack,
        'GetRegisterTicketDetails',
        {
          runtime: 'python3.9',
          handler: 'aws/lambda/auth/registration/details.handler',
          description: 'Gets information associated to registration ticket',
          environment: {
            ...EnvVars,
          },
        },
      ),
      'PATCH /users': {
        authorizer: 'UserAuthorizer',
        function: new Function(stack, 'UserUpdate', {
          runtime: 'python3.9',
          handler: 'aws/lambda/users/update.handler',
          description: 'Endpoint for updating user details',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /user': {
        authorizer: 'UserAuthorizer',
        function: new Function(stack, 'FetchUser', {
          runtime: 'python3.9',
          handler: 'aws/lambda/users/fetch.handler',
          description: 'Endpoint for fetching user attributes',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /user/details': {
        function: new Function(stack, 'FetchUserDetails', {
          runtime: 'python3.9',
          handler: 'aws/lambda/users/user_details.handler',
          description: 'Endpoint for fetching all user details',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /users/settings': {
        authorizer: 'UserAuthorizer',
        function: new Function(stack, 'FetchUserSettings', {
          runtime: 'python3.9',
          handler: 'aws/lambda/users/fetch_settings.handler',
          description: 'Endpoint for fetching user settings',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'PUT /users/settings': {
        authorizer: 'UserAuthorizer',
        function: new Function(stack, 'UpdateUserSettings', {
          runtime: 'python3.9',
          handler: 'aws/lambda/users/update_settings.handler',
          description: 'Endpoint for updating user settings',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'POST /users/feedback': {
        authorizer: 'UserAuthorizer',
        function: new Function(stack, 'GetUserFeedback', {
          runtime: 'python3.9',
          handler: 'aws/lambda/users/feedback.handler',
          description: 'Endpoint for getting user feedback',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: [...perms, getEmailPolicy(feedbackEmail)],
        }),
      },
      'DELETE /users': {
        authorizer: 'UserAuthorizer',
        function: new Function(stack, 'UserDelete', {
          runtime: 'python3.9',
          handler: 'aws/lambda/users/delete.handler',
          description: 'Endpoint for deleting user',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /admin/beta-emails': {
        authorizer: 'AdminAuthorizer',
        function: new Function(stack, 'AdminBetaEmailReadAll', {
          runtime: 'python3.9',
          handler: 'aws/lambda/admin/beta_emails/read_all.handler',
          description: 'Endpoint for retrieving all beta emails',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'POST /admin/beta-emails': {
        authorizer: 'AdminAuthorizer',
        function: new Function(stack, 'AdminBetaEmailCreate', {
          runtime: 'python3.9',
          handler: 'aws/lambda/admin/beta_emails/create.handler',
          description: 'Endpoint for adding one or more beta emails',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'DELETE /admin/beta-emails': {
        authorizer: 'AdminAuthorizer',
        function: new Function(stack, 'AdminBetaEmailDelete', {
          runtime: 'python3.9',
          handler: 'aws/lambda/admin/beta_emails/delete.handler',
          description: 'Endpoint for deleting a beta email',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /documents/{id}/search': {
        function: new Function(stack, 'DocumentSearch', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/search.handler',
          description: 'Endpoint for searching for documents',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /documents/{id}': {
        function: new Function(stack, 'DocumentRead', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/read.handler',
          description: 'Endpoint for retrieving a document',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'POST /documents/{id}/index': {
        authorizer: 'DocumentAuthorizer',
        function: new Function(stack, 'DocumentIndex', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/index.handler',
          description: 'Endpoint for indexing a document',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /documents': {
        function: new Function(stack, 'DocumentReadAll', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/read_all.handler',
          description: 'Endpoint for retrieving all documents',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /documents/example': {
        function: new Function(stack, 'GetExampleDocument', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/example.handler',
          description: 'Endpoint for retrieving the example document',
          environment: {...EnvVars},
          permissions: perms,
        }),
      },
      'POST /documents': {
        function: new Function(stack, 'DocumentCreate', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/create.handler',
          description: 'Endpoint for creating a new document',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'PATCH /documents/{id}': {
        function: new Function(stack, 'DocumentUpdate', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/update.handler',
          description: 'Endpoint for updating an existing document',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /documents/download/{id}': {
        function: new Function(stack, 'DocumentDownload', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/download.handler',
          description: 'Endpoint for downloading a document',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'DELETE /documents/{id}': {
        function: new Function(stack, 'DocumentDelete', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/delete.handler',
          description: 'Endpoint for deleting a document',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /validation/{document_extension}': {
        function: new Function(stack, 'DocumentGetRules', {
          runtime: 'python3.9',
          handler: 'aws/lambda/documents/validation.handler',
          description: 'Endpoint for validating a document',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: [...perms],
        }),
      },
      'GET /terms-of-service': {
        function: new Function(stack, 'TermsOfService', {
          runtime: 'python3.9',
          handler: 'aws/lambda/legal_documents/terms_of_service.handler',
          description:
            'Endpoint for viewing the JSON formatted Terms of Service',
          environment: {...EnvVars, LEGALS_TABLE: legalsTable.tableName},
          permissions: [...perms, 'dynamodb'],
        }),
      },
      'GET /privacy-statement': {
        function: new Function(stack, 'PrivacyStatement', {
          runtime: 'python3.9',
          handler: 'aws/lambda/legal_documents/privacy_policy.handler',
          description:
            'Endpoint for viewing the JSON formatted Privacy Statement',
          environment: {...EnvVars, LEGALS_TABLE: legalsTable.tableName},
          permissions: [...perms, 'dynamodb'],
        }),
      },
      'GET /quotas': {
        authorizer: 'DocumentAuthorizer',
        function: new Function(stack, 'QuotasRead', {
          runtime: 'python3.9',
          handler: 'aws/lambda/quotas/read.handler',
          description: "Endpoint for fetching the user's quotas",
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },
      'GET /dictionary': {
        authorizer: 'DocumentAuthorizer',
        function: new Function(stack, 'Dictionary', {
          runtime: 'python3.9',
          handler: 'aws/lambda/dictionary/dictionary.handler',
          description:
            'Endpoint for retrieving the definition of a word from the dictionary',
          environment: {...EnvVars},
        }),
      },
      'POST /documents/{document_id}/tts': {
        authorizer: 'DocumentAuthorizer',
        function: new Function(stack, 'TextToSpeechDispatchJob', {
          runtime: 'python3.9',
          handler: 'aws/lambda/tts/dispatch_job.handler',
          description:
            'Endpoint for dispatching a job to handle synthesizing text into speech',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: [...perms, getPollyPolicy()],
        }),
      },
      'GET /documents/{document_id}/{section_id}/tts': {
        authorizer: 'DocumentAuthorizer',
        function: new Function(stack, 'TextToSpeechFetchSectionAudio', {
          runtime: 'python3.9',
          handler: 'aws/lambda/tts/fetch_section_audio.handler',
          description:
            'Endpoint for retrieving the TTS audio for a particular section',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: [...perms, getPollyPolicy()],
        }),
      },
      'GET /documents/{document_id}/tts': {
        authorizer: 'DocumentAuthorizer',
        function: new Function(stack, 'TextToSpeechFetchAudio', {
          runtime: 'python3.9',
          handler: 'aws/lambda/tts/fetch_audio.handler',
          description: 'Endpoint for retrieving the TTS audio',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: [...perms, getPollyPolicy()],
        }),
      },
      'POST /documents/{document_id}/summary/{section_id}': {
        authorizer: 'DocumentAuthorizer',
        function: new Function(stack, 'CreateSectionSummary', {
          runtime: 'python3.9',
          handler: 'aws/lambda/section_summary/create_section_summary.handler',
          description:
            'Endpoint for creating a summary for a particular section',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: [...perms],
        }),
      },
      'GET /documents/{document_id}/summary': {
        authorizer: 'DocumentAuthorizer',
        function: new Function(stack, 'FetchDocumentSummaries', {
          runtime: 'python3.9',
          handler:
            'aws/lambda/section_summary/fetch_document_summaries.handler',
          description:
            'Endpoint for fetching summaries for a particular document',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: [...perms],
        }),
      },
      'POST /documents/{id}/explain': {
        authorizer: 'DocumentAuthorizer',
        function: new Function(stack, 'Explain', {
          runtime: 'python3.9',
          handler: 'aws/lambda/explain/explain.handler',
          description:
            'Endpoint for returning an explanation of a selection of text using AI Prompt',
          environment: {...EnvVars},
          vpc: vpc,
          vpcSubnets: vpcSubnet,
          permissions: perms,
        }),
      },

      // Version 2 API endpoints.
    },
  });

  const legalFunction = new Function(stack, 'legalFunction', {
    runtime: 'nodejs18.x',
    handler: 'aws/scripts/legalsScript.handler',
    description: 'Handler to upload the legals to the database',
    environment: {
      ...EnvVars,
      LEGALS_TABLE: legalsTable.tableName,
    },
    permissions: [...perms, 'dynamodb'],
    enableLiveDev: false,
    bind: [legalsTable],
    copyFiles: [{from: 'aws/scripts/data', to: 'aws/scripts/data'}],
  });

  new Script(stack, 'LegalsScript', {
    onCreate: legalFunction,
    onUpdate: legalFunction,
  });

  const exampleDocumentFunction = new Function(stack, 'CreateExampleDocument', {
    runtime: 'nodejs18.x',
    handler: 'aws/scripts/createExampleDocument.handler',
    description: 'Handler to upload example document to s3',
    environment: {
      ...EnvVars,
    },
    permissions: [...perms, 's3'],
    enableLiveDev: false,
    bind: [bucket],
    copyFiles: [{from: 'aws/scripts/data', to: 'aws/scripts/data'}],
  });

  new Script(stack, 'ExampleDocumentScript', {
    onCreate: exampleDocumentFunction,
    onUpdate: exampleDocumentFunction,
  });

  api.addRoutes(stack, {
    'GET /socket-url': {
      authorizer: 'GuestAuthorizer',
      function: new Function(stack, 'GetSocketURL', {
        runtime: 'python3.9',
        handler: 'aws/lambda/get_comms.handler',
        description: 'Endpoint for retrieving a summarisation',
        environment: {
          ...EnvVars,
          WEBSOCKET_URL: websocket.url,
          TICKETS_TABLE: socketTicketsTable.tableName,
        },
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: [...perms, 'dynamodb'],
      }),
    },
  });

  //////////// SNS/SQS //////////////

  /////////////////////////////////
  const fileExtraction = new Function(stack, 'FileExtraction', {
    runtime: 'python3.9',
    handler: 'aws/lambda/documents/upload.handler',
    description: 'Notification function when file is file uploaded',
    environment: {
      ...EnvVars,
      BUS_NAME: eventBus.eventBusName,
      WEBSOCKET_URL: websocket.url.replace('wss://', ''),
      SOCKETS_TABLE: socketTable.tableName,
      TICKETS_TABLE: socketTicketsTable.tableName,
    },
    vpc: vpc,
    vpcSubnets: vpcSubnet,
    permissions: [...perms, websocket, 'dynamodb', 'events'],
  });

  bucket.addNotifications(stack, {
    UploadedFileNotification: {
      function: fileExtraction,
      events: ['object_created_post'],
    },
  });

  const audioCreation = new Function(stack, 'AudioCreation', {
    runtime: 'python3.9',
    handler: 'aws/lambda/tts/handle_notification.handler',
    description: 'Notification function when audio file is created',
    environment: {
      ...EnvVars,
      BUS_NAME: eventBus.eventBusName,
      WEBSOCKET_URL: websocket.url.replace('wss://', ''),
      SOCKETS_TABLE: socketTable.tableName,
      TICKETS_TABLE: socketTicketsTable.tableName,
    },
    vpc: vpc,
    vpcSubnets: vpcSubnet,
    permissions: [...perms, websocket, 'events'],
    bind: [socketTable],
    timeout: '5 minutes',
  });

  bucket.addNotifications(stack, {
    UploadedAudioNotification: {
      function: audioCreation,
      events: ['object_created_put'],
    },
  });

  // Outputs the generated site url to the cli once deployed
  stack.addOutputs({
    // SiteUrl: site.customDomainUrl || site.url,
    ApiUrl: api.customDomainUrl || api.url,
    WebsocketUrl: websocket.url,
  });

  return {
    api,
    bucket,
    textractBucket,
    EnvVars,
    perms,
    vpc,
    vpcSubnet,
  };
}
