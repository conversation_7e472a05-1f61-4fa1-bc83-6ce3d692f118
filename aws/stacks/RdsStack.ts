import {Peer, Port, SecurityGroup, SubnetType, Vpc} from 'aws-cdk-lib/aws-ec2';
import {StackContext} from 'sst/constructs';
import {Function, Script} from 'sst/constructs';

export function RdsStack({app, stack}: StackContext) {
  const vpcId = process.env.VPC_ID;
  const securityGroupId = process.env.SECURITY_GROUP_ID;

  const pgVpcId = 'PGVpc';
  const pgSecurityGroupId = 'PGDBSecurityGroup';

  if (!vpcId) console.error("No 'vpcId' specified, creating new");

  const tailoVpc = vpcId
    ? Vpc.fromLookup(stack, pgVpcId, {vpcId})
    : new Vpc(stack, 'PGVpc', {
        natGateways: 1,
        subnetConfiguration: [
          {
            cidrMask: 24,
            name: 'Private',
            subnetType: SubnetType.PRIVATE_ISOLATED,
          },
          {
            cidrMask: 24,
            name: 'PrivateWithEgress',
            subnetType: SubnetType.PRIVATE_WITH_EGRESS,
          },
          {
            cidrMask: 24,
            name: 'Public',
            subnetType: SubnetType.PUBLIC,
          },
        ],
      });

  if (!securityGroupId)
    console.error("No 'securityGroupId' specified, creating new");

  const securityGroup = securityGroupId
    ? SecurityGroup.fromLookupById(stack, pgSecurityGroupId, securityGroupId)
    : new SecurityGroup(stack, 'PGDBSecurityGroup', {
        vpc: tailoVpc,
        description: 'Security Group to access RDS system from Lambda',
        allowAllOutbound: true,
      });

  if (!securityGroupId) {
    securityGroup.addIngressRule(
      Peer.anyIpv4(),
      Port.allTraffic(),
      'Allow all traffic for now',
    );
  }

  const migrateFunction = new Function(stack, 'migrateFunction', {
    runtime: 'nodejs18.x',
    handler: 'aws/scripts/migrationScript.handler',
    description: 'Handler to upload the migrations to the database',
    environment: {
      RDS_HOST: process.env.CLUSTER_ENDPOINT!,
      RDS_USERNAME: 'admin', // might not need to be sent in, could be extracted from secret?
      RDS_DB_NAME: process.env.CLUSTER_DB_NAME!,
      RDS_DB_PORT: process.env.CLUSTER_PORT!,
      SECRET_NAME: process.env.CLUSTER_SECRET_ARN!,
    },
    vpc: tailoVpc,
    vpcSubnets: {subnetType: SubnetType.PRIVATE_WITH_EGRESS},
    permissions: ['rds-data', 'secretsmanager'],
    enableLiveDev: false,
    nodejs: {
      install: ['kysely', 'uuid'],
    },
    timeout: '5 minutes',
    copyFiles: [{from: 'db/migrations', to: 'db/migrations'}],
  });
  new Script(stack, 'MigrationScript', {
    onCreate: migrateFunction,
    onUpdate: migrateFunction,
  });

  return {
    securityGroup,
    tailoVpc,
  };
}
