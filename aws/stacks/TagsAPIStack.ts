import {Function, StackContext, use} from 'sst/constructs';
import {ApiStack} from './ApiStack';

export function TagsApiStack({app, stack}: StackContext) {
  const {api, EnvVars, perms, vpc, vpcSubnet} = use(ApiStack);

  stack.setDefaultFunctionProps({
    runtime: 'python3.9',
    timeout: '30 seconds',
  });

  api.addRoutes(stack, {
    'GET /v2/tags': {
      function: new Function(stack, 'V2ListTags', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/tags/list.handler',
        description: "Endpoint for fetching the user's tags",
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
  });
}
