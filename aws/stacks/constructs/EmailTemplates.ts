import {Stack} from 'sst/constructs';
import fs from 'fs';
import {extname} from 'path';
import * as ses from 'aws-cdk-lib/aws-ses';

interface EmailTemplates {
  MagicLinkLogin: TemplateInfo;
  MagicLinkRegistration: TemplateInfo;
  WaitListEmail: TemplateInfo;
  WelcomeEmail: TemplateInfo;
}

interface TemplateInfo {
  foldername: string;
  subject: string;
}

type EmailTemplateResponse = {
  [key in keyof EmailTemplates]: string;
};

interface ExtractedImageProperties {
  extension: string;
  base64: string;
}

interface ExtractedImages {
  [key: string]: ExtractedImageProperties;
}

const emailPath = 'aws/emails/';
const templates: EmailTemplates = {
  MagicLinkLogin: {
    foldername: 'MagicLinkLogin',
    subject: 'Sign in link',
  },
  MagicLinkRegistration: {
    foldername: 'MagicLinkRegistration',
    subject: 'Registration link',
  },
  WaitListEmail: {
    foldername: 'WaitListEmail',
    subject: 'Join the waitlist',
  },
  WelcomeEmail: {
    foldername: 'WelcomeEmail',
    subject: 'Welcome to our platform',
  },
};

function getImages(foldername: string): ExtractedImages | undefined {
  const imageFolder = 'images';
  const path = `${emailPath}${foldername}/${imageFolder}`;
  if (!fs.existsSync(path)) return undefined;

  const retVal: ExtractedImages = {};
  const images = fs.readdirSync(path);
  for (const imageName of images) {
    const image = fs.readFileSync(`${path}/${imageName}`, {encoding: 'base64'});
    retVal[`${imageFolder}/${imageName}`] = {
      extension: extname(imageName).replace('.', ''),
      base64: image,
    };
  }

  return retVal;
}

function getHTML(foldername: string) {
  const path = `${emailPath}${foldername}`;
  return fs.readFileSync(`${path}/index.html`, 'utf-8');
}

function generateTemplate(stack: Stack, templateInfo: TemplateInfo) {
  const templateHTML = getHTML(templateInfo.foldername);

  const templateName = `${stack.stackName}-${templateInfo.foldername}`;

  fs.writeFileSync('aws/emails/test.html', templateHTML);

  new ses.CfnTemplate(stack, templateName, {
    template: {
      subjectPart: templateInfo.subject,
      htmlPart: templateHTML,
      templateName: templateName,
    },
  });

  return templateName;
}

export function EmailTemplates(stack: Stack): EmailTemplateResponse {
  const retTemplates: EmailTemplateResponse = {
    MagicLinkLogin: generateTemplate(stack, templates.MagicLinkLogin),
    MagicLinkRegistration: generateTemplate(
      stack,
      templates.MagicLinkRegistration,
    ),
    WaitListEmail: generateTemplate(stack, templates.WaitListEmail),
    WelcomeEmail: generateTemplate(stack, templates.WelcomeEmail),
  };

  return retTemplates;
}
