import {Function, StackContext, use} from 'sst/constructs';
import {ApiStack} from './ApiStack';

export function QuotasApiStack({app, stack}: StackContext) {
  const {api, EnvVars, perms, vpc, vpcSubnet} = use(ApiStack);

  stack.setDefaultFunctionProps({
    runtime: 'python3.9',
    timeout: '30 seconds',
  });

  api.addRoutes(stack, {
    'GET /v2/quotas': {
      function: new Function(stack, 'V2GetQuotas', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/quotas/read.handler',
        description: "Endpoint for fetching the user's quotas",
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
  });
}
