import {NextjsSite, StackContext, use} from 'sst/constructs';
import {ApiStack} from './ApiStack';
import {isLocal} from './helpers/isRunningLocal';
import {isDevelopment} from './helpers/isDevelopment';
import {getDomainProps} from './helpers/createEndpoints';
import {getAmplitudeKey} from './helpers/getAmplitudeKey';

export function NextStack({app, stack}: StackContext) {
  const apiStack = use(ApiStack);

  const {nextDomainProps} = getDomainProps(app);
  const amplitudeKey = getAmplitudeKey(app);

  const site = new NextjsSite(stack, 'site', {
    customDomain:
      isLocal(app) || isDevelopment(app) ? undefined : nextDomainProps,
    openNextVersion: '2.2.4',
    environment: {
      NEXT_PUBLIC_API_URL: apiStack.api.customDomainUrl || apiStack.api.url,
      API_URL: apiStack.api.customDomainUrl || apiStack.api.url,
      NEXT_PUBLIC_BUCKET_URL: apiStack.bucket.bucketName + '.s3.amazonaws.com',
      NEXT_PUBLIC_AMPLITUDE_API_KEY: amplitudeKey,
      NEXT_PUBLIC_PUSHER_KEY: process.env.PUSHER_KEY ?? '',
      NEXT_PUBLIC_SECRET: process.env.NEXT_PUBLIC_SECRET ?? '',
      NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET ?? '',
      NEXTAUTH_URL: process.env.NEXTAUTH_URL ?? '',
      SENTRY_DNS: process.env.SENTRY_DNS ?? '',
      NEXT_PUBLIC_MICROSOFT_CLARITY:
        process.env.NEXT_PUBLIC_MICROSOFT_CLARITY ?? '',
      NEXT_PUBLIC_GOOGLE_ANALYTICS:
        process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS ?? '',
      MAINTENANCE_MODE: process.env.MAINTENANCE_MODE ?? 'false',
    },
  });

  site.attachPermissions(['sns']);

  stack.addOutputs({
    SiteUrl: site.customDomainUrl || site.url,
  });
}
