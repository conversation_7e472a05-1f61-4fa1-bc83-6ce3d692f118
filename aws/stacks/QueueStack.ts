import {StackContext, Queue, use, RDS} from 'sst/constructs';
import {RdsStack} from './RdsStack';
import {isLocal as runningLocal} from './helpers/isRunningLocal';
import {IVpc, SubnetSelection, SubnetType} from 'aws-cdk-lib/aws-ec2';
import {Duration} from 'aws-cdk-lib';
import {getPermissions} from './helpers/getPermissions';
import {getAmplitudeKey} from './helpers/getAmplitudeKey';
import {getSNSPolicy} from './helpers/getSnsPolicy';
import * as sns from 'aws-cdk-lib/aws-sns';
import {DeduplicationScope, FifoThroughputLimit} from 'aws-cdk-lib/aws-sqs';

export function QueueStack({stack, app}: StackContext) {
  stack.setDefaultFunctionProps({
    runtime: 'python3.9',
    timeout: '30 seconds',
  });

  const isLocal = runningLocal(app);

  let EnvVars: Record<string, string> = {
    SENTRY_DNS: process.env.SENTRY_DNS!,
  };

  let vpc: IVpc | undefined = undefined;
  let usingRds = !isLocal; // if not isLocal then should be using RDS
  const vpcSubnet: SubnetSelection | undefined = vpc
    ? {subnetType: SubnetType.PRIVATE_WITH_EGRESS}
    : undefined;

  const textractTopic = new sns.Topic(stack, 'TextractTopic', {
    topicName: `TextractQueueTopic_${app.stage}`,
  });

  if (isLocal) {
    EnvVars = {
      ...EnvVars,
      LOCAL_DB_HOST: process.env.DATABASE_HOST!,
      LOCAL_DB_PORT: process.env.DATABASE_PORT!,
      LOCAL_DB_USER: process.env.DATABASE_USER!,
      LOCAL_DB_NAME: process.env.DATABASE_NAME!,
    };
  } else {
    const {tailoVpc} = use(RdsStack);
    vpc = tailoVpc;
    EnvVars = {
      ...EnvVars,
      RDS_HOST: process.env.CLUSTER_ENDPOINT!,
      RDS_USERNAME: 'admin', // might not need to be sent in, could be extracted from secret?
      RDS_DB_NAME: process.env.CLUSTER_DB_NAME!,
      RDS_DB_PORT: process.env.CLUSTER_PORT!,
      SECRET_NAME: process.env.CLUSTER_SECRET_ARN!,
    };
  }

  const perms = getPermissions(usingRds);
  const amplitudeKey = getAmplitudeKey(app);

  const hubspotEventQueue = new Queue(stack, 'HubspotSendEventQueue', {
    consumer: {
      function: {
        handler: 'aws/lambda/queues/hubspot/send_event.handler',
        environment: {...EnvVars},
      },
    },
    cdk: {
      queue: {
        fifo: true,
      },
    },
  });

  const amplitudeEventQueue = new Queue(stack, 'AmplitudeTrackEventQueue', {
    consumer: {
      function: {
        handler: 'aws/lambda/queues/amplitude/track.handler',
        environment: {AMPLITUDE_KEY: amplitudeKey, ...EnvVars},
      },
    },
    cdk: {
      queue: {
        fifo: true,
      },
    },
  });

  const elasticDocumentBlockEventQueue = new Queue(
    stack,
    'ElasticDocumentIndexBlockEventQueue',
    {
      consumer: {
        function: {
          handler: 'aws/lambda/queues/elastic/index_block.handler',
          environment: {
            ...EnvVars,
            ELASTIC_API_KEY: process.env.ELASTIC_API_KEY!,
            ELASTIC_ENDPOINT: process.env.ELASTIC_ENDPOINT!,
          },
        },
      },
      cdk: {
        queue: {
          fifo: true,
        },
      },
    },
  );

  const elasticDocumentEventQueue = new Queue(
    stack,
    'ElasticIndexDocumentEventQueue',
    {
      consumer: {
        function: {
          handler: 'aws/lambda/queues/elastic/index_document.handler',
          environment: {
            ...EnvVars,
            ELASTIC_DOCUMENT_BLOCK_EVENT_QUEUE_URL:
              elasticDocumentBlockEventQueue.queueUrl,
            ELASTIC_API_KEY: process.env.ELASTIC_API_KEY!,
            ELASTIC_ENDPOINT: process.env.ELASTIC_ENDPOINT!,
          },
          permissions: perms,
          vpc: vpc,
          vpcSubnets: vpcSubnet,
        },
      },
      cdk: {
        queue: {
          fifo: true,
        },
      },
    },
  );

  const kickoffTextractEventQueue = new Queue(
    stack,
    'KickoffTextractEventQueue',
    {
      consumer: {
        function: {
          reservedConcurrentExecutions: 1,
          handler: 'aws/lambda/queues/textract/kickoff.handler',
          environment: {
            ...EnvVars,
            TOPIC_ARN: textractTopic.topicArn,
            SNS_ARN: getSNSPolicy(stack).roleArn,
          },
          permissions: perms,
          vpc: vpc,
          vpcSubnets: vpcSubnet,
        },
      },
      cdk: {
        queue: {
          fifo: true,
          maxMessageSizeBytes: 262144,
          visibilityTimeout: Duration.minutes(30),
          fifoThroughputLimit: FifoThroughputLimit.PER_MESSAGE_GROUP_ID,
          deduplicationScope: DeduplicationScope.MESSAGE_GROUP,
        },
      },
    },
  );

  return {
    hubspotEventQueue,
    amplitudeEventQueue,
    elasticDocumentBlockEventQueue,
    elasticDocumentEventQueue,
    kickoffTextractEventQueue,
    textractTopic,
  };
}
