import {Function, StackContext, use} from 'sst/constructs';
import {ApiStack} from './ApiStack';
import {getPollyPolicy} from './helpers/policies';

export function DocumentsApiStack({app, stack}: StackContext) {
  const {api, EnvVars, perms, vpc, vpcSubnet} = use(ApiStack);

  stack.setDefaultFunctionProps({
    runtime: 'python3.9',
    timeout: '30 seconds',
  });

  const commentThreadsFunction = new Function(
    stack,
    'DocumentsApiCommentThreadsFunction',
    {
      runtime: 'python3.9',
      handler: 'aws/lambda/v2/documents/comment_threads/main.handler',
      description: 'Function for handling comment threads endpoints',
      environment: {...EnvVars},
      vpc: vpc,
      vpcSubnets: vpcSubnet,
      permissions: perms,
    },
  );

  api.addRoutes(stack, {
    'GET /v2/documents/{id}': {
      authorizer: 'DocumentAuthorizer',
      function: new Function(stack, 'V2DocumentRead', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/read.handler',
        description: 'Endpoint for retrieving a document',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
    'PATCH /v2/documents/{id}': {
      function: new Function(stack, 'V2DocumentUpdate', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/update.handler',
        description: 'Endpoint for retrieving a document',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
    'GET /v2/documents/{id}/state': {
      authorizer: 'DocumentAuthorizer',
      function: new Function(stack, 'V2DocumentStateRead', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/state.handler',
        description: 'Endpoint for retrieving a document state',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
    'GET /v2/documents/{id}/comment-threads': {
      function: commentThreadsFunction,
    },
    'PUT /v2/documents/{id}/comment-threads': {
      function: commentThreadsFunction,
    },
    'PATCH /v2/documents/{id}/comment-threads/{thread_id}': {
      function: commentThreadsFunction,
    },
    'DELETE /v2/documents/{id}/comment-threads/{thread_id}': {
      function: commentThreadsFunction,
    },
    'PUT /v2/documents/{id}/highlights': {
      function: new Function(stack, 'V2DocumentSaveHighlight', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/highlights/save.handler',
        description: 'Endpoint for saving a document highlight',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
    'DELETE /v2/documents/{id}/highlights/{highlight_id}': {
      function: new Function(stack, 'V2DocumentDeleteHighlight', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/highlights/delete.handler',
        description: 'Endpoint for saving a document highlight',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
    'GET /v2/documents/{id}/state-upload-url': {
      authorizer: 'DocumentAuthorizer',
      function: new Function(stack, 'V2DocumentStateUploadUrl', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/state_upload_url.handler',
        description: 'Endpoint for retrieving the state upload URL',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: perms,
      }),
    },
    'POST /v2/documents/{id}/media': {
      authorizer: 'DocumentAuthorizer',
      function: new Function(stack, 'V2FetchMediaPresignedUrl', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/media/presigned_url.handler',
        description: 'Endpoint for fetching a presigned URL for a media file',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: [...perms],
      }),
    },
    'POST /v2/documents/{id}/audio': {
      function: new Function(stack, 'V2DocumentCreateAudio', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/audio/create.handler',
        description: 'Endpoint for creating document audio',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: [...perms, getPollyPolicy()],
      }),
    },

    'POST /v2/documents/status': {
      function: new Function(stack, 'V2GetDocumentStatus', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/status.handler',
        description: 'Endpoint for getting document(s) status',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: [...perms],
      }),
    },

    'GET /v2/documents/{id}/tags': {
      function: new Function(stack, 'V2GetDocumentTags', {
        runtime: 'python3.9',
        handler: 'aws/lambda/v2/documents/tags/list.handler',
        description: 'Gets the tags attached to the document and its comments',
        environment: {...EnvVars},
        vpc: vpc,
        vpcSubnets: vpcSubnet,
        permissions: [...perms],
      }),
    },
  });
}
