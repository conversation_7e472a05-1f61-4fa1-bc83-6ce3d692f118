import {StackContext, Function, use, RDS} from 'sst/constructs';
import {RdsStack} from './RdsStack';
import {EventBusStack} from './EventBusStack';
import {ApiStack} from './ApiStack';
import {WebsocketStack} from './WebsocketStack';
import * as sns from 'aws-cdk-lib/aws-sns';
import {IVpc, SubnetSelection, SubnetType} from 'aws-cdk-lib/aws-ec2';
import {isLocal as runningLocal} from './helpers/isRunningLocal';
import {getDocAnalysisPolicy} from './helpers/getDocAnalysisPolicy';
import {SnsEventSource} from 'aws-cdk-lib/aws-lambda-event-sources';
import {getPermissions} from './helpers/getPermissions';
import {getSNSPolicy} from './helpers/getSnsPolicy';
import {QueueStack} from './QueueStack';

export function ExtractorStack({app, stack}: StackContext) {
  stack.setDefaultFunctionProps({
    runtime: 'python3.9',
    timeout: '30 seconds',
  });

  const isLocal = runningLocal(app);
  let vpc: IVpc | undefined = undefined;
  let usingRds = !isLocal; // if not isLocal then should be using RDS
  const vpcSubnet: SubnetSelection | undefined = vpc
    ? {subnetType: SubnetType.PRIVATE_WITH_EGRESS}
    : undefined;

  const {eventBus} = use(EventBusStack);
  const {websocket, socketTable, socketTicketsTable} = use(WebsocketStack);
  const {
    amplitudeEventQueue,
    elasticDocumentEventQueue,
    kickoffTextractEventQueue,
    textractTopic,
  } = use(QueueStack);
  const {bucket, textractBucket} = use(ApiStack);

  let EnvVars: Record<string, string> = {
    BUCKET_NAME: bucket.bucketName,
    TEXTRACT_BUCKET_NAME: textractBucket.bucketName,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY!,
    MISTRAL_API_KEY: process.env.MISTRAL_API_KEY!,
    FEATURE_USE_MISTRAL_FOR_OCR: process.env.FEATURE_USE_MISTRAL_FOR_OCR!,
    AMPLITUDE_EVENT_QUEUE_URL: amplitudeEventQueue.queueUrl,
    ELASTIC_DOCUMENT_EVENT_QUEUE_URL: elasticDocumentEventQueue.queueUrl,
    SENTRY_DNS: process.env.SENTRY_DNS!,
  };

  // If we are running local then we don't need to generate the VPC or associated nat gateways
  if (isLocal) {
    EnvVars = {
      ...EnvVars,
      LOCAL_DB_HOST: process.env.DATABASE_HOST!,
      LOCAL_DB_PORT: process.env.DATABASE_PORT!,
      LOCAL_DB_USER: process.env.DATABASE_USER!,
      LOCAL_DB_NAME: process.env.DATABASE_NAME!,
    };
  } else {
    const {tailoVpc} = use(RdsStack);
    vpc = tailoVpc;
    EnvVars = {
      ...EnvVars,
      RDS_HOST: process.env.CLUSTER_ENDPOINT!,
      RDS_USERNAME: 'admin', // might not need to be sent in, could be extracted from secret?
      RDS_DB_NAME: process.env.CLUSTER_DB_NAME!,
      RDS_DB_PORT: process.env.CLUSTER_PORT!,
      SECRET_NAME: process.env.CLUSTER_SECRET_ARN!,
    };
  }

  const perms = getPermissions(usingRds);

  const uploadedFileFunction = new Function(
    stack,
    'DocumentUploadedNotification',
    {
      runtime: 'container',
      container: {
        file: 'dockerfile.file_upload',
      },
      handler: 'aws/lambda/',
      description: 'Notification function when file is file uploaded',
      environment: {
        ...EnvVars,
        LOCAL_DB_HOST: 'host.docker.internal',
        WEBSOCKET_URL: websocket.url.replace('wss://', ''),
        TEXTRACT_QUEUE_URL: kickoffTextractEventQueue.queueUrl,
        SOCKETS_TABLE: socketTable.tableName,
        TICKETS_TABLE: socketTicketsTable.tableName,
        TOPIC_ARN: textractTopic.topicArn,
        SNS_ARN: getSNSPolicy(stack).roleArn,
      },
      vpc: vpc,
      vpcSubnets: vpcSubnet,
      permissions: [...perms, websocket, getDocAnalysisPolicy()],
      bind: [socketTable],
      timeout: '5 minutes',
    },
  );

  // Create Lambda function for processing Textract completion events
  const resultProcessor = new Function(
    stack,
    'TextractExtractionResultProcessor',
    {
      runtime: 'container',
      container: {
        file: 'dockerfile.textract',
      },
      handler: 'aws/lambda/',
      description: 'Process Textract completion events',
      environment: {
        ...EnvVars,
        LOCAL_DB_HOST: 'host.docker.internal',
        WEBSOCKET_URL: websocket.url.replace('wss://', ''),
        SOCKETS_TABLE: socketTable.tableName,
        TICKETS_TABLE: socketTicketsTable.tableName,
        TOPIC_ARN: textractTopic.topicArn,
      },
      vpc: vpc,
      vpcSubnets: vpcSubnet,
      permissions: [...perms, websocket, getDocAnalysisPolicy()],
      bind: [socketTable],
      timeout: '15 minutes', // Max Lambda timeout
      memorySize: '5 GB',
    },
  );

  // Subscribe the Lambda function to the SNS topic
  resultProcessor.addEventSource(new SnsEventSource(textractTopic));

  // Grant the Lambda function permissions to access Textract
  resultProcessor.attachPermissions(['textract']);

  eventBus.addRules(stack, {
    ExtractionRule: {
      pattern: {
        source: ['extraction.process'],
        detailType: ['extraction-info'],
      },
      targets: {
        ExtractionTarget1: {
          function: uploadedFileFunction,
        },
      },
    },
  });
}
