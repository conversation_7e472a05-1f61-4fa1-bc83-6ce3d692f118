import {StackContext, WebSocketApi, Function, Table} from 'sst/constructs';

export function WebsocketStack({app, stack}: StackContext) {
  stack.setDefaultFunctionProps({
    runtime: 'python3.9',
    timeout: '30 seconds',
  });

  const socketTable = new Table(stack, 'WebsocketConnectionsTable', {
    fields: {
      userId: 'string',
      sessionId: 'string',
    },
    primaryIndex: {partitionKey: 'userId', sortKey: 'sessionId'},
  });
  const socketTicketsTable = new Table(stack, 'WebsocketTicketsTable', {
    fields: {
      ticketId: 'string',
      userId: 'string',
      consumed: 'string',
    },
    primaryIndex: {partitionKey: 'ticketId', sortKey: 'userId'},
  });

  const websocket = new WebSocketApi(stack, 'WebsocketApi', {
    defaults: {
      function: {
        timeout: 20,
        runtime: 'python3.9',
        environment: {
          SOCKETS_TABLE: socketTable.tableName,
          TICKETS_TABLE: socketTicketsTable.tableName,
        },
        bind: [socketTable],
        permissions: ['dynamodb'],
      },
    },
    authorizer: {
      type: 'lambda',
      identitySource: ['route.request.querystring.ticket'],
      function: new Function(stack, 'WebsocketAuthoriser', {
        handler: 'aws/lambda/authorizers/websocket_auth.handler',
        description: 'Authorisation for guest login',
        environment: {
          TICKETS_TABLE: socketTicketsTable.tableName,
          SOCKETS_TABLE: socketTable.tableName,
        },
        permissions: ['dynamodb'],
      }),
    },
    routes: {
      $connect: 'aws/lambda/websocket/connect.main',
      $disconnect: 'aws/lambda/websocket/disconnect.main',
      sendmessage: 'aws/lambda/websocket/send_message.main',
      $default: 'aws/lambda/websocket/default.main',
    },
  });

  return {
    websocket,
    socketTable,
    socketTicketsTable,
  };
}
