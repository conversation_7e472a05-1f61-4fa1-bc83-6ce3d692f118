import {ApiRouteProps, Function, StackContext, use} from 'sst/constructs';
import {ApiStack} from './ApiStack';
import {createV2Endpoints} from './helpers/createV2Endpoints';

export function AuthApiStack({app, stack}: StackContext) {
  const {api, EnvVars, perms, vpc, vpcSubnet} = use(ApiStack);

  stack.setDefaultFunctionProps({
    runtime: 'python3.9',
    timeout: '30 seconds',
  });

  const authFunction = new Function(stack, 'AuthAPIFunction', {
    runtime: 'python3.9',
    handler: 'aws/lambda/v2/auth/main.handler',
    description: 'Function for handling V2 auth calls',
    environment: {...EnvVars},
    vpc: vpc,
    vpcSubnets: vpcSubnet,
    permissions: perms,
  });

  const routes = createV2Endpoints([
    {path: 'auth/login', method: 'POST', function: authFunction},
    {path: 'auth/login/verify-ticket', method: 'GET', function: authFunction},
    {path: 'auth/login/verify-otp', method: 'POST', function: authFunction},
  ]);

  api.addRoutes(stack, routes);
}
