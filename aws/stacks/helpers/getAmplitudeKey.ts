import {App} from 'sst/constructs';

export function getAmplitudeKey(app: App) {
  let key = '';
  switch (app.stage) {
    case 'staging':
      key = '227f978ad54214b52b147bb956de0074';
      break;
    // No alpha key and don't know how to create one so using prod for now
    case 'alpha':
      key = '8e27bc8e048f5869d9567b3886644bac';
      break;
    case 'production':
      key = 'bd5200dcfa6f39db8aad1f2043fc2865';
      break;
    case 'production-us':
      key = 'bd5200dcfa6f39db8aad1f2043fc2865';
      break;
    default:
      key = '3396c28673c6726899085d5d0961b2a3';
  }
  return key;
}
