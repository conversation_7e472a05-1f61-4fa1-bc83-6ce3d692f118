import * as iam from 'aws-cdk-lib/aws-iam';

export function getKMSPolicy() {
  return new iam.PolicyStatement({
    effect: iam.Effect.ALLOW,
    actions: ['kms:*'],
    resources: ['*'],
  });
}

export function getEmailPolicy(emailSource: string) {
  return new iam.PolicyStatement({
    effect: iam.Effect.ALLOW,
    actions: ['ses:SendEmail', 'ses:SendRawEmail', 'ses:SendTemplatedEmail'],
    resources: ['*'],
    conditions: {
      StringEquals: {
        'ses:FromAddress': emailSource,
      },
    },
  });
}

export function getPollyPolicy() {
  const role = new iam.PolicyStatement({
    effect: iam.Effect.ALLOW,
    actions: ['polly:StartSpeechSynthesisTask', 'polly:SynthesizeSpeech'],
    resources: ['*'],
  });

  return role;
}
