import {Function} from 'sst/constructs';

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export interface V2EndpointArgs {
  method: HttpMethod;
  path: string;
  function: Function;
}
export function createV2Endpoints(endpoints: V2EndpointArgs[]) {
  return endpoints.reduce((acc, route) => {
    const key = `${route.method} /v2/${route.path}`;
    acc[key] = route.function;
    return acc;
  }, {} as Record<string, Function>);
}
