import {App} from 'sst/constructs';
import {stages} from '../constants/stages';

export function isLocal(app: App) {
  let res = false;

  if (app.mode === 'dev') {
    return true;
  } else if (app.mode === 'remove') {
    // if the mode is remove we need to check if the stack we are removing matches a stage
    if (!Object.values(stages).includes(app.stage)) {
      res = true;
    }
  }

  return res;
}
