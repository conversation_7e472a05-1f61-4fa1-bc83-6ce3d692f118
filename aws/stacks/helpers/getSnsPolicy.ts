import {Stack} from 'sst/constructs';
import * as iam from 'aws-cdk-lib/aws-iam';

export function getSNSPolicy(stack: Stack) {
  const role = new iam.Role(stack, 'TextractRole', {
    inlinePolicies: {
      Test: new iam.PolicyDocument({
        statements: [
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['sns:Publish'],
            resources: ['*'],
          }),
        ],
      }),
    },
    assumedBy: new iam.ServicePrincipal('textract.amazonaws.com'),
  });

  return role;
}
