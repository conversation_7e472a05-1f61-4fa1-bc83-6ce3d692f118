import {ApiDomainProps, App} from 'sst/constructs';
import {isProduction} from './isProduction';
import {SsrDomainProps} from 'sst/constructs/SsrSite';
import {DOMAIN} from '../constants/domain';

const FE_SUBDOMAIN = 'legacy';
const BE_SUBDOMAIN = 'api';

const REGIONS: {[key: string]: string} = {
  'eu-west-2': 'uk',
  'us-west-2': 'us',
};

function getDeploymentRegion(app: App): string {
  const value = REGIONS[app.region];
  if (!value) {
    throw new Error('Invalid deployment region');
  }
  return value;
}

export function createEndpoints(app: App) {
  const stage = app.stage.toLowerCase();
  const stageDomain = `${isProduction(app) ? '' : stage + '.'}${DOMAIN}`;

  const regionValue = getDeploymentRegion(app);

  const nextUrl = `${FE_SUBDOMAIN}.${regionValue}.${stageDomain}`;
  const apiUrl = `${BE_SUBDOMAIN}.${regionValue}.${stageDomain}`;

  return {nextUrl, apiUrl};
}

export function getDomainProps(app: App) {
  const {nextUrl, apiUrl} = createEndpoints(app);
  const nextDomainProps: SsrDomainProps = {
    domainName: nextUrl,
    hostedZone: DOMAIN,
  };
  const apiDomainProps: ApiDomainProps = {
    domainName: apiUrl,
    hostedZone: DOMAIN,
  };

  return {nextDomainProps, apiDomainProps};
}
