import {Function, StackContext, use} from 'sst/constructs';
import {ApiStack} from './ApiStack';

export function CommentsApiStack({app, stack}: StackContext) {
  const {api, EnvVars, perms, vpc, vpcSubnet} = use(ApiStack);

  stack.setDefaultFunctionProps({
    runtime: 'python3.9',
    timeout: '30 seconds',
  });

  const commentsFunction = new Function(stack, 'CommentsAPIFunction', {
    runtime: 'python3.9',
    handler: 'aws/lambda/v2/comments/main.handler',
    description: 'Function for handling comments endpoints',
    environment: {...EnvVars},
    vpc: vpc,
    vpcSubnets: vpcSubnet,
    permissions: perms,
  });

  api.addRoutes(stack, {
    'GET /v2/comments': {
      function: commentsFunction,
    },
    'PATCH /v2/comments/{comment_id}': {
      function: commentsFunction,
    },
    'DELETE /v2/comments/{comment_id}': {
      function: commentsFunction,
    },
    'PUT /v2/comments': {
      function: commentsFunction,
    },
  });
}
