from os import environ
from urllib.parse import unquote
from elasticsearch import Elasticsearch
from helpers.logging import get_tailo_logger

class Repository:
    def __init__(self) -> None:
        self.client = Elasticsearch(
            environ["ELASTIC_ENDPOINT"], api_key=environ["ELASTIC_API_KEY"]
        )
        self.logger = get_tailo_logger()

    # Search elastic to find document blocks by keywords.
    def search(self, user_uuid, document_uuid, keywords):
        try:
            query = unquote(keywords)
            
            response = self.client.search(
                index="document-blocks",
                body={
                    "size": 1000,
                    "sort" : [
                        {
                            "index": "asc"
                        },
                        "_score"
                    ],
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "match_phrase": {
                                        "document_uuid": {
                                            "query": document_uuid
                                        }
                                    }
                                }
                            ],
                            "should": [
                                {
                                    "match_phrase": {
                                        "content": query
                                    }
                                },
                                {
                                    "match_phrase": {
                                        "section_title": query
                                    }
                                }
                            ],
                            "minimum_should_match": 1
                        }
                    },
                    "suggest": {
                        "text": query,
                        "simple_phrase": {
                            "phrase": {
                                "field": "content.trigram",
                                "size": 1,
                                "gram_size": 3,
                                "direct_generator": [
                                    {
                                        "field": "content.trigram",
                                        "suggest_mode": "always"
                                    }
                                ],
                                "highlight": {
                                    "pre_tag": "<em>",
                                    "post_tag": "</em>"
                                }
                            }
                        }
                    },
                    "highlight": {
                        "fields": {
                            "section_title": {},
                            "content": {}
                        }
                    }
                },
            )
            
            return response
        except Exception as e:
            self.logger.exception(f"Error searching Elastic Search: {e}")
