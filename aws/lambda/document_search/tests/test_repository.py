from pytest import MonkeyPatch
from elasticsearch import Elasticsearch
from document_search.repository import Repository

def test_repository_search(mocker):
  with MonkeyPatch.context() as mp:
    mp.setenv("ELASTIC_API_KEY", "1234")
    mp.setenv("ELASTIC_ENDPOINT", "https://mock.com")

    mocker.patch.object(Elasticsearch, '__init__', return_value=None)

    mocker.patch.object(Elasticsearch, 'search', return_value={
      "hits": {
          "hits": [
            {
              "_source": {
                "document_uuid": "1234-123-123-123",
                "user_uuid": "1234-123-123-123",
                "section_id": "1234-123-123-123"
              }
            }
          ]
      }
    })

    repository = Repository()
    results = repository.search("1234", "1234", "test")

    for result in results["hits"]["hits"]:
      assert result["_source"]["document_uuid"] == "1234-123-123-123"
      assert result["_source"]["user_uuid"] == "1234-123-123-123"
      assert result["_source"]["section_id"] == "1234-123-123-123"
