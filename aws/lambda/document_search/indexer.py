import json
import boto3
from uuid import uuid4
from os import environ


class Indexer:
    def __init__(self) -> None:
        self.sqs = boto3.client("sqs")

    def index_document(self, user_uuid, document_uuid, bucket_name):
        self.sqs.send_message(
            QueueUrl=environ["ELASTIC_DOCUMENT_EVENT_QUEUE_URL"],
            MessageBody=json.dumps(
                {
                    "user_uuid": user_uuid,
                    "document_uuid": document_uuid,
                    "bucket_name": bucket_name,
                },
                indent=4,
                default=str,
            ),
            MessageGroupId=uuid4().hex,
            MessageDeduplicationId=uuid4().hex,
        )

    def index_block(
        self,
        user_uuid,
        document_uuid,
        section_id,
        section_title,
        block_id,
        block_type,
        index,
        content,
    ):
        block = {
            "user_uuid": user_uuid,
            "document_uuid": document_uuid,
            "section_id": section_id,
            "section_title": section_title,
            "block_id": block_id,
            "type": block_type,
            "content": content,
            "index": index,
            "_extract_binary_content": 1,
            "_reduce_whitespace": 1,
            "_run_ml_inference": 1,
        }

        self.sqs.send_message(
            QueueUrl=environ["ELASTIC_DOCUMENT_BLOCK_EVENT_QUEUE_URL"],
            MessageBody=json.dumps(block, indent=4, default=str),
            MessageGroupId=uuid4().hex,
            MessageDeduplicationId=uuid4().hex,
        )
