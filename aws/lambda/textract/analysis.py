"""
After textract has completed, this lambda is invoked to perform additional
extraction and post-processing of the document.
"""

import json
import traceback
from datetime import datetime, timezone

from boto3 import client
from database import Document, DocumentStatus, User
from database.instance import DB
from extractor.document_structure import ExtractedDocument
from extractor.exceptions import ExtractionException
from extractor.pdf_extractor import PDFExtractor
from extractor.post_extraction_processor import post_extraction_process
from extractor.progress_tracking import report_error, update_document_status
from helpers.amplitude import AmplitudeEvent, Track
from helpers.environment import is_local
from helpers.functions import get_now, logger, session
from helpers.sentry import sentry_init

textract_client = client("textract")
s3_client = client("s3", region_name=session.region_name)

sentry_init(is_local())


def get_user_and_document(document_path: str) -> tuple[User, Document]:
    with DB() as db_session:
        document = Document.by_path(session=db_session, path=document_path)

        if document is None:
            raise Exception("Unable to find uploaded document database item")

        user = User.get_by_id(session=db_session, id=document.owner_user_id)

        if user is None:
            raise Exception("Unable to find user by uuid")

        db_session.expunge(user)
        db_session.expunge(document)

    return user, document


def handler(event, context):
    event_data = json.loads(event.decode("utf-8")) if isinstance(event, bytes) else event
    for record in event_data["Records"]:
        message = json.loads(record["Sns"]["Message"])
        job_id = message["JobId"]
        document_location = message["DocumentLocation"]
        bucket_name = document_location["S3Bucket"]
        document_key = document_location["S3ObjectName"]

        file_object = s3_client.get_object(
            Bucket=bucket_name,
            Key=document_key,
        )

        user, document = get_user_and_document(document_key)

        finish_extraction(job_id, document, user, bucket_name, file_object)


def get_textract_blocks(job_id: str):
    blocks = []

    result = textract_client.get_document_analysis(JobId=job_id)

    blocks.extend(result["Blocks"])

    ## check result for "NextToken" key
    ## if it exists, call get_document_analysis with NextToken
    ## if not, return the result
    while "NextToken" in result:
        result = textract_client.get_document_analysis(
            JobId=job_id,
            NextToken=result["NextToken"],
        )
        blocks.extend(result["Blocks"])

    return blocks


# Perform additional extraction post textract for PDF, then common post processing
# for all document types
def finish_extraction(
    job_id: str,
    document: Document,
    user: User,
    bucket_name: str,
    file_object: dict,
) -> None:
    amplitude_event_data = {
        "type": "pdf",
        "character_count": -1,
    }
    user_tracking_id = "UNTRACKED"
    try:
        user_tracking_id: str = document.user_tracking_id

        # TODO: Should this be set before or after textract?
        update_document_status(document, user, DocumentStatus.EXTRACTING)
        logger.info(f"document {document.status}")

        created_time: datetime = document.date_created.replace(tzinfo=timezone.utc)
        process_start_datetime: datetime = get_now()
        textract_time: int = (process_start_datetime - created_time).seconds

        amplitude_event_data["textract_time"] = f"{textract_time} seconds"
        amplitude_event_data["textract_time_seconds"] = textract_time
        amplitude_event_data["file_size"] = f"{document.filesize} bytes"
        amplitude_event_data["page_count"] = document.page_count

        if not job_id:
            raise Exception("No job id")

        blocks = get_textract_blocks(job_id)

        pdf_start_time = get_now()
        stream = file_object["Body"].read()

        # Perform post-textract extraction
        pdf_extractor = PDFExtractor(stream=stream, textract=blocks)
        data: ExtractedDocument = pdf_extractor.get_extracted_data()

        amplitude_event_data["pdf_extraction_time"] = (
            f"{(get_now() - pdf_start_time).seconds} seconds"
        )
        amplitude_event_data["pdf_extraction_time_seconds"] = (get_now() - pdf_start_time).seconds

        data_str = str(data)
        amplitude_event_data["character_count"] = len(data_str.replace(" ", ""))

        # Perform common post-extraction processing
        post_extraction_process(document, user, data, amplitude_event_data)

    except ExtractionException as ee:
        print(traceback.format_exc())
        print(f"Extraction error: {ee}")
        amplitude_event_data["status"] = "error"
        amplitude_event_data["error_type"] = ee.message
        Track(
            tracking_id=user_tracking_id,
            event=AmplitudeEvent.DOC_PROCESSED,
            event_data=amplitude_event_data,
        )
        report_error(document, user, {"code": -1, "message": str(ee)})
    except Exception as e:
        print(traceback.format_exc())
        print(f"ERROR: {e}")
        amplitude_event_data["status"] = "error"
        amplitude_event_data["error_type"] = str(e)  # UNKNOWN
        Track(
            tracking_id=user_tracking_id,
            event=AmplitudeEvent.DOC_PROCESSED,
            event_data=amplitude_event_data,
        )
        report_error(document, user, {"code": -1, "message": str(e)})
