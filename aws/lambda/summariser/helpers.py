from os import environ
from logging import getLogger, ERROR as LOGGING_ERROR, INFO as LOGGING_INFO


# Taken from https://stackoverflow.com/a/17215533/6470494
class SafeDict(dict):
    def __missing__(self, key):
        return "{" + key + "}"


# TODO: Temporary, we need to split out the global helpers module so that it doesn't require other internal modules like database
__is_local = environ.get("IS_LOCAL") == "true"

# Define logger
logger = getLogger()
logger.setLevel(level=LOGGING_INFO if __is_local else LOGGING_ERROR)
