import concurrent.futures
import json
from concurrent.futures import as_completed
from os import environ
from .helpers import logger

import tiktoken
from langchain import PromptTemplate
from langchain.callbacks import get_openai_callback
from langchain.chains.summarize import load_summarize_chain
from langchain.chat_models import ChatOpenAI
from langchain.docstore.document import Document as LangchainDocument
from langchain.text_splitter import RecursiveCharacterTextSplitter

from .constants import MODELS, TOKEN_TO_WORD, SummarisationMethod
from .exceptions import (
    ChunkSummariesException,
    CombineChunksException,
    RecommendedMethodException,
    StuffSummarisationException,
)

model = MODELS["gpt-4o-mini"]
encoding = tiktoken.encoding_for_model(str(model["name"]))


def generate_quick_summary(
    data: str,
    main_prompt: str,
    summary_prompt: str,
    threaded: bool = True,
):
    logger.info(f"Main Prompt: {main_prompt}")
    logger.info(f"Summary Prompt: {summary_prompt}")

    return _summarise(main_prompt, summary_prompt, data, None, threaded)


def _summarise(
    main_prompt: str, summary_prompt: str, data: str, word_limit=None, threaded=True
):
    summary_type = get_recommended_summarisation_type(main_prompt, data, word_limit)

    output, meta_data = (
        get_stuff_summarise(main_prompt, data)
        if summary_type is SummarisationMethod.STUFF.value
        else chunk_summarise_all(
            main_prompt, data, summary_prompt, word_limit, threaded
        )
    )

    log_out_metadata(meta_data)

    return output, meta_data


def log_out_metadata(meta_data):
    logger.info("Run Stats:")
    logger.info(json.dumps(meta_data, indent=4))


def get_concat_doc_text(document_extracted: dict):
    text = ""
    for page in document_extracted["pages"]:
        for content_item in page["content"]:
            if "text" in content_item:
                text += content_item["text"] + "\n"
            if "header" in content_item:
                text += content_item["header"] + "\n"
    return text


def get_recommended_summarisation_type(prompt, doc_text, word_limit=False):
    try:
        token_limit_per_request = model["token_limit"] - model["max_tokens"]
        prompt_token_count = len(encoding.encode(prompt))
        doc_token_count = len(encoding.encode(doc_text))
        summarisation_method = SummarisationMethod.CHUNKING.value

        logger.info("===========================")
        logger.info(f"max token length: {model['token_limit']}")
        logger.info(f"prompt token length: {prompt_token_count}")
        logger.info(f"doc token length: {doc_token_count}")
        logger.info("===========================")

        if word_limit:
            word_limit_as_tokens = round(word_limit * 1.25)
            if (
                prompt_token_count + doc_token_count + word_limit_as_tokens
            ) <= token_limit_per_request:
                summarisation_method = SummarisationMethod.STUFF.value

        else:
            if (
                # Check if the prompt and the document fit inside the context window with 10% margin
                (prompt_token_count + doc_token_count) * 1.1
                <= token_limit_per_request
            ):
                summarisation_method = SummarisationMethod.STUFF.value

        logger.info(f"suggested method: {summarisation_method}")
        logger.info("===========================")

        return summarisation_method

    except Exception as e:
        print(e)
        raise RecommendedMethodException(str(e))


def combine_chunks(summaries, doc_text, reduce_template, word_limit=None):
    try:
        summaries_doc = LangchainDocument(page_content=summaries)

        summaries_token_count = len(encoding.encode(summaries))

        if word_limit is None or word_limit == 0:
            word_limit = str(round(len(doc_text.split()) / 4))

        reduce_template_token_count = len(encoding.encode(reduce_template))
        reduce_prompt = PromptTemplate(
            template=reduce_template, input_variables=["text"]
        )

        combine_max_tokens = calculate_max_tokens(
            model["token_limit"],
            model["max_tokens"],
            reduce_template_token_count + summaries_token_count,
        )

        # TODO Don't think we need this with the bigger model but does need addressed properly
        #
        # if (
        #     summaries_token_count
        #     + len(encoding.encode(reduce_template))
        #     + (int(word_limit) * 1.25)
        #     > model["token_limit"]
        # ):
        #     logger.info("super long document, using 16k model")
        #     combine_max_tokens = (
        #         MODELS["gpt-3.5-turbo-16k"]["token_limit"]
        #         - (reduce_template_token_count + summaries_token_count)
        #     ) - 100

        with get_openai_callback() as cb:
            combine_llm = get_open_ai_client(combine_max_tokens)
            reduce_chain = load_summarize_chain(
                llm=combine_llm, chain_type="stuff", prompt=reduce_prompt, verbose=True
            )

            output = reduce_chain(
                {"input_documents": [summaries_doc]}, return_only_outputs=True
            )

            meta_data = {
                "total_tokens": cb.total_tokens,
                "total_cost": cb.total_cost,
                "completion_tokens": cb.completion_tokens,
                "prompt_tokens": cb.prompt_tokens,
            }
        return output, meta_data

    except Exception as e:
        print(e)
        raise CombineChunksException(str(e))


def chunk_summarise(doc, prompt, llm, index):
    # this function is called by each thread so we can summarise in parallel
    with get_openai_callback() as cb:
        map_chain = load_summarize_chain(llm, chain_type="stuff", prompt=prompt)

        chunk_summary = map_chain.run([doc])
        logger.info(
            f"Summary tokens: {len(encoding.encode(chunk_summary))} Summary Preview: {chunk_summary[:250]} \n"
        )

        meta_data = {
            "total_tokens": cb.total_tokens,
            "total_cost": cb.total_cost,
            "completion_tokens": cb.completion_tokens,
            "prompt_tokens": cb.prompt_tokens,
        }

    return {"summary": chunk_summary, "index": index, "meta_data": meta_data}


def get_open_ai_client(max_tokens):
    return ChatOpenAI(
        model=str(model["name"]),
        temperature=0,
        max_tokens=max_tokens,
        openai_api_key=environ.get("OPENAI_API_KEY"),
    )


def chunk_summarise_all(
    main_prompt,
    doc_text,
    summary_prompt,
    word_limit,
    threaded,
):
    try:
        prompt_token_count = len(encoding.encode(main_prompt))
        # TODO: Look at if using half the model window is a good measure given how asymmetric they are
        chunk_size_tokens = model["token_limit"] - (
            model["max_tokens"] + prompt_token_count
        )

        max_tokens = calculate_max_tokens(
            model["token_limit"],
            model["max_tokens"],
            chunk_size_tokens,
        )

        llm = get_open_ai_client(max_tokens)

        chunk_size_chars = (
            chunk_size_tokens * TOKEN_TO_WORD
        )  # multiplied by 4 for rough conversion between tokens and chars
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size_chars, chunk_overlap=40
        )

        texts = text_splitter.split_text(doc_text)
        docs = [LangchainDocument(page_content=t) for t in texts]
        response_word_count = round(max_tokens * TOKEN_TO_WORD) / max(1, len(docs) - 1)

        summary_list = [None] * len(docs)
        chunk_prompts = []
        chunk_prompt = (
            f"Please provide an answer of around {str(response_word_count)} words. "
            + main_prompt
        )

        for i, doc in enumerate(docs):
            chunk_prompts.append(
                PromptTemplate(template=chunk_prompt, input_variables=["text"])
            )

        overall_meta_data = {
            "total_tokens": 0,
            "total_cost": 0.0,
            "completion_tokens": 0,
            "prompt_tokens": 0,
        }

        if threaded:
            # create a thread for each chunk for parallel summarising - faster
            with concurrent.futures.ThreadPoolExecutor(
                max_workers=len(docs)
            ) as executor:
                futures = [
                    executor.submit(
                        chunk_summarise,
                        docs[index],
                        chunk_prompts[index],
                        llm,
                        index,
                    )
                    for index, item in enumerate(docs)
                ]

            for future in as_completed(futures):
                # when each thread finishes, add
                # summary responses to list at correct index to preserve order
                result = future.result()
                index = result["index"]
                summary = result["summary"]
                meta_data = result["meta_data"]
                for key in meta_data.keys():
                    overall_meta_data[key] = overall_meta_data[key] + meta_data[key]
                summary_list[index] = summary
        else:
            count = len(docs)
            for idx, item in enumerate(docs):
                logger.info(f"Running {idx + 1}/{count + 1}")
                result = chunk_summarise(docs[idx], chunk_prompts[idx], llm, idx)
                index = result["index"]
                summary = result["summary"]
                meta_data = result["meta_data"]
                for key in meta_data.keys():
                    overall_meta_data[key] = overall_meta_data[key] + meta_data[key]
                summary_list[index] = summary

        summaries = "\n\n".join(summary_list)
        logger.info(
            f"Summary chunks generated, total word length of summaries: {len(summaries.split())}"
        )

        output, combine_meta_data = combine_chunks(
            summaries=summaries,
            doc_text=doc_text,
            reduce_template=summary_prompt,
            word_limit=word_limit,
        )

        for key in combine_meta_data.keys():
            overall_meta_data[key] = overall_meta_data[key] + combine_meta_data[key]

        return output, overall_meta_data

    except Exception as e:
        print(e)
        raise ChunkSummariesException(str(e))


def calculate_max_tokens(context: int, max_tokens: int, input: int):
    taken = context - input
    # it can only be as high as the max token window
    return min(max_tokens, taken)


def stuff_summarise(prompt, doc_text):
    try:
        prompt_token_count = len(encoding.encode(prompt))
        doc_token_count = len(encoding.encode(doc_text))
        max_tokens = calculate_max_tokens(
            model["token_limit"],
            model["max_tokens"],
            prompt_token_count + doc_token_count,
        )

        logger.info(f"recommended max tokens: {max_tokens}")
        llm = get_open_ai_client(max_tokens)

        prompt = PromptTemplate(template=prompt, input_variables=["text"])
        chain = load_summarize_chain(
            llm,
            chain_type="stuff",
            prompt=prompt,
            # verbose=True, # Determines whether langchain spews out entire prompt to logs
        )

        doc = LangchainDocument(page_content=doc_text)

        output = chain({"input_documents": [doc]}, return_only_outputs=True)

        return output
    except Exception as e:
        print(e)
        raise StuffSummarisationException(str(e))


# number of tokens that will be used
# ChatGPT returns tokens used


def get_stuff_summarise(prompt: str, data: str):
    with get_openai_callback() as cb:
        output = stuff_summarise(prompt, data)
        meta_data = {
            "total_tokens": cb.total_tokens,
            "total_cost": cb.total_cost,
            "completion_tokens": cb.completion_tokens,
            "prompt_tokens": cb.prompt_tokens,
        }
    return output, meta_data
