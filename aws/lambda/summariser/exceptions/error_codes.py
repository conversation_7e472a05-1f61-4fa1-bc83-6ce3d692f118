from enum import IntEnum


class SummarisationError(IntEnum):
    UNKNOWN = 0
    STUFFSUMMMARISATION = 1
    RECOMMENDEDMETHOD = 2
    CHUNKSUMMARIES = 3
    COMBINECHUNKS = 4


def get_error_message(code: int) -> str:
    base_message = "An error occured summarising the document"
    message_table = {
        SummarisationError.STUFFSUMMMARISATION: "stuff summarisation",
        SummarisationError.RECOMMENDEDMETHOD: "recommended summarisation",
        SummarisationError.CHUNKSUMMARIES: "chunk summaries",
        SummarisationError.COMBINECHUNKS: "combine chunks",
    }

    try:
        error = SummarisationError(code)
        if error == SummarisationError.UNKNOWN:
            return "An unknown error occured summarising the document data"

        return f"{base_message} {message_table[error]}"
    except Exception:
        return "Unknown error code"