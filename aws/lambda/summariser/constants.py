from enum import Enum

# VARIABLES
BULLET_LIMIT = 6
WORD_LIMIT = 120
MODELS = {
    "gpt-3.5-turbo-16k": {
        "name": "gpt-3.5-turbo-16k",
        "token_limit": 16385,
        "max_tokens": 4096,
    },
    "gpt-3.5-turbo-0125": {
        "name": "gpt-3.5-turbo-0125",
        "token_limit": 16385,
        "max_tokens": 4096,
    },
    "gpt-3.5-turbo-1106": {
        "name": "gpt-3.5-turbo-1106",
        "token_limit": 16385,
        "max_tokens": 4096,
    },
    "gpt-4o-mini-2024-07-18": {
        "name": "gpt-4o-mini-2024-07-18",
        "token_limit": 128000,
        "max_tokens": 16000,
    },
}

# Aliases
MODELS["gpt-4o-mini"] = MODELS["gpt-4o-mini-2024-07-18"]
MODELS["gpt-3.5-turbo"] = MODELS["gpt-3.5-turbo-0125"]

PROMPT = """
    %s
    "{text}"
    SUMMARY:
    """


# ENUMS
class SummarisationMethod(Enum):
    STUFF = "stuff"
    CHUNKING = "chunking"


SUMMARISATION_LANGUAGE = {
    "eu-west-2": "UK English",
    "us-west-2": "American English",
}

SUMMARISE_PROMPT_TEMPLATE = """
    Provide a comprehensive summary of the given TEXT.
    The summary should capture the main points and key details of the text
    while conveying the author's intended meaning accurately.
    Please ensure that the summary is in the form of a bulleted list, with no
    more than {bullets} bullet points.
    The length of each bullet should be appropriate to capture the main points
    and key details of the text, without including unnecessary information or
    becoming overly long.
    The summary must be in {language}. Each bullet point should start with a short 
    phrase of 2-5 words that summarises the key concept or main idea of that point. 
    This phrase should be emphasised in **bold**. Follow the bold phrase with a colon, 
    then provide the rest of the information.

    TEXT:
    {text}
"""

SUMMARISE_SUMMARIES_PROMPT_TEMPLATE = """ The following is a set of summaries:
        '''{text}'''
        These summaries were created by your response to the following prompt:
        '''{prompt}'''
        Take these and distill it into one coherent summary. Please provide an
        answer with no more than {bullets} bullet points. Use the original
        prompt as guidance when producing the summary. Avoid any unnecessary
        repetition in your answer.
        The summary must be in {language}
        SUMMARY:
        """

TOKEN_TO_WORD = 4
