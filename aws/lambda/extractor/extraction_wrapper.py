"""
This file contains the wrappers for the different extraction methods.

It does not handle the pre and processing of the document, only the extraction
of the content, either directly or invoking an async process.
"""

import json
from abc import ABC, abstractmethod
from os import environ
from typing import Union

import boto3
from database import Document, DocumentStatus, User
from helpers.functions import logger

from extractor import DocumentType

from .document_extractor import DocumentExtractor
from .document_structure import ExtractedDocument
from .docx_extractor import DocxExtractor
from .exceptions import InvalidFileTyleMethodException, OCRRequiredExtractionException
from .mistral_extractor import MistralExtractor
from .post_extraction_processor import post_extraction_process
from .progress_tracking import update_document_status

sqs_client = boto3.client("sqs")
bucket_name = environ["BUCKET_NAME"]
textract_bucket_name = environ["TEXTRACT_BUCKET_NAME"]


# Abstraction for extractor wrappers which will either delegate or perform extraction
class ExtractionWrapper(ABC):
    document_type: str
    extractor: Union[DocumentExtractor, None]
    user: User
    document: Document

    @abstractmethod
    def __init__(self, stream: bytes, user: User, document: Document):
        raise NotImplementedError

    def _init(
        self,
        document_type: str,
        user: User,
        document: Document,
        extractor: Union[DocumentExtractor, None] = None,
    ):
        self.extractor = extractor
        self.document_type = document_type
        self.user = user
        self.document = document

    @abstractmethod
    def start_extraction(self, amplitude_event_data: dict):
        pass


# For classes that perform extraction right now
class SynchronousExtractionWrapper(ExtractionWrapper):
    def get_extracted_data(self) -> ExtractedDocument:
        if self.extractor is None:
            msg = "No extractor"
            raise Exception(msg)
        return self.extractor.get_extracted_data()

    def start_extraction(self, amplitude_event_data: dict):
        amplitude_event_data["type"] = self.document_type
        update_document_status(self.document, self.user, DocumentStatus.EXTRACTING)
        data: ExtractedDocument = self.get_extracted_data()
        # Common document post-processing
        post_extraction_process(self.document, self.user, data, amplitude_event_data)


# For classes that delegate to async extraction
class AsynchronousExtractionWrapper(ExtractionWrapper):
    def start_extraction(self, amplitude_event_data: dict):
        pass


### Concrete classes for specific solutions to extraction


# DOCX extraction with mammoth
class DocxExtractionWrapper(SynchronousExtractionWrapper):
    def __init__(self, stream: bytes, user: User, document: Document):
        self._init("docx", user, document, DocxExtractor(stream))


# PDF extraction with Mistral
class ImageBasedPdfExtractionWrapper(SynchronousExtractionWrapper):
    def __init__(self, stream: bytes, user: User, document: Document):
        self._init("image-based pdf", user, document, MistralExtractor(stream))

    # Override to set filename
    def get_extracted_data(self) -> ExtractedDocument:
        if self.extractor is None:
            msg = "No extractor"
            raise Exception(msg)
        mistral_extractor = MistralExtractor(self.extractor.document)
        mistral_extractor.set_filename(self.document.filename)
        return mistral_extractor.get_extracted_data()


# PDF extraction with textract
class TextualPdfExtractionWrapper(AsynchronousExtractionWrapper):
    def __init__(self, stream: bytes, user: User, document: Document):
        # For textract, we don't need an extractor instance as it's async
        self._init("textual pdf", user, document)

    def start_extraction(self, amplitude_event_data: dict) -> None:
        amplitude_event_data["type"] = self.document_type
        update_document_status(self.document, self.user, DocumentStatus.STARTING_EXTRACTION)
        logger.info(f"SENDING MESSAGE TO QUEUE FOR {self.document.uuid} \n")
        sqs_client.send_message(
            QueueUrl=environ["TEXTRACT_QUEUE_URL"],
            MessageBody=json.dumps(
                {
                    "bucket_name": bucket_name,
                    "object_key": self.document.path,
                    "textract_bucket_name": textract_bucket_name,
                    "document_uuid": self.document.uuid,
                    "user_uuid": self.user.estendio_id,
                },
            ),
            MessageGroupId=str(self.user.estendio_id + "_extracting"),
            MessageDeduplicationId=str(self.document.uuid),
        )
        # NOTE: No call to post_extraction_process as this is done once async extraction is complete.
        # See textract/analysis.py


# Map extraction type (calculated at file upload time) to wrapper class
def get_extractor_wrapper(document_type: DocumentType) -> type[ExtractionWrapper]:
    if document_type == DocumentType.TEXTUAL_PDF:
        return TextualPdfExtractionWrapper
    if document_type == DocumentType.IMAGE_BASED_PDF:
        if environ.get("FEATURE_USE_MISTRAL_FOR_OCR", "false").lower() == "true":
            return ImageBasedPdfExtractionWrapper
        msg = "OCR required for image-based PDFs"
        raise OCRRequiredExtractionException(msg)
    if document_type == DocumentType.DOCX:
        return DocxExtractionWrapper
    msg = "Invalid document type for extraction"
    raise InvalidFileTyleMethodException(msg)
