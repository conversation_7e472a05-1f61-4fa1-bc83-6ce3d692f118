"""
Mistral AI extractor for PDFs. Supports OCR
"""

import base64
import json
import logging
from typing import Any, Optional
from uuid import uuid4

from bs4 import BeautifulSoup, Tag
from helpers.logging import get_tailo_logger
from markdown import markdown
from markdown.extensions.tables import TableExtension
from l2m4m import LaTeX2MathMLExtension
from mistralai import DocumentURLChunk, models

from . import mistral_client
from .constants import HeadingTag
from .document_extractor import DocumentExtractor
from .document_structure import *

logger: logging.Logger = get_tailo_logger()


class MistralExtractor(DocumentExtractor):
    filename: str = ""
    images_data: list[dict[str,Any]] = []

    # Initialise parent class that handles some processing
    def __init__(self, stream: bytes):
        super().__init__(stream, "pdf")

    # Set the filename, which is used when uploading to Mistral
    def set_filename(self, filename: str) -> None:
        self.filename = filename

    def extract_from_mistral(self) -> dict[str,Any]:
        with mistral_client() as client:
            # Upload PDF file to Mistral's OCR service
            uploaded_file: models.UploadFileOut = client.files.upload(
                file=models.File(
                    file_name=str(self.filename),
                    content=self.document,
                ),
                purpose="ocr",
            )

            # Get URL for the uploaded file
            signed_url: models.FileSignedURL = client.files.get_signed_url(
                file_id=uploaded_file.id,
                expiry=1,
            )

            # Process PDF with OCR, including embedded images
            pdf_response: models.OCRResponse = client.ocr.process(
                document=DocumentURLChunk(document_url=signed_url.url),
                model="mistral-ocr-latest",
                include_image_base64=True,
            )

            # Convert response to JSON format
            response_dict: dict[str,Any] = json.loads(pdf_response.model_dump_json())

            return response_dict

    # Split the Mistral output into sections
    def tag_detection(self, html_tags: BeautifulSoup) -> tuple[list, list[ExtractedHeader]]:
        tag_to_class_mapping: dict[str, Any] = {
            "h1": lambda tag: ExtractedHeadingSegment(
                title=tag.text.strip(),
                font_size=self.heading_font_size[HeadingTag.H1.value],
                id=uuid4().hex,
                bbox=[],
                format="none",
                tag=HeadingTag.H1.value,
            ),
            "h2": lambda tag: ExtractedHeadingSegment(
                title=tag.text.strip(),
                font_size=self.heading_font_size[HeadingTag.H2.value],
                id=uuid4().hex,
                bbox=[],
                format="none",
                tag=HeadingTag.H2.value,
            ),
            "h3": lambda tag: ExtractedHeadingSegment(
                title=tag.text.strip(),
                font_size=self.heading_font_size[HeadingTag.H3.value],
                id=uuid4().hex,
                bbox=[],
                format="none",
                tag=HeadingTag.H3.value,
            ),
            "p": lambda tag: ExtractedParagraphSegment(
                bbox=[],
                text=tag.text.strip(),
                font_size=self.base_font,
                formats=self._extract_formats_from_paragraph(tag),
                id=uuid4().hex,
            ),
            "mathblock": lambda tag: ExtractedMathSegment(
                bbox=[],
                markup=self._helper_flatten_tag_content(tag),
                font_size=self.base_font,
                id=uuid4().hex,
            ),
            "ul": lambda tag: ExtractedListSegment(
                format="unordered",
                items=self._extract_list_items(tag),
                id=uuid4().hex,
            ),
            "ol": lambda tag: ExtractedListSegment(
                format="ordered",
                items=self._extract_list_items(tag),
                id=uuid4().hex,
            ),
            "table": lambda tag: self._extract_table(tag),
            "img": lambda tag: self._extract_image(tag),
        }

        extracted_segments = []
        extracted_headers = []

        for tag in html_tags.find_all(list(tag_to_class_mapping.keys())):
            if tag.name in tag_to_class_mapping:
                segment = tag_to_class_mapping[tag.name](tag)
                extracted_segments.append(segment)

                # Add to headers if it's a heading
                if isinstance(segment, ExtractedHeadingSegment):
                    extracted_headers.append(
                        ExtractedHeader(
                            title=segment.title,
                            font_size=segment.font_size,
                            id=segment.id,
                            tag=segment.tag or HeadingTag.H2.value, # default as tag is optional
                        ),
                    )

        return extracted_segments, extracted_headers

    def _helper_flatten_tag_content(self, tag: Tag) -> str:
        """Flatten the contents of a tag into a string"""
        if isinstance(tag.contents, list):
            return ' '.join([str(c) for c in tag.contents])
        return str(tag.contents)

    def _extract_formats_from_paragraph(self, tag) -> list[ExtractFormat]:
        """Split paragraph into inline sections with the same format"""
        formats = []
        for child in tag.contents:
            if isinstance(child, Tag):
                format_type = "none"
                content_type = "text"
                url = None

                if child.name == "strong" or child.name == "b":
                    format_type = "bold"
                elif child.name == "em" or child.name == "i":
                    format_type = "italic"
                elif child.name == "u":
                    format_type = "underline"
                elif child.name == "a" and "href" in child.attrs:
                    format_type = "link"
                    url = child.attrs["href"]
                elif child.name == "sup":
                    format_type = "super"
                elif child.name == "sub":
                    format_type = "sub"
                elif child.name == "math":
                    format_type = "math"
                    content_type = "markup" # need raw content tags

                formats.append(ExtractFormat(
                    type=format_type,
                    size=self.base_font,
                    value=child.text if content_type == "text" else self._helper_flatten_tag_content(child),
                    bbox=[],
                    font="default",
                    url=url,
                ))
            else:
                # Plain text
                formats.append(ExtractFormat(
                    type="none",
                    size=self.base_font,
                    value=str(child),
                    bbox=[],
                    font="default",
                ))

        return formats

    def _extract_list_items(self, tag) -> list[ExtractedListItem]:
        items = []
        for li in tag.find_all("li", recursive=False):
            # Check for nested lists
            nested_items = []
            for nested_list in li.find_all(["ul", "ol"], recursive=False):
                nested_items.extend(self._extract_list_items(nested_list))
                nested_list.extract()  # Remove from parent to get clean text

            items.append(ExtractedListItem(
                value=li.text.strip(),
                items=nested_items,
                bbox=[],
            ))

        return items

    def _extract_table(self, table_tag) -> ExtractedTableSegment:
        headers = []
        rows = []

        # Extract headers
        if table_tag.find("thead"):
            for th in table_tag.find("thead").find_all("th"):
                headers.append({"type": "text", "text": th.text.strip()})
        else:
            # Use first row as header if no thead
            first_row = table_tag.find("tr")
            if first_row:
                for cell in first_row.find_all(["th", "td"]):
                    headers.append({"type": "text", "text": cell.text.strip()})
                first_row.extract()  # Remove from table

        # Extract rows
        for tr in table_tag.find_all("tr"):
            row = []
            for td in tr.find_all("td"):
                row.append({"type": "text", "text": td.text.strip()})
            if row:
                rows.append(row)

        table_content = ExtractedTableContent(headers=headers, rows=rows)
        return ExtractedTableSegment(content=table_content, id=uuid4().hex)

    def _extract_image(self, tag) -> Optional[ExtractedImagesSegment]:
        """Extract image data from an img tag and match with self.images_data"""
        # Get the image ID from the tag
        img_id: str = tag.get("src", "")

        # Find matching image in self.images_data
        image_data: Optional[dict[str, Any]] = next((img for img in self.images_data if img["id"] == img_id), None)
        if not image_data:
            return None

        # Extract image data from base64 string
        try:
            # Parse base64 data
            img_base64 = image_data.get("image_base64", "")
            if img_base64.startswith("data:image/"):
                img_format = img_base64.split(";")[0].split("/")[-1]
                img_data = img_base64.split(",")[1]
            else:
                # Assume JPEG if format not specified in base64 string
                img_format = "jpeg"
                img_data = img_base64

            img_bytes = base64.b64decode(img_data)

            # Calculate width and height from coordinates
            width = abs(image_data.get("bottom_right_x", 0) - image_data.get("top_left_x", 0))
            height = abs(image_data.get("bottom_right_y", 0) - image_data.get("top_left_y", 0))

            # Create image segment
            return ExtractedImagesSegment(
                width=int(width),
                height=int(height),
                ext=img_format,
                original_width=int(width),
                original_height=int(height),
                image=img_bytes,
                id=image_data.get("id", uuid4().hex),
                transform="",
            )
        except Exception as e:
            logger.error(f"Failed to process image data: {e}")
            return None

    def _expose_nested_top_level_tags(self, html_tags: BeautifulSoup) -> None:
        """Extract nested top-level tags to the root level, preserving content order."""
        top_level_tags: list[str] = ["h1", "h2", "h3", "p", "ul", "ol", "table", "img", "mathblock"]

        changes_made = True
        while changes_made:
            changes_made = False

            for parent_tag in html_tags.find_all(top_level_tags):
                nested_tags = parent_tag.find_all(top_level_tags, recursive=False)

                if nested_tags:
                    changes_made = True

                    for nested_tag in reversed(nested_tags):
                        parent_of_parent = parent_tag.parent
                        if parent_of_parent is None:
                            continue

                        # Clone the nested tag
                        new_tag = html_tags.new_tag(nested_tag.name)
                        for attr, value in nested_tag.attrs.items():
                            new_tag[attr] = value
                        new_tag.extend(nested_tag.contents)

                        # Find insertion position
                        parent_index = parent_of_parent.contents.index(parent_tag)

                        # Split content into before/after the nested tag
                        before_content = []
                        after_content = []
                        in_before = True

                        for content in parent_tag.contents:
                            if content is nested_tag:
                                in_before = False
                                continue
                            if in_before:
                                before_content.append(content)
                            else:
                                after_content.append(content)

                        nested_tag.extract()

                        # Create tag for content after the nested tag
                        if after_content:
                            after_tag = html_tags.new_tag(parent_tag.name)
                            for attr, value in parent_tag.attrs.items():
                                after_tag[attr] = value

                            for content in after_content:
                                if hasattr(content, "extract"):
                                    content.extract()
                                after_tag.append(content)

                            parent_of_parent.insert(parent_index + 2, after_tag)

                        # Insert the extracted tag
                        parent_of_parent.insert(parent_index + 1, new_tag)

                    # Remove empty parent tags
                    is_empty = len(parent_tag.contents) == 0
                    has_only_whitespace = parent_tag.string and parent_tag.string.strip() == ""
                    if is_empty or has_only_whitespace:
                        parent_tag.extract()

    # Process the Mistral output, separated for DI reasons
    def _process_mistral_output(
        self, extracted_json: dict[str, Any],
    ) -> tuple[ExtractedDetails, list[ExtractedPage]]:
        # Save image data, flattened for reference once pages flattened
        for sublist in [page["images"] for page in extracted_json["pages"]]:
            self.images_data.extend(sublist)

        # Extract the markdown by joining all the markdown from the pages together
        # Double newline used to ensure content not accidentally merged with last page
        markdown_txt: str = "\n\n".join(
            [page["markdown"] for page in extracted_json["pages"]],
        )
        logger.debug("Markdown: %s", markdown_txt)

        # Convert markdown to HTML
        html_txt: str = markdown(markdown_txt, extensions=[
            TableExtension(use_align_attribute=True),
            LaTeX2MathMLExtension(),
        ])
        logger.debug("html_txt: %s", html_txt)
        html_tags: BeautifulSoup = BeautifulSoup(html_txt, "html.parser")

        ### Fixes for edge cases at HTML level ###

        # Rename <math display="block"> to <mathblock> to make our extraction simpler
        for math_tag in html_tags.find_all("math", {"display": "block"}):
            math_tag.name = "mathblock"

        # Ensure top level tags are not buried in other tags
        self._expose_nested_top_level_tags(html_tags)

        # Temp fix - ensure that any h1 tags in the middle of the document (not at start) are changed to h2
        # - We are aware that this may be the wrong choice for these
        for h1 in html_tags.find_all("h1")[1:]:
            h1.name = "h2"

        logger.debug("html_tags: %s", html_tags)

        ### End of fixes ###

        extracted_segment, extracted_headers = self.tag_detection(html_tags)

        extracted_pages: list[ExtractedPage] = [
            ExtractedPage(1, {}, [], extracted_segment, [], []),
        ]
        document_details = ExtractedDetails(1, self.base_font, [])

        return document_details, extracted_pages

    def run(self) -> tuple[ExtractedDetails, list[ExtractedPage]]:
        extracted_json: dict[str, Any] = self.extract_from_mistral()
        logger.debug("extracted_json: %s", extracted_json)

        return self._process_mistral_output(extracted_json)
