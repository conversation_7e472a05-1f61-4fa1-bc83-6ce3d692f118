from enum import Enum
from os import environ

from mistralai import Mistral

from .docx_extractor import DocxExtractor
from .pdf_extractor import PDFExtractor


# Type of document from point of view of extraction requirements
class DocumentType(Enum):
    TEXTUAL_PDF = "pdf"
    IMAGE_BASED_PDF = "pdf-ocr"
    DOCX = "docx"
    UNKNOWN = "unknown"


def mistral_client() -> Mistral:
    return Mistral(
        api_key=environ.get("MISTRAL_API_KEY"),
    )


__all__ = ["DocxExtractor", "PDFExtractor"]
