import base64
import io
import re
from typing import List, Literal
from uuid import uuid4

import docx
import mammoth
from bs4 import BeautifulSoup
from bs4.element import Tag
from docx.enum.shape import WD_INLINE_SHAPE
from PIL import Image

from .document_extractor import DocumentExtractor
from .document_structure import (
    ExtractedDetails,
    ExtractedHeader,
    ExtractedHeadingSegment,
    ExtractedImagesSegment,
    ExtractedListItem,
    ExtractedListSegment,
    ExtractedPage,
    ExtractedParagraphSegment,
    ExtractedTableContent,
    ExtractedTableSegment,
    ExtractFormat,
)

# No guarantee Mammoth won't break this but HTML only supports H1-6
heading_pattern = r"h[1-6]"


class DocxExtractor(DocumentExtractor):
    document: BeautifulSoup
    doc = None  # DOCX
    image_blocks = []
    image_counter = 0
    style_map = "u => u"

    def __init__(self, stream):
        super().__init__(stream, "docx")

        # Perform docx conversion to html
        result = mammoth.convert_to_html(io.BytesIO(stream), style_map=self.style_map)
        html = result.value
        # MAMMOTH: Returns the HTML tags from the document as a list
        self.document = BeautifulSoup(html, "html.parser")
        # DOCX: Returns the document
        self.doc = docx.Document(io.BytesIO(stream))

        # Extract images from docx document to standard image format
        self.image_blocks = [
            item
            for item in self.doc.inline_shapes
            if item.type == WD_INLINE_SHAPE.LINKED_PICTURE
            or item.type == WD_INLINE_SHAPE.PICTURE
        ]

    def extract_table(self, table: Tag) -> ExtractedTableSegment:
        header_row = []
        rows = []
        # check if table has a thead tag
        if table.thead:
            for row in table.thead.children:
                row_item = []
                for cell in row.children:
                    row_item.append(cell.string)
                if len(header_row) == 0:
                    header_row = row_item
                else:
                    rows.append(row_item)
        else:
            table_tags = table.find_all("tr")
            for tr in table_tags:
                row_item = []
                for td in tr.find_all("td"):
                    row_item.append(td.string)
                if len(header_row) == 0:
                    header_row = row_item
                else:
                    rows.append(row_item)

        content = ExtractedTableContent([], [])
        content.headers = [{"type": "text", "text": cell} for cell in header_row]
        content.rows = [
            [{"type": "text", "text": cell} for cell in row] for row in rows
        ]

        return ExtractedTableSegment(content=content, id=uuid4().hex)

    # -> ExtractedParagraphSegment:
    def get_tag_format(self, tag: Tag) -> ExtractFormat:
        format_type: Literal[
            "none", "bold", "italic", "link", "super", "sub", "underline"
        ] = "none"
        url = None

        if tag.name == "strong":
            format_type = "bold"
        elif tag.name == "em":
            format_type = "italic"
        elif tag.name == "u":
            format_type = "underline"
        elif tag.name == "a" and "href" in tag.attrs:
            format_type = "link"
            url = tag.attrs["href"]
        else:
            # print("Tag format unknown", tag.prettify())
            pass

        return ExtractFormat(format_type, self.base_font, tag.text, [], url)

    def extract_paragraph(self, tag: Tag) -> ExtractedParagraphSegment:
        formats = []
        for item in tag.contents:
            if isinstance(item, Tag):
                formats.append(self.get_tag_format(item))
            else:
                formats.append(
                    ExtractFormat("none", self.base_font, item.text, [], None)
                )

        return ExtractedParagraphSegment(
            text=tag.text,
            font_size=self.base_font,
            formats=formats,
            bbox=[],
            id=uuid4().hex,
        )

    def extract_list(self, tag: Tag) -> ExtractedListSegment:
        list_type = ""
        if tag.name == "ol":
            list_type = "ordered"
        if tag.name == "ul":
            list_type = "unordered"

        items = self.extract_list_items(tag)

        return ExtractedListSegment(
            format=list_type,
            items=items,
            id=uuid4().hex,
        )

    def extract_list_items(self, tag: Tag) -> ExtractedListItem:
        list_items = []
        for item in tag.children:
            value = ""
            sub_items = []
            if item.name is None:
                value = item.text
            else:
                for sub_item in item.children:
                    if sub_item.name is None:
                        value = sub_item.text
                    else:
                        sub_items = self.extract_list_items(sub_item)
            list_items.append(
                ExtractedListItem(
                    value=value,
                    items=sub_items,
                    bbox=[],
                )
            )

        return list_items

    def tag_detection(self) -> tuple[List, List[ExtractedHeader]]:
        ret_extracted_segments: List = []
        ret_extracted_headers: List[ExtractedHeader] = []
        image_height = 0
        image_width = 0
        image_bytes = bytes()

        for item in self.document.find_all():
            if item.name == "p" and item.parent.name == "[document]":
                extract_paragraph = self.extract_paragraph(item)
                ret_extracted_segments.append(extract_paragraph)

            if re.match(pattern=heading_pattern, string=item.name):
                title = ""

                if item.string is not None:
                    title = item.string
                else:
                    titles = list(item.stripped_strings)

                    if len(titles) > 0:
                        title = titles[-1]
                    else:
                        # print("No clue on this item", item.prettify(), file=sys.stderr)
                        continue  # Skip and empty tag

                attributes = {
                    "title": title,
                    "id": uuid4().hex,
                }

                ret_extracted_segments.append(
                    ExtractedHeadingSegment(
                        **attributes,
                        font_size=self.heading_font_size["h" + item.name[1]],
                        tag=item.name,
                        bbox=[],
                        format="none",
                    )
                )
                ret_extracted_headers.append(
                    ExtractedHeader(
                        **attributes,
                        font_size=self.heading_font_size["h" + item.name[1]],
                        tag=item.name,
                    )
                )
            elif item.name.startswith("h"):
                # print("Unknown H element detected", item.name, file=sys.stderr)
                pass

            # List Detection
            if item.parent.name == "[document]" and (
                item.name == "ol" or item.name == "ul"
            ):
                extracted_list = self.extract_list(item)
                ret_extracted_segments.append(extracted_list)

            # Table Detection
            if item.name == "table":
                extracted_table = self.extract_table(item)
                ret_extracted_segments.append(extracted_table)

            # Image Detection
            if item.name == "img":
                split_image = item.attrs["src"].split(",")
                image_type = split_image[0]
                # image_type is returned as "data:image/(DATA);base64"
                image_extension = image_type.split("/")[-1].split(";")[0]

                image_ascii = split_image[1]
                image_bytes = base64.b64decode(image_ascii.encode("ascii"))
                img_bytes = Image.open(io.BytesIO(image_bytes))

                image_height = img_bytes.height
                image_width = img_bytes.width

                if self.image_counter < len(self.image_blocks):
                    picture_shape = self.image_blocks[self.image_counter]
                    image_height = picture_shape.height.cm
                    image_width = picture_shape.width.cm
                    self.image_counter += 1

                image = ExtractedImagesSegment(
                    image_width,
                    image_height,
                    image_extension,
                    img_bytes.width,
                    img_bytes.height,
                    image_bytes,
                    uuid4().hex,
                )
                ret_extracted_segments.append(image)

        return ret_extracted_segments, ret_extracted_headers

    def run(self) -> tuple[ExtractedDetails, list[ExtractedPage]]:
        extracted_pages: list[ExtractedPage] = []

        extracted_segment, extracted_headers = self.tag_detection()

        page_extract = ExtractedPage(1, {}, [], extracted_segment, [], [])
        extracted_pages.append(page_extract)

        document_details = ExtractedDetails(1, self.base_font, [])

        return document_details, extracted_pages
