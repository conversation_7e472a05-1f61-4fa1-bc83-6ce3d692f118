import re
from collections.abc import Sequence
from dataclasses import replace
from typing import Dict, List, Literal, TypeVar
from uuid import uuid4

import numpy as np

from .constants import ExtractType, HeadingTag
from .document_structure import (
    ExtractedDetails,
    ExtractedDocument,
    ExtractedFootnoteSegment,
    ExtractedListSegment,
    ExtractedPage,
    ExtractedParagraphSegment,
    ExtractedSegment,
    ExtractedTOC,
    ExtractFormat,
    ExtractRelationship,
    Section,
    SubSection,
)

T = TypeVar("T")


class DocumentExtractor:
    document_type: Literal["pdf", "docx", "none"]
    document: bytes
    _preliminary_titles = ("abstract", "references", "keywords", "table of contents", "appendices")
    extracted_title = ""
    base_font = 12
    heading_tags = [
        HeadingTag.H1.value,
        HeadingTag.H2.value,
        HeadingTag.H3.value,
        HeadingTag.H4.value,
        HeadingTag.H5.value,
        HeadingTag.H6.value,
    ]
    heading_font_size = {
        HeadingTag.H1.value: 22,
        HeadingTag.H2.value: 20,
        HeadingTag.H3.value: 18,
        HeadingTag.H4.value: 16,
        HeadingTag.H5.value: 14,
        HeadingTag.H6.value: 12,
    }

    def __init__(self, stream: bytes, document_type: Literal["pdf", "docx", "none"] = "none"):
        self.document = stream
        self.list_numerical_start = re.compile(r"^\d+", re.ASCII)
        self.all_capital_chars = re.compile(r"^([A-Z:\s]|[^\w\s])+$", re.ASCII)
        self.strip_regex = re.compile(r"[^\w]+", re.ASCII)
        self.capitals_regex = re.compile(r"^[A-Z]", re.ASCII)
        self.default_font = "Comic Sans"  # Lol
        self.document_type = document_type

    def _percentage_difference(self, value_one: float, value_two: float, debug=False):
        top_diff = abs(value_one - value_two)
        bottom_diff = (value_one + value_two) / 2
        if debug:
            print("top", top_diff)
            print("bottom", bottom_diff)

        return (top_diff / bottom_diff) * 100

    def _get_most_common_or_max(self, values: list[float]) -> int:
        """
        Returns the most common item found in a sequence
        TODO: Potentially move as a helper function if needed outside extractor
        """
        values, counts = np.unique(values, return_counts=True)  # type: ignore
        first_val = counts[0]
        all_same = all(count_val == first_val for count_val in counts)
        idx = np.argmax(counts)
        return values[int(idx)] if not all_same else max(values)

    def _get_most_common(self, values: Sequence[T]) -> T:
        """
        Returns the most common item found in a sequence
        TODO: Potentially move as a helper function if needed outside extractor
        """
        values, counts = np.unique(values, return_counts=True)  # type: ignore
        idx = np.argmax(counts)
        return values[int(idx)]

    def _regex_strip_none_ascii_chars(self, string: str):
        """
        Returns the none ascii stripped text
        """
        return "".join(self.strip_regex.sub("", string)).strip()

    def _regex_strip_none_ascii_chars_lower(self, string: str):
        """
        Returns the none ascii stripped text lower cased
        """
        return self._regex_strip_none_ascii_chars(string).lower()

    def run(self) -> tuple[ExtractedDetails, list[ExtractedPage]]:
        """
        Run extractor on the attached document

        TODO: Not the best name so may make sense to change
        """
        return (
            ExtractedDetails(
                page_count=0, base_font_size=self.base_font, toc=[], summary=[]
            ),
            [],
        )

    def _are_reference_paragraphs(self, first_paragraph: str, second_paragraph: str):
        """
        Checks if both paragraphs a bunch of references or footnotes i.e.
        `[9] Andrew Begel and Beth Simon`
        """
        reference_start = re.compile(r"^(\[[\d]+\] )", re.ASCII)

        return reference_start.match(first_paragraph.strip()) and reference_start.match(
            second_paragraph.strip()
        )

    def _is_same_list_paragraph(self, first_pargraph: str, second_paragraph: str):
        """
        Assess both paragraphs and determines if they are effectively part of
        the same block of text.
        """

        # Has the setence ended on the first paragraph and a new one started
        # on the next
        if first_pargraph.strip().endswith(".") and re.match(
            r"^[A-Z]", second_paragraph.strip()
        ):
            return False

        if self._are_reference_paragraphs(first_pargraph, second_paragraph):
            return False

        return True

    def _is_same_paragraph_segment(
        self,
        first_paragraph: ExtractedParagraphSegment,
        second_paragraph: ExtractedParagraphSegment,
    ) -> bool:
        """
        Assess both paragraph segments and determins if they are part of
        the same block of text
        """
        first_paragraph_text = first_paragraph.text.strip()
        second_paragraph_text = second_paragraph.text.strip()

        # Make sure we are dealing with paragraphs that have formats
        if len(first_paragraph.formats) == 0 or len(second_paragraph.formats) == 0:
            return False

        # Do the paragraph font sizes match
        if first_paragraph.font_size != second_paragraph.font_size:
            return False

        # Do both paragraphs start with a bold word/character
        if (
            first_paragraph.formats[0].type == "bold"
            and second_paragraph.formats[0].type == "bold"
        ):
            return False

        # Does the first paragraph end in a super and the second paragraph
        # start with a capital letter
        if first_paragraph.formats[-1].type == "super" and self.capitals_regex.match(
            second_paragraph.text.strip()
        ):
            return False

        # Compare the most common font between the two paragraphs, if different
        # then the context between the two paragraphs will likely be different
        # so don't merge
        first_paragraph_fonts = [format.font for format in first_paragraph.formats]
        second_paragraph_fonts = [format.font for format in second_paragraph.formats]
        filtered_first_fonts = list(
            filter(lambda item: item is not None, first_paragraph_fonts)
        )
        filtered_second_fonts = list(
            filter(lambda item: item is not None, second_paragraph_fonts)
        )

        if len(filtered_first_fonts) > 0 and len(filtered_second_fonts) > 0:
            first_paragraph_common_font = self._get_most_common(
                first_paragraph_fonts,
            )
            second_paragraph_common_font = self._get_most_common(
                second_paragraph_fonts,
            )
            if first_paragraph_common_font != second_paragraph_common_font:
                return False

        return self._is_same_paragraph_text(
            first_paragraph_text,
            second_paragraph_text,
        )

    def _is_same_paragraph_text(
        self,
        first_paragraph: str,
        second_paragraph: str,
    ):
        """
        Assess both paragraphs and determines if they are effectively part of
        the same block of text.
        """
        all_capital_start = re.compile(r"^[A-Z]{3,}$", re.ASCII)

        stripped_first_paragraph = first_paragraph.strip()
        stripped_second_paragraph = second_paragraph.strip()

        # Does the second paragraph start with a capital letter?
        if stripped_first_paragraph.endswith(".") and re.match(
            r"^[A-Z]", stripped_second_paragraph
        ):
            return False

        # Does the first paragraph finish the sentence and the next paragraph
        # starts with a list?
        # TODO: Potentially something to add to list merging
        if stripped_first_paragraph.endswith(".") and self.list_numerical_start.match(
            stripped_second_paragraph
        ):
            return False

        # Are both paragraphs starting with all capitals? If so then likely
        # not to be continued
        first_paragraph_first_word = stripped_first_paragraph.split(" ")[0]
        second_paragraph_first_word = stripped_second_paragraph.split(" ")[0]
        if all_capital_start.match(
            self._regex_strip_none_ascii_chars(first_paragraph_first_word)
        ) and all_capital_start.match(
            self._regex_strip_none_ascii_chars(second_paragraph_first_word)
        ):
            return False

        if all_capital_start.match(
            self._regex_strip_none_ascii_chars(first_paragraph_first_word)
        ) and self.capitals_regex.match(stripped_second_paragraph):
            return False

        if self._are_reference_paragraphs(first_paragraph, second_paragraph):
            return False

        # Does the first paragraph end with a: signifying either a list or
        # data block
        if stripped_first_paragraph.endswith(":"):
            return False

        return True

    def _merge_list(
        self,
        list_one: ExtractedListSegment,
        list_two: ExtractedListSegment,
        merge_paragraph=False,
    ):
        """
        Merges both ExtractedListSegements into one. If merge_paragraph is True then
        the last item on list_one and first item on list_two will be merged into one.
        """
        copied_list_one = replace(list_one)
        copied_list_two = replace(list_two)

        # TODO: Add deepcopy for dataclasses
        if merge_paragraph:
            copied_list_one.items[-1].value += " " + copied_list_two.items[0].value
            del copied_list_two.items[0]

        return ExtractedListSegment(
            list_one.format,
            copied_list_one.items + copied_list_two.items,
            copied_list_one.id,
        )

    def _strip_list_text(self, list_extract: ExtractedListSegment):
        """
        Removes white space from all ExtractedListSegment items
        """
        all_items = list_extract.items
        for item in all_items:
            item.value = item.value.strip()

        return ExtractedListSegment(
            list_extract.format,
            all_items,
            list_extract.id,
        )

    def _is_same_ordered_list(
        self, list_one: ExtractedListSegment, list_two: ExtractedListSegment
    ):
        """
        Assesses the starting character of both list segments and determines
        whether the second list is a continuation of the first list.
        """
        list_one = self._strip_list_text(list_one)
        list_two = self._strip_list_text(list_two)

        is_list_one_numerical = any(
            self.list_numerical_start.match(item.value) for item in list_one.items
        )
        is_list_two_numerical = any(
            self.list_numerical_start.match(item.value) for item in list_two.items
        )

        if is_list_one_numerical != is_list_two_numerical:
            return False, False

        last_item = list_one.items[-1]

        last_item_search = self.list_numerical_start.search(last_item.value)
        first_item_search = None
        first_item_idx = -1

        for idx in range(len(list_two.items)):
            list_two_item = list_two.items[idx]
            search = self.list_numerical_start.search(list_two_item.value)
            if search is not None:
                first_item_search = search
                first_item_idx = idx
                break

        if first_item_idx == -1:
            return False, False

        if last_item_search is None or first_item_search is None:
            return False, False

        last_item_number = int(last_item_search.group(0))
        first_item_number = int(first_item_search.group(0))

        # Return if the number on the next list is equal to the
        # number on the last item on the previous list + 1
        # e.g. 26 -> 27
        is_continued = last_item_number + 1 == first_item_number

        return (is_continued, first_item_idx > 0 if is_continued else False)

    def _is_unordered_continued_ordered(
        self, list_one: ExtractedListSegment, list_two: ExtractedListSegment
    ):
        is_same_paragraph = self._is_same_list_paragraph(
            list_one.items[-1].value,
            list_two.items[0].value,
        )

        if is_same_paragraph:
            return True, True

        return self._is_same_ordered_list(list_one, list_two)

    def _attempt_list_merge(
        self, list_one: ExtractedListSegment, list_two: ExtractedListSegment
    ):
        """
        Attempts to merge the two lists. If successful a new list is returned,
        if failure, None is returned
        """
        if list_one.format == "ordered" and list_two.format == "unordered":
            # We should check the next unordered at least contains the same
            # numerical or character start
            is_continued, continued_paragraph = self._is_unordered_continued_ordered(
                list_one, list_two
            )
            if is_continued:
                merged_item = self._merge_list(
                    list_one,
                    list_two,
                    continued_paragraph,
                )
                return merged_item

        if list_one.format == "unordered" and list_two.format == "unordered":
            continued_paragraph = self._is_same_list_paragraph(
                list_one.items[-1].value,
                list_two.items[0].value,
            )
            merged_item = self._merge_list(
                list_one,
                list_two,
                continued_paragraph,
            )
            return merged_item

        if list_one.format == "ordered" and list_two.format == "ordered":
            # TEMP - WE NEED TO CONFIRM ORDERED TYPE I.E 1. OR A. ETC...
            is_continued, continued_paragraph = self._is_same_ordered_list(
                list_one, list_two
            )
            if not is_continued:
                return None
            merged_item = self._merge_list(
                list_one,
                list_two,
                continued_paragraph,
            )
            return merged_item

        # TODO: Yet to find a scenario where `unordered` -> `ordered` is a
        # viable merge. Need further scenarios to implement
        return None

    def _is_same_list(self, *args):
        """
        Assess all list segemets to see if they can be merged. If they can then
        a merged segment is returned otherwise, None is returned.
        """
        if len(args) <= 1:
            return None

        list_start = args[0]

        for idx in range(1, len(args)):
            next_item = args[idx]
            merged_item = self._attempt_list_merge(list_start, next_item)
            if merged_item is None:
                return None
            else:
                list_start = merged_item

        return list_start

    def _remove_list_tags(self, list_item: ExtractedListSegment):
        """
        Removed the list value starting tags such as numbers,
        fullstops and dash
        """
        new_item = list_item
        for item in new_item.items:
            item.value = item.value.lstrip("0123456789.- ")
        return new_item

    def _trim_list_segment(self, all_data: list, idx: int):
        """
        Trims starting tags from list and merges with proceeding list item's
        if detected to be a continuation
        """
        item = all_data[idx]
        lists = [item]
        # Loop through and get all lists that come straight after
        for next_idx in range(idx + 1, len(all_data)):
            next_item = all_data[next_idx]
            # If we've come to end of list set then break out
            if next_item.type != ExtractType.LIST.value:
                break
            lists.append(next_item)

        # No continuation, trim tags and return
        if len(lists) == 1:
            lists[0] = self._remove_list_tags(lists[0])
            return

        # If we have more than one list then send lists off to
        # assess if they're the same
        merged_list = self._is_same_list(*lists)

        # Unable to merge, remove all list item tags and return
        if merged_list is None:
            for list in lists:
                list = self._remove_list_tags(list)
            return

        merged_list = self._remove_list_tags(merged_list)

        # Update first list item to the merged list
        all_data[idx] = merged_list

        # Get all remaining indexes after current
        indexes = [idx + del_idx for del_idx in range(1, len(lists))]

        # Remove indexes going backwards
        for del_idx in indexes[::-1]:
            del all_data[del_idx]

    def _merge_paragraphs(self, paragraphs: list[ExtractedParagraphSegment]):
        """
        Combines all paragraphs in the provided list into one
        ExtractedParagraphSegment
        """
        merged_text = ""
        merged_formats: List[ExtractFormat] = []

        for paragraph in paragraphs:
            merged_text += paragraph.text + " "
            merged_formats = merged_formats + paragraph.formats

        # use first paragraph items
        return ExtractedParagraphSegment(
            paragraphs[0].bbox,
            merged_text.strip(),
            paragraphs[0].font_size,
            merged_formats,
            paragraphs[0].id,
        )

    def _trim_paragraph_segment(self, all_data: list, idx: int):
        """
        Attempts to merge the paragraph with proceeding paragraph items
        """
        item = all_data[idx]

        if item.type != ExtractType.TEXT.value:
            return

        paragraphs = [item]

        for next_idx in range(idx + 1, len(all_data)):
            next_item = all_data[next_idx]
            if next_item.type != ExtractType.TEXT.value:
                break

            if not self._is_same_paragraph_segment(paragraphs[-1], next_item):
                break

            paragraphs.append(next_item)

        if len(paragraphs) == 1:
            return

        merged_paragraph = self._merge_paragraphs(paragraphs)

        # Update first list item to the merged list
        all_data[idx] = merged_paragraph

        # Get all remaining indexes after current
        indexes = [idx + del_idx for del_idx in range(1, len(paragraphs))]

        # Remove indexes going backwards
        for del_idx in indexes[::-1]:
            del all_data[del_idx]

    def _trim_all_content(self, all_data: list):
        """
        Loops through all content within dataset and merges data if detected
        to have been split. Currently only works with lists
        """
        for idx, item in enumerate(all_data):
            if idx + 1 >= len(all_data):
                break

            if item.type == ExtractType.LIST.value:
                self._trim_list_segment(all_data, idx)
            elif item.type == ExtractType.TEXT.value:
                self._trim_paragraph_segment(all_data, idx)

    def _link_footnotes(self, pages) -> Dict[str, ExtractedFootnoteSegment]:
        """
        Generates IDs for footnotes and adds relationship links to any
        superscript items that might link to the footnotes on the page
        """
        footnotes = {}

        for page in pages:
            if len(page.footnotes) == 0:
                continue

            footnote_id = str(uuid4())
            footnotes[footnote_id] = page.footnotes

            superscripts = [
                footnote.superscript
                for segment in page.footnotes
                for footnote in segment.items
            ]

            for content in page.content:
                if not isinstance(content, ExtractedParagraphSegment):
                    continue

                for format in content.formats:
                    if format.type != "super":
                        continue

                    split_super = format.value.split(",")
                    in_footnotes = list(
                        filter(lambda item: item in superscripts, split_super)
                    )

                    if not in_footnotes:
                        continue

                    format.relationships.append(
                        ExtractRelationship(
                            "footnote",
                            footnote_id,
                        )
                    )

        return footnotes

    def get_extracted_data(self):
        """
        Extracts data and generates sections
        """
        if self.document is None:
            raise FileNotFoundError()
        details, pages = self.run()

        # all_footnotes = self._link_footnotes(pages)
        # all_footnotes = {}

        all_data = [item for content in pages for item in content.content]

        # Trim content should only be done on PDFS
        if self.document_type == "pdf":
            self._trim_all_content(all_data)

        first_heading = -1
        heading_tag = None

        # Should this only be text?
        root_data = []

        for idx, data in enumerate(all_data):
            # TODO: What happens if the first item is text and not a heading?
            if data.type != ExtractType.HEADING.value:
                root_data.append(data)
                continue

            if data.type == ExtractType.HEADING.value:
                heading_tag = data.tag
                first_heading = idx
                break

        starts_with_title = heading_tag == HeadingTag.H1.value

        document_name:str = "Document"
        if not isinstance(self.document, bytes) and self.document.name is not None and self.document.name != "[document]":
            document_name = self.document.name

        sections = {}
        if first_heading == -1:
            # Return root_text with section being document name
            segment = ExtractedSegment(
                id=uuid4().hex,
                title=document_name,
                font_size=self.heading_font_size[HeadingTag.H1.value],
                content=root_data,
                tag=HeadingTag.H1.value,
            )
            sections[segment.id] = segment

            toc = self._generate_toc(sections)
            details.toc = toc
            details.title = document_name

        else:
            sections = {}

            # Is there root data we need to add to the start
            if root_data:
                pre_segment = Section(
                    id=uuid4().hex,
                    title=document_name if starts_with_title else "",
                    font_size=self.heading_font_size[HeadingTag.H1.value],
                    content=root_data,
                    tag=HeadingTag.H1.value,
                )
                sections[pre_segment.id] = pre_segment

            chunked_sections = self._chunk_data(all_data, first_heading)
            toc = self._generate_toc(chunked_sections)

            sections = {**sections, **chunked_sections}
            details.toc = toc
            details.title = self.extracted_title
            # details.footnotes = all_footnotes

        extracted_document = ExtractedDocument(details, sections)
        extracted_document.details.word_count = extracted_document.get_word_count()
        return extracted_document

    def _generate_section_toc(self, id: str, sections: dict, altered_ids: list[str]):
        """
        Generates table of contents for a section
        """
        section = sections[id]
        toc = ExtractedTOC(
            title=section.title,
            size=section.font_size,
            id=section.id,
            tag=section.tag,
            sections=[],
        )

        # Early return to make it on the same level as h2's.
        # Also keeping it as h1 allows for the FE to grab the title
        if toc.tag == HeadingTag.H1.value:
            return toc

        nested_sections = []
        for content in section.content:
            if content.type == ExtractType.SECTION.value:
                if content.id in altered_ids:
                    continue
                altered_ids.append(content.id)
                child_section = sections[content.id]
                nested_sections.append(
                    self._generate_section_toc(child_section.id, sections, altered_ids)
                )

        toc.sections = nested_sections
        return toc

    def _generate_toc(self, sections: dict[str, Section]):
        """
        Generates table of contents
        """
        copied = sections.copy()
        toc: list[ExtractedTOC] = []

        altered_ids: list[str] = []
        for id in copied:
            if id in altered_ids:
                continue

            toc.append(self._generate_section_toc(id, copied, altered_ids))

        return toc

    def _chunk_data(self, data: list, start_idx: int = 0) -> dict[str, Section]:
        """
        Converts all data into chunks based on headings
        """

        section_list: list[Section] = []

        section = {
            "id": "",
            "title": "",
            "font_size": 16,
            "tag": HeadingTag.H2.value,
            "content": [],
        }
        for idx in range(start_idx, len(data)):
            current_item = data[idx]
            if current_item.type == ExtractType.HEADING.value:
                # Is there already a section that's been populated?
                if section["title"] != "":
                    # sections[section["id"]] = Section(**section)
                    section_list.append(Section(**section))
                    section = {
                        "id": "",
                        "title": "",
                        "font_size": 16,
                        "tag": HeadingTag.H2.value,
                        "content": [],
                    }

                section["title"] = current_item.title
                section["id"] = current_item.id
                section["font_size"] = current_item.font_size
                section["tag"] = current_item.tag
                continue

            section["content"].append(current_item)

        if section["title"] != "":
            # sections[section["id"]] = Section(**section)
            section_list.append(Section(**section))

        self._set_extracted_title(section_list)

        sections: dict[str, Section] = {item.id: item for item in section_list}

        self._nest_sections(sections)
        self._set_section_word_count(sections)
        return sections

    def _set_section_word_count(self, sections: Dict[str, Section]):
        for key in sections.keys():
            section = sections[key]
            sub_section_keys = [
                content.id
                for content in section.content
                if content.type == ExtractType.SECTION.value
            ]
            child_word_count = 0
            word_count = 0
            for id in sub_section_keys:
                child_word_count += sections[id].get_word_count()

            word_count += section.get_word_count()

            section.word_count = word_count
            section.child_word_count = child_word_count

    def _set_extracted_title(self, sections: List[Section]):
        h1_list = list(filter(lambda item: item.tag == HeadingTag.H1.value, sections))
        has_h1 = len(h1_list) > 0

        h1_element = h1_list[0] if has_h1 else None

        if not has_h1 and sections[0].title != "Document" and sections[0].title != "":
            sections[0].tag = HeadingTag.H1.value
            h1_element = sections[0]

        # Shouldn't ever really be None at this point but python linter cried
        self.extracted_title = (
            h1_element.title.strip()
            if h1_element is not None
            else self.document.name.strip()
        )

    def _nest_sections(self, sections: dict) -> None:
        """
        Nests sections based on headings
        """
        checked_ids = []
        for id in sections:
            section = sections[id]
            section_parent_tag = int(section.tag[1])
            prev_added_tag = -1
            for next_id in sections:
                if next_id == id or next_id in checked_ids:
                    continue

                next_section = sections[next_id]

                next_tag = int(next_section.tag[1])
                if next_tag <= section_parent_tag:
                    break
                if prev_added_tag != -1 and next_tag > prev_added_tag:
                    continue
                prev_added_tag = next_tag

                section.content.append(SubSection(next_section.id, next_section.title))
            checked_ids.append(id)
