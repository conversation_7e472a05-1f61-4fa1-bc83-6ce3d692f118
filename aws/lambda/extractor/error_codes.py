from enum import IntEnum


class ExtractorError(IntEnum):
    UNKNOWN = 0
    IMAGEEXTRACTION = 1
    TEXTEXTRACTION = 2
    HEADEREXTRACTION = 3
    LINKEXTRACTION = 4
    PAGEEXTRACTION = 5
    FONTEXTRACTION = 6
    HEADERSTYLING = 7
    CONTENTPARSING = 8
    DOCUMENTPARSING = 9
    TABLEEXTRACTION = 10
    TABLESTYLING = 11
    OCRREQUIRED = 12
    ECRYPTEDFILE = 13
    INVALIDFILETYPE = 14


def get_error_message(code: int) -> str:
    base_message = "An error occured extracting the document"
    message_table = {
        ExtractorError.IMAGEEXTRACTION: "images",
        ExtractorError.TEXTEXTRACTION: "text",
        ExtractorError.HEADEREXTRACTION: "headers",
        ExtractorError.LINKEXTRACTION: "links",
        ExtractorError.PAGEEXTRACTION: "pages",
        ExtractorError.FONTEXTRACTION: "fonts",
        ExtractorError.HEADERSTYLING: "header styles",
        ExtractorError.TABLEEXTRACTION: "tables",
    }

    try:
        error = ExtractorError(code)
        if error == ExtractorError.UNKNOWN:
            return "An unknown error occured extracting the document data"

        return f"{base_message} {message_table[error]}"
    except Exception:
        return "Unknown error code"
