from .error_codes import ExtractorError


class ExtractionException(Exception):
    code = 0
    message = ""

    def __init__(self, code: int, message: str) -> None:
        self.code = code
        self.message = message
        super().__init__(message)

    def as_dict(self):
        return {"code": self.code, "message": self.message}


class UnknownExtractionException(ExtractionException):
    def __init__(self, message="An unknown extraction error occured") -> None:
        super().__init__(ExtractorError.UNKNOWN, message)


class ImageExtrationException(ExtractionException):
    def __init__(self, message="Error extracting document images") -> None:
        super().__init__(ExtractorError.IMAGEEXTRACTION, message)


class TextExtractionException(ExtractionException):
    def __init__(self, message="Error extracting document text") -> None:
        super().__init__(ExtractorError.TEXTEXTRACTION, message)


class HeaderExtractionException(ExtractionException):
    def __init__(self, message="Error extracting document headers") -> None:
        super().__init__(ExtractorError.HEADEREXTRACTION, message)


class LinkExtractionException(ExtractionException):
    def __init__(self, message="Error extracting document links") -> None:
        super().__init__(ExtractorError.LINKEXTRACTION, message)


class PageExtractionException(ExtractionException):
    def __init__(self, message="Error extraction document pages") -> None:
        super().__init__(ExtractorError.PAGEEXTRACTION, message)


class FontExtractionException(ExtractionException):
    def __init__(self, message="Error extracting document font") -> None:
        super().__init__(ExtractorError.FONTEXTRACTION, message)


class ContentParsingException(ExtractionException):
    def __init__(self, message="Error extracting document content") -> None:
        super().__init__(ExtractorError.CONTENTPARSING, message)


class DocumentParsingException(ExtractionException):
    def __init__(self, message: str) -> None:
        super().__init__(ExtractorError.DOCUMENTPARSING, message)


class HeaderStylingExtractionException(ExtractionException):
    def __init__(self, message="Error extracting document header styles") -> None:
        super().__init__(ExtractorError.HEADERSTYLING, message)


class TableExtractionException(ExtractionException):
    def __init__(self, message="Error extracting document text") -> None:
        super().__init__(ExtractorError.TABLEEXTRACTION, message)


class TableStylingExtractionException(ExtractionException):
    def __init__(self, message="Error extracting document table styles") -> None:
        super().__init__(ExtractorError.TABLESTYLING, message)


class OCRRequiredExtractionException(ExtractionException):
    def __init__(self, message="Error document requires OCR") -> None:
        super().__init__(ExtractorError.OCRREQUIRED, message)


class EncryptedFileExtractionException(ExtractionException):
    def __init__(self, message="Error document is encrypted") -> None:
        super().__init__(ExtractorError.ECRYPTEDFILE, message)


class InvalidFileTyleMethodException(ExtractionException):
    def __init__(self, message="Error document is of incorrect type") -> None:
        super().__init__(ExtractorError.INVALIDFILETYPE, message)
