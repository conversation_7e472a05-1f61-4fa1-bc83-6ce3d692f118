"""
Transforms raw extracted document data into a format that can be efficiently stored,
searched, and presented to users in the Tailo application. This is stored back into S3
along with any extracted images.

This is called after extraction is complete (from textract or other extractors). After
this the document is ready for display.
"""

import json
import re
import traceback
from datetime import datetime, timezone
from os import environ
from typing import Union
from uuid import uuid4

import boto3
from database import Document, DocumentStatus
from database.models import User
from database.services import commit as commit_document
from document_search.indexer import Indexer
from helpers.amplitude import AmplitudeEvent, Track
from helpers.functions import get_now, logger, session
from summariser import generate_quick_summary
from summariser.constants import (
    SUMMARISATION_LANGUAGE,
    SUMMARISE_PROMPT_TEMPLATE,
    SUMMARISE_SUMMARIES_PROMPT_TEMPLATE,
)
from summariser.helpers import SafeDict

from .document_extractor import ExtractType
from .document_structure import ExtractedDocument
from .progress_tracking import report_error, update_document_status

# Should be in DI, defined centrally
s3_client = boto3.client("s3", region_name=session.region_name)
bucket_name: str = environ["BUCKET_NAME"]


# Upload image from block to S3
def upload_image(image: dict, path: str, s3_client, bucket_name: str) -> dict:
    """
    number
    type
    bbox
    width
    height
    ext e.g. jpeg
    xres
    yres
    bpc
    transform
    size
    image
    """
    extension = image["ext"]
    image_data = image["image"]
    image_key: str = f"{path}/images/{uuid4().hex}.{extension}"

    s3_client.put_object(
        Bucket=bucket_name,
        Key=image_key,
        Body=image_data,
    )

    return {
        "key": image_key,
        "width": image["width"],
        "height": image["height"],
        "transform": image["transform"],
    }


# Upload image to s3 and replace image data and extension with s3 key and meta
def parse_image_block(block: dict, object_key: str, bucket_name: str):
    image_data = upload_image(block, object_key, s3_client, bucket_name)
    logger.info(image_data)
    block.update(image_data)
    block.pop("image")
    block.pop("ext")


# Generate summary from extracted data and store in data.details.summary
def generate_summary(
    document: Document,
    user: User,
    data: ExtractedDocument,
    amplitude_event_data: dict,
) -> None:
    try:
        logger.info("running analysis: summary")
        bullet_count = 6
        language: Union[str, None] = SUMMARISATION_LANGUAGE.get(
            str(session.region_name),
        )
        if language is None:
            raise Exception("Invalid summarisation language")

        prompt: str = SUMMARISE_PROMPT_TEMPLATE.format_map(
            SafeDict(
                bullets=bullet_count,
                language=language,
            ),
        )
        summary: str = SUMMARISE_SUMMARIES_PROMPT_TEMPLATE.format_map(
            SafeDict(
                bullets=bullet_count,
                prompt=prompt.replace("{text}", ""),
                language=language,
            ),
        )

        data_str = str(data)

        amplitude_event_data["character_count"] = len(data_str.replace(" ", ""))

        output, _ = generate_quick_summary(
            data=data_str,
            main_prompt=prompt,
            summary_prompt=summary,
        )

        logger.info(f"Summary: {output}")
    except Exception as e:
        logger.exception(f"Error generating summary: {e}")
        output = {"output_text": "Summary generation failed"}

    output_bullets = output["output_text"].split("\n")

    summary_list = []
    logger.info("Generated Bullets")

    for bullet in output_bullets:
        # Clean up the bullet start, mainly removing hyphens
        clean_bullet: str = re.sub("^[^A-Za-z0-9 ]+", "", bullet).strip()
        # Don't add empty bullets
        if clean_bullet:
            summary_list.append(clean_bullet)

    data.details.summary = summary_list

    amplitude_event_data["summary_bullets"] = data.details.summary
    amplitude_event_data["summary_characters"] = len(
        "".join(data.details.summary).replace(" ", ""),
    )


# Loop over all blocks in sections and push any images found to s3
# NOTE: This is required by the upload method as it removes the image
#      byte data from the data_dict
def parse_images(data_dict: dict, document: Document) -> None:
    for section in data_dict["sections"].values():
        content = section["content"]
        for item in content:
            if item["type"] == ExtractType.IMAGE.value:
                parse_image_block(item, document.path, bucket_name)


# Upload processed document data to s3
def upload_processed_data(data_dict: dict, document: Document) -> None:
    # Convert data to json string and push to S3
    json_data: bytes = json.dumps(data_dict, indent=4).encode("utf-8")
    extracted_key: str = f"{document.path}/extracted.json"
    logger.info(f"extracted_key: {extracted_key}")

    s3_client.put_object(Bucket=bucket_name, Key=extracted_key, Body=json_data)

    # Update document with extracted path
    document.extracted_path = extracted_key


# Set extraction information and metadata on document and commit
def update_document_metadata(data: ExtractedDocument, document: Document) -> None:
    meta: dict = document.meta if document.meta is not None else {}
    if data.details.title != "Document":
        document.extracted_title = str(data.details.title)
        document.display_name = str(data.details.title)

        meta["title"] = data.details.title

        # TODO: Should this be moved out of surrounding condition?
        if data.details.authors is not None:
            meta["author"] = (data.details.authors,)
    else:
        document.display_name = document.filename

    logger.info(f"meta: {meta}")
    logger.info(f"document: {document}")

    document.meta = meta

    commit_document(document)


# Index document into elasticsearch
def index_document(document_uuid: str, user_uuid: str) -> None:
    indexer = Indexer()
    indexer.index_document(
        user_uuid=user_uuid,
        document_uuid=document_uuid,
        bucket_name=bucket_name,
    )


# Track amplitude event on completion of extraction and post-processing
def track_amplitude(document: Document, amplitude_event_data: dict) -> None:
    created_time: datetime = document.date_created.replace(tzinfo=timezone.utc)
    amplitude_event_data["time_processed"] = f"{(get_now() - created_time).seconds} seconds"
    amplitude_event_data["time_processed_seconds"] = (get_now() - created_time).seconds
    amplitude_event_data["status"] = "available"
    Track(
        tracking_id=document.user_tracking_id,
        event=AmplitudeEvent.DOC_PROCESSED,
        event_data=amplitude_event_data,
    )


# Generate summary, parse images, and push processed document to s3
def post_extraction_process(
    document: Document,
    user: User,
    data: ExtractedDocument,
    amplitude_event_data: dict,
) -> None:
    try:
        update_document_status(document, user, DocumentStatus.GENERATING_SUMMARY)
        generate_summary(document, user, data, amplitude_event_data)

        update_document_status(document, user, DocumentStatus.FINISHING_UP)
        # Extract dict from data as primary data from here on
        data_json = data.dict()
        parse_images(data_json, document)
        upload_processed_data(data_json, document)
        update_document_metadata(data, document)

        document_name: str = (
            document.display_name if document.display_name is not None else document.filename
        )
        update_document_status(
            document,
            user,
            DocumentStatus.COMPLETE,
            {"name": document_name},
        )

        # Post-completion tasks - tracking and such

        # TODO: Likely remove this shortly
        index_document(document.uuid, user.estendio_id)
        track_amplitude(document, amplitude_event_data)

        logger.info(f"Extraction complete for document of id `{document.id}`")
    except Exception as e:
        print(traceback.format_exc())
        print(f"ERROR: {e}")
        amplitude_event_data["status"] = "error"
        amplitude_event_data["error_type"] = -1  # UNKNOWN
        Track(
            tracking_id=document.user_tracking_id,
            event=AmplitudeEvent.DOC_PROCESSED,
            event_data=amplitude_event_data,
        )
        if "document" in locals():
            report_error(document, user, {"code": -1, "message": str(e)})
