from enum import Enum


class TextractLayoutBlock(Enum):
    TITLE = "LAYOUT_TITLE"
    HEADER = "LAYOUT_HEADER"
    FOOTER = "LAYOUT_FOOTER"
    SECTION_HEADER = "LAYOUT_SECTION_HEADER"
    PAGE_NUMBER = "LAYOUT_PAGE_NUMBER"
    LIST = "LAYOUT_LIST"
    FIGURE = "LAYOUT_FIGURE"
    TABLE = "LAYOUT_TABLE"
    KEY_VALUE = "LAYOUT_KEY_VALUE"
    TEXT = "LAYOUT_TEXT"


class ExtractType(Enum):
    TEXT = "paragraph"
    HEADING = "heading"
    IMAGE = "img"
    TABLE = "table"
    SECTION = "section"
    LIST = "list"
    FOOTNOTE = "footnote"
    FOOTER = "footer"
    MATH = "math"


class HeadingTag(Enum):
    H1 = "h1"
    H2 = "h2"
    H3 = "h3"
    H4 = "h4"
    H5 = "h5"
    H6 = "h6"


SPACE_WIDTH = 0.2
