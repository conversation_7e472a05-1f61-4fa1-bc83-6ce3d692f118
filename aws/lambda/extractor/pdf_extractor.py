import json
import os
import re
import time
import traceback
import uuid
from dataclasses import asdict, dataclass, field
from functools import reduce
from typing import Any, Dict, List, Set, Tuple, Union

import numpy as np
from dotenv import load_dotenv
from fitz import Matrix, Page, Pixmap, Rect, csRGB
from fitz import open as open_pdf
from typing_extensions import Literal

from .constants import (
    SPACE_WIDTH,
    ExtractType,
    HeadingTag,
    TextractLayoutBlock,
)
from .document_extractor import DocumentExtractor
from .document_structure import (
    ExtractedDetails,
    ExtractedFootnoteItem,
    ExtractedFootnoteSegment,
    ExtractedHeader,
    ExtractedHeadingSegment,
    ExtractedImagesSegment,
    ExtractedListItem,
    ExtractedListSegment,
    ExtractedPage,
    ExtractedParagraphSegment,
    ExtractedTableContent,
    ExtractedTableSegment,
    ExtractFormat,
    TextractLine,
)
from .exceptions import (
    DocumentParsingException,
    ExtractionException,
    FontExtractionException,
    HeaderStylingExtractionException,
    ImageExtrationException,
    LinkExtractionException,
    TableStylingExtractionException,
)

image_xref_id_mapped: Dict = {}
load_dotenv()

HEADER_WORD_LIMIT = 15
tessdata = os.getenv("TESSDATA_PREFIX")

mat = Matrix(5, 5)  # high resolution matrix

ocr_time = 0
pix_time = 0
INVALID_UNICODE = chr(0xFFFD)  # the "Invalid Unicode" character


@dataclass
class HeaderFormatValues:
    font_sizes: Set[float] = field(
        default_factory=lambda: set(),
    )
    font_familys: Set[str] = field(
        default_factory=lambda: set(),
    )


@dataclass
class HeaderFormats:
    bold: HeaderFormatValues = HeaderFormatValues()
    italic: HeaderFormatValues = HeaderFormatValues()
    none: HeaderFormatValues = HeaderFormatValues()


@dataclass
class HeaderRules:
    max_length: int = 0
    has_numbered_starts: bool = False
    has_all_caps: bool = False
    formats: HeaderFormats = HeaderFormats()
    asdict = asdict


class PDFExtractor(DocumentExtractor):
    textract: list[dict]
    has_title = False
    page_count = 0
    # incorrect_words = {} We'll use this in future to take a count of words
    # on end of lines that are potentially incorrectly hyphenated

    def __init__(self, stream, textract):
        """
        Initialise the PDF Extractor
        """
        super().__init__(open_pdf(stream=stream, filetype="pdf"), "pdf")
        self.textract = textract
        self.font_style_regex = re.compile(r"\.([ibu])([+\w]*)$", re.ASCII)
        self.single_char_start = re.compile(r"^[a-zA-Z] ", re.ASCII)
        self.numerical_start = re.compile(r"^(\d\.?)+ ", re.ASCII)
        self.page_count = self.document.page_count
        self._header_rules = HeaderRules(0, False, False, HeaderFormats())
        # Try to capture non titles such as Figure 1, Table 1, Picture 1 etc...
        self._non_title_regex = re.compile(r"^[\w]+[ \.]{1,2}[\d]{1,2}", re.ASCII)
        self._non_headings = []
        self.metadata = self.document.metadata

    def get_tessocr(self, page: Page, bbox: Rect):
        """Return OCR-ed span text using Tesseract.

        Args:
            page: fitz.Page
            bbox: fitz.Rect or its tuple
        Returns:
            The OCR-ed text of the bbox.
        """
        global ocr_time, pix_time, tess, mat
        # Step 1: Make a high-resolution image of the bbox.
        t0 = time.perf_counter()
        pix: Pixmap = page.get_pixmap(
            matrix=mat,
            clip=self._rect_to_bbox(bbox),
        )

        t1 = time.perf_counter()
        # TODO: Investigate - if pixmap has a smaller width than 3 then we get:
        # Image too small to scale!! (2x36 vs min width of 3)
        # Line cannot be recognized!!
        ocrpdf = open_pdf("pdf", pix.pdfocr_tobytes())
        ocrpage = ocrpdf[0]
        text = ocrpage.get_text()
        if text.endswith("\n"):
            text = text[:-1]
        t2 = time.perf_counter()
        ocr_time += t2 - t1
        pix_time += t1 - t0
        return text

    def extract_images_from_page(self, doc, page):
        """
        Extract all the images that are on the page
        """
        try:
            # All images per page extracted into a public/imgs dir
            image_list_with_smask = page.get_images()
            image_list_with_box = page.get_image_info(hashes=False, xrefs=True)

            images = []

            for img in image_list_with_box:  # enumerate the image list
                xref = img["xref"]  # get the XREF of the image
                original_height = img["height"]
                original_width = img["width"]

                try:
                    pix = Pixmap(
                        doc,
                        xref,
                    )  # create a Pixmap
                except Exception as e:
                    # TODO: Some images give invalid xrefs, is there a way to fix this?
                    print(f"skipping image on page {page.number}")
                    print(f"reason {e}")
                    continue

                # Ensure image is RGB as that is a valid format for all image types
                pix = Pixmap(csRGB, pix)

                width = img["bbox"][2] - img["bbox"][0]
                height = img["bbox"][3] - img["bbox"][1]

                # Get smask to preserves transparency
                smask = None
                mask = None
                for image in image_list_with_smask:
                    if image[0] == xref:
                        smask = image[1]

                if smask:
                    mask = Pixmap(doc, smask)  # (2) mask pixmap

                if xref in image_xref_id_mapped:
                    images.append(
                        {
                            "bbox": img["bbox"],
                            "id": image_xref_id_mapped[xref],
                            "width": width,
                            "height": height,
                            "pixmap": pix,
                            "mask": mask,
                            "original_width": original_width,
                            "original_height": original_height,
                        },
                    )
                else:
                    image_id = uuid.uuid4()
                    image_xref_id_mapped[xref] = image_id
                    images.append(
                        {
                            "bbox": img["bbox"],
                            "id": image_id,
                            "width": width,
                            "height": height,
                            "pixmap": pix,
                            "mask": mask,
                            "original_width": original_width,
                            "original_height": original_height,
                        },
                    )

                pix = None
            return images
        except Exception as e:
            print(e)
            raise ImageExtrationException(str(e))

    def map_link_to_span(self, page: Page, block: dict):
        """
        Map link to the passed in span
        """
        try:
            links = page.get_links()
            # Img block
            block_copy = block.copy()
            if block_copy["type"] == 1:
                for link in links:
                    if link["from"].intersects(block_copy["bbox"]):
                        if "uri" not in link:
                            continue
                        block_copy["link"] = link["uri"]
            # Text block
            if block_copy["type"] == 0:
                for line in block_copy["lines"]:
                    for span in line["spans"]:
                        for link in links:
                            if "uri" not in link:
                                continue
                            if link["from"].intersects(span["bbox"]):
                                span["link"] = link["uri"]

            return block_copy
        except Exception as e:
            print(e)
            raise LinkExtractionException(str(e))

    def _get_base_font(self) -> None:
        """
        Gets the most common font from the document which could dictate base
        font.
        """
        try:
            all_sizes = []

            # TODO: Random pages can interfere with shorter documents by getting the wrong base font size.

            # The level of loops here should be considered a crime
            for page in self.document:
                page_obj = json.loads(page.get_text("json"))
                blocks = page_obj["blocks"]

                for block in blocks:
                    if "lines" not in block:
                        continue

                    for line in block["lines"]:
                        if "spans" not in line:
                            continue

                        for span in line["spans"]:
                            all_sizes.append(round(span["size"], 2))

            values, counts = np.unique(all_sizes, return_counts=True)
            if len(values) == 0:
                return

            idx = np.argmax(counts)

            self.base_font = round(values[idx], 2)
        except ExtractionException as ee:
            raise ee
        except Exception as e:
            print(e)
            raise FontExtractionException(str(e))

    def _table_to_json(self, table) -> ExtractedTableSegment:
        """
        Converts PyMuPDF table data to usable JSON
        """
        try:
            headers = table.header
            cells = table.extract()
            table_content = ExtractedTableContent([], [])

            for header in headers.names:
                if header is None or len(header) == 0:
                    continue
                header = header.strip().replace("\n", " ")
                header_item = {"type": ExtractType.TEXT.value, "text": header}
                table_content.headers.append(header_item)

            start_idx = 0 if len(headers.names) == 0 else 1

            for idx, row in enumerate(cells):
                if idx < start_idx:
                    continue

                row_list = []
                for cell in row:
                    if cell is None or cell == "":
                        continue
                    cell_item = {"type": ExtractType.TEXT.value, "text": cell}
                    row_list.append(cell_item)

                table_content.rows.append(row_list)

            return ExtractedTableSegment(
                content=table_content,
                id=uuid.uuid4().hex,
                type=ExtractType.TABLE.value,
            )
        except Exception as e:
            raise TableStylingExtractionException(str(e))

    def _increase_tag_value(self, tag: str):
        """
        Returns the next tag value down
        """
        if tag not in self.heading_tags:
            raise ValueError("Tag does not exist")

        index = self.heading_tags.index(tag)
        return self.heading_tags[index - 1 if index > 0 else 0]

    def _assign_valid_h1(self, headings: dict[str, str]) -> dict[str, str]:
        """
        Checks Assigns a valid H1 if there is only one H2 and it is the first
        heading item. If this condition is not met then nothing is changed
        """
        if self.has_title or len(headings) == 0:
            return headings

        h2_count = len(
            list(filter(lambda item: item == HeadingTag.H2.value, headings.values())),
        )
        if h2_count > 1 or next(iter(headings.values())) != HeadingTag.H2.value:
            return headings

        return {k: self._increase_tag_value(v) for k, v in headings.items()}

    def _get_all_headings(
        self,
        pages: List[ExtractedPage],
    ) -> List[ExtractedHeadingSegment]:
        all_heading_segments: list[ExtractedHeadingSegment] = [
            block
            for page in pages
            for block in page.content
            if isinstance(block, ExtractedHeadingSegment)
        ]

        return all_heading_segments

    def _get_all_subheadings(
        self,
        pages: List[ExtractedPage],
    ) -> List[ExtractedHeadingSegment]:
        all_heading_segments = self._get_all_headings(pages)

        return list(
            filter(lambda item: item.tag != HeadingTag.H1.value, all_heading_segments),
        )

    def _is_preliminary_heading(self, title: str):
        """
        Returns whether the provided text is a preliminary title
        """
        # Remove all whitespace to handle stupid titles like "A B S T R A C T"
        return title.lower().replace(" ", "") in self._preliminary_titles

    def _get_unique_sizes(self, headers: List):
        # Remove any unique sizes for preliminary titles
        header_sizes = [header.font_size for header in headers if header.tag != HeadingTag.H1.value]
        return np.unique(header_sizes)[::-1].tolist()

    def _indent_appendix_headings(
        self,
        all_headings: List[ExtractedHeadingSegment],
        heading_tags: Dict[str, str],
    ):
        """
        Handles appendix subheadings by searching for a title starting with 'appendix'
        and then indenting subheadings underneath if they start with a single character and
        a number
        """
        # Regex to detect A.X or a.x
        re_appendix_sub = re.compile(r"^([a-zA-Z]\.[0-9])")
        # Store whether we have found an appendix and it's tag index
        hit_appendix = False
        appendix_tag_index = 0
        for heading in all_headings:
            # Check if heading is appendix and if so store necessart information and skip
            if heading.title.lower().startswith("appendix"):
                hit_appendix = True
                appendix_tag = heading_tags[heading.id]
                appendix_tag_index = self.heading_tags.index(appendix_tag)
                continue

            # If we've hit an appendix heading and if the current heading satisfies the regex then
            # increase the tag index by 1 from the appendix heading
            if hit_appendix and re_appendix_sub.match(heading.title):
                new_tag_idx = (
                    appendix_tag_index + 1
                    if appendix_tag_index < len(self.heading_tags) - 1
                    else appendix_tag_index
                )
                heading_tags[heading.id] = self.heading_tags[new_tag_idx]

    def _indent_numbered_headings(
        self,
        all_headings: List[ExtractedHeadingSegment],
        heading_tags: Dict[str, str],
    ):
        """
        Loops through the headings and adds indentation based on numerical values
        if they exist at the start of the header.

        e.g. 2.1 -> 2.1.1
        """
        # Store last known numbered item so we can handle scenarios where there
        # are subitems that are not numbered inbetween numbered headings
        prev_heading: Union[ExtractedHeadingSegment, None] = None
        prev_numerical: Union[re.Match[str], None] = None
        for heading in all_headings:
            curr_numerical = self.numerical_start.match(heading.title)

            # No point if not numericaly in'it
            if not curr_numerical:
                continue

            # If we don't have a previous numerical then store it and move on in'it
            # have to use or statement for LoGiC
            if not prev_heading or not prev_numerical:
                prev_heading = heading
                prev_numerical = curr_numerical
                continue

            prev_tag = heading_tags[prev_heading.id]
            curr_tag = heading_tags[heading.id]

            # Remove whitespace split results by running filter
            prev_group = len(
                list(
                    filter(
                        lambda item: item.strip() != "",
                        prev_numerical.group().split("."),
                    ),
                ),
            )
            curr_group = len(
                list(
                    filter(
                        lambda item: item.strip() != "",
                        curr_numerical.group().split("."),
                    ),
                ),
            )

            # If both match the size and tags match then nowt to do
            # store current heading data in prev
            if prev_group == curr_group and prev_tag == curr_tag:
                prev_heading = heading
                prev_numerical = curr_numerical
                continue

            prev_tag_index = self.heading_tags.index(prev_tag)

            # If this is an increment then tag needs to be increased by 1
            if prev_group < curr_group:
                new_tag_idx = (
                    prev_tag_index + 1
                    if prev_tag_index < len(self.heading_tags) - 1
                    else prev_tag_index
                )
                heading_tags[heading.id] = self.heading_tags[new_tag_idx]
            # If they match then make the current item take on the tag of the previous header
            elif prev_group == curr_group:
                heading_tags[heading.id] = self.heading_tags[prev_tag_index]

            # All done, store curr heading data as prev and then move on
            # for more indenting fun times
            prev_heading = heading
            prev_numerical = curr_numerical

    def _indent_all_cap_headings(
        self,
        all_headings: List[ExtractedHeadingSegment],
        heading_tags: Dict[str, str],
    ):
        hit_all_caps = False
        all_cap_tag_index = 0
        for heading in all_headings:
            # If we've hit a tag then store the index and flag we've passed one
            if self.all_capital_chars.match(heading.title):
                hit_all_caps = True
                appendix_tag = heading_tags[heading.id]
                all_cap_tag_index = self.heading_tags.index(appendix_tag)
                continue

            # If we've flagged that we've passed an all_caps heading then do work
            if hit_all_caps:
                # Grab the current tag index of this heading
                appendix_tag = heading_tags[heading.id]
                heading_tag_index = self.heading_tags.index(appendix_tag)

                # If the tag index is below the current all cap tag then safe to assume
                # this heading is at a higher level and should cancel out the all cap flag
                if heading_tag_index < all_cap_tag_index:
                    hit_all_caps = False
                    continue

                # Otherwise if the tag is of the same level as the all caps then
                # indent by one
                if heading_tag_index == all_cap_tag_index:
                    new_tag_idx = (
                        all_cap_tag_index + 1
                        if all_cap_tag_index < len(self.heading_tags) - 1
                        else all_cap_tag_index
                    )
                    heading_tags[heading.id] = self.heading_tags[new_tag_idx]

    def _filter_formatted_headings(
        self,
        pages: List[ExtractedPage],
    ):
        unformatted_headings: List[str] = []
        filtered_heading_segments: List[ExtractedHeadingSegment] = []

        headings_to_delete: List[int] = []

        if self.has_title:
            filtered_heading_segments = self._get_all_headings(pages)
            hit_h1 = False

            for idx, heading in enumerate(filtered_heading_segments):
                if heading.tag == HeadingTag.H1.value:
                    hit_h1 = True
                    headings_to_delete.append(idx)
                    continue

                if not hit_h1:
                    unformatted_headings.append(heading.id)
                    headings_to_delete.append(idx)
                    continue

                if self._is_preliminary_heading(heading.title):
                    unformatted_headings.append(heading.id)  # Assign H2
                    headings_to_delete.append(idx)
                    continue
            # Loop through the indexes backwards as this will mean next indexes
            # which are lower won't change value after deletion
            for idx in reversed(headings_to_delete):
                del filtered_heading_segments[idx]

            return filtered_heading_segments, unformatted_headings

        # If there is no title then we just grab all subheadings and
        # filter by preliminary headings

        # Get all subheadings i.e. not H1
        filtered_heading_segments = self._get_all_subheadings(pages)
        # Store indexes to delete after loop is finsished

        for idx, heading in enumerate(filtered_heading_segments):
            if self._is_preliminary_heading(heading.title):
                unformatted_headings.append(heading.id)  # Assign H2
                headings_to_delete.append(idx)

        # Loop through the indexes backwards as this will mean next indexes
        # which are lower won't change value after deletion
        for idx in reversed(headings_to_delete):
            del filtered_heading_segments[idx]

        return filtered_heading_segments, unformatted_headings

    def _get_header_sizes(
        self,
        headers: List[ExtractedHeader],
        pages: List[ExtractedPage],
    ) -> Dict[str, str]:
        """
        Applied appropriate header size to each ExtractedHeader in the list
        """
        try:
            ret_item: Dict[str, str] = {}
            header_tags = self.heading_tags[1:]

            # Get all unique header sizes in order of highest to lowest
            unique_header_sizes = self._get_unique_sizes(headers)
            if len(unique_header_sizes) == 0:
                return ret_item

            filtered_heading_segments, unformatted_headings = self._filter_formatted_headings(pages)

            # Starting tag index
            tag_index = 0

            # Loop through all unique sizes and grab headings that match that size
            for size in unique_header_sizes:
                # Get heading tag for current header_size
                heading_tag = header_tags[tag_index]

                filtered = list(
                    filter(
                        lambda item: item.font_size == size and item.tag != HeadingTag.H1.value,
                        filtered_heading_segments,
                    ),
                )

                if len(filtered) == 0:
                    continue

                # Initally give all headings the value that is currently assigned to headings
                # at this size
                for item in filtered:
                    ret_item[item.id] = heading_tag

                # Get all formats for the headings at this font size
                heading_formats = np.array([item.format for item in filtered])

                # If there is more than one heading format then loop through each format and
                # assign valid heading increments
                if len(heading_formats) > 1:
                    first_format = heading_formats[0]

                    # Get all unique formats and in order of which they appear in the document
                    _, idx = np.unique(heading_formats, return_index=True)
                    unique_formats = heading_formats[np.sort(idx)]

                    # Generate a dictionary that will hold the formats and alongside
                    # their index value
                    format_dict = {}
                    for format in unique_formats:
                        format_dict[format] = tag_index
                        if tag_index < len(header_tags) - 1:
                            tag_index += 1

                    # Now loop through each format and grab the headings that match the format
                    # and apploy the apprpriate tag based on index
                    for format in unique_formats:
                        if format == first_format:
                            continue

                        for item in filtered:
                            if item.format != format:
                                continue
                            format_index = format_dict[item.format]
                            selected_tag = header_tags[format_index if format_index <= 4 else 4]
                            ret_item[item.id] = selected_tag

                    # Since we've altered the tag index already based on formats we can
                    # now skip the increment after this block
                    continue

                if tag_index < len(header_tags) - 1:
                    tag_index += 1

            # Extra checks
            self._indent_numbered_headings(filtered_heading_segments, ret_item)
            self._indent_appendix_headings(filtered_heading_segments, ret_item)
            self._indent_all_cap_headings(filtered_heading_segments, ret_item)

            if not self.has_title:
                ret_item = self._assign_valid_h1(ret_item)

            # Add all preliminary titles at the end as H2
            for id in unformatted_headings:
                ret_item[id] = header_tags[0]

            return ret_item

        except ExtractionException as ee:
            raise ee
        except Exception as e:
            print(e)
            raise HeaderStylingExtractionException(str(e))

    def unnormalise_bbox(
        self,
        Width: float,
        Height: float,
        Left: float,
        Top: float,
        page: Page,
    ) -> Rect:
        """
        Convert Textract bbox to PyMuPDF Rect class
        """
        ret_val = {"x0": 0.0, "y0": 0.0, "x1": 0.0, "y1": 0.0}
        page_rect: Rect = page.rect

        page_width = page_rect.width
        page_height = page_rect.height

        start_left_pos = page_width * Left
        calc_width = page_width * Width
        start_top_pos = page_height * Top
        calc_height = page_height * Height

        ret_val["x0"] = start_left_pos
        ret_val["y0"] = start_top_pos
        ret_val["x1"] = start_left_pos + calc_width
        ret_val["y1"] = start_top_pos + calc_height

        return Rect(ret_val["x0"], ret_val["y0"], ret_val["x1"], ret_val["y1"])

    def _get_font_type(
        self,
        span,
        flags: Union[int, None],
        debug=False,
    ) -> Literal["link", "bold", "italic", "sub", "super", "none"]:
        """
        Returns one of the possible 6 values for font types based on
        font-family name.
        """
        if debug:
            print("flags", flags)

        if "link" in span:
            if debug:
                print("Is link")
            return "link"

        if not flags or not span["text"].strip():
            if debug:
                print("no flags or empty text")
            return "none"

        if flags & 2**0:
            if debug:
                print("Super:", flags)
            return "super"
        if flags & 2**4:
            if debug:
                print("Bold:", flags)
            return "bold"
        if flags & 2**1:
            if debug:
                print("Italic:", flags)
            return "italic"
        # Last resort but debatable if we need to do this at all
        return self._get_style_from_font_family(span["font"], debug)

    def _get_style_from_font_family(
        self,
        span_font: str,
        debug=False,
    ) -> Literal["link", "bold", "italic", "none"]:
        """
        Searches the font family for patterns that could give font styling
        information.

        Currently supports font families that match 'AdvTTebdce2e6.I'
        """

        lower_span_font = span_font.lower()
        if debug:
            print(lower_span_font)
        if "bold" in lower_span_font:
            if debug:
                print("Returning bold 1")
            return "bold"
        if "italic" in lower_span_font:
            if debug:
                print("Returning italic 1")
            return "italic"

        if debug:
            print(span_font)
        if span_font.endswith("B"):
            if debug:
                print("Returning bold 2")
            return "bold"
        if span_font.endswith("I"):
            if debug:
                print("Returning italic 2")
            return "italic"

        ret_search = self.font_style_regex.search(lower_span_font)
        if ret_search is None:
            if debug:
                print("Returning none")
            return "none"
        found_group = ret_search.group()

        # Gets first character after .
        if found_group[1] == "b":
            return "bold"
        if found_group[1] == "i":
            return "italic"

        # TODO: If this far then fire off sentry report of font family

        # Last but certainly not least nothing found so return none for now
        return "none"

    def _get_span_format(
        self,
        text: str,
        span: dict,
        flags: Union[int, None],
        debug=False,
    ) -> ExtractFormat:
        """
        Return the format for the span (link, bold, italic, or none)
        """
        font_type = self._get_font_type(span, flags, debug)

        return ExtractFormat(
            font_type,
            round(span["size"], 2),
            text,
            span["bbox"],
            span["font"],
            span["link"] if font_type == "link" else None,
        )

    def _ocr_span(self, span: dict, page: Page) -> str:
        span_text = span["text"]

        text_ltrimmed = span_text.lstrip()
        start_spaces = " " * (len(span_text) - len(text_ltrimmed))  # leading spaces

        text_rtrimmed = span_text.rstrip()
        # trailing spaces
        end_spaces = " " * (len(span_text) - len(text_rtrimmed))

        return start_spaces + self.get_tessocr(page, self._bbox_to_rect(span["bbox"])) + end_spaces

    def _extract_span(self, span: dict, page: Page, debug=False):
        """
        Extract the spans returning the full text, line sizes, and formats
        """
        span_font_size = self.base_font if span["text"].strip() == "" else round(span["size"], 2)

        text = span["text"] if INVALID_UNICODE not in span["text"] else self._ocr_span(span, page)

        flags = span.get("flags")

        split_span = text.split(" ")
        span_font_sizes = [span_font_size for word in split_span if word.strip() != ""]

        span_format = self._get_span_format(text, span, flags, debug)

        average_line_size = self.base_font

        if len(span_font_sizes) > 0:
            values, counts = np.unique(span_font_sizes, return_counts=True)
            idx = np.argmax(counts)
            average_line_size = values[idx]

        return {
            "text": text,
            "line_sizes": [average_line_size],
            "formats": [span_format],
        }

    def _reduce_spans(self, span_one: dict, span_two: dict):
        """
        Merges 2 extracted spans together
        """
        # Doing this manually instead of nice spread operators because
        # for some reason python would sometimes skip the first item
        return {
            "text": span_one["text"] + span_two["text"],
            "line_sizes": span_one["line_sizes"] + span_two["line_sizes"],
            "formats": span_one["formats"] + span_two["formats"],
        }

    def _append_space_format_item(self, formats: list[ExtractFormat]):
        if len(formats) == 0:
            return
        last_item = formats[-1]
        last_bbox = last_item.bbox

        space_bbox = [
            last_bbox[2],
            last_bbox[1],
            last_bbox[2] + SPACE_WIDTH,
            last_bbox[3],
        ]
        formats.append(
            ExtractFormat(
                "none",
                last_item.size,
                " ",
                space_bbox,
                last_item.font,
            ),
        )

    def _extract_line(self, idx: int, line: dict, page: Page, debug=False):
        """
        Extract the line returning the collected text, size, and formats
        """

        extracted_spans = [self._extract_span(span, page, debug) for span in line["spans"]]

        if debug:
            for span in extracted_spans:
                print(span)

        starting_item = {"text": "", "line_sizes": [], "formats": []}
        merged = reduce(self._reduce_spans, extracted_spans, starting_item)

        average_line_size = self.base_font
        if len(merged["line_sizes"]) > 0:
            line_sizes: List[float] = merged["line_sizes"]
            average_line_size = float(
                self._get_most_common_or_max(line_sizes),
            )  # TODO: Do we just get max?

        if debug:
            print(f"als line {average_line_size}")

        return {
            "text": merged["text"],
            "sizes": [average_line_size],
            "formats": merged["formats"],
            "bbox": line["bbox"],
        }

    def _reduce_lines(self, line_one: dict, line_two: dict):
        """
        Reduce lines into one single line block
        """
        return {
            "text": line_one["text"] + " " + line_two["text"],
            "sizes": (line_one["sizes"] + line_two["sizes"]),
            "formats": line_one["formats"] + line_two["formats"],
        }

    def _textract_line_format(self, line: TextractLine) -> ExtractFormat:
        return ExtractFormat(
            "none",
            self.base_font,
            line.text,
            self._rect_to_bbox(line.bbox),
            self.default_font,
        )

    def _format_textract_line(
        self,
        tt_line: TextractLine,
        blocks,
    ):
        line_formats = []
        line_sizes = []
        # line_font = self.default_font
        stripped_line = self._regex_strip_none_ascii_chars_lower(tt_line.text)

        for block_lines in blocks:
            for line in block_lines:
                # Set Debug
                for format in line["formats"]:
                    if format.type == "super":
                        break

                # Strip out special chars from pymupdf line
                stripped_pmu = self._regex_strip_none_ascii_chars_lower(line["text"])

                merged_formats = self._regex_strip_none_ascii_chars_lower(
                    "".join(
                        [format.value for format in line["formats"] if format.type != "super"],
                    ),
                )

                if stripped_pmu != stripped_line and merged_formats != stripped_line:
                    continue

                # If this far then line text matches that in textract
                # Copy so as to ensure we are not sharing
                line_formats = line["formats"].copy()
                line_sizes = line["sizes"].copy()

                # Line found so break out of loop since
                break

            # If formats and line sizes are populated then we can break out of loop
            if len(line_formats) > 0 and len(line_sizes) > 0:
                break

        return line_formats, line_sizes

    def _format_lines_with_blocks(self, tt_lines: list[TextractLine], blocks):
        """
        Loops through all the textract lines and attempts to find the a pymupdf line
        that matches to give it the formatting.

        If a 1 to 1 match isn't found then line is merged with previous and/or next
        line to see if textract has accidentally split lines when it shouldn't.
        """
        ret_list: list[dict] = []
        skip_idx = False
        for idx in range(len(tt_lines)):
            # This feels like the solution 16 year old me would come up with
            if skip_idx:
                skip_idx = False
                continue

            # Strip out special chars from textract line
            textract_line = tt_lines[idx]

            prev_line = tt_lines[idx - 1] if idx > 0 else None
            next_line = tt_lines[idx + 1] if not idx + 1 >= len(tt_lines) else None

            curr_line = self._regex_strip_none_ascii_chars_lower(textract_line.text)
            if curr_line == "":
                continue

            line_formats, line_sizes = self._format_textract_line(textract_line, blocks)

            if len(line_formats) == 0 and len(line_sizes) == 0:
                # Not pleasant but effectively merged previous line and
                # next line together with current and assess whether
                # there are matches in pymupdf lines
                prev_block = (
                    self._textract_merge_lines(prev_line, textract_line)
                    if prev_line is not None
                    else None
                )
                next_block = (
                    self._textract_merge_lines(textract_line, next_line)
                    if next_line is not None
                    else None
                )
                # Creating these here to stop the 'may be unbound' errors
                prev_line_formats = []
                prev_line_sizes = []
                next_line_formats = []
                next_line_sizes = []
                if prev_block:
                    prev_line_formats, prev_line_sizes = self._format_textract_line(
                        prev_block,
                        blocks,
                    )
                if next_block:
                    next_line_formats, next_line_sizes = self._format_textract_line(
                        next_block,
                        blocks,
                    )

                is_first = len(prev_line_formats) > 0
                is_second = len(next_line_formats) > 0

                if prev_block and is_first:
                    # I'm still not convinced this is the best way to do this, but it works for the current test document
                    prev_line_formats[-1].value += " "
                    ret_list[-1 if len(ret_list) > 0 else 0] = {
                        "text": prev_block.text,
                        "sizes": prev_line_sizes,
                        "formats": prev_line_formats,
                    }
                    continue
                if next_block and is_second:
                    # Same here, not convinced but works for now
                    next_line_formats[-1].value += " "
                    ret_list.append(
                        {
                            "text": next_block.text,
                            "sizes": next_line_sizes,
                            "formats": next_line_formats,
                        },
                    )
                    continue

                line_formats.append(self._textract_line_format(textract_line))
                line_sizes.append(self.base_font)

            last_item = line_formats[-1]
            last_bbox = last_item.bbox
            space_bbox = [last_bbox[2], last_bbox[1], last_bbox[2] + 0.2, last_bbox[3]]
            line_formats.append(
                ExtractFormat("none", last_item.size, " ", space_bbox, last_item.font),
            )

            ret_list.append(
                {
                    "text": textract_line.text,
                    "sizes": line_sizes,
                    "formats": line_formats,
                },
            )

        return ret_list

    def _pymupdf_is_same_line(self, block_one: dict, block_two: dict):
        return (
            block_one["bbox"][1] == block_two["bbox"][1]
            and block_one["bbox"][3] == block_two["bbox"][3]
        )

    def _get_all_sizes(self, extracted_lines):
        return [
            item.size
            for line in extracted_lines
            for item in line["formats"]
            if item.value.strip() != ""
        ]

    def _padd_extracted_line(self, lines: list[dict]):
        """
        Adds a space to the end of the line if detected to need one.
        Currently spaces will only be added if the next line doesn'the
        start with `)`, `.`, `,`, `-`, `"`, `'`
        """
        copied_lines = lines.copy()

        for idx in range(len(copied_lines)):
            if idx + 1 == len(copied_lines):
                # Last line in block, for safety add a space
                self._append_space_format_item(copied_lines[idx]["formats"])
                break

            # Don't add space if next line starts with the characters ') . , -'
            if re.match(
                r"^[\)\.\"\',-]",
                copied_lines[idx + 1]["text"].strip(),
            ):
                continue

            self._append_space_format_item(copied_lines[idx]["formats"])

        return copied_lines

    def _extract_block(self, block: dict, page: Page, area: Rect, debug=False):
        """
        Extract the text block collating all it's lines
        """
        extracted_lines = [
            self._extract_line(idx, line, page, debug) for idx, line in enumerate(block["lines"])
        ]

        padded_lines = self._padd_extracted_line(extracted_lines)

        # TODO: create a function and have it return a tuple of line_heights
        # and a modified list by modifying and measuring to save
        # re-running the list of lines repeatedly
        line_heights = [self.__get_line_height(line["bbox"]) for line in padded_lines]
        most_common_height = self._get_most_common(line_heights)

        all_sizes = self._get_all_sizes(extracted_lines)

        if len(all_sizes) > 1:
            comparitor = self._get_most_common_or_max(all_sizes)
            percentage_marker = 0.2
            # I'll be honest there is no clever maths here, I just threw in
            # numbers and hoped for the best
            minimum_diff = comparitor * percentage_marker

            for line in extracted_lines:
                for item in line["formats"]:
                    if item.type == "super":
                        continue
                    # TODO check if we also need to add a regex check to item
                    # value
                    if (
                        abs(comparitor - item.size) >= minimum_diff
                        and item.value.strip() != ""
                        and re.search("[a-zA-Z]", item.value) is None
                    ):
                        if debug:
                            print("size:", item.size)
                            print("comparitor:", comparitor)
                            print("Mafs:", abs(comparitor - item.size))
                            print(minimum_diff)
                            print(item.value)
                        # OUTPUT POTENTIAL SUPERSCRIPT
                        item.type = "super"

        filtered_lines = list(
            filter(
                lambda line: self._is_within_bbox(
                    area,
                    line,
                    most_common_height,
                ),
                padded_lines,
            ),
        )

        if len(filtered_lines) == 1 and filtered_lines[0]["text"].strip() == "":
            return [
                {
                    "text": "",
                    "sizes": [],
                    "formats": [],
                    "bbox": (filtered_lines[0]["bbox"] if "bbox" in filtered_lines[0] else []),
                },
            ]

        if debug:
            print("FILTERED")
            print(json.dumps(filtered_lines, indent=4, default=str))

        return filtered_lines

    def _textract_is_same_line(self, block_one, block_two):
        """
        Asesses the two blocks passed in by whether they have similar top values and
        by whether the block top elements intersect in terms of top positions
        """
        item_bbox = block_one["Geometry"]["BoundingBox"]
        next_bbox = block_two["Geometry"]["BoundingBox"]

        # Ensure we are working with valid Text blocks
        if "Text" not in block_one or "Text" not in block_two:
            return False
        if block_one["Text"] is None or block_two["Text"] is None:
            return False

        # Round Top positions to nearest 2 Decimal Point to ensure they
        # can at least be capable of being on the same line
        curr_top = round(block_one["Geometry"]["BoundingBox"]["Top"], 2)
        next_top = round(block_two["Geometry"]["BoundingBox"]["Top"], 2)

        if curr_top != next_top:
            return False

        # Take highest top element and calculate the bottom of it then compare the top
        # value for the second block, does that value come inbetween the full height of
        # the first element?
        smallest_top = block_one if item_bbox["Top"] < next_bbox["Top"] else block_two
        largest_top = block_one if smallest_top == block_two else block_two

        bottom = (
            smallest_top["Geometry"]["BoundingBox"]["Top"]
            + smallest_top["Geometry"]["BoundingBox"]["Height"]
        )

        return largest_top["Geometry"]["BoundingBox"]["Top"] < bottom

    def _merge_rects(self, rect_one: Rect, rect_two: Rect) -> Rect:
        furthest_left = min([rect_one.x0, rect_two.x0])
        highest_top = min([rect_one.y0, rect_two.y0])
        furthest_right = max([rect_one.x1, rect_two.x1])
        lowest_bottom = max([rect_one.y1, rect_two.y1])
        return Rect(furthest_left, highest_top, furthest_right, lowest_bottom)

    def _textract_merge_lines(self, line_one: TextractLine, line_two: TextractLine):
        # if lines end with hyphen, we want to remove the trailing space after it before merging
        merged_text = (
            line_one.text.rstrip() + line_two.text
            if line_one.text.rstrip().endswith("-")
            else line_one.text + " " + line_two.text
        )
        # if merged_words not in spell: add pyspellchecker library and pull out the merged words from text
        #     self._set_incorrect_words(merged_words) add to incorrect words dict
        return TextractLine(
            merged_text,
            self._merge_rects(line_one.bbox, line_two.bbox),
        )

    def _textract_merge_text_blocks(self, block_one: dict, block_two: dict):
        """
        Merges both textract text blocks into one combining the text and the
        """
        item_bbox = block_one["Geometry"]["BoundingBox"]
        next_bbox = block_two["Geometry"]["BoundingBox"]

        # Merge boundry boxes and text
        merged_text = block_one["Text"] + " " + block_two["Text"]
        merged_bbox = {
            "Height": max([item_bbox["Height"], next_bbox["Height"]]),
            "Left": min([item_bbox["Left"], next_bbox["Left"]]),
            "Top": max([item_bbox["Top"], next_bbox["Top"]]),
            "Width": item_bbox["Width"] + next_bbox["Width"],
        }

        # create a copy of the original item to then override the item in the list
        merged_item = block_one.copy()
        merged_item["Text"] = merged_text
        merged_item["Geometry"]["BoundingBox"] = merged_bbox

        return merged_item

    def _get_block_children(self, block: dict, blocks: list[dict], debug=False):
        """
        Grabs the child blocks listed in the Child Relationship
        """
        ret_val: list = []

        if (
            "Relationships" not in block
            or block["Relationships"] is None
            or len(block["Relationships"]) == 0
        ):
            return ret_val

        for relationship in block["Relationships"]:
            if relationship["Type"] != "CHILD":
                continue
            ret_val = list(
                filter(lambda sub_block: sub_block["Id"] in relationship["Ids"], blocks),
            )

        # Loop through lines returned and merge any that have been incorrectly split
        for idx in range(len(ret_val)):
            if idx + 1 >= len(ret_val):
                break

            current_block = ret_val[idx]
            next_block = ret_val[idx + 1]

            if self._textract_is_same_line(current_block, next_block):
                merged_block = self._textract_merge_text_blocks(
                    current_block,
                    next_block,
                )

                # overwrite item in the list and remove the next
                ret_val[idx] = merged_block
                del ret_val[idx + 1]

        return ret_val

    def _reduce_text_blocks(self, rect_one, rect_two):
        """
        Squash list of text blocks into a single usable block
        """
        # checks if the text ends with a hyphen and matches the format values of the two blocks to match the text
        if rect_one["text"].rstrip().endswith("-"):
            while rect_one["formats"][-1].value.strip() == "" and len(rect_one["formats"]) > 0:
                del rect_one["formats"][-1]

            if len(rect_two["formats"]) > 0:
                rect_one["formats"][-1].value = (
                    rect_one["formats"][-1].value.rstrip() + rect_two["formats"][0].value
                )

                del rect_two["formats"][0]

            return {
                "text": rect_one["text"] + rect_two["text"],
                "sizes": rect_one["sizes"] + rect_two["sizes"],
                "formats": rect_one["formats"] + rect_two["formats"],
            }
        return {
            "text": rect_one["text"] + " " + rect_two["text"],
            "sizes": rect_one["sizes"] + rect_two["sizes"],
            "formats": rect_one["formats"] + rect_two["formats"],
        }

    def _rect_to_bbox(self, rect: Rect) -> list[float]:
        """
        Returns the un-marked bbox based on the rect
        """
        return [rect.x0, rect.y0, rect.x1, rect.y1]

    def _bbox_to_rect(self, bbox: list[float]):
        """
        Returns the Rect object for the passed in bbox
        """
        return Rect(*bbox)

    def _lines_in_same_paragraph(
        self,
        top_line: Dict,
        bottom_line: Dict,
        debug=False,
    ) -> bool:
        top_line_sizes = list(set([size for size in top_line["sizes"]]))
        bottom_line_sizes = list(set([size for size in bottom_line["sizes"]]))

        top_line_formats = list(
            set(
                [format.type for format in top_line["formats"] if format.value.strip() != ""],
            ),
        )
        bottom_line_formats = list(
            set(
                [format.type for format in bottom_line["formats"] if format.value.strip() != ""],
            ),
        )

        top_line_width = top_line["bbox"][0] + top_line["bbox"][2]
        bottom_line_width = bottom_line["bbox"][0] + bottom_line["bbox"][2]

        top_line_shorter = (
            top_line_width < bottom_line_width
            and self._percentage_difference(top_line_width, bottom_line_width) >= 55
        )

        different_formats = (
            len(top_line_formats) == 1 and top_line_formats[0] not in bottom_line_formats
        )

        different_sizes = len(top_line_sizes) == 1 and top_line_sizes[0] not in bottom_line_sizes

        # Return True if top line is shorter and a different format OR top
        # line is shorter and a different font_size
        if (top_line_shorter and different_formats) or (top_line_shorter and different_sizes):
            return False

        return True

    def _get_paragraphs(self, rect: Rect, page: Page, debug=False) -> List[List[Dict]]:
        """
        Grabs the area in PyMuPDF and formats the found blocks into
        usable data for ExtractedTextBlock
        """
        text_page = json.loads(page.get_textpage(rect).extractJSON(sort=True))
        if len(text_page["blocks"]) == 0:
            return []

        mapped_blocks = [self.map_link_to_span(page, block) for block in text_page["blocks"]]

        blocks = [
            self._extract_block(block, page, rect, debug)
            for block in mapped_blocks
            if len(block["lines"]) > 0
        ]

        paragraph_blocks: List[List[Dict]] = []

        formatted_lines = [line for block in blocks for line in block]

        paragraph_block = []
        for idx, line in enumerate(formatted_lines):
            # If last item in formatted_lines
            paragraph_block.append(line)
            if idx == len(formatted_lines) - 1:
                break

            next_line = formatted_lines[idx + 1]

            same_paragraph = self._lines_in_same_paragraph(line, next_line, debug)

            if not same_paragraph:
                paragraph_blocks.append(paragraph_block.copy())
                paragraph_block = []

        paragraph_blocks.append(paragraph_block)

        return paragraph_blocks

    def _get_all_textblocks(self, rect: Rect, page: Page, debug=False) -> List[Dict]:
        paragraphs = self._get_paragraphs(rect, page, debug)

        if len(paragraphs) == 0:
            return [{"text": "", "size": self.base_font, "formats": []}]

        start_item = {"text": "", "sizes": [self.base_font], "formats": []}
        reduced_paragraphs = [
            reduce(self._reduce_text_blocks, paragraph, start_item) for paragraph in paragraphs
        ]

        reduced_blocks = []
        for paragraph in reduced_paragraphs:
            reduced_blocks.append(
                {
                    "text": paragraph["text"],
                    "size": self._get_most_common(paragraph["sizes"]),
                    "formats": paragraph["formats"],
                },
            )

        return reduced_blocks

    def _get_textblock(self, rect: Rect, page: Page, debug=False) -> Dict:
        paragraphs = self._get_paragraphs(rect, page, debug)
        if len(paragraphs) == 0:
            return {"text": "", "size": self.base_font, "formats": []}

        merged_paragraphs: List[Dict] = []
        for paragraph in paragraphs:
            merged_paragraphs.extend(paragraph)

        start_item = {"text": "", "sizes": [], "formats": []}
        reduced = reduce(self._reduce_text_blocks, merged_paragraphs, start_item)
        if len(reduced["sizes"]) == 0:
            reduced["sizes"] = [self.base_font]

        return {
            "text": reduced["text"],
            "size": self._get_most_common(reduced["sizes"]),
            "formats": reduced["formats"],
        }

    def _is_within_bbox(
        self,
        rect: Rect,
        line: dict,
        common_height: float,
    ) -> bool:
        """
        Attempts to ascertain if a line is located within a given Rect
        Takes in all blocks common heights
        """
        textract_bbox = self._rect_to_bbox(rect)
        pymupdf_bbox = line["bbox"]

        acceptable_x_difference = 3.0
        acceptable_y_difference = common_height / 2

        # Is the left of the box too far to the left of the area block?
        x0_diff = abs(pymupdf_bbox[0] - textract_bbox[0])
        if pymupdf_bbox[0] < textract_bbox[0] and x0_diff > acceptable_x_difference:
            return False

        # is the right of the box too far to the right of the area block?
        x1_diff = abs(pymupdf_bbox[2] - textract_bbox[2])
        if pymupdf_bbox[2] > textract_bbox[2] and x1_diff > acceptable_x_difference:
            return False

        # Is the top of the box too high above the area block?
        y0_diff = abs(pymupdf_bbox[1] - textract_bbox[1])
        # if pymupdf_bbox[1] < textract_bbox[1]:
        if pymupdf_bbox[1] < textract_bbox[1] and y0_diff > acceptable_y_difference:
            return False

        # Is the bottom of the box too low down the area block?
        y1_diff = abs(pymupdf_bbox[3] - textract_bbox[3])
        # if pymupdf_bbox[3] > textract_bbox[3]:
        if pymupdf_bbox[3] > textract_bbox[3] and y1_diff > acceptable_y_difference:
            return False

        return True

    def __get_line_height(self, bbox: tuple[float, float, float, float]):
        return round(abs(bbox[1] - bbox[3]), 2)

    def _block_to_image(self, rect: Rect, page: Page):
        """
        Grabs the specified block and return an image of that area
        """
        pixmap = page.get_pixmap(clip=rect, dpi=180)

        return ExtractedImagesSegment(
            pixmap.width,
            pixmap.height,
            "png",
            pixmap.width,
            pixmap.height,
            pixmap.tobytes(),
            uuid.uuid4().hex,
        )

    def _extract_table(self, rect: Rect, page: Page):
        """
        Extract the table. Currently returns the table as an image
        """
        return self._block_to_image(rect, page)

    def _is_ordered_list_item(self, list_item: ExtractedListItem) -> bool:
        return (
            self.single_char_start.match(list_item.value) is not None
            or self.numerical_start.match(list_item.value) is not None
        )

    def _get_list_type(
        self,
        list_items: list[ExtractedListItem],
    ) -> Literal["ordered", "unordered"]:
        """
        Returns the list type based on the list_item content (ordered/unordered)
        """
        return (
            "ordered"
            if all(self._is_ordered_list_item(item) for item in list_items)
            else "unordered"
        )

    def _tidy_extracted_list(self, list_items: list[ExtractedListItem]):
        """
        Loops through the extracted list and merges ExtractedListItems that
        could be considered to be the same element.
        """
        for idx, item in enumerate(list_items):
            # TODO: Store this result so can be used in next iteration
            if self._is_ordered_list_item(item):
                continue

            if idx == 0:
                continue

            prev_item = list_items[idx - 1]
            next_item = list_items[idx + 1] if not idx + 1 >= len(list_items) else None

            if next_item is not None:
                if self._is_ordered_list_item(prev_item) and self._is_ordered_list_item(
                    next_item,
                ):
                    # List item is on next column and spilled over
                    if item.bbox[0] > prev_item.bbox[2]:
                        prev_item.value += " " + item.value
                        del list_items[idx]
                        continue

    def _extract_list(
        self,
        block_children: list,
        rect: Rect,
        page: Page,
        all_blocks: list,
    ):
        """
        Extract the detected list item from the block area
        """
        formatted_children = []
        for child in block_children:
            child_rect = self.unnormalise_bbox(
                **child["Geometry"]["BoundingBox"],
                page=page,
            )

            formatted_item = self._get_textblock(child_rect, page)
            formatted_children.append(
                ExtractedListItem(
                    formatted_item["text"].strip(),
                    [],
                    self._rect_to_bbox(child_rect),
                ),
            )

        # Ensure there are no separated list items that shouldn't be split
        self._tidy_extracted_list(formatted_children)
        # Left this here so we can get the list type before then stripping all the stuff off the start of the text
        list_type = self._get_list_type(formatted_children)

        return ExtractedListSegment(list_type, formatted_children, uuid.uuid4().hex)

    def _get_heading_font_format(self, formats: list[ExtractFormat]):
        filtered_formats = list(
            filter(lambda format: format.value.strip() != "", formats),
        )

        if len(filtered_formats) == 0:
            return "none"

        # If both the start and the end formats are the same then return that format
        if filtered_formats[0].type == filtered_formats[-1].type:
            return filtered_formats[0].type

        # TODO: if all else return none for now
        return filtered_formats[0].type

    def _find_metadata_title(self) -> Union[Dict, None]:
        metadata_title = ""

        if self.metadata and "title" in self.metadata:
            metadata_title = self.metadata["title"].lower()

        if len(metadata_title.strip()) == 0:
            return None

        all_headings = list(
            filter(
                lambda block: (
                    block["BlockType"] == TextractLayoutBlock.TITLE.value
                    or block["BlockType"] == TextractLayoutBlock.SECTION_HEADER.value
                )
                and block["Page"] <= 3,
                self.textract,
            ),
        )

        formatted_titles = [
            [
                item,
                self._get_layout_format(
                    item,
                    self.textract,
                    self.document[item["Page"] - 1],
                    None,
                ),
            ]
            for item in all_headings
        ]

        title_block: Union[dict, None] = None

        for item, layout_blocks in formatted_titles:
            if len(layout_blocks) != 1:
                continue

            layout_block = layout_blocks[0]

            if not isinstance(layout_block, ExtractedHeadingSegment):
                continue

            if layout_block.title.lower() == metadata_title:
                title_block = item

        return title_block

    def _calculate_most_confident_title(self) -> Union[dict, None]:
        """
        Calculate the most confident title based on the headings list
        """
        metadata_title = self._find_metadata_title()

        if metadata_title:
            return metadata_title

        title_blocks = list(
            filter(
                lambda block: block["BlockType"] == "LAYOUT_TITLE" and block["Page"] <= 3,
                self.textract,
            ),
        )

        if len(title_blocks) == 1:
            title_blocks.extend(
                list(
                    filter(
                        lambda block: block["BlockType"] == TextractLayoutBlock.SECTION_HEADER.value
                        and block["Page"] <= 3,
                        self.textract,
                    ),
                ),
            )

        formatted_titles = [
            [
                item,
                self._get_layout_format(
                    item,
                    self.textract,
                    self.document[item["Page"] - 1],
                    None,
                ),
            ]
            for item in title_blocks
        ]

        potential_title_block: Union[dict, None] = None
        potential_title_format = None

        for item, layout_blocks in formatted_titles:
            if len(layout_blocks) != 1:
                continue

            layout_block = layout_blocks[0]

            if not isinstance(layout_block, ExtractedHeadingSegment):
                continue

            if potential_title_format is None:
                potential_title_block = item
                potential_title_format = layout_block
                continue

            if layout_block.font_size > potential_title_format.font_size:
                potential_title_block = item
                potential_title_format = layout_block
                continue

        return potential_title_block

    def _is_item_header(self, layout_block: dict, debug=False):
        ret_val = False
        block_type = layout_block["BlockType"]
        if block_type == TextractLayoutBlock.HEADER.value:
            ret_val = True

        for header_bbox in self.header_boxes:
            rounded_bbox = self._round_textract_bounding_box(
                layout_block["Geometry"]["BoundingBox"],
            )
            if header_bbox == rounded_bbox:
                if debug:
                    print("header_bbox matches")
                ret_val = True

            if (
                header_bbox["Left"] == rounded_bbox["Left"]
                and header_bbox["Top"] == rounded_bbox["Top"]
                and header_bbox["Height"] == rounded_bbox["Height"]
            ):
                if debug:
                    print("left and top match")
                ret_val = True

        return ret_val

    def _is_item_footer(self, layout_block: dict):
        ret_val = False
        block_type = layout_block["BlockType"]
        if block_type == TextractLayoutBlock.FOOTER.value:
            ret_val = True

        if (
            self._round_textract_bounding_box(
                layout_block["Geometry"]["BoundingBox"],
            )
            in self.footer_boxes
        ):
            ret_val = True

        return ret_val

    def _get_textract_rect(self, page: Page, block):
        bbox = block["Geometry"]["BoundingBox"]
        return self.unnormalise_bbox(**bbox, page=page)

    def _get_layout_format(
        self,
        layout_block: dict,
        all_blocks: list[dict],
        page: Page,
        title_id: Union[str, None],
        debug=False,
    ) -> List[Any]:
        """
        Extract each layout item to their specified ExtractedSegment
        """
        block_type = layout_block["BlockType"]

        block_formats: List[Any] = []

        # TODO: Temporary, currently leave headers and footers null
        if self._is_item_header(layout_block):
            return block_formats

        block_rect = self._get_textract_rect(page, layout_block)

        # Image blocks with no child elements
        if block_type == TextractLayoutBlock.FIGURE.value:
            block_formats.append(self._block_to_image(block_rect, page))
            return block_formats
        if block_type == TextractLayoutBlock.TABLE.value:
            block_formats.append(self._extract_table(block_rect, page))
            return block_formats

        block_children = self._get_block_children(layout_block, all_blocks, debug)
        invalid_heading = layout_block["Id"] in self._non_headings

        if not invalid_heading and (
            block_type == TextractLayoutBlock.TITLE.value
            or block_type == TextractLayoutBlock.SECTION_HEADER.value
        ):
            formatted_block = self._get_textblock(
                block_rect,
                page,
            )

            if formatted_block is None:
                return block_formats

            tag = HeadingTag.H2.value
            if title_id is not None and layout_block["Id"] == title_id:
                tag = HeadingTag.H1.value
                self.extracted_title = formatted_block["text"]
                self.has_title = True

            format = self._get_heading_font_format(formatted_block["formats"])

            block_formats.append(
                ExtractedHeadingSegment(
                    formatted_block["text"].strip(),
                    formatted_block["size"],
                    uuid.uuid4().hex,
                    self._rect_to_bbox(block_rect),
                    format,
                    tag=tag,
                ),
            )
        elif invalid_heading or block_type == TextractLayoutBlock.TEXT.value:
            all_textblocks = self._get_all_textblocks(block_rect, page)

            if all_textblocks is None or len(all_textblocks) == 0:
                return block_formats

            for textblock in all_textblocks:
                if debug:
                    print(textblock["text"])
                if self._is_text_block_heading(textblock, debug):
                    format = self._get_heading_font_format(textblock["formats"])
                    block_formats.append(
                        ExtractedHeadingSegment(
                            textblock["text"].strip(),
                            textblock["size"],
                            uuid.uuid4().hex,
                            self._rect_to_bbox(block_rect),
                            format,
                            HeadingTag.H2.value,
                        ),
                    )
                    continue

                block_formats.append(
                    ExtractedParagraphSegment(
                        self._rect_to_bbox(block_rect),
                        textblock["text"],
                        textblock["size"],
                        textblock["formats"],
                        uuid.uuid4().hex,
                    ),
                )

        elif block_type == TextractLayoutBlock.LIST.value:
            if not self._is_valid_list(block_children):
                for child in block_children:
                    block_formats.extend(
                        self._get_layout_format(
                            child,
                            all_blocks,
                            page,
                            title_id,
                            debug,
                        ),
                    )
            else:
                block_formats.append(
                    self._extract_list(
                        block_children,
                        block_rect,
                        page,
                        all_blocks,
                    ),
                )

        # elif self._is_item_footer(layout_block):
        #     block_formats.append(
        #         self._extract_footnotes(block_children, block_rect, page)
        #     )

        return block_formats

    def _is_valid_list(self, block_children: List[Dict]):
        invalid = any(
            [
                child["BlockType"] == TextractLayoutBlock.SECTION_HEADER.value
                for child in block_children
            ],
        )

        return not invalid

    def _get_page_size(self, page: Page):
        """
        Gets the size of the page (width/height)
        """
        page_rect: Rect = page.rect
        return {"width": page_rect.width, "height": page_rect.height}

    def _get_page_blocks(self, page: Page):
        """
        Get the appropriate textract blocks for the page
        """
        page_blocks = list(
            filter(
                lambda block: block["Page"] == page.number + 1 and block["BlockType"] != "PAGE",
                self.textract,
            ),
        )

        # Get all layout blocks for the page
        layout_blocks = list(
            filter(
                lambda block: block["BlockType"].startswith("LAYOUT"),
                page_blocks,
            ),
        )

        list_blocks: list[int] = []

        for block in layout_blocks:
            if block["BlockType"] == TextractLayoutBlock.LIST.value:
                for relationship in block["Relationships"]:
                    list_blocks.extend(relationship["Ids"])

        filtered_layout_blocks = list(
            filter(lambda block: block["Id"] not in list_blocks, layout_blocks),
        )

        return page_blocks, filtered_layout_blocks

    def _tag_pages(self, tag_ids: Dict[str, str], page: ExtractedPage) -> ExtractedPage:
        tagged_content = list(
            map(
                lambda item: (
                    ExtractedHeadingSegment(
                        item.title,
                        item.font_size,
                        item.id,
                        item.bbox,
                        item.format,
                        tag=tag_ids[item.id],
                    )
                    if isinstance(item, ExtractedHeadingSegment) and item.tag != HeadingTag.H1.value
                    else item
                ),
                page.content,
            ),
        )

        return ExtractedPage(
            page.page,
            page.page_size,
            page.header,
            tagged_content,
            page.footer,
            page.footnotes,
        )

    def _round_textract_bounding_box(self, bbox):
        return {
            "Height": round(bbox["Height"], 2),
            "Left": round(bbox["Left"], 2),
            "Top": round(bbox["Top"], 2),
            "Width": round(bbox["Width"], 2),
        }

    def _get_document_headers(self):
        headers = list(
            filter(
                lambda block: block["BlockType"] == TextractLayoutBlock.HEADER.value,
                self.textract,
            ),
        )
        header_bboxes = list(
            map(
                lambda item: self._round_textract_bounding_box(
                    item["Geometry"]["BoundingBox"],
                ),
                headers,
            ),
        )

        trimmed_headers = [dict(value) for value in {tuple(bbox.items()) for bbox in header_bboxes}]
        self.header_boxes = trimmed_headers

    def _get_document_footers(self):
        footers = list(
            filter(
                lambda block: block["BlockType"] == TextractLayoutBlock.FOOTER.value,
                self.textract,
            ),
        )

        footer_bboxes = list(
            map(
                lambda item: self._round_textract_bounding_box(
                    item["Geometry"]["BoundingBox"],
                ),
                footers,
            ),
        )

        # Trim duplicate bboxes by converting list of dictionaries to list of tuples which
        # contains the values so `[{'Height': 0.01, 'Left': 0.07, 'Top': 0.05, 'Width': 0.03}]`
        # becomes `[(('Height', 0.01), ('Left', 0.07), ('Top', 0.05), ('Width', 0.03))]`
        # which hashes values and removes duplicates though ordering is lost

        trimmed_footers = [dict(value) for value in {tuple(bbox.items()) for bbox in footer_bboxes}]

        self.footer_boxes = trimmed_footers

    def _extract_footnotes(
        self,
        tt_lines: list[TextractLine],
        block_rect: Rect,
        page: Page,
    ):
        formatted_block = self._get_textblock(block_rect, page)
        if formatted_block is None:
            return None

        formats = formatted_block["formats"]
        footnotes: List[ExtractedFootnoteItem] = []

        item = None
        in_super = False

        for format in formats:
            if format.type == "super":
                in_super = True
                if item is None:
                    item = ExtractedFootnoteItem(
                        format.value,
                        [],
                        "",
                        self.base_font,
                        [],
                        "",
                    )
                    continue
                if item.text != "":
                    footnotes.append(item)
                    item = ExtractedFootnoteItem(
                        format.value,
                        [],
                        "",
                        self.base_font,
                        [],
                        "",
                    )
                    continue
            if in_super and item is not None:
                item.text += format.value.strip() + " "
                item.formats.append(format)

        if item is not None:
            footnotes.append(item)

        if len(footnotes) == 0:
            return None

        return ExtractedFootnoteSegment(
            self._rect_to_bbox(block_rect),
            footnotes,
            uuid.uuid4().hex,
        )

    def _skip_heading(self, formatted_heading: dict):
        all_format_types = [
            format.type for format in formatted_heading["formats"] if format.value.strip() != ""
        ]

        # Is there more than one formatting type? Invalid if so
        return self._invalid_title_start(formatted_heading) or len(set(all_format_types)) > 1

    def _get_document_headings(self):
        text_heading_blocks = list(
            filter(
                lambda block: block["BlockType"] == TextractLayoutBlock.TITLE.value
                or block["BlockType"] == TextractLayoutBlock.SECTION_HEADER.value,
                self.textract,
            ),
        )

        trimmed_text_heading_blocks = [
            item for item in text_heading_blocks if not self._is_item_header(item)
        ]

        formatted_blocks = []
        for block in trimmed_text_heading_blocks:
            page_num = block["Page"] - 1
            page = self.document[page_num]
            block_rect = self._get_textract_rect(page, block)

            formatted_block = self._get_textblock(
                block_rect,
                page,
            )

            if self._skip_heading(formatted_block):
                self._non_headings.append(block["Id"])
                continue

            formatted_blocks.append(formatted_block)

        if len(formatted_blocks) == 0:
            return

        max_length = max([len(item["text"]) for item in formatted_blocks])
        numbered_starts = list(
            filter(
                lambda item: self.list_numerical_start.match(
                    item["text"].strip(),
                ),
                formatted_blocks,
            ),
        )
        has_numbered_starts = len(numbered_starts) > 0

        all_caps = any(
            [
                self.all_capital_chars.match(item["text"].strip()) is not None
                for item in formatted_blocks
            ],
        )

        for item in formatted_blocks:
            all_font_sizes = [
                round(format.size, 2) for format in item["formats"] if format.value.strip() != ""
            ]
            all_font_families = [format.font for format in item["formats"]]

            bold = all(
                [format.type == "bold" for format in item["formats"] if format.value.strip() != ""],
            )
            italic = all(
                [
                    format.type == "italic"
                    for format in item["formats"]
                    if format.value.strip() != ""
                ],
            )

            if bold:
                self._header_rules.formats.bold.font_sizes.update(all_font_sizes)
                self._header_rules.formats.bold.font_familys.update(all_font_families)
            if italic:
                self._header_rules.formats.italic.font_sizes.update(all_font_sizes)
                self._header_rules.formats.italic.font_familys.update(all_font_families)
            if not bold and not italic:  # none
                self._header_rules.formats.none.font_sizes.update(all_font_sizes)
                self._header_rules.formats.none.font_familys.update(all_font_families)

        self._header_rules.max_length = max_length
        self._header_rules.has_numbered_starts = has_numbered_starts
        self._header_rules.has_all_caps = all_caps

    # def _set_incorrect_words(self, corrected_word: str):
    #     self.incorrect_words.update({corrected_word: count + 1})
    # not sure if we'll write it this way but this is where we'll wanted to update the incorrect words dict with the count of each word that has to be corrected
    # we can then check the dict for each word to see if the count if equal or less than the instances of the word in the document

    def _is_block_not_heading(self, text_block: Dict, debug=False):
        ret_val = True
        # TODO: Removed as it fixed an issue but need to be sure it's not caused regressions
        # I have tested 11 documents and all headings are extracting correctly so maybe it'll be reyt
        # leave until March and if no errors are found then this can be fully deleted
        """
        if len(text_block["text"]) > self._header_rules.max_length:
            if debug:
                print("failed: longer than max length")
                print(f"rule: {self._header_rules.max_length}")
                print(f"length: {len(text_block["text"])}")
            ret_val = False
        """

        # is the first word a non capital letter
        first_word = text_block["text"].strip().split(" ")[0]
        if len(first_word) > 1 and (
            self.capitals_regex.match(first_word) is None
            and self.numerical_start.match(text_block["text"].strip()) is None
        ):
            if debug:
                print("failed is not capital start or number")
            ret_val = False

        font_sizes = list(
            set(
                [
                    round(format.size, 2)
                    for format in text_block["formats"]
                    if format.value.strip() != ""
                ],
            ),
        )

        font_families = list(
            set(
                [format.font for format in text_block["formats"] if format.value.strip() != ""],
            ),
        )

        sizes_length = len(font_sizes)

        # If font size isn't in heading font sizes then likely not a heading
        if sizes_length != 1:
            if debug:
                print("fail: Too many font families")
            ret_val = False

        family_length = len(font_families)

        if family_length != 1:
            if debug:
                print(f"fail: family not in font_families: {font_families[0]}")
            ret_val = False

        font_styling = list(
            set(
                [format.type for format in text_block["formats"] if format.value.strip() != ""],
            ),
        )

        if len(font_styling) == 1 and font_styling[0] == "bold":
            if font_sizes[0] not in self._header_rules.formats.bold.font_sizes:
                if debug:
                    print("fail: font size not in bold headings")
                ret_val = False
            if font_families[0] not in self._header_rules.formats.bold.font_familys:
                if debug:
                    print("fail: font family not in bold headings")
                ret_val = False
        if len(font_styling) == 1 and font_styling[0] == "italic":
            if font_sizes[0] not in self._header_rules.formats.italic.font_sizes:
                if debug:
                    print("fail: font size not in italic headings")
                ret_val = False
            if font_families[0] not in self._header_rules.formats.italic.font_familys:
                if debug:
                    print("fail: font family not in italic headings")
                ret_val = False
        if len(font_styling) == 1 and font_styling[0] == "none":
            if font_sizes[0] not in self._header_rules.formats.none.font_sizes:
                if debug:
                    print("fail: font size not in non styled headings")
                ret_val = False
            if font_families[0] not in self._header_rules.formats.none.font_familys:
                if debug:
                    print("fail: font family not in non styled headings")
                ret_val = False

        return ret_val

    def _invalid_title_start(self, textblock: Dict):
        """
        Assesses whether the textblock begins with text that we class as not a heading
        i.e. `fig`, `table`, and `figure` as well as if it proceeded with a number
        e.g. `table 1`
        """
        invalid_headings = ["fig.", "table", "figure"]

        textbox_text = textblock["text"].strip()

        return any(
            [
                textbox_text.lower().startswith(heading)
                and self._non_title_regex.match(textbox_text) is not None
                for heading in invalid_headings
            ],
        )

    def _is_text_block_heading(self, text_block: Dict, debug=False):
        # If text is longer than the max length of headings then
        # probably not a heading
        if not self._is_block_not_heading(text_block, debug):
            if debug:
                print("fail: _is_block_not_heading")
            return False

        ret_val = False

        invalid_start = self._invalid_title_start(text_block)

        if invalid_start:
            return False

        all_caps = self.all_capital_chars.match(text_block["text"].strip()) is not None

        has_numbered_start = (
            self.list_numerical_start.match(
                text_block["text"].strip(),
            )
            is not None
        )

        if self._header_rules.has_all_caps and all_caps:
            if debug:
                print("pass: has all caps")
            ret_val = True

        if self._header_rules.has_numbered_starts and has_numbered_start:
            if debug:
                print("pass: has numbered started")
            ret_val = True

        if len(text_block["text"]) <= self._header_rules.max_length:
            if debug:
                print("pass: is at or below max length")
            ret_val = True

        if debug:
            print("Reached end")

        return ret_val

    def _extract_page(
        self,
        title_block: Union[Dict, None],
        page: Page,
    ) -> Tuple[List[ExtractedHeader], ExtractedPage]:
        # Get blocks for current page
        page_blocks, layout_blocks = self._get_page_blocks(page)

        extracted_headers: List[ExtractedHeader] = []

        extracted_segments = []

        for layout_block in layout_blocks:
            extracted_segments.extend(
                self._get_layout_format(
                    layout_block,
                    page_blocks,
                    page,
                    title_block["Id"] if title_block is not None else None,
                ),
            )

        footnotes: list[ExtractedFootnoteSegment] = list(  # type: ignore
            filter(
                lambda item: isinstance(
                    item,
                    ExtractedFootnoteSegment,
                ),
                extracted_segments,
            ),
        )

        filtered_segments = list(
            filter(
                lambda item: item is not None and not isinstance(item, ExtractedFootnoteSegment),
                extracted_segments,
            ),
        )
        # Extract all headings from the processed segments
        headings: list[ExtractedHeadingSegment] = list(  # type: ignore
            filter(
                lambda item: isinstance(
                    item,
                    ExtractedHeadingSegment,
                ),
                filtered_segments,
            ),
        )

        # Create ExtractedHeader item for each heading in segment
        extracted_headers.extend(
            [
                ExtractedHeader(
                    heading.title,
                    heading.font_size,
                    heading.id,
                    str(heading.tag),
                )
                for heading in headings
            ],
        )

        # Create ExtractedPage item for current page
        extracted_page = ExtractedPage(
            page.number,
            self._get_page_size(page),
            [],  # Empty Header for now
            filtered_segments,
            [],  # Empty Footer for now
            footnotes,
        )

        return extracted_headers, extracted_page

    def run(self) -> tuple[ExtractedDetails, list[ExtractedPage]]:
        """
        Start the extraction process for the PDF
        """
        try:
            self._get_base_font()
            self._get_document_headers()
            self._get_document_footers()
            self._get_document_headings()

            extracted_headers: List[ExtractedHeader] = []
            extracted_pages: List[ExtractedPage] = []
            title_block = self._calculate_most_confident_title()

            for page in self.document:
                page_headers, extracted_page = self._extract_page(title_block, page)
                extracted_headers.extend(page_headers)
                extracted_pages.append(extracted_page)

            sizes = self._get_header_sizes(extracted_headers, extracted_pages)

            tagged_pages = [self._tag_pages(sizes, page) for page in extracted_pages]

            document_details = ExtractedDetails(
                self.document.page_count,
                self.base_font,
                extracted_headers,
                authors=self.metadata.get("author", None),
            )

            return document_details, tagged_pages

        except ExtractionException as ee:
            raise ee
        except Exception as e:
            print(e)
            print(traceback.format_exc())
            raise DocumentParsingException(str(e))
