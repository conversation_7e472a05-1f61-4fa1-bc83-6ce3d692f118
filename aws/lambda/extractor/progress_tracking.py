"""
Handles reporting of errors and updating of extraction status for documents
as part of the extraction and post-processing process. Includes persistance
to database and update to customer via websocket.
"""

import json
from logging import Logger, getLogger
from os import environ
from typing import Union

from database import Document, DocumentStatus, User
from database.instance import DB
from database.services import commit as commit_document
from websocket_utils import (
    MessageTarget,
    MessageType,
    WebsocketClient,
    WebsocketMessage,
    build_message,
)

logger: Logger = getLogger()
ws_client = WebsocketClient(environ['WEBSOCKET_URL'], environ['SOCKETS_TABLE'])


# Update document status in database and send websocket message to customer
def update_document_status(
    document: Document,
    user: User,
    status: DocumentStatus,
    msg_in: Union[str, dict, None] = None,
) -> None:
    msg: Union[str, None] = json.dumps(msg_in) if isinstance(msg_in, dict) else msg_in

    message: WebsocketMessage = build_message(
        MessageType.DOCUMENT_STATUS,
        document.uuid,
        MessageTarget.DOCUMENT,
        document.uuid,
        status.value,
        msg,
    )
    ws_client.send_message(user.estendio_id, message)
    document.status = status.value
    commit_document(document)


# Report error to user and update document in database
def report_error(
    document: Document,
    user: Union[User, None],
    data: Union[dict, None] = None,
) -> None:
    # Deal with cases of missing data
    if data is None:
        data = {}
    if user is None:
        with DB() as db_session:
            user = User.get_by_id(db_session, document.owner_user_id)
            if user is None:
                msg: str = (
                    f'Unable to find user by uuid in order to report error: {json.dumps(data)}'
                )
                logger.error(msg)

    # Create the database items to link user to document
    message: WebsocketMessage = build_message(
        MessageType.DOCUMENT_STATUS,
        document.uuid,
        MessageTarget.DOCUMENT,
        document.uuid,
        DocumentStatus.ERROR.value,
        json.dumps(data),
    )
    ws_client.send_message(user.estendio_id, message)  # type: ignore (resolved above)
    document.status = DocumentStatus.ERROR.value
    commit_document(document)
