import pytest
from typing import List
from extractor.document_structure import ExtractedParagraphSegment
from ..document_extractor import DocumentExtractor

from .raw_data.merging_paragraphs import (
    paragraph_font_12,
    paragraph_base_font,
    paragraph_colon_end,
    paragraph_lowercase_start,
    paragraph_bold_start_1,
    paragraph_bold_start_2,
    paragraph_empty_format,
    paragraph_full_stop_end,
    paragraph_all_cap_start_1,
    paragraph_all_cap_start_2,
    paragraph_capital_start,
    paragraph_list_item_start,
    paragraph_superscript_end,
    paragraph_base_font_family,
    paragraph_different_font_family,
    paragraph_reference_start_1,
    paragraph_reference_start_2,
)

paragraph_comparison_list = [
    ([paragraph_empty_format, paragraph_empty_format], False),
    ([paragraph_font_12, paragraph_base_font], False),
    ([paragraph_font_12, paragraph_font_12], True),
    ([paragraph_bold_start_1, paragraph_bold_start_2], False),
    ([paragraph_bold_start_1, paragraph_lowercase_start], True),
    ([paragraph_superscript_end, paragraph_capital_start], False),
    ([paragraph_superscript_end, paragraph_lowercase_start], True),
    ([paragraph_base_font_family, paragraph_different_font_family], False),
    ([paragraph_different_font_family, paragraph_different_font_family], True),
    ([paragraph_full_stop_end, paragraph_capital_start], False),
    ([paragraph_capital_start, paragraph_capital_start], True),
    ([paragraph_full_stop_end, paragraph_list_item_start], False),
    ([paragraph_bold_start_2, paragraph_list_item_start], True),
    ([paragraph_all_cap_start_1, paragraph_all_cap_start_2], False),
    ([paragraph_all_cap_start_1, paragraph_lowercase_start], True),
    ([paragraph_all_cap_start_1, paragraph_capital_start], False),
    ([paragraph_all_cap_start_1, paragraph_lowercase_start], True),
    ([paragraph_reference_start_1, paragraph_reference_start_2], False),
    ([paragraph_reference_start_1, paragraph_lowercase_start], True),
    ([paragraph_colon_end, paragraph_lowercase_start], False),
]


@pytest.fixture()
def extractor_class():
    yield DocumentExtractor(stream=None)


def are_paragraphs_merged(
    edited_list: List[ExtractedParagraphSegment],
    original_list: List[ExtractedParagraphSegment],
) -> bool:
    combined_text = original_list[0].text + " " + original_list[1].text
    combined_formats = original_list[0].formats + original_list[1].formats

    return (
        len(edited_list) == 1
        and edited_list[0].text == combined_text
        and edited_list[0].formats == combined_formats
    )


def idfn(combination):
    if isinstance(combination, List):
        paragraph_one = combination[0]
        paragraph_two = combination[1]

        return f"{paragraph_one.id}->{paragraph_two.id}"


@pytest.mark.parametrize(
    "combination,should_pass",
    paragraph_comparison_list,
    ids=idfn,
)
def test_paragraphs(
    extractor_class: DocumentExtractor,
    combination,
    should_pass,
):
    copied = combination.copy()

    extractor_class._trim_all_content(combination)

    if should_pass:
        assert are_paragraphs_merged(combination, copied)
        return

    assert len(combination) == 2
