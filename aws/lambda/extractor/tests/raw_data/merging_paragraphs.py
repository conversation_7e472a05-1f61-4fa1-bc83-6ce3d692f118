from ..constants import DEFAULT_FONT_FAMILY
from ...document_structure import ExtractedParagraphSegment, ExtractFormat

BASE_FONT = 14

paragraph_font_12 = ExtractedParagraphSegment(
    [],
    "Paragraph with font 12",
    12,
    [
        ExtractFormat(
            "none",
            12,
            "Paragraph with font 12.",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "Font 12",
)

paragraph_base_font = ExtractedParagraphSegment(
    [],
    "Paragraph with base font",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "Paragraph with base font.",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "Base font",
)

paragraph_empty_format = ExtractedParagraphSegment(
    [],
    "Paragraph with empty format.",
    BASE_FONT,
    [],
    "Empty Format",
)

paragraph_bold_start_1 = ExtractedParagraphSegment(
    [],
    "Paragraph with bold start 1",
    BASE_FONT,
    [
        ExtractFormat(
            "bold",
            BASE_FONT,
            "Paragraph",
            [],
            DEFAULT_FONT_FAMILY,
        ),
        ExtractFormat(
            "none",
            BASE_FONT,
            "with bold start 1.",
            [],
            DEFAULT_FONT_FAMILY,
        ),
    ],
    "Bold start 1",
)

paragraph_bold_start_2 = ExtractedParagraphSegment(
    [],
    "Paragraph with bold start 2",
    BASE_FONT,
    [
        ExtractFormat(
            "bold",
            BASE_FONT,
            "Paragraph",
            [],
            DEFAULT_FONT_FAMILY,
        ),
        ExtractFormat(
            "none",
            BASE_FONT,
            "with bold start 2.",
            [],
            DEFAULT_FONT_FAMILY,
        ),
    ],
    "Bold start 2",
)

paragraph_superscript_end = ExtractedParagraphSegment(
    [],
    "Paragraph with a superscript end. 20",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "Paragraph with a superscript end.",
            [],
            DEFAULT_FONT_FAMILY,
        ),
        ExtractFormat(
            "super",
            BASE_FONT,
            "20",
            [],
            DEFAULT_FONT_FAMILY,
        ),
    ],
    "Superscript end",
)

paragraph_capital_start = ExtractedParagraphSegment(
    [],
    "Paragraph capital start",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "Paragraph capital start",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "Capital start",
)

paragraph_lowercase_start = ExtractedParagraphSegment(
    [],
    "paragraph lowercase start",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "paragraph lowercase start",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "lowercase start",
)

paragraph_full_stop_end = ExtractedParagraphSegment(
    [],
    "Paragraph full stop end.",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "Paragraph full stop end.",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "Full stop end",
)

paragraph_list_item_start = ExtractedParagraphSegment(
    [],
    "1. List Item start",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "1. List Item start",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "List item start",
)

paragraph_all_cap_start_1 = ExtractedParagraphSegment(
    [],
    "CAPITALS start 1",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "CAPITALS start 1.",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "All capital start 1",
)

paragraph_all_cap_start_2 = ExtractedParagraphSegment(
    [],
    "CAPITALS start 2.",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "CAPITALS start 2.",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "All capital start 2",
)

paragraph_colon_end = ExtractedParagraphSegment(
    [],
    "Paragraph colon end:",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "Paragraph colon end:",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "Colon end",
)

paragraph_base_font_family = ExtractedParagraphSegment(
    [],
    "Paragraph with common font format",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "Paragraph",
            [],
            DEFAULT_FONT_FAMILY,
        ),
        ExtractFormat(
            "none",
            BASE_FONT,
            "with common",
            [],
            DEFAULT_FONT_FAMILY,
        ),
        ExtractFormat(
            "none",
            BASE_FONT,
            "font format",
            [],
            DEFAULT_FONT_FAMILY,
        ),
    ],
    f"{DEFAULT_FONT_FAMILY} font family",
)

paragraph_different_font_family = ExtractedParagraphSegment(
    [],
    "Paragraph with common font format",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "Paragraph",
            [],
            "Times New Roman",
        ),
        ExtractFormat(
            "none",
            BASE_FONT,
            "with common",
            [],
            "Times New Roman",
        ),
        ExtractFormat(
            "none",
            BASE_FONT,
            "font format",
            [],
            "Times New Roman",
        ),
    ],
    "Times New Roman font family",
)

paragraph_reference_start_1 = ExtractedParagraphSegment(
    [],
    "[1] Paragraph reference start 1",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "[1] Paragraph reference start 1",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "Reference start 1",
)

paragraph_reference_start_2 = ExtractedParagraphSegment(
    [],
    "[2] Paragraph reference start 2",
    BASE_FONT,
    [
        ExtractFormat(
            "none",
            BASE_FONT,
            "[2] Paragraph reference start 2",
            [],
            DEFAULT_FONT_FAMILY,
        )
    ],
    "Reference start 2",
)
