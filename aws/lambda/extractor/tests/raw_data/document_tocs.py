from typing import List
from dataclasses import dataclass
from ..test_documents.pdf_data import export_data


@dataclass
class TOCItem:
    value: str
    subitems: List


pdf_tocs = {
    "research_paper": {
        "data": export_data["research_paper.pdf"],
        "toc": [
            TOCItem(
                value="Education and Social Exclusion of Children with Chronic Diseases: The School Reintegration",
                subitems=[],
            ),
            TOCItem(value="Abstract", subitems=[]),
            TOCItem(value="1. Introduction", subitems=[]),
            TOCItem(
                value="2. Theoretical Framework",
                subitems=[
                    TOCItem(
                        value="2.1 School reintegration after hospitalization",
                        subitems=[],
                    ),
                    TOCItem(
                        value="2.2 The role of the teacher in the formal school during reintegration",
                        subitems=[],
                    ),
                    TOCItem(
                        value="2.3 Technology in the service of school reintegration of children with chronic diseases",
                        subitems=[],
                    ),
                ],
            ),
            TOCItem(value="3. Method of research", subitems=[]),
            TOCItem(value="4. Discussion", subitems=[]),
            TOCItem(value="5. Conclusion", subitems=[]),
            TOCItem(value="References", subitems=[]),
            TOCItem(value="Sites", subitems=[]),
        ],
    },
    "is_trust_a_necessary_component": {
        "data": export_data["is_trust_a_necessary_component.pdf"],
        "toc": [
            TOCItem(
                value="Is trust a necessary component of relationship management?",
                subitems=[],
            ),
            TOCItem(
                value="INTRODUCTION",
                subitems=[TOCItem(value="Joy Chia", subitems=[])],
            ),
            TOCItem(value="RELATIONSHIP MANAGEMENT", subitems=[]),
            TOCItem(value="METHOD", subitems=[]),
            TOCItem(value="FINDINGS", subitems=[]),
            TOCItem(value="DISCUSSION OF FINDINGS", subitems=[]),
            TOCItem(value="CONCLUSION",subitems=[]),
            TOCItem(value="References", subitems=[]),
        ],
    },
}
