from ...document_structure import (
    ExtractedHeader,
    ExtractedHeadingSegment,
    ExtractedPage,
    ExtractedParagraphSegment,
    ExtractFormat,
)

DEFAULT_FONT = "Comic Sans"

extracted_headers = [
    ExtractedHeader(
        title="Document Title",
        font_size=18,
        id="6bf485963d2d4cdcb9217cb4b55562ff",
        tag="h1",
    ),
    ExtractedHeader(
        title="Heading 1", font_size=10, id="eeb30856c1234d7389cf3c61e0e3e158", tag="h2"
    ),
    ExtractedHeader(
        title="Heading 2",
        font_size=10,
        id="8485854511c249519d25cff7fa373b28",
        tag="h2",
    ),
    ExtractedHeader(
        title="Heading 3", font_size=10, id="b87758b854e3410faf0f1d361c45ab46", tag="h2"
    ),
    ExtractedHeader(
        title="Heading 4",
        font_size=11,
        id="6e319434d7614e77997513b37dbbe7ce",
        tag="h2",
    ),
    ExtractedHeader(
        title="Heading 5",
        font_size=7,
        id="49edb3013e3a474fba625db740e07a5e",
        tag="h2",
    ),
    ExtractedHeader(
        title="Heading 6", font_size=11, id="1da92beec41f4b9483483352972056ee", tag="h2"
    ),
    ExtractedHeader(
        title="Heading 7",
        font_size=11,
        id="891926e0fa8e4efcbbbe89187fc84484",
        tag="h2",
    ),
]

extracted_pages = [
    ExtractedPage(
        page=1,
        page_size={"width": 595.2760009765625, "height": 793.7009887695312},
        header=[],
        content=[
            ExtractedHeadingSegment(
                title="Document Title",
                font_size=18,
                id="6bf485963d2d4cdcb9217cb4b55562ff",
                bbox=[
                    61.93287695631989,
                    85.24663818272938,
                    404.82603124446723,
                    142.17652967467666,
                ],
                format="bold",
                type="heading",
                tag="h1",
            ),
            ExtractedHeadingSegment(
                title="Heading 1",
                font_size=10,
                id="eeb30856c1234d7389cf3c61e0e3e158",
                bbox=[
                    61.79212741322681,
                    242.35281672543715,
                    94.79623430419633,
                    250.02109626874062,
                ],
                format="bold",
                type="heading",
                tag="h2",
            ),
            ExtractedParagraphSegment(
                bbox=[
                    309.66921909708617,
                    673.1488839323974,
                    545.9753500525403,
                    731.1562684792243,
                ],
                text="This is a sample paragraph",
                font_size=10,
                formats=[
                    ExtractFormat(
                        type="none",
                        size=10.000100135803223,
                        value="This is a sample paragraph",
                        bbox=[
                            310.3935852050781,
                            672.04541015625,
                            406.25762939453125,
                            683.0755004882812,
                        ],
                        font=DEFAULT_FONT,
                    )
                ],
                id="",
                type="paragraph",
            ),
            ExtractedHeadingSegment(
                title="Heading 2",
                font_size=10,
                id="8485854511c249519d25cff7fa373b28",
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
                format="bold",
                type="heading",
                tag="h2",
            ),
            ExtractedParagraphSegment(
                bbox=[
                    309.66921909708617,
                    673.1488839323974,
                    545.9753500525403,
                    731.1562684792243,
                ],
                text="This is a sample paragraph",
                font_size=10,
                formats=[
                    ExtractFormat(
                        type="none",
                        size=10.000100135803223,
                        value="This is a sample paragraph",
                        bbox=[
                            310.3935852050781,
                            672.04541015625,
                            406.25762939453125,
                            683.0755004882812,
                        ],
                        font=DEFAULT_FONT,
                    )
                ],
                id="",
                type="paragraph",
            ),
            ExtractedHeadingSegment(
                title="Heading 3",
                font_size=10,
                id="b87758b854e3410faf0f1d361c45ab46",
                bbox=[
                    62.25778730854654,
                    511.97217804243337,
                    101.14038635016823,
                    521.3125120510388,
                ],
                format="bold",
                type="heading",
                tag="h2",
            ),
            ExtractedParagraphSegment(
                bbox=[
                    309.66921909708617,
                    673.1488839323974,
                    545.9753500525403,
                    731.1562684792243,
                ],
                text="This is a sample paragraph",
                font_size=10,
                formats=[
                    ExtractFormat(
                        type="none",
                        size=10.000100135803223,
                        value="This is a sample paragraph",
                        bbox=[
                            310.3935852050781,
                            672.04541015625,
                            406.25762939453125,
                            683.0755004882812,
                        ],
                        font=DEFAULT_FONT,
                    )
                ],
                id="",
                type="paragraph",
            ),
            ExtractedHeadingSegment(
                title="Heading 4",
                font_size=11,
                id="6e319434d7614e77997513b37dbbe7ce",
                bbox=[
                    62.3750970742567,
                    630.9744641020006,
                    118.4796345767354,
                    639.8232275333546,
                ],
                format="bold",
                type="heading",
                tag="h2",
            ),
            ExtractedParagraphSegment(
                bbox=[
                    309.66921909708617,
                    673.1488839323974,
                    545.9753500525403,
                    731.1562684792243,
                ],
                text="This is a sample paragraph",
                font_size=10,
                formats=[
                    ExtractFormat(
                        type="none",
                        size=10.000100135803223,
                        value="This is a sample paragraph",
                        bbox=[
                            310.3935852050781,
                            672.04541015625,
                            406.25762939453125,
                            683.0755004882812,
                        ],
                        font=DEFAULT_FONT,
                    )
                ],
                id="",
                type="paragraph",
            ),
            ExtractedParagraphSegment(
                bbox=[
                    309.66921909708617,
                    673.1488839323974,
                    545.9753500525403,
                    731.1562684792243,
                ],
                text="This is a sample paragraph",
                font_size=10,
                formats=[
                    ExtractFormat(
                        type="none",
                        size=10.000100135803223,
                        value="This is a sample paragraph",
                        bbox=[
                            310.3935852050781,
                            672.04541015625,
                            406.25762939453125,
                            683.0755004882812,
                        ],
                        font=DEFAULT_FONT,
                    )
                ],
                id="",
                type="paragraph",
            ),
            ExtractedHeadingSegment(
                title="Heading 5",
                font_size=7,
                id="49edb3013e3a474fba625db740e07a5e",
                bbox=[
                    310.14452544762025,
                    692.8333801215813,
                    380.6212345236527,
                    700.3136861854796,
                ],
                format="bold",
                type="heading",
                tag="h2",
            ),
            ExtractedParagraphSegment(
                bbox=[
                    309.66921909708617,
                    673.1488839323974,
                    545.9753500525403,
                    731.1562684792243,
                ],
                text="This is a sample paragraph",
                font_size=10,
                formats=[
                    ExtractFormat(
                        type="none",
                        size=10.000100135803223,
                        value="This is a sample paragraph",
                        bbox=[
                            310.3935852050781,
                            672.04541015625,
                            406.25762939453125,
                            683.0755004882812,
                        ],
                        font=DEFAULT_FONT,
                    )
                ],
                id="",
                type="paragraph",
            ),
            ExtractedHeadingSegment(
                title="Heading 6",
                font_size=11,
                id="1da92beec41f4b9483483352972056ee",
                bbox=[
                    62.33235108098961,
                    73.63516740227851,
                    97.44411751568259,
                    82.55634278669515,
                ],
                format="bold",
                type="heading",
                tag="h2",
            ),
            ExtractedParagraphSegment(
                bbox=[
                    309.66921909708617,
                    673.1488839323974,
                    545.9753500525403,
                    731.1562684792243,
                ],
                text="This is a sample paragraph",
                font_size=10,
                formats=[
                    ExtractFormat(
                        type="none",
                        size=10.000100135803223,
                        value="This is a sample paragraph",
                        bbox=[
                            310.3935852050781,
                            672.04541015625,
                            406.25762939453125,
                            683.0755004882812,
                        ],
                        font=DEFAULT_FONT,
                    )
                ],
                id="",
                type="paragraph",
            ),
            ExtractedHeadingSegment(
                title="Heading 7",
                font_size=11,
                id="891926e0fa8e4efcbbbe89187fc84484",
                bbox=[
                    62.69922240461074,
                    95.76158024497272,
                    199.2711629413434,
                    107.08478771913047,
                ],
                format="italic",
                type="heading",
                tag="h2",
            ),
            ExtractedParagraphSegment(
                bbox=[
                    309.66921909708617,
                    673.1488839323974,
                    545.9753500525403,
                    731.1562684792243,
                ],
                text="This is a sample paragraph",
                font_size=10,
                formats=[
                    ExtractFormat(
                        type="none",
                        size=10.000100135803223,
                        value="This is a sample paragraph",
                        bbox=[
                            310.3935852050781,
                            672.04541015625,
                            406.25762939453125,
                            683.0755004882812,
                        ],
                        font=DEFAULT_FONT,
                    )
                ],
                id="",
                type="paragraph",
            ),
        ],
        footer=[],
        footnotes=[],
    )
]
