same_line = [
    {
        "BlockType": "LINE",
        "ColumnIndex": None,
        "ColumnSpan": None,
        "Confidence": 99.59454345703125,
        "EntityTypes": None,
        "Geometry": {
            "BoundingBox": {
                "Height": 0.008753332309424877,
                "Left": 0.10299962013959885,
                "Top": 0.5511887073516846,
                "Width": 0.008721238002181053,
            },
            "Polygon": [
                {"X": 0.10299962013959885, "Y": 0.5511894226074219},
                {"X": 0.11171576380729675, "Y": 0.5511887073516846},
                {"X": 0.11172085255384445, "Y": 0.559941291809082},
                {"X": 0.10300467163324356, "Y": 0.5599420070648193},
            ],
        },
        "Hint": None,
        "Id": "596746a7-9b84-4efb-902e-54a623f70693",
        "Page": 2,
        "PageClassification": None,
        "Query": None,
        "Relationships": [
            {"Ids": ["b6790e78-ccdc-44fe-8699-e14c58fc9d8a"], "Type": "CHILD"}
        ],
        "RowIndex": None,
        "RowSpan": None,
        "SelectionStatus": None,
        "Text": "2",
        "TextType": None,
    },
    {
        "BlockType": "LINE",
        "ColumnIndex": None,
        "ColumnSpan": None,
        "Confidence": 99.23207092285156,
        "EntityTypes": None,
        "Geometry": {
            "BoundingBox": {
                "Height": 0.009615062735974789,
                "Left": 0.12932230532169342,
                "Top": 0.5506027936935425,
                "Width": 0.12381067872047424,
            },
            "Polygon": [
                {"X": 0.12932230532169342, "Y": 0.550613284111023},
                {"X": 0.2531268000602722, "Y": 0.5506027936935425},
                {"X": 0.25313296914100647, "Y": 0.5602074265480042},
                {"X": 0.1293279528617859, "Y": 0.5602178573608398},
            ],
        },
        "Hint": None,
        "Id": "a3751197-d1e2-4e3c-a94b-d8ed6250e7b0",
        "Page": 2,
        "PageClassification": None,
        "Query": None,
        "Relationships": [
            {"Ids": ["a03a42ba-83ff-4661-906c-ff499db1d2cf"], "Type": "CHILD"}
        ],
        "RowIndex": None,
        "RowSpan": None,
        "SelectionStatus": None,
        "Text": "BACKGROUND",
        "TextType": None,
    },
]

different_top_vals = [
    {
        "BlockType": "LINE",
        "ColumnIndex": None,
        "ColumnSpan": None,
        "Confidence": 85.42286682128906,
        "EntityTypes": None,
        "Geometry": {
            "BoundingBox": {
                "Height": 0.009243689477443695,
                "Left": 0.5378562808036804,
                "Top": 0.7188300490379333,
                "Width": 0.3569953441619873,
            },
            "Polygon": [
                {"X": 0.5378562808036804, "Y": 0.7192507982254028},
                {"X": 0.894843339920044, "Y": 0.7188300490379333},
                {"X": 0.8948516845703125, "Y": 0.7276530861854553},
                {"X": 0.5378633737564087, "Y": 0.7280737161636353},
            ],
        },
        "Hint": None,
        "Id": "bf7690b3-bafa-46dc-8eec-537d9a4bc9d6",
        "Page": 12,
        "PageClassification": None,
        "Query": None,
        "Relationships": [
            {
                "Ids": [
                    "5164087a-f00f-49cb-81a5-504db23fecd2",
                    "115d3bdd-5f3f-4f61-8d1d-15d45b242a95",
                    "b03560e5-5ec5-4533-b492-af5a6c7bc5b7",
                ],
                "Type": "CHILD",
            }
        ],
        "RowIndex": None,
        "RowSpan": None,
        "SelectionStatus": None,
        "Text": "nical Report. https://repository.tudelft.nl/islandora/object/uuid%3A8bc685df-",
        "TextType": None,
    },
    {
        "BlockType": "LINE",
        "ColumnIndex": None,
        "ColumnSpan": None,
        "Confidence": 73.88785552978516,
        "EntityTypes": None,
        "Geometry": {
            "BoundingBox": {
                "Height": 0.007159218657761812,
                "Left": 0.5383420586585999,
                "Top": 0.7288227677345276,
                "Width": 0.12836907804012299,
            },
            "Polygon": [
                {"X": 0.5383420586585999, "Y": 0.7289740443229675},
                {"X": 0.6667051911354065, "Y": 0.7288227677345276},
                {"X": 0.666711151599884, "Y": 0.7358307838439941},
                {"X": 0.5383476614952087, "Y": 0.7359820008277893},
            ],
        },
        "Hint": None,
        "Id": "9e17fc5c-0fd1-4204-93b8-9ef654915729",
        "Page": 12,
        "PageClassification": None,
        "Query": None,
        "Relationships": [
            {"Ids": ["b9b86010-579d-443d-8e02-36f81e90b806"], "Type": "CHILD"}
        ],
        "RowIndex": None,
        "RowSpan": None,
        "SelectionStatus": None,
        "Text": "fcf4-4ed8-8ef3-a11e75f82c76",
        "TextType": None,
    },
]

different_intersecting_points = [
    {
        "BlockType": "LINE",
        "ColumnIndex": None,
        "ColumnSpan": None,
        "Confidence": 99.66265869140625,
        "EntityTypes": None,
        "Geometry": {
            "BoundingBox": {
                "Height": 0.009382889606058598,
                "Left": 0.1017816886305809,
                "Top": 0.4650315046310425,
                "Width": 0.3809768855571747,
            },
            "Polygon": [
                {"X": 0.1017816886305809, "Y": 0.4654828608036041},
                {"X": 0.48275163769721985, "Y": 0.4650315046310425},
                {"X": 0.4827585816383362, "Y": 0.4739631414413452},
                {"X": 0.10178728401660919, "Y": 0.4744144082069397},
            ],
        },
        "Hint": None,
        "Id": "2663c302-2e7e-4620-95ab-aa65911e9e15",
        "Page": 12,
        "PageClassification": None,
        "Query": None,
        "Relationships": [
            {
                "Ids": [
                    "ccf8cefe-82bd-4259-bef9-9b9cef13a5ad",
                    "ab5e62ae-6d14-4745-a09a-0794a6805f37",
                    "5bd83f2a-c3b0-4988-b550-05e614b3d5f7",
                    "6bac35ab-e7d8-42db-9b11-b334c61a1e39",
                    "e9e8e344-3239-4d16-ba1c-aa8ce008b355",
                    "8693f766-6f24-4c2e-9cfc-3b75e15c8d2b",
                    "3d4f572e-063a-4265-b685-a4a2ba7e4240",
                    "802a50c7-a607-4e45-8982-0ff1cb14eb9d",
                    "04fffd8a-9ce1-4fdc-8111-727f19e196b5",
                    "a1547fe4-b831-4a33-a7ff-2401c3953fa3",
                    "f0174643-4939-4b63-8876-6f4b2c873415",
                    "e747b7cc-0e46-4846-a281-b558de09e478",
                ],
                "Type": "CHILD",
            }
        ],
        "RowIndex": None,
        "RowSpan": None,
        "SelectionStatus": None,
        "Text": "[18] Thomas Fritz and Sebastian Muller. 2016. Leveraging Biometric Data to Boost",
        "TextType": None,
    },
    {
        "BlockType": "LINE",
        "ColumnIndex": None,
        "ColumnSpan": None,
        "Confidence": 98.58563995361328,
        "EntityTypes": None,
        "Geometry": {
            "BoundingBox": {
                "Height": 0.009215809404850006,
                "Left": 0.12616382539272308,
                "Top": 0.4749424457550049,
                "Width": 0.35725119709968567,
            },
            "Polygon": [
                {"X": 0.12616382539272308, "Y": 0.47536560893058777},
                {"X": 0.4834081828594208, "Y": 0.4749424457550049},
                {"X": 0.48341500759124756, "Y": 0.48373520374298096},
                {"X": 0.12616941332817078, "Y": 0.4841582775115967},
            ],
        },
        "Hint": None,
        "Id": "2aa4999b-325e-4a52-bed1-f62e3220171a",
        "Page": 12,
        "PageClassification": None,
        "Query": None,
        "Relationships": [
            {
                "Ids": [
                    "d345fdec-8913-495b-9504-eb4338dbf5a7",
                    "42db0983-990d-49e0-8dcf-9690f2f5db24",
                    "c83fa497-7147-4a96-9161-9a4dae9ea90b",
                    "9ed86575-d35d-4f43-9d4c-bf476a5769c3",
                    "fd3f0f40-7726-4f97-9d43-6008ca649955",
                ],
                "Type": "CHILD",
            }
        ],
        "RowIndex": None,
        "RowSpan": None,
        "SelectionStatus": None,
        "Text": "Software Developer Productivity. 66-77. https://doi.org/10.1109/SANER.2016.107",
        "TextType": None,
    },
]
