import json
import pytest
from extractor.pdf_extractor import PDFExtractor
from extractor.document_structure import ExtractFormat, TextractLine
from .raw_data.spans import Spans
from .raw_data.example_extracted_data import extracted_headers, extracted_pages
from .raw_data.textract_headers_footers import textract_headers_footers
from .raw_data.textract_lines import (
    same_line,
    different_top_vals,
    different_intersecting_points,
)
from .test_documents.pdf_data import export_data
from fitz import Rect

span_list = [
    (Spans.italic, "italic"),
    (Spans.bold, "bold"),
    (Spans.standard, "none"),
    (Spans.link, "link"),
    (Spans.bold_font_id, "bold"),
    (Spans.italic_font_id, "italic"),
    (Spans.invalid_font, "none"),
    (Spans.superscript, "super"),
    (Spans.superscript_empty, "none"),
    (Spans.bold_flag, "bold"),
    (Spans.italic_flag, "italic"),
]

font_id_list = [
    ("AdvTT1a1e9df6.B", "bold"),
    ("AdvTT1a1e9df6.B+FF", "bold"),
    ("AdvTT1a1e9df6.I", "italic"),
    ("AdvTT1a1e9df6.I+20", "italic"),
    ("ArialBold", "bold"),
    # This is fine as the other method will handle these font families
    ("dsfasdfkjlhlaksdf", "none"),
]

is_header_list = [
    (
        {
            "BlockType": "LAYOUT_HEADER",
            "Geometry": {
                "BoundingBox": {
                    "Height": 0.0,
                    "Left": 0.0,
                    "Top": 0.0,
                    "Width": 0.0,
                },
            },
        },
        True,
    ),
    (
        {
            "BlockType": "LAYOUT_FOOTER",
            "Geometry": {
                "BoundingBox": {
                    "Height": 0.0,
                    "Left": 0.0,
                    "Top": 0.0,
                    "Width": 0.0,
                },
            },
        },
        False,
    ),
    (
        {
            "BlockType": "LAYOUT_TEXT",
            "Geometry": {
                "BoundingBox": {
                    "Height": 0.0,
                    "Left": 0.0,
                    "Top": 0.0,
                    "Width": 0.0,
                },
            },
        },
        False,
    ),
    (
        {
            "BlockType": "LAYOUT_TEXT",
            "Geometry": textract_headers_footers[0]["Geometry"],
        },
        True,
    ),
    (
        {
            "BlockType": "LAYOUT_TEXT",
            "Geometry": textract_headers_footers[1]["Geometry"],
        },
        False,
    ),
]

is_footer_list = (
    (
        {
            "BlockType": "LAYOUT_FOOTER",
            "Geometry": {
                "BoundingBox": {
                    "Height": 0.0,
                    "Left": 0.0,
                    "Top": 0.0,
                    "Width": 0.0,
                },
            },
        },
        True,
    ),
    (
        {
            "BlockType": "LAYOUT_HEADER",
            "Geometry": {
                "BoundingBox": {
                    "Height": 0.0,
                    "Left": 0.0,
                    "Top": 0.0,
                    "Width": 0.0,
                },
            },
        },
        False,
    ),
    (
        {
            "BlockType": "LAYOUT_TEXT",
            "Geometry": {
                "BoundingBox": {
                    "Height": 0.0,
                    "Left": 0.0,
                    "Top": 0.0,
                    "Width": 0.0,
                },
            },
        },
        False,
    ),
    (
        {
            "BlockType": "LAYOUT_TEXT",
            "Geometry": textract_headers_footers[0]["Geometry"],
        },
        False,
    ),
    (
        {
            "BlockType": "LAYOUT_TEXT",
            "Geometry": textract_headers_footers[1]["Geometry"],
        },
        True,
    ),
)

textract_lines = [
    (*same_line, True),
    (*different_top_vals, False),
    (*different_intersecting_points, False),
]


@pytest.fixture()
def pdf_class():
    data = export_data["annoying.pdf"]
    textract_json = json.loads(open(data.textract_path).read())
    yield PDFExtractor(
        stream=open(data.pdf_path, "rb").read(),
        textract=textract_json,
    )


"""======================== TESTS ========================"""


@pytest.mark.parametrize("font_id_name,expected", font_id_list)
def test_pdf_get_style_from_font_family(
    pdf_class: PDFExtractor, font_id_name, expected
):
    assert pdf_class._get_style_from_font_family(font_id_name) == expected


@pytest.mark.parametrize("span,expected", span_list)
def test_span_list(pdf_class: PDFExtractor, span, expected):
    span_text = span["text"]
    value = pdf_class._get_span_format(span["text"], span, span["flags"])
    assert isinstance(value, ExtractFormat)
    assert value.type == expected
    assert value.value == span_text


def test_get_heading_sizes(pdf_class: PDFExtractor):
    result = pdf_class._get_header_sizes(extracted_headers, extracted_pages)
    expected = {
        "6e319434d7614e77997513b37dbbe7ce": "h2", # Heading 4
        "1da92beec41f4b9483483352972056ee": "h2", # Heading 6
        "891926e0fa8e4efcbbbe89187fc84484": "h3", # Heading 7
        "eeb30856c1234d7389cf3c61e0e3e158": "h4", # Heading 1
        "8485854511c249519d25cff7fa373b28": "h4", # Heading 2
        "b87758b854e3410faf0f1d361c45ab46": "h4", # Heading 3
        "49edb3013e3a474fba625db740e07a5e": "h5", # Heading 5
    }
    assert isinstance(result, dict)

    for tag in result.values():
        assert tag != "h1"

    assert result == expected


def test_round_textract_bounding_box(pdf_class: PDFExtractor):
    bbox = {
        "Height": 0.012075720354914665,
        "Left": 0.4700376093387604,
        "Top": 0.04688919335603714,
        "Width": 0.46342989802360535,
    }
    expected = {"Height": 0.01, "Left": 0.47, "Top": 0.05, "Width": 0.46}
    result = pdf_class._round_textract_bounding_box(bbox)
    assert result == expected


def test_values_get_document_headers(pdf_class: PDFExtractor):
    # Check if each are in since the order is not always correct
    # in `_get_document_header_footers`
    expected = {"Height": 0.01, "Left": 0.07, "Top": 0.05, "Width": 0.03}
    pdf_class.textract = textract_headers_footers

    pdf_class._get_document_headers()

    assert expected in pdf_class.header_boxes


def test_values_get_document_footers(pdf_class: PDFExtractor):
    expected = {"Height": 0.01, "Left": 0.16, "Top": 0.95, "Width": 0.66}
    pdf_class.textract = textract_headers_footers

    pdf_class._get_document_footers()

    assert expected in pdf_class.footer_boxes


@pytest.mark.parametrize("block,expected", is_header_list)
def test_is_item_header(pdf_class: PDFExtractor, block: dict, expected: bool):
    pdf_class.textract = textract_headers_footers
    pdf_class._get_document_headers()

    assert pdf_class._is_item_header(block) is expected


@pytest.mark.parametrize("block,expected", is_footer_list)
def test_is_item_footer(pdf_class: PDFExtractor, block: dict, expected: bool):
    pdf_class.textract = textract_headers_footers
    pdf_class._get_document_footers()

    assert pdf_class._is_item_footer(block) is expected


@pytest.mark.parametrize("block_one,block_two,expected", textract_lines)
def test_textract_is_same_line(pdf_class: PDFExtractor, block_one, block_two, expected):
    assert pdf_class._textract_is_same_line(block_one, block_two) == expected


def test_textract_merge_text_blocks_valid(pdf_class: PDFExtractor):
    block_one = {
        "Text": "This is",
        "Geometry": {
            "BoundingBox": {
                "Height": 0.007,
                "Left": 0.538,
                "Top": 0.728,
                "Width": 0.128,
            }
        },
    }
    block_two = {
        "Text": "a test",
        "Geometry": {
            "BoundingBox": {
                "Height": 0.003,
                "Left": 0.05,
                "Top": 0.013,
                "Width": 0.4,
            }
        },
    }

    expected_text = "This is a test"
    expected_bbox = {
        "Height": 0.007,  # Expecting highest height
        "Left": 0.05,  # Gets furthest left
        "Top": 0.728,  # Get's tallest top
        "Width": 0.528,  # Combines width
    }

    response = pdf_class._textract_merge_text_blocks(block_one, block_two)

    assert (
        response["Text"] == expected_text
        and response["Geometry"]["BoundingBox"] == expected_bbox
    )


def test_increase_tag_value_min(pdf_class: PDFExtractor):
    response = pdf_class._increase_tag_value("h1")
    assert response == "h1"


def test_increase_tag_value_valid(pdf_class: PDFExtractor):
    response = pdf_class._increase_tag_value("h3")
    assert response == "h2"


def test_increase_tag_value_invalid(pdf_class: PDFExtractor):
    with pytest.raises(ValueError):
        pdf_class._increase_tag_value("h8")


def test_assign_valid_h1_multi_h2(pdf_class: PDFExtractor):
    data = {
        "1": "h2",
        "2": "h2",
        "3": "h3",
        "4": "h4",
    }

    response = pdf_class._assign_valid_h1(data)

    assert response == data


def test_assign_valid_h1_h2_second(pdf_class: PDFExtractor):
    data = {
        "1": "h3",
        "2": "h2",
        "3": "h3",
        "4": "h4",
    }

    response = pdf_class._assign_valid_h1(data)

    assert response == data


def test_assign_valid_h1_valid(pdf_class: PDFExtractor):
    data = {
        "1": "h2",
        "2": "h3",
        "3": "h3",
        "4": "h4",
    }

    expected = {
        "1": "h1",
        "2": "h2",
        "3": "h2",
        "4": "h3",
    }

    response = pdf_class._assign_valid_h1(data)

    assert response == expected


def test_textract_merge_lines(pdf_class: PDFExtractor):
    line_one_dict = {
        "text": "business-to- ",
        "bbox": {
            "x0": 0.007,
            "y0": 0.538,
            "x1": 0.728,
            "y1": 0.128,
        },
    }
    line_two_dict = {
        "text": "business",
        "bbox": {
            "x0": 0.003,
            "y0": 0.05,
            "x1": 0.013,
            "y1": 0.4,
        },
    }
    line_one = TextractLine(
        line_one_dict["text"],
        Rect(
            line_one_dict["bbox"]["x0"],
            line_one_dict["bbox"]["y0"],
            line_one_dict["bbox"]["x1"],
            line_one_dict["bbox"]["y1"],
        ),
    )
    line_two = TextractLine(
        line_two_dict["text"],
        Rect(
            line_two_dict["bbox"]["x0"],
            line_two_dict["bbox"]["y0"],
            line_two_dict["bbox"]["x1"],
            line_two_dict["bbox"]["y1"],
        ),
    )

    expected_text = "business-to-business"
    expected_bbox = Rect(0.003, 0.05, 0.728, 0.4)

    response = pdf_class._textract_merge_lines(line_one, line_two)
    assert response.text == expected_text and response.bbox == expected_bbox


def test_reduce_text_blocks(pdf_class: PDFExtractor):
    rect_one = {
        "text": " 36. Grönroos, C. (1997) 'Value-driven relational marketing: From products to resources and competencies', Journal of Marketing Management, Vol.",
        "sizes": [10, 10, 7, 7],
        "formats": [
            ExtractFormat(
                type="none",
                size=10,
                value="36. Grönroos, C. (1997) 'Value-driven relational",
                bbox=[
                    363.0224275588989,
                    220.87621226906776,
                    521.4751967787743,
                    228.86424670927227,
                ],
                font="Comic Sans",
                url=None,
            ),
            ExtractFormat(
                type="none",
                size=10,
                value=" ",
                bbox=[
                    521.4751967787743,
                    220.87621226906776,
                    521.6751967787743,
                    228.86424670927227,
                ],
                font="Comic Sans",
                url=None,
            ),
            ExtractFormat(
                type="none",
                size=7.970200061798096,
                value="marketing: From products to resources and",
                bbox=[
                    377.462158203125,
                    230.89279174804688,
                    517.3583984375,
                    238.8629913330078,
                ],
                font="AdvM2701",
                url=None,
            ),
            ExtractFormat(
                type="none",
                size=7.970200061798096,
                value=" ",
                bbox=[
                    517.3583984375,
                    230.89279174804688,
                    517.5583984375,
                    238.8629913330078,
                ],
                font="AdvM2701",
                url=None,
            ),
            ExtractFormat(
                type="none",
                size=7.970200061798096,
                value="competencies’,",
                bbox=[
                    377.462158203125,
                    240.87069702148438,
                    424.232177734375,
                    248.8408966064453,
                ],
                font="AdvM2701",
                url=None,
            ),
            ExtractFormat(
                type="none",
                size=7.970200061798096,
                value=" Journal of Marketing Management",
                bbox=[
                    424.232177734375,
                    240.87069702148438,
                    529.8236694335938,
                    248.8408966064453,
                ],
                font="AdvM2702",
                url=None,
            ),
            ExtractFormat(
                type="none",
                size=7.970200061798096,
                value=", Vol.",
                bbox=[
                    529.8531494140625,
                    240.87069702148438,
                    548.3743896484375,
                    248.8408966064453,
                ],
                font="AdvM2701",
                url=None,
            ),
            ExtractFormat(
                type="none",
                size=7.970200061798096,
                value=" ",
                bbox=[
                    548.3743896484375,
                    240.87069702148438,
                    548.5743896484375,
                    248.8408966064453,
                ],
                font="AdvM2701",
                url=None,
            ),
        ],
    }
    rect_two = {
        "text": "13, pp 407-419.",
        "sizes": [7],
        "formats": [
            ExtractFormat(
                type="none",
                size=7.970200061798096,
                value="13, pp 407–419.",
                bbox=[
                    377.4621276855469,
                    250.84860229492188,
                    428.46258544921875,
                    258.8188171386719,
                ],
                font="AdvM2701",
                url=None,
            ),
            ExtractFormat(
                type="none",
                size=7.970200061798096,
                value=" ",
                bbox=[
                    428.46258544921875,
                    250.84860229492188,
                    428.66258544921874,
                    258.8188171386719,
                ],
                font="AdvM2701",
                url=None,
            ),
        ],
    }
    expected_text = " 36. Grönroos, C. (1997) 'Value-driven relational marketing: From products to resources and competencies', Journal of Marketing Management, Vol. 13, pp 407-419."
    expected_sizes = [10, 10, 7, 7, 7]
    expected_formats = [
        ExtractFormat(
            type="none",
            size=10,
            value="36. Grönroos, C. (1997) 'Value-driven relational",
            bbox=[
                363.0224275588989,
                220.87621226906776,
                521.4751967787743,
                228.86424670927227,
            ],
            font="Comic Sans",
            url=None,
        ),
        ExtractFormat(
            type="none",
            size=10,
            value=" ",
            bbox=[
                521.4751967787743,
                220.87621226906776,
                521.6751967787743,
                228.86424670927227,
            ],
            font="Comic Sans",
            url=None,
        ),
        ExtractFormat(
            type="none",
            size=7.970200061798096,
            value="marketing: From products to resources and",
            bbox=[
                377.462158203125,
                230.89279174804688,
                517.3583984375,
                238.8629913330078,
            ],
            font="AdvM2701",
            url=None,
        ),
        ExtractFormat(
            type="none",
            size=7.970200061798096,
            value=" ",
            bbox=[
                517.3583984375,
                230.89279174804688,
                517.5583984375,
                238.8629913330078,
            ],
            font="AdvM2701",
            url=None,
        ),
        ExtractFormat(
            type="none",
            size=7.970200061798096,
            value="competencies’,",
            bbox=[
                377.462158203125,
                240.87069702148438,
                424.232177734375,
                248.8408966064453,
            ],
            font="AdvM2701",
            url=None,
        ),
        ExtractFormat(
            type="none",
            size=7.970200061798096,
            value=" Journal of Marketing Management",
            bbox=[
                424.232177734375,
                240.87069702148438,
                529.8236694335938,
                248.8408966064453,
            ],
            font="AdvM2702",
            url=None,
        ),
        ExtractFormat(
            type="none",
            size=7.970200061798096,
            value=", Vol.",
            bbox=[
                529.8531494140625,
                240.87069702148438,
                548.3743896484375,
                248.8408966064453,
            ],
            font="AdvM2701",
            url=None,
        ),
        ExtractFormat(
            type="none",
            size=7.970200061798096,
            value=" ",
            bbox=[
                548.3743896484375,
                240.87069702148438,
                548.5743896484375,
                248.8408966064453,
            ],
            font="AdvM2701",
            url=None,
        ),
        ExtractFormat(
            type="none",
            size=7.970200061798096,
            value="13, pp 407–419.",
            bbox=[
                377.4621276855469,
                250.84860229492188,
                428.46258544921875,
                258.8188171386719,
            ],
            font="AdvM2701",
            url=None,
        ),
        ExtractFormat(
            type="none",
            size=7.970200061798096,
            value=" ",
            bbox=[
                428.46258544921875,
                250.84860229492188,
                428.66258544921874,
                258.8188171386719,
            ],
            font="AdvM2701",
            url=None,
        ),
    ]

    response = pdf_class._reduce_text_blocks(rect_one, rect_two)
    assert (
        response["text"] == expected_text
        and response["sizes"] == expected_sizes
        and response["formats"] == expected_formats
    )


def test_is_within_bbox(pdf_class: PDFExtractor):
    rect = Rect(
        70.4368507862091,
        295.7661699652672,
        523.9042848348618,
        318.1494755707681,
    )

    line = {
        "text": "https://www.monkeyinmychair.org/upload/news/Teacher%20Companion%20for%20web2.pdf ]. ",
        "sizes": [9],
        "formats": [
            ExtractFormat(
                type="none",
                size=9.956949234008789,
                value="https://www.monkeyinmychair.org/upload/news/Teacher%20Companion%20for%20web2.pdf ]. ",
                bbox=[
                    90.96088409423828,
                    306.60345458984375,
                    481.5296325683594,
                    317.6333923339844,
                ],
                font="TimesNewRoman",
                url=None,
            )
        ],
        "bbox": [
            90.96088409423828,
            306.60345458984375,
            481.5296325683594,
            317.6333923339844,
        ],
    }
    common_height = 11.03

    assert pdf_class._is_within_bbox(rect, line, common_height)


def test_is_within_bbox_failure(pdf_class: PDFExtractor):
    rect = Rect(
        70.4368507862091,
        295.7661699652672,
        523.9042848348618,
        318.1494755707681,
    )

    line = {
        "text": " Invalid text ",
        "sizes": [9],
        "formats": [
            ExtractFormat(
                type="none",
                size=9.956949234008789,
                value=" ",
                bbox=[
                    70.9211654663086,
                    318.12347412109375,
                    73.40963745117188,
                    329.1534118652344,
                ],
                font="TimesNewRoman",
                url=None,
            )
        ],
        "bbox": [
            70.9211654663086,
            318.12347412109375,
            73.40963745117188,
            329.1534118652344,
        ],
    }

    common_height = 11.03

    assert not pdf_class._is_within_bbox(rect, line, common_height)
