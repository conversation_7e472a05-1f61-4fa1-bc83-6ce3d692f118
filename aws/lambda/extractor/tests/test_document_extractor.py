import pytest  # type: ignore
from typing import List
from extractor.document_structure import (
    ExtractedParagraphSegment,
    Section,
    ExtractedTOC,
    ExtractedListSegment,
    ExtractedListItem,
)
import copy
from ..document_extractor import DocumentExtractor
from .raw_data.sections import sections
from .raw_data.merging_lists import (
    split_up_list_1,
    split_up_list_2,
    split_up_list_3,
    split_up_list_4,
    split_up_list_5,
    split_up_list_6,
)
from .raw_data.footnote_pages import (
    no_footnotes_page,
    multiple_footnote_page,
    single_footnote_page,
)


@pytest.fixture()
def extractor_class():
    yield DocumentExtractor(stream=None)


most_common_max_list = (
    ([14, 14, 11, 12, 20, 5, 5, 5], 5),
    ([20, 14, 15, 55, 1, 25, 11, 6], 55),
    ([44, 1, 2, 4, 5, 11, 11, 12, 12], 11),
)


def test_generate_toc_empty(extractor_class: DocumentExtractor):
    """
    Tests empty sections as a sanity check for empty data
    """
    empty_test = extractor_class._generate_toc({})
    assert empty_test == []


def test_generate_toc_single(extractor_class: DocumentExtractor):
    """
    Tests single toc item to make sure we are handling single titled documents
    """
    section = Section(
        id="1", title="Single Title", font_size=12, content=tuple([]), tag="h1"
    )
    single_data = {"1": section}

    single_test = extractor_class._generate_toc(single_data)
    expected_result = [
        ExtractedTOC(
            title=section.title,
            size=section.font_size,  # type: ignore
            id=section.id,
            tag=section.tag,
            sections=[],
        )
    ]

    assert single_test == expected_result


def test_generate_toc_indented(extractor_class: DocumentExtractor):
    """
    Tests embedded sections to ensure subsections are added as such in the
    generated toc
    """
    result = extractor_class._generate_toc(sections)
    # TODO: Should this be here or in the file where sections is stored?
    expected_result = [
        ExtractedTOC(
            title="Title",
            size=18,
            id="1",
            tag="h1",
            sections=[],
        ),
        ExtractedTOC(
            title="Heading 1",
            size=10,
            id="2",
            tag="h4",
            sections=[],
        ),
        ExtractedTOC(
            title="Heading 2",
            size=10,
            id="3",
            tag="h4",
            sections=[],
        ),
        ExtractedTOC(
            title="Heading 3",
            size=10,
            id="4",
            tag="h4",
            sections=[],
        ),
        ExtractedTOC(
            title="Heading 4",
            size=11,
            id="5",
            tag="h2",
            sections=[
                ExtractedTOC(
                    title="SubHeading 1",
                    size=7,
                    id="0.1",
                    tag="h5",
                    sections=[],
                )
            ],
        ),
        ExtractedTOC(
            title="Method",
            size=11,
            id="6",
            tag="h2",
            sections=[
                ExtractedTOC(
                    title="SubHeading 2",
                    size=11,
                    id="0.2",
                    tag="h3",
                    sections=[],
                ),
                ExtractedTOC(
                    title="SubHeading 3",
                    size=11,
                    id="0.3",
                    tag="h3",
                    sections=[],
                ),
            ],
        ),
    ]
    assert result == expected_result


def test_ordered_list_continuation(extractor_class: DocumentExtractor):
    list_1_copy = copy.deepcopy(split_up_list_1)
    list_4_copy = copy.deepcopy(split_up_list_4)
    result = extractor_class._attempt_list_merge(list_1_copy, list_4_copy)

    # TODO: Move these into their own dataset file
    expected_result = ExtractedListSegment(
        format="ordered",
        items=[
            ExtractedListItem(
                value="1. First Item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="2. Second Item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="3. Third Item",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="4. Fourth item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="5. Fifth item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
        ],
        id="1",
        type="list",
    )

    assert result == expected_result


def test_ordered_list_continuation_split_paragraph(
    extractor_class: DocumentExtractor,
):
    """
    Tests that sections that are split up
    """
    list_1_copy = copy.deepcopy(split_up_list_1)
    list_2_copy = copy.deepcopy(split_up_list_2)
    result = extractor_class._attempt_list_merge(list_1_copy, list_2_copy)

    # TODO: Move these into their own dataset file
    expected_result = ExtractedListSegment(
        format="ordered",
        items=[
            ExtractedListItem(
                value="1. First Item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="2. Second Item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="3. Third Item Third item extended.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="4. Fifth item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
        ],
        id="1",
        type="list",
    )

    assert result == expected_result


def test_ordered_list_continuation_non_stripped_split_paragraph(
    extractor_class: DocumentExtractor,
):
    """
    Tests that sections that are split up
    """
    list_5_copy = copy.deepcopy(split_up_list_5)
    list_6_copy = copy.deepcopy(split_up_list_6)
    result = extractor_class._attempt_list_merge(list_5_copy, list_6_copy)

    print(result)

    # TODO: Move these into their own dataset file
    expected_result = ExtractedListSegment(
        format="ordered",
        items=[
            ExtractedListItem(
                value="1. First Item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="2. Second Item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="3. Third Item Third item extended.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="4. Fifth item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
        ],
        id="1",
        type="list",
    )

    assert result == expected_result


def test_different_list_type_continuation(extractor_class: DocumentExtractor):
    """
    Tests that lists of different types can still be merged if detected to be
    continued
    """
    list_1_copy = copy.deepcopy(split_up_list_1)
    list_3_copy = copy.deepcopy(split_up_list_3)
    result = extractor_class._attempt_list_merge(list_1_copy, list_3_copy)

    # TODO: Move these into their own dataset file
    expected_result = ExtractedListSegment(
        format="ordered",
        items=[
            ExtractedListItem(
                value="1. First Item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="2. Second Item.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="3. Third Item third item extended.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
            ExtractedListItem(
                value="Fourth item lives here.",
                items=[],
                bbox=[
                    62.3654994057124,
                    386.4004770033789,
                    110.48812078380615,
                    395.6968239345668,
                ],
            ),
        ],
        id="1",
        type="list",
    )

    assert result == expected_result


def test_set_extracted_title_no_h1(extractor_class: DocumentExtractor):
    """
    Should set the first item of the list as a h1
    """
    section_list = [
        Section(
            id="1",
            title="Heading 1",
            font_size=8,
            content=[],
            tag="h2",
        ),
        Section(
            id="2",
            title="Heading 2",
            font_size=8,
            content=[],
            tag="h2",
        ),
        Section(
            id="3",
            title="Heading 3",
            font_size=8,
            content=[],
            tag="h2",
        ),
    ]

    extractor_class._set_extracted_title(section_list)

    assert extractor_class.extracted_title == "Heading 1"


def test_set_extracted_title_has_h1(extractor_class: DocumentExtractor):
    """
    Should go for the item that has been marked as a h1
    """
    section_list = [
        Section(
            id="1",
            title="Heading 1",
            font_size=8,
            content=[],
            tag="h2",
        ),
        Section(
            id="2",
            title="Heading 2",
            font_size=8,
            content=[],
            tag="h2",
        ),
        Section(
            id="3",
            title="Heading 3",
            font_size=8,
            content=[],
            tag="h1",
        ),
        Section(
            id="4",
            title="Heading 4",
            font_size=8,
            content=[],
            tag="h2",
        ),
    ]

    extractor_class._set_extracted_title(section_list)

    assert extractor_class.extracted_title == "Heading 3"


def are_paragraphs_merged(
    edited_list: List[ExtractedParagraphSegment],
    original_list: List[ExtractedParagraphSegment],
) -> bool:
    combined_text = original_list[0].text + " " + original_list[1].text
    combined_formats = original_list[0].formats + original_list[1].formats

    return (
        len(edited_list) == 1
        and edited_list[0].text == combined_text
        and edited_list[0].formats == combined_formats
    )


@pytest.mark.parametrize("list,expected", most_common_max_list)
def test_get_most_common_or_max(extractor_class: DocumentExtractor, list, expected):
    assert extractor_class._get_most_common_or_max(list) == expected


def validate_footnotes(footnotes, pages):

    for item in pages[0].content:
        for format in item.formats:
            if format.type != "super":
                continue

            relationships = format.relationships

            if len(footnotes.keys()) > 0:
                if (
                    # As of right now should only have 1 relationship
                    len(relationships) != 1
                    or relationships[0].type != "footnote"
                    or relationships[0].id not in footnotes.keys()
                ):
                    return False

            else:
                if len(relationships) > 0:
                    return False

    return True


def test_link_footnotes_single(extractor_class: DocumentExtractor):
    footnotes = extractor_class._link_footnotes(single_footnote_page)
    assert footnotes is not None and len(footnotes.keys()) == 1

    assert validate_footnotes(footnotes, single_footnote_page)


def test_link_footnotes_multiple(extractor_class: DocumentExtractor):
    footnotes = extractor_class._link_footnotes(multiple_footnote_page)
    assert footnotes is not None and len(footnotes.keys()) == 1

    assert validate_footnotes(footnotes, multiple_footnote_page)


def test_link_footnotes_none(extractor_class: DocumentExtractor):
    footnotes = extractor_class._link_footnotes(no_footnotes_page)
    assert footnotes is not None and len(footnotes.keys()) == 0

    assert validate_footnotes(footnotes, no_footnotes_page)
