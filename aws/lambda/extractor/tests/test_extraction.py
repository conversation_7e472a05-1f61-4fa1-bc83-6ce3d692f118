import pytest
import json
from extractor import PDFExtractor, DocxExtractor
from .test_documents.pdf_data import export_data, PdfData
from .test_documents.word_data import word_data, WordData

docx_list = [item for item in word_data.values()]
pdf_list = [item for item in export_data.values()]


@pytest.mark.parametrize("data", docx_list, ids=[item.name for item in docx_list])
def test_docx(data: WordData):
    extractor = DocxExtractor(stream=open(data.doc_path, "rb").read())

    assert extractor.get_extracted_data()


@pytest.mark.parametrize("data", pdf_list, ids=[item.name for item in pdf_list])
def test_pdf(data: PdfData):
    textract_json = json.loads(open(data.textract_path).read())
    extractor = PDFExtractor(
        stream=open(data.pdf_path, "rb").read(), textract=textract_json
    )

    assert extractor.get_extracted_data()
