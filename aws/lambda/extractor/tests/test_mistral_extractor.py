import json
import logging
import os
import pathlib
from typing import Any, Literal
from unittest.mock import MagicMock

import pytest
from bs4 import BeautifulSoup

from ..document_structure import (
    ExtractedDetails,
    ExtractedHeadingSegment,
    ExtractedImagesSegment,
    ExtractedPage,
    ExtractedParagraphSegment,
)
from ..mistral_extractor import MistralExtractor

LOGGER = logging.getLogger(__name__)

# Get the path to the sample Mistral output JSON
cur_path = str(pathlib.Path(__file__).parent.resolve())
SAMPLE_MISTRAL_OUTPUT_PATH = os.path.join(cur_path, "test_documents/mistral/")

def load_example(file_name) -> dict[str, Any]:
    with open(SAMPLE_MISTRAL_OUTPUT_PATH + str(file_name + ".json")) as f:
        return json.load(f)

@pytest.fixture
def mistral_extractor() -> MistralExtractor:
    # Create a MistralExtractor instance with a dummy PDF stream
    extractor = MistralExtractor(b"dummy_pdf_content")
    # Set a base font size for testing
    extractor.base_font = 12
    return extractor


def test_process_mistral_output(mistral_extractor, monkeypatch):
    """Test the _process_mistral_output method with a sample Mistral output"""

    # Mock uuid4 to return predictable IDs for testing
    mock_ids = ["id1", "id2", "id3", "id4", "id5", "id6", "id7"]
    mock_id_generator = iter(mock_ids)

    def mock_uuid4() -> MagicMock:
        mock_id = MagicMock()
        mock_id.hex = next(mock_id_generator)
        return mock_id

    monkeypatch.setattr("uuid.uuid4", mock_uuid4)

    # Process the sample Mistral output
    document_details, extracted_pages = mistral_extractor._process_mistral_output(load_example("sample_output"))

    # Print the actual content for debugging
    LOGGER.debug("Actual content types:")
    for item in extracted_pages[0].content:
        LOGGER.debug(f"  {type(item).__name__}: {getattr(item, 'title', getattr(item, 'text', ''))}")

    # Verify document details
    assert isinstance(document_details, ExtractedDetails)
    assert document_details.page_count == 1
    assert document_details.base_font_size == 12
    assert isinstance(document_details.toc, list)

    # Verify extracted pages
    assert isinstance(extracted_pages, list)
    assert len(extracted_pages) == 1
    assert isinstance(extracted_pages[0], ExtractedPage)
    assert extracted_pages[0].page == 1

    # Verify content extraction
    content = extracted_pages[0].content
    assert len(content) > 0

    # Check for heading extraction
    headings = [item for item in content if isinstance(item, ExtractedHeadingSegment)]
    assert len(headings) > 0

    # The exact titles may vary based on how the markdown is parsed
    # Just check that we have headings

    # Check for paragraph extraction
    paragraphs = [item for item in content if isinstance(item, ExtractedParagraphSegment)]
    assert len(paragraphs) > 0

    # Lists may be parsed differently depending on the markdown parser
    # They might be parsed as paragraphs or other elements
    # So we don't assert on them specifically

    # Check for image extraction
    images = [item for item in content if isinstance(item, ExtractedImagesSegment)]
    if images:
        assert images[0].width > 0
        assert images[0].height > 0


def test_nested_tag_extraction(mistral_extractor):
    """Test the extraction of nested tags in different positions (beginning, middle, end)"""

    # Create HTML with nested tags in different positions
    html_with_nested_tags = """
    <p><h2>Start at beginning</h2> of paragraph.</p>
    <p>Middle of <h2>Nested in middle</h2> paragraph.</p>
    <p>End of paragraph <h2>Nested at end</h2></p>
    """
    soup = BeautifulSoup(html_with_nested_tags, "html.parser")

    # Apply the nested tag extraction
    mistral_extractor._expose_nested_top_level_tags(soup)
    # Get all top-level tags after extraction
    top_level_tags = [tag.name for tag in soup.find_all(recursive=False)]

    # Check that all h2 tags are now at the top level
    assert top_level_tags.count("h2") == 3
    assert top_level_tags.count("p") >= 3  # May be more if paragraphs were split

    # Check the content of the first paragraph (with nested tag at beginning)
    first_p = soup.find_all("p")[0]
    assert " of paragraph." in first_p.text
    assert "Nested at beginning" not in first_p.text

    # Check the content of the second paragraph (with nested tag in middle)
    second_p_parts = [p for p in soup.find_all("p") if "Middle" in p.text]
    assert len(second_p_parts) > 0
    assert "Nested in middle" not in second_p_parts[0].text

    # Check the content of the third paragraph (with nested tag at end)
    third_p = [p for p in soup.find_all("p") if "End of paragraph" in p.text][0]
    assert "Nested at end" not in third_p.text


def test_run_method_calls_process_mistral_output(mistral_extractor, monkeypatch):
    """Test that the run method calls extract_from_mistral and _process_mistral_output"""

    # Mock the extract_from_mistral method
    def mock_extract_from_mistral():
        return load_example("sample_output")

    monkeypatch.setattr(mistral_extractor, "extract_from_mistral", mock_extract_from_mistral)

    # Create a spy on the _process_mistral_output method
    original_process_method = mistral_extractor._process_mistral_output
    process_spy = MagicMock(side_effect=original_process_method)
    monkeypatch.setattr(mistral_extractor, "_process_mistral_output", process_spy)

    # Call the run method
    result = mistral_extractor.run()

    # Output the result for debugging in a way that is visible in testing
    LOGGER.debug(f"Run method result: {result}")

    # Verify that extract_from_mistral was called
    assert isinstance(result, tuple)
    assert len(result) == 2
    assert isinstance(result[0], ExtractedDetails)
    assert isinstance(result[1], list)

    # Verify that _process_mistral_output was called with the correct arguments
    process_spy.assert_called_once_with(load_example("sample_output"))

def _process_mistral_output(
    mistral_extractor: MistralExtractor,
    json_data: dict,
    monkeypatch: pytest.MonkeyPatch,
) -> list[dict[str, Any]]:
    """Helper function to process Mistral output and return document structure"""
    # Mock the extract_from_mistral method to return the provided JSON
    def mock_extract_from_mistral() -> dict:
        return json_data

    monkeypatch.setattr(mistral_extractor, "extract_from_mistral", mock_extract_from_mistral)

    # Call the method under test
    result = mistral_extractor.get_extracted_data()

    # Verify the result is an ExtractedDocument
    assert result is not None

    # Verify sections were created
    assert len(result.sections) > 0

    # Extract document structure
    document_structure = []

    for section in result.sections.values():
        # Extract section information
        section_info = {
            "tag": getattr(section, "tag", None),
            "title": section.title,
            "content": [],
        }

        # Extract content types from section
        for item in section.content:
            content_type = getattr(item, "type", None)
            section_info["content"].append(content_type)

        document_structure.append(section_info)

    LOGGER.debug("Document structure: %s", document_structure)

    return document_structure

# Define test cases with expected document structure
MISTRAL_TEST_CASES: list[dict[Literal["name", "expected_structure"], Any]] = [
    {
        "name": "sample_output",
        "expected_structure": [
            {
                "tag": "h1",
                "title": "How Cultures Change",
                "content": ["paragraph", "paragraph", "paragraph", "img", "paragraph",
                           "section", "section", "section"],
            },
            {
                "tag": "h2",
                "title": "15. CULTURAL ECOLOGY, CULTURAL",
                "content": ["paragraph"],
            },
            {
                "tag": "h2",
                "title": "Evolution-Biological and Sociocultural",
                "content": [
                    "paragraph", "paragraph", "paragraph", "paragraph",
                    "paragraph", "paragraph",
                ],
            },
            {
                "tag": "h2",
                "title": "Systems-Theoretic Anthropological",
                "content": ["paragraph", "paragraph", "paragraph", "paragraph", "paragraph"],
            },
        ],
    },
    {
        "name": "with-table",
        "expected_structure": [
            {
                "tag": "h1",
                "title": "Document Title (Heading Style 1)",
                "content": ["section", "section", "section", "section", "section", "section",
                           "section", "section", "section", "section", "section", "section",
                           "section", "section"],
            },
            {
                "tag": "h2",
                "title": "Topic 1 (Heading Style 2)",
                "content": ["paragraph"],
            },
            {
                "tag": "h2",
                "title": "Topic 2 (Heading Style 2)",
                "content": [],
            },
            {
                "tag": "h2",
                "title": "Subtopic A (Heading Style 3)",
                "content": ["paragraph"],
            },
            {
                "tag": "h2",
                "title": "Subtopic B (Heading Style 3)",
                "content": ["paragraph", "paragraph"],
            },
            {
                "tag": "h2",
                "title": "Topic 3 (Heading Style 2)",
                "content": ["paragraph"],
            },
            {
                "tag": "h2",
                "title": "Topic 4 (Heading Style 2)",
                "content": ["table"],
            },
            {
                "tag": "h2",
                "title": "Topic 5 (Heading Style 2)",
                "content": ["paragraph", "img"],
            },
            {
                "tag": "h2",
                "title": "Topic 6 (Heading Style 2)",
                "content": ["paragraph"],
            },
            {
                "tag": "h2",
                "title": "Topic 7 (Heading Style 2)",
                "content": ["paragraph"],
            },
            {
                "tag": "h2",
                "title": "Topic 8 (Heading Style 2)",
                "content": [],
            },
            {
                "tag": "h2",
                "title": "Subtopic A (Heading Style 3)",
                "content": ["paragraph"],
            },
            {
                "tag": "h2",
                "title": "Subtopic B (Heading Style 3)",
                "content": ["paragraph"],
            },
            {
                "tag": "h2",
                "title": "Topic 9 (Heading Style 2)",
                "content": ["paragraph"],
            },
            {
                "tag": "h2",
                "title": "Topic 10 (Heading Style 2)",
                "content": ["paragraph"],
            },
        ],
    },
    {
        "name": "with-maths",
        "expected_structure": [
            {
                "tag": "h1",
                "title": "Document",
                "content": ["paragraph", "math", "paragraph", "paragraph"]
            },
            {
                "tag": "h1",
                "title": "Error in Using Reduced Clocks",
                "content": ["paragraph", "paragraph"]
            }
        ]
    },
    # Additional test cases can be added here
]

@pytest.fixture
def mistral_test_case(request: pytest.FixtureRequest) -> dict[str, Any]:
    """Fixture that provides test case data to the test function"""
    return request.param

@pytest.mark.parametrize(
    "mistral_test_case",
    MISTRAL_TEST_CASES,
    ids=[test_case["name"] for test_case in MISTRAL_TEST_CASES],
)
def test_get_extracted_data_with_sample_json(
    mistral_extractor: MistralExtractor,
    monkeypatch: pytest.MonkeyPatch,
    mistral_test_case: dict[str, Any],
):
    """Test get_extracted_data with multiple sample JSON files and verify document structure"""
    # Get the test case from the parameter
    test_case = mistral_test_case

    LOGGER.debug("Testing Mistral extraction with %s", test_case["name"])

    json_data = load_example(test_case["name"])

    # Process the Mistral output
    document_structure = _process_mistral_output(
        mistral_extractor,
        json_data,
        monkeypatch,
    )

    # Verify document structure matches expected structure
    assert len(document_structure) == len(test_case["expected_structure"]), (
        f"Document structure for {test_case['name']} has wrong number of sections.\n"
        f"Expected: {len(test_case['expected_structure'])} sections\n"
        f"Actual: {len(document_structure)} sections"
    )

    # Compare each section
    expected_structure = test_case["expected_structure"]
    for i, (actual, expected) in enumerate(zip(document_structure, expected_structure)):
        # Check tag
        assert actual["tag"] == expected["tag"], (
            f"Section {i} tag doesn't match expected tag.\n"
            f"Expected: {expected['tag']}\n"
            f"Actual: {actual['tag']}"
        )

        # Check title
        assert actual["title"] == expected["title"], (
            f"Section {i} title doesn't match expected title.\n"
            f"Expected: {expected['title']}\n"
            f"Actual: {actual['title']}"
        )

        # Check content types
        assert actual["content"] == expected["content"], (
            f"Section {i} content doesn't match expected content.\n"
            f"Expected: {expected['content']}\n"
            f"Actual: {actual['content']}"
        )

    # Verify that we have at least one section
    assert len(document_structure) > 0, "No sections found in document"
