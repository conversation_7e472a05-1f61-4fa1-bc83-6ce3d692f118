from typing import Dict, List
import pytest  # type: ignore
import json
from extractor.pdf_extractor import PDFExtractor
from .raw_data.document_tocs import TOCItem, pdf_tocs
from .test_documents.pdf_data import PdfData


def refine_toc(items: List) -> List[TOCItem]:
    ret_list: List[TOCItem] = []
    for item in items:
        item = TOCItem(
            item.title, refine_toc(item.sections) if len(item.sections) > 0 else []
        )
        ret_list.append(item)
    return ret_list


@pytest.mark.parametrize("key", pdf_tocs, ids=[key for key in pdf_tocs.keys()])
def test_pdf(key: str):
    pdf_data: Dict = pdf_tocs[key]
    data: PdfData = pdf_data["data"]
    toc = pdf_data["toc"]

    textract_json = json.loads(open(data.textract_path).read())
    extractor = PDFExtractor(
        stream=open(data.pdf_path, "rb").read(), textract=textract_json
    )

    extracted_data = extractor.get_extracted_data()

    extracted_toc = refine_toc(extracted_data.details.toc)
    print(extracted_toc)

    assert extracted_toc == toc
