{"pages": [{"index": 0, "markdown": "Rather than have the probability of the event at $k$ be uniform across the event(s) with the lowest clock ( $\\operatorname{argmin}_{e^{\\prime}}^{\\prime} \\mathbf{C}_{e^{\\prime}, k}$ ), in the absence of the uncontrollable clocks we must assign some probability to all uncontrollable events $\\mathcal{E} \\backslash \\mathcal{E}^{\\text {ctrl }}$ as well as the event(s) with the lowest clock. This is still done in a uniform manner.\n\n$$\n\\begin{aligned}\n& \\text { Original: } \\quad P\\left(\\mathbf{I}_{k} \\mid \\overrightarrow{\\mathbf{C}}_{k}\\right)=P\\left(\\mathbf{I}_{k}=\\min _{e^{\\prime}} \\mathbf{C}_{e^{\\prime}, k}\\right) \\\\\n& \\text { Modified: } \\quad P\\left(\\mathbf{I}_{k} \\mid \\mathbf{E}_{K}, \\overrightarrow{\\mathbf{C}}_{k}\\right)=\\left\\{\\begin{array}{ll}\nP\\left(\\mathbf{I}_{k}=\\mathbf{C}_{\\mathbf{E}_{k}, k}\\right) & \\text { if } \\mathbf{E}_{k} \\in \\mathcal{E}^{\\text {ctrl }} \\\\\n\\frac{1}{\\min I+1} & \\text { if } \\mathbf{E}_{k} \\notin \\mathcal{E}^{\\text {ctrl }} \\wedge \\mathbf{I}_{k} \\leqslant \\min I \\\\\n0 & \\text { if } \\mathbf{E}_{k} \\notin \\mathcal{E}^{\\text {ctrl }} \\wedge \\mathbf{I}_{k}>\\min I\n\\end{array}\\right. \\\\\n& \\text { where } \\min I=\\min _{e^{\\prime}} \\mathbf{C}_{e^{\\prime}, k}\n\\end{aligned}\n$$\n\nThere are now two main cases for calculating the probability of the interval, and these depend on the current event (this adds an extra term to the conditions on the left of the probability in (6.11)). If the current event is controllable, then the interval must be the same as that of the event's clock. If instead the current event is not controllable then, similarly to the conditional distribution for the current event, the distribution over intervals is a uniform distribution across all intervals less than the lowest clock.\n\nThe reason for these changes is so that the probability for valid clocks is not set to 0 because it does not generate the event and interval values. Indeed, the model becomes discriminative, as the missing clocks mean that when smoothing is finished the trajectory cannot be reproduced using only the predicted clock values.\n\n# Error in Using Reduced Clocks \n\nOnly maintaining the clocks for controllable events will increase error in estimating clock values via smoothing. To evaluate the amount of added error, we performed smoothing with the artificial data created in $\\S 5.3 .2$, with parameters $3 \\leqslant|\\delta| \\leqslant 8,3 \\leqslant|\\mathcal{E}| \\leqslant 5 \\mid, 3 \\leqslant \\max _{e}\\left(\\left|\\mathcal{C}_{e}\\right|\\right) \\leqslant 5 \\mid$, $5 \\leqslant|\\sigma| \\leqslant 20$, averaged over 5 different $\\sigma$ per problem and 10 random problems of each size and configuration. Each problem was smoothed via the GSMPDBN from the last chapter and also the reduced clock version detailed above.\n\nThe results are given in fig. 6.1, plotting the GSMPDBN error on the x -axis, and the reduced-clock error on the $y$-axis. As in $\\S 5.3 .2$, the error is a mean error across all steps of the trajectory, and averaged across clocks such that an error of 1 means that 0 probability", "images": [], "dimensions": {"dpi": 200, "height": 2339, "width": 1653}}], "model": "mistral-ocr-2505-completion", "usage_info": {"pages_processed": 1, "doc_size_bytes": 5731690}}