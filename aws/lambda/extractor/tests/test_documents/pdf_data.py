from dataclasses import dataclass
import pathlib

cur_path = str(pathlib.Path(__file__).parent.resolve())

pdfs_path = cur_path + "/pdfs/"
textracts_path = cur_path + "/textract/"


@dataclass
class PdfData:
    pdf_path: str
    textract_path: str
    name: str


def __create_data(pdf_path, textract_path, name):
    return PdfData(f"{pdfs_path}{pdf_path}", f"{textracts_path}{textract_path}", name)


export_data: dict[str, PdfData] = {
    "annoying.pdf": __create_data("annoying.pdf", "annoying.json", "annoying"),
    "newsletter.pdf": __create_data(
        "newsletter Fri 23rd Feb 2024.pdf", "newsletter.json", "newsletter"
    ),
    "neurodivergent.pdf": __create_data(
        "neurodivergent.pdf", "neurodivergent.json", "neurodivergent"
    ),
    "is_trust_a_necessary_component.pdf": __create_data(
        "is_trust_a_necessary_component.pdf",
        "is_trust_a_necessary_component.json",
        "is_trust_a_necessary_component",
    ),
    "research_paper.pdf": __create_data(
        "research_paper.pdf",
        "research_paper.json",
        "research_paper",
    ),
}

# TAKES TOO LONG BUT COMMENTING JUST IN CASE
"""
"copy_of_accessibility.pdf": __create_data(
    "copy_of_accessibility.pdf",
    "copy_of_accessibility.json",
    "copy_of_accessibility",
),
"""
