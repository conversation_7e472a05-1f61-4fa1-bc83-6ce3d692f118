import os
from dataclasses import dataclass
import pathlib

cur_path = str(pathlib.Path(__file__).parent.resolve())

word_path = cur_path + "/word/"


@dataclass
class WordData:
    doc_path: str
    name: str


def __create_data(filename, name):
    return WordData(f"{word_path}{filename}", name)


def __get_data() -> dict[str, WordData]:
    pdf_list = list(
        filter(
            lambda item: item.endswith(".docx"),
            os.listdir(f"{word_path}"),
        ),
    )

    ret_val: dict[str, WordData] = {}
    for item in pdf_list:
        ret_val[item] = __create_data(item, item.replace(".docx", ""))
        pass

    return ret_val


word_data: dict[str, WordData] = __get_data()
