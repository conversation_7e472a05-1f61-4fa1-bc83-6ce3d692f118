from dataclasses import asdict, dataclass, field
from typing import Any, Dict, List, Literal, Optional, Union

from fitz import Rect
from typing_extensions import Tuple

from .constants import ExtractType


@dataclass
class TextractLine:
    asdict = asdict
    text: str
    bbox: Rect


@dataclass
class ExtractRelationship:
    type: Literal["footnote"]  # Currently just footnote but room for expansion
    id: str


@dataclass
class ExtractFormat:
    type: Literal[
        "none",
        "bold",
        "italic",
        "link",
        "super",
        "sub",
        "underline",
        "math",
    ]
    size: float
    value: str
    bbox: list[float]
    font: str
    url: Union[str, None] = None
    relationships: list[ExtractRelationship] = field(
        default_factory=lambda: [],
    )

    asdict = asdict


@dataclass
class PageRects:
    header_box: Rect = Rect()
    content_box: Rect = Rect()
    footer_box: Rect = Rect()


@dataclass
class ExtractedHeader:
    title: str
    font_size: int
    id: str
    tag: str = "h2"

    asdict = asdict


@dataclass
class ExtractedTOC:
    asdict = asdict
    title: str
    size: int
    id: str
    tag: str = "h2"
    sections: list = None


@dataclass
class ExtractedSegment:
    dict = asdict
    id: str
    title: str
    font_size: int
    content: List[Dict[str, Any]]
    tag: str = "h2"
    word_count: int = 0

    def __str__(self) -> str:
        text_content: List[str] = []

        text_content.append(self.title)

        for item in self.content:
            # Skip tables as we currently just extract them as images.
            # TODO: Update this when we get round to proper tables i guess
            if (
                item.type == ExtractType.IMAGE.value
                or item.type == ExtractType.TABLE.value
                or item.type == ExtractType.SECTION.value
            ):
                continue
            if item.type == ExtractType.LIST.value:
                for list_item in item.items:
                    text_content.append(list_item.value)
                    for sub_item in list_item.items:
                        text_content.append(sub_item.value)
            if item.type != ExtractType.TEXT.value:
                continue
            text_content.append(item.text)

        return "\n".join(text_content)

    def get_word_count(self) -> int:
        return len(self.__str__().split(" "))


@dataclass
class ExtractedHeadingSegment:
    dict = asdict
    title: str
    font_size: int
    id: str
    bbox: list[float]
    format: Literal["none", "bold", "italic", "link"]
    type: Optional[str] = ExtractType.HEADING.value
    tag: Optional[str] = "h2"


@dataclass
class ExtractedParagraphSegment:
    bbox: list
    text: str
    font_size: int
    formats: List[ExtractFormat]
    id: str
    type: Optional[str] = ExtractType.TEXT.value

    dict = asdict


@dataclass
class ExtractedMathSegment:
    bbox: list
    markup: str
    font_size: int
    id: str
    type: Optional[str] = ExtractType.MATH.value

    dict = asdict


@dataclass
class ExtractedImagesSegment:
    dict = asdict

    width: float
    height: float
    ext: str
    original_width: float
    original_height: float
    image: bytes
    id: str
    type: Optional[str] = ExtractType.IMAGE.value
    transform: Optional[str] = ""


@dataclass
class ExtractedTableContent:
    todict = asdict
    headers: List[dict]
    rows: List[dict]


@dataclass
class ExtractedTableSegment:
    todict = asdict
    content: ExtractedTableContent
    id: str
    type: Optional[str] = ExtractType.TABLE.value


@dataclass
class ExtractedListItem:
    value: str
    items: List
    bbox: List[float]


@dataclass
class ExtractedListSegment:
    todict = asdict
    format: Literal["ordered", "unordered"]
    items: List[ExtractedListItem]
    id: str
    type: str = ExtractType.LIST.value


@dataclass
class ExtractedFootnoteItem:
    superscript: str
    bbox: list
    text: str
    font_size: int
    formats: List[ExtractFormat]
    id: str
    type: Optional[str] = ExtractType.FOOTNOTE.value


@dataclass
class ExtractedFootnoteSegment:
    bbox: list
    items: List[ExtractedFootnoteItem]
    id: str
    type: Optional[str] = ExtractType.FOOTNOTE.value


@dataclass
class ExtractedPage:
    asdict = asdict
    page: int
    page_size: Dict[str, Any]
    header: List[Dict[str, Any]]
    content: List[Any]
    footer: List[Dict[str, Any]]
    footnotes: list[ExtractedFootnoteSegment]


@dataclass
class ExtractedFooterSegment:
    bbox: list
    text: str
    font_size: int
    formats: List[ExtractedSegment]
    id: str
    type: Optional[str] = ExtractType.FOOTER.value
    nameddest: Optional[str] = ""

    asdict = asdict

    def insert_nameddest(self, nameddest: dict, id: str):
        self.nameddest = nameddest["nameddest"] if "nameddest" in nameddest else id


@dataclass
class Section:
    asdict = asdict
    id: str
    title: str
    font_size: float
    content: Tuple
    tag: str = "h2"
    word_count: int = 0
    child_word_count: int = 0

    def __init__(self, id, title, font_size, tag, content):
        self.id = id
        self.title = title
        self.font_size = font_size
        self.tag = tag
        self.content = content

    def __str__(self) -> str:
        text_content: List[str] = []

        text_content.append(self.title)

        for item in self.content:
            # Skip tables as we currently just extract them as images.
            # TODO: Update this when we get round to proper tables i guess
            if (
                item.type == ExtractType.IMAGE.value
                or item.type == ExtractType.TABLE.value
                or item.type == ExtractType.SECTION.value
            ):
                continue
            if item.type == ExtractType.LIST.value:
                for list_item in item.items:
                    text_content.append(list_item.value)
                    for sub_item in list_item.items:
                        text_content.append(sub_item.value)
            if item.type != ExtractType.TEXT.value:
                continue
            text_content.append(item.text)

        return "\n".join(text_content)

    def get_word_count(self) -> int:
        return len(str(self).split(" "))


@dataclass
class SubSection:
    asdict = asdict
    id: str
    title: str
    type: str = ExtractType.SECTION.value
    asdict = asdict
    id: str
    title: str
    type: str = ExtractType.SECTION.value


@dataclass
class ExtractedDetails:
    page_count: int
    base_font_size: int
    toc: List[ExtractedTOC]
    summary: List[str] = None
    word_count: Optional[int] = None
    title: Optional[str] = None
    footnotes: Optional[Dict[str, ExtractedFootnoteSegment]] = None
    authors: Optional[str] = None

    asdict = asdict


@dataclass
class ExtractedDocument:
    details: ExtractedDetails
    sections: Dict[str, ExtractedSegment]
    dict = asdict

    def __str__(self) -> str:
        text_content: List[str] = []

        for content in self.sections.values():
            text_content.append(content.__str__())

        return "\n".join(text_content)

    def get_word_count(self) -> int:
        return len(self.__str__().split(" "))
