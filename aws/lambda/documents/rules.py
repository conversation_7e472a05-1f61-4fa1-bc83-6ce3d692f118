from dataclasses import asdict, dataclass


@dataclass
class DocumentRules:
    file_size: int
    extension: str
    dict = asdict


class PDFDocumentRules(DocumentRules):
    def __init__(self):
        self.file_size = 20971520  # 20MB
        self.extension = "pdf"


class DOCXDocumentRules(DocumentRules):
    def __init__(self):
        self.file_size = 20971520  # 20MB
        self.extension = "docx"
