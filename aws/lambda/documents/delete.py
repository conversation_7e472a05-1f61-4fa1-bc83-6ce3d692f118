from datetime import datetime, timezone

from middleware.schemas import AuthoriseDocumentRequestContext
from middleware import wrappers
from database.instance import DB
from database import Document
from helpers.wrap import (
    TailoError,
    logger,
    tailo_response,
)
from helpers.sentry import sentry_wrapper


@sentry_wrapper("delete_document_handler")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:
        global db

        with DB() as db_session:

            document = request_context.document
            db_session.add(request_context.document)

            if document is None:
                return tailo_response(
                    TailoError.INVALID_DOCUMENT, "Unable to find specified document"
                )

            user = request_context.user

            if user is None:
                return tailo_response(
                    TailoError.MISSING_USER, "Unable to find user details"
                )

            if document.owner_user_id != user.id:
                return tailo_response(
                    TailoError.DENIED, "User does not have permissions to edit document"
                )

            if document.date_deleted != None:
                return tailo_response(
                    TailoError.INVALID_DOCUMENT, "Document already deleted"
                )

            document.date_deleted = datetime.now(tz=timezone.utc)
            db_session.commit()

        return tailo_response(TailoError.OK, "Document deleted")
    except Exception as e:
        logger.exception("Exception in create: ", e)
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
