from helpers.sentry import sentry_wrapper
from middleware.schemas import AuthoriseDocumentRequestContext
from middleware import wrappers
from .rules import DocumentRules, DOCXDocumentRules, PDFDocumentRules


@sentry_wrapper("document_validation_handler")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    # Importing here to avoid circular imports
    from helpers.wrap import (
        TailoError,
        get_path_parameters,
        logger,
        tailo_response,
    )

    try:
        rules: DocumentRules

        # TODO: add path_parameters to AuthoriseDocumentRequestContext
        path_parameters = get_path_parameters(request_context.event)

        document_extension = path_parameters["document_extension"].lower()

        # TODO: Create fuction to return the appropriate rules based on the extension
        if document_extension == "pdf":
            rules = PDFDocumentRules()
        elif document_extension == "docx":
            rules = DOCXDocumentRules()
        else:
            return tailo_response(TailoError.INVALID_VALUE, "Invalid Extension")
        logger.info(rules.dict())

        return tailo_response(TailoError.OK, "OK", rules.dict())

    except Exception as e:
        logger.exception("Exception in create: ", e)
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
