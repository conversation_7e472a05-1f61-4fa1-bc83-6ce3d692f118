from datetime import datetime, timezone
from os import environ

from boto3 import Session, client
from middleware.schemas import AuthoriseDocumentRequestContext
from middleware import wrappers
from database.instance import DB
from database import Document
from helpers.sentry import sentry_wrapper
from helpers.wrap import (
    TailoError,
    get_body,
    logger,
    tailo_response,
)

session = Session()

s3_client = client("s3", region_name=session.region_name)
credentials = session.get_credentials()
current_credentials = credentials.get_frozen_credentials()
bucket_name = environ["BUCKET_NAME"]


@sentry_wrapper("update_document_handler")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:
        body = get_body(request_context.event)

        if not body:
            return tailo_response(TailoError.MISSING_PARAM, "Require new_name")

        if "new_name" not in body:
            return tailo_response(TailoError.MISSING_PARAM, "Require new_name")

        new_name = body["new_name"]

        if len(new_name) == 0:
            return tailo_response(TailoError.INVALID_VALUE, "New name invalid")

        token_content = request_context.token_content

        user_uuid = token_content.user_id
        logger.info(f"user_uuid {user_uuid}")
        logger.info(f"document_id {request_context.document.uuid}")

        # Create the database items to link user to document
        with DB() as db_session:
            document = request_context.document
            db_session.add(document)
            logger.info(document)

            user = request_context.user

            if document is None:
                return tailo_response(TailoError.INVALID_DOCUMENT, "Document not found")

            # Ensure user is the owner of the document
            if document.owner_user_id != user.id:
                return tailo_response(
                    TailoError.DENIED, "User does not have permissions to edit document"
                )

            document.name = new_name
            document.date_modified = datetime.now(tz=timezone.utc)
            db_session.commit()

            new_details = document.to_response_json(user.id)

        return tailo_response(TailoError.OK, "OK", new_details)

    except Exception as e:
        logger.exception("Exception in create: ", e)
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
