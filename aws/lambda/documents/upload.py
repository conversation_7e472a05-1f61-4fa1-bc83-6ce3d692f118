import json
from os import environ
from urllib.parse import unquote

from boto3 import client
from database.instance import DB
from database import Document, DocumentStatus, User
from helpers.wrap import session
from helpers.logging import get_tailo_logger
from websocket_utils import (
    MessageTarget,
    MessageType,
    build_message,
    WebsocketClient,
)

dynamodb_client = client("dynamodb")
bus_client = client("events", region_name=session.region_name)
bus_name = environ["BUS_NAME"]
logger = get_tailo_logger()

ws_client = WebsocketClient(
    environ["WEBSOCKET_URL"],
    environ["SOCKETS_TABLE"],
)


def handler(event, context):
    try:
        # TODO: Find out if this function is run on a per document upload
        # basis OR does it clump documents into one event?
        # event in container is sent through in bytes not string.
        event_data = (
            json.loads(event.decode("utf-8")) if isinstance(event, bytes) else event
        )
        logger.info(f"UPLOAD HANDLER RECORDS LENGTH: {len(event_data['Records'])}")
        for event_record in event_data["Records"]:

            event_s3 = event_record["s3"]

            # AWS inserts '+' for spaces into the key value, these need removing
            # as these are not in the bucket name
            object_key = unquote(event_s3["object"]["key"].replace("+", " "))
            logger.info(f"object_key {object_key}")

            # Create the database items to link user to document
            with DB() as db_session:
                document = Document.by_path(session=db_session, path=object_key)

                if document is None:
                    raise Exception("Unable to find uploaded document database item")

                logger.info(f"document {document.status}")

                user = User.get_by_id(db_session, document.owner_user_id)

                if user is None:
                    raise Exception("Unable to find owner user of document")

                user_uuid = user.estendio_id

                document.set_status(
                    session=db_session,
                    status=DocumentStatus.CHECKING_DOCUMENT,
                )

                message = build_message(
                    MessageType.DOCUMENT_STATUS,
                    document.uuid,
                    MessageTarget.DOCUMENT,
                    document.uuid,
                    DocumentStatus.CHECKING_DOCUMENT.value,
                )
                ws_client.send_message(user.estendio_id, message)

                logger.info(f"document {document.status}")

                document_id = document.id

                db_session.commit()

            event_response = bus_client.put_events(
                Entries=[
                    {
                        "Source": "extraction.process",
                        "EventBusName": bus_name,
                        "DetailType": "extraction-info",
                        "Detail": json.dumps(
                            {"user_uuid": user_uuid, "document_id": document_id}
                        ),
                    },
                ]
            )

            logger.info("event response:")
            logger.info(event_response)

    except Exception as e:
        logger.exception(f"Exception in create: {e}")
        logger.exception(f"Exception in create: {e}")
