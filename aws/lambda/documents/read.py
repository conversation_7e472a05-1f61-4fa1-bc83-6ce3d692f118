import json
from os import environ
from datetime import datetime, timezone

from middleware import wrappers
from boto3 import client
from requests import request
from database.instance import DB
from database import LegacyDocumentAudio, TTSVoice
from middleware.schemas import AuthoriseDocumentRequestContext
from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    get_path_parameters,
    get_authorization_token,
    logger,
    session,
    tailo_response,
)

from helpers.sentry import sentry_wrapper
from helpers.functions import tailo_http_response

bucket_name = environ["BUCKET_NAME"]


@sentry_wrapper("read_document_handler")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:
        auth_token = get_authorization_token(request_context.event)

        with DB() as db_session:
            db_session.add(request_context.user)
            db_session.add(request_context.document)

            extracted_url = request_context.document.extracted_path

            s3_client = client("s3", region_name=session.region_name)

            file_object = s3_client.get_object(Bucket=bucket_name, Key=extracted_url)

            file_contents = json.loads(file_object["Body"].read())

            license_response = request(
                "GET",
                f"{environ['LICENSE_URL']}/user/attributes",
                headers={"Authorization": f"Bearer {auth_token}"},
            )

            settings_response_json = license_response.json()

            if not license_response.ok:
                return tailo_http_response(
                    TailoError.DENIED,
                    (
                        settings_response_json["message"]
                        if "message" in settings_response_json
                        else "An error occurred fetching user settings"
                    ),
                )

            settings = settings_response_json["data"]["settings"]

            tts_voice = TTSVoice.by_name(
                session=db_session, name=settings.get("Voice", "Brian")
            )

            # Prepare the details for response.
            summary_audio = LegacyDocumentAudio.by_paragraph_id(
                session=db_session,
                document_id=request_context.document.id,
                section_id="summary",
                paragraph_id="summary",
                tts_voice_id=tts_voice.id,
            )

            # Set a base level for those that haven't processed.
            file_contents["details"]["summary_tts"] = {}
            file_contents["details"]["summary_tts"]["status"] = "unprocessed"
            file_contents["details"]["summary_tts"]["audio_file"] = None

            # Attach the audio status of each paragraph.
            if summary_audio is not None:
                audioFilePath = s3_client.generate_presigned_url(
                    ClientMethod="get_object",
                    Params={
                        "Bucket": bucket_name,
                        "Key": summary_audio.path + summary_audio.name,
                    },
                    ExpiresIn=86400,
                )

                file_contents["details"]["summary_tts"]["status"] = summary_audio.status
                file_contents["details"]["summary_tts"]["audio_file"] = audioFilePath

            # Prepare the sections for response.
            for section in file_contents["sections"].values():
                for block in section["content"]:
                    # If the block is of type "paragraph"/"list".
                    if block["type"] in ["paragraph", "list"]:
                        # Check for paragraph/list audio.
                        document_audio = LegacyDocumentAudio.by_paragraph_id(
                            session=db_session,
                            document_id=request_context.document.id,
                            section_id=section["id"],
                            paragraph_id=block["id"],
                            tts_voice_id=tts_voice.id,
                        )

                        # Set a base level for those that haven't processed.
                        block["tts"] = {}
                        block["tts"]["status"] = "unprocessed"
                        block["tts"]["audio_file"] = None

                        # Attach the audio status of each paragraph.
                        if document_audio is not None:
                            audioFilePath = s3_client.generate_presigned_url(
                                ClientMethod="get_object",
                                Params={
                                    "Bucket": bucket_name,
                                    "Key": document_audio.path + document_audio.name,
                                },
                                ExpiresIn=86400,
                            )

                            block["tts"]["status"] = document_audio.status
                            block["tts"]["audio_file"] = audioFilePath

                    # If the block is of type "image".
                    if block["type"] == "img":
                        block
                        logger.info(block)
                        presigned_url = s3_client.generate_presigned_url(
                            ClientMethod="get_object",
                            Params={"Bucket": bucket_name, "Key": block["key"]},
                            ExpiresIn=86400,  # one day in seconds, if issue still persists, we may need to change loading method in FE
                        )
                        block["url"] = presigned_url
                        block.pop("key")

            request_context.document.last_read_at = datetime.now(timezone.utc)
            db_session.commit()

            return tailo_response(TailoError.OK, "OK", file_contents)
    except Exception as e:
        logger.exception(f"error in read: {e}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
