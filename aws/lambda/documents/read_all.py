from time import time
from os import environ
from middleware.schemas import AuthoriseUserRequestContext
from middleware import wrappers

from boto3 import client
from database.instance import DB
from database import User
from helpers.sentry import sentry_wrapper, trace
from helpers.wrap import (
    TailoError,
    get_query_strings,
    logger,
    session,
    tailo_response,
)


s3_client = client("s3", region_name=session.region_name)
bucket_name = environ["BUCKET_NAME"]


@trace
def list_all_documents(user: User, page=1, limit=1000, get_deleted=False):
    logger.info(f"Using user: {user}")
    with DB() as db_session:
        db_session.add(user)
        if not user:
            return tailo_response(TailoError.MISSING_USER, "Unable to find user")

        user_documents = user.get_documents(
            session=db_session,
            page=page,
            limit=limit,
            get_deleted=get_deleted,
        )

        values = []

        for document in user_documents:
            values.append(document.to_response_json(user_id=user.id))
        return {
            "count": len(user_documents),
            "values": values,
        }


@sentry_wrapper("read_all_documents_handler")
@wrappers.authorise_user
def handler(request_context: AuthoriseUserRequestContext):
    try:
        start_time = time()
        # token_content = get_authorizer_context(event)

        get_deleted = False
        limit = 1000
        page = 1

        query_strings = get_query_strings(request_context.event)
        logger.info(f"query_strings: {query_strings}")

        if query_strings is not None:
            if "deleted" in query_strings:
                get_deleted = str(query_strings["deleted"]).lower() == "true"
            if "page" in query_strings:
                try:
                    page_val = int(query_strings["page"])
                    page = page_val
                except Exception:
                    pass
            if "limit" in query_strings:
                try:
                    limit_val = int(query_strings["limit"])
                    limit = limit_val
                except Exception:
                    pass

        logger.info(f"get_deleted {get_deleted}")
        logger.info(f"limit {limit}")
        logger.info(f"page {page}")

        user_uuid = request_context.token_content.user_id
        logger.info(user_uuid)

        # Create the database items to link user to document

        response = list_all_documents(request_context.user)

        print("Overall time: ", round(time() - start_time, 2))
        return tailo_response(TailoError.OK, "OK", response)

    except Exception as e:
        logger.exception("Error in read: ", e)
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
