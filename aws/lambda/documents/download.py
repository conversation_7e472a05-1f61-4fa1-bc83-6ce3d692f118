from datetime import datetime, timezone, timedelta
from os import environ
from middleware.schemas import AuthoriseDocumentRequestContext
from middleware import wrappers
from boto3 import Session, client
from helpers.sentry import sentry_wrapper
from helpers.functions import tailo_http_response
from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    get_path_parameters,
    logger,
)

session = Session()
expiry_time = 60


@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:
        document_path = request_context.document.path

        s3_client = client("s3", region_name=session.region_name)

        presigned_url = s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": environ["BUCKET_NAME"], "Key": document_path},
            ExpiresIn=expiry_time,
        )

        return tailo_http_response(
            TailoError.OK,
            {
                "url": presigned_url,
                "expires_at": (
                    datetime.now(timezone.utc) + timedelta(seconds=expiry_time)
                ).isoformat(),
            },
        )
    except Exception as e:
        logger.exception(f"error in download: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
