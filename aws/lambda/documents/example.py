import json
from os import environ

from boto3 import client
from helpers.wrap import <PERSON><PERSON><PERSON>rro<PERSON>, logger, session, tailo_response
from helpers.sentry import sentry_wrapper

bucket_name = environ["BUCKET_NAME"]
example_key = environ["EXAMPLE_DOCUMENT_KEY"]
s3_client = client("s3", region_name=session.region_name)

@sentry_wrapper("example_document_handler")
def handler(event, context):
    try:
        example_object = s3_client.get_object(Bucket=bucket_name, Key=example_key)
        example_contents = json.loads(example_object["Body"].read())
        # TODO - update to the new section first format
        # for page in example_contents["pages"]:
        # for block in page["content"]:
        # if block["type"] == "img":
        #     # Is image block
        #     block
        #     logger.info(block)
        #     presigned_url = s3_client.generate_presigned_url(
        #         ClientMethod="get_object",
        #         Params={"Bucket": bucket_name, "Key": block["key"]},
        #         ExpiresIn=900,  # one hour in seconds, increase if needed
        #     )
        #     block["url"] = presigned_url
        #     block.pop("key")

        return tailo_response(TailoError.OK, "OK", example_contents)
    except s3_client.exceptions.NoSuchKey:
        logger.exception(f"No example file in bucket with key {example_key}")
        return tailo_response(TailoError.INVALID_DOCUMENT, "Example file not available")
    except Exception as e:
        logger.exception(f"error in example: {e}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
