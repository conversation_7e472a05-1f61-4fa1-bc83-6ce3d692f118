from os import environ
from middleware.schemas import AuthoriseDocumentRequestContext
from middleware import wrappers
from database.instance import DB
from database import Document
from helpers.environment import is_local
from document_search.repository import Repository

from helpers.wrap import (
    logger,
    TailoError,
    sentry_init,
    get_query_strings,
    get_path_parameters,
    get_authorizer_context,
)

from helpers.functions import (
    tailo_http_response,
)

sentry_init(is_local())

bucket_name = environ["BUCKET_NAME"]
api_key = environ["ELASTIC_API_KEY"]
endpoint = environ["ELASTIC_ENDPOINT"]


def transform(hit):
    output = {
        "document_uuid": hit["_source"]["document_uuid"],
        "section_id": hit["_source"]["section_id"],
        "block_id": hit["_source"]["block_id"],
        "section_title": hit["_source"]["section_title"].strip(),
    }

    content = []

    if "content" in hit["highlight"]:
        for result in hit["highlight"]["content"]:
            content.append(result)

    if "section_title" in hit["highlight"]:
        for result in hit["highlight"]["section_title"]:
            content.append(result)

    output["content"] = content

    return output


@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:
        query = get_query_strings(request_context.event)

        if not query:
            return tailo_http_response(
                TailoError.MISSING_PARAM, "Require at least one query parameter"
            )

        if "keywords" not in query:
            return tailo_http_response(
                TailoError.INVALID_VALUE, "The 'keywords' query parameter is required"
            )

        token_content = request_context.token_content
        user_uuid = token_content.user_id

        with DB() as session:
            document = request_context.document
            session.add(document)

            if document is None:
                return tailo_http_response(
                    TailoError.INVALID_DOCUMENT, "Unable to find specified document"
                )

            if not request_context.user.can_read_doc(session, document):
                return tailo_http_response(TailoError.DENIED, "Denied")

            repository = Repository()

            result = repository.search(
                user_uuid=user_uuid,
                document_uuid=document.uuid,
                keywords=query["keywords"],
            )

            output = {
                "hits": list(map(transform, result["hits"]["hits"])),
                "suggestions": result["suggest"]["simple_phrase"],
            }

            return tailo_http_response(TailoError.OK, "OK", output)
    except Exception as e:
        logger.exception(f"error in read: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
