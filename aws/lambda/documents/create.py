from datetime import datetime, timezone
from os import environ
from uuid import uuid4

from boto3 import Session, client
from middleware import wrappers
from middleware.schemas import AuthoriseDocumentRequestContext
from database.instance import DB
from database import Document, DocumentStatus
from helpers.functions import tailo_http_response
from helpers.sentry import sentry_wrapper
from helpers.wrap import (
    TailoError,
    get_body,
    logger,
)

# TODO: match what we do in documents/delete.py and move declarations over to appropriate function calls in functions.py
session = Session()
s3_client = client("s3", region_name=session.region_name)
credentials = session.get_credentials()
current_credentials = credentials.get_frozen_credentials()
bucket_name = environ["BUCKET_NAME"]


# TODO: pass in file size and compare to limit set as per Rules.py
@sentry_wrapper("create_document_handler")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    # Take in the extension, filename, mime/type and possibly size
    try:
        body = get_body(request_context.event)

        if not body:
            return tailo_http_response(
                TailoError.MISSING_PARAM,
                "Require exttest.txtension, filename, and mime",
            )

        logger.info(body)

        # feature_limit = token_content.features.get(DOCUMENT_UPLOAD_STANDARD, None)
        # if feature_limit is None:
        #     return tailo_http_response(TailoError.DENIED, "Invalid Upload Limit")

        if "filename" not in body:
            return tailo_http_response(TailoError.MISSING_PARAM, "Require filename")

        filename = body["filename"]
        logger.info(filename)

        if "extension" not in body:
            return tailo_http_response(TailoError.MISSING_PARAM, "Require extension")

        # Lowercase the file extension for processing.
        extension = body["extension"].lower()

        logger.info(extension)

        if extension != ".pdf" and extension != ".docx":
            return tailo_http_response(
                TailoError.INVALID_DOCUMENT, "Document must be a PDF or DOCX"
            )

        if "mime" not in body:
            return tailo_http_response(TailoError.MISSING_PARAM, "Require mime")

        mime = body["mime"]
        logger.info(mime)

        if (
            mime != "application/pdf"
            and mime
            != "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ):
            return tailo_http_response(
                TailoError.INVALID_DOCUMENT, "Wrong MIME type for the document: " + mime
            )

        user_uuid = request_context.token_content.user_id
        logger.info(user_uuid)

        document_uuid = uuid4()
        logger.info(document_uuid)

        upload_path = f"{user_uuid}/{document_uuid}/{filename}"
        logger.info(upload_path)

        # Create the database items to link user to document
        with DB() as db_session:
            user = request_context.user

            if not user:
                return tailo_http_response(
                    TailoError.MISSING_USER, "Unable to find user"
                )

            now = datetime.now(tz=timezone.utc)

            # total_count = user.get_total_pages_for_interval(
            #     db_session,
            #     feature_limit.config.interval,
            #     now,
            # )

            # # TODO: How do we handle if feature_limit.config.repeat is False?
            # if total_count >= feature_limit.config.value:
            #     return tailo_http_response(
            #         TailoError.DENIED,
            #         "Document page limit reached",
            #     )

            created_doc = Document.create(
                session=db_session,
                uuid=document_uuid,
                owner_user_id=user.id,
                filename=filename,
                type=mime,
                path=upload_path,
                user_tracking_id=request_context.token_content.tracking_id,
                status=DocumentStatus.UPLOADING.value,
                date_created=now,
                date_modified=now,
            )

            created_json = created_doc.to_response_json(user.id)

        response = {
            "document": created_json,
            "upload_url": s3_client.generate_presigned_post(
                bucket_name,
                upload_path,
                {"acl": "private", "Content-Type": mime},
                Conditions=[{"acl": "private"}, {"Content-Type": mime}],
            ),
        }

        logger.info(response)
        response["aws_access_key"] = current_credentials.access_key

        return tailo_http_response(TailoError.OK, "OK", response)
    except Exception as e:
        logger.exception(f"Exception in create: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
