from os import environ
from database.instance import DB
from database import Document, User
from helpers.environment import is_local
from document_search.indexer import Indexer

from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    get_path_parameters,
    logger,
    sentry_init,
    tailo_http_response,
)

sentry_init(is_local())
bucket_name = environ["BUCKET_NAME"]


def index_document(user_uuid, document_uuid):
    indexer = Indexer()
    indexer.index_document(
        user_uuid=user_uuid,
        document_uuid=document_uuid,
        bucket_name=bucket_name,
    )


def handler(event, context):
    try:
        token_content = get_authorizer_context(event)
        path_parameters = get_path_parameters(event)
        document_uuid = path_parameters["id"]
        user_uuid = token_content.user_id

        with DB() as db_session:
            document = Document.by_uuid(db_session, document_uuid)

            if document is None:
                return tailo_http_response(
                    TailoError.INVALID_DOCUMENT, "Unable to find specified document"
                )

            user = User.get_by_estendio_id(db_session, user_uuid)

            if user is None:
                return tailo_http_response(
                    TailoError.MISSING_USER, "Unable to fetch user details"
                )

            if not user.can_read_doc(db_session, document):
                return tailo_http_response(TailoError.DENIED, "Denied")

            index_document(user_uuid, document_uuid)

            return tailo_http_response(TailoError.OK, "OK")
    except Exception as e:
        logger.exception(f"error in read: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
