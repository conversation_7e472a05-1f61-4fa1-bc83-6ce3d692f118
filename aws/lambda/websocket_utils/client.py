import json
from .messages import WebsocketMessage
from boto3 import client  # type: ignore


class WebsocketClient:
    def __init__(self, websocket_url: str, sockets_table: str):
        self.sockets_table = sockets_table
        self.dynamodb_client = client("dynamodb")
        self.api_gateway_client = client(
            "apigatewaymanagementapi",
            endpoint_url=f"https://{websocket_url}",
        )

    def send_message(self, user_uuid: str, message: WebsocketMessage):
        user_sessions = self._get_user_sessions(user_uuid)

        message_dict = message.dict()
        message_bytes = json.dumps(message_dict).encode("utf-8")

        for session in user_sessions:
            try:
                self.api_gateway_client.post_to_connection(
                    Data=message_bytes, ConnectionId=session
                )
            except self.api_gateway_client.exceptions.GoneException:
                self.dynamodb_client.delete_item(
                    Key={"userId": {"S": user_uuid}, "sessionId": {"S": session}},
                    TableName=self.sockets_table,
                )
            except Exception as e:
                print(e)

    def _get_user_sessions(self, user_uuid: str):
        user_sessions = []
        response = self.dynamodb_client.query(
            ExpressionAttributeValues={":v1": {"S": user_uuid}},
            KeyConditionExpression="userId = :v1",
            TableName=self.sockets_table,
        )
        items = self._format_query_results(response)

        for item in items:
            user_sessions.append(item["sessionId"])

        return user_sessions

    def _format_query_results(self, results):
        ret_item = []
        for item in results["Items"]:
            row_item = {}
            for key, value in item.items():
                if "S" in value:
                    row_item[key] = str(value["S"])
            ret_item.append(row_item)

        return ret_item
