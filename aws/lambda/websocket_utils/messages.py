from enum import Enum
from typing import Union
from dataclasses import dataclass, asdict
from typing import Optional


class MessageType(Enum):
    DOCUMENT_STATUS = "document status"
    SUMMARY_STATUS = "summary status"
    DOCUMENT_AUDIO_STATUS = "document audio status"


class MessageTarget(Enum):
    DOCUMENT = "document"
    DOCUMENT_AUDIO = "document audio"
    SUMMARY = "summary"


@dataclass
class WebsocketMessage:
    update_type: str
    document_id: str
    target: str
    target_id: str
    # TODO: We should ideally make this either str or dict
    # but python 3.9 does not support that. 3.10 does though
    # so look into upgrading
    value: Union[str, dict]
    message: Optional[str]

    def dict(self):
        return {k: v for k, v in asdict(self).items()}


def build_message(
    update_type: MessageType,
    document_id: str,
    target: MessageTarget,
    target_id: str,
    value: Union[str, dict],
    message: Union[str, None] = None,
):
    return WebsocketMessage(
        update_type.value,
        document_id,
        target.value,
        target_id,
        value,
        message,
    )
