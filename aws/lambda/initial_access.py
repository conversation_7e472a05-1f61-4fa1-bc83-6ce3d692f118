from helpers.wrap import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    tailo_response,
)


def handler(event, context):
    return tailo_response(TailoError.DENIED, "Denied")
    """
    body = get_body(event)
    if not body:
        return tailo_response(TailoError.MISSING_PARAM, "Require access_code")

    if "access_code" not in body:
        return tailo_response(TailoError.MISSING_PARAM, "Require access_code")

    access_code = body["access_code"]

    if not access_code:
        return tailo_response(Tailo<PERSON>rror.MISSING_PARAM, "Require access_code")

    try:
        with DB() as session:
            # Ensure that the password sent in is correct
            early_access_qry = select(EarlyAccess).where(
                EarlyAccess.password == access_code
            )
            result = session.execute(early_access_qry).first()

            if not result:
                return tailo_response(TailoError.DENIED, "Denied")

            # Get the ACCESS_ONLY role from the roles table

            roles_qry = select(Role).where(Role.name == environ["ROLE_ACCESS_ONLY"])
            role: Role = session.execute(roles_qry).first()

            if role is None:
                return tailo_response(<PERSON><PERSON><PERSON><PERSON><PERSON>.ROLE_MISSING, "No available roles")

            # sqlalchemy doesn't return an object instance but a tuple so we need to manually get the object
            role = role[0]
            # TODO: the guest user flow will need to be refined; currently we don't check the tos neither on FE nor BE
            now = datetime.now(tz=timezone.utc)
            new_user = User.create_guest(
                session=session,
                email=None,
                tos_accepted_at=now,
                date_created=now,
                date_modified=now,
            )

            UserRole.create(session=session, user_id=new_user.id, role_id=role.id)

            private_key = serialization.load_pem_private_key(
                environ["PRIVATE_KEY"].encode("utf-8"),
                password=None,
                backend=default_backend(),
            )

            access_token, access_expiry = generate_access_token(
                user_id=new_user.uuid,
                permissions=role.definition["permissions"],
                private_key=private_key,
            )

            # Create refresh_token
            refresh_uuid = str(uuid.uuid4())
            refresh_expiry = datetime.now(tz=timezone.utc) + timedelta(days=30)

            issued_at = datetime.now(tz=timezone.utc)

            UserSession.create(
                session=session,
                user_id=new_user.id,
                refresh_token=refresh_uuid,
                issued_at=issued_at,
                expires_at=refresh_expiry,
            )

            # Session variables cannot be used outside a session. assign vars for return
            new_user_uuid = new_user.uuid
            role_permissions = role.definition["permissions"]

        auth_data = {
            "auth": {
                "refresh_token": refresh_uuid,
                "access_token": access_token,
                "access_expiry": access_expiry.timestamp(),
            },
            "details": {
                "id": new_user_uuid,
                "permissions": role_permissions,
            },
        }

        return tailo_response(TailoError.OK, "OK", auth_data)
    except Exception as e:
        logger.exception(f"Error in initial_access: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
    """
