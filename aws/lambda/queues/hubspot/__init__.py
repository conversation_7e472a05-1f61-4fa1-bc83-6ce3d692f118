import json
from datetime import datetime
from os import environ
from uuid import uuid4

import boto3

sqs_client = boto3.client("sqs")


def _send_hub_spot_event(name: str, message: str, email: str, attributes: dict = {}):
    sqs_client.send_message(
        QueueUrl=environ["HUBSPOT_EVENT_QUEUE_URL"],
        MessageBody=json.dumps(
            {
                "name": name,
                "email": email,
                "attributes": {
                    "message": message,
                    **attributes,
                },
                "timestamp": datetime.now().isoformat(),
            }
        ),
        MessageGroupId=uuid4().hex,
        MessageDeduplicationId=uuid4().hex,
    )


def send_registration_event(user_email: str, success: bool, opt_in: bool = False):
    _send_hub_spot_event(
        "Registration",
        "User has completed registration",
        user_email,
        {"success": success, "opt_in": opt_in, "status": "Registered"},
    )


def new_user_created_event(user_email: str, success: bool):
    _send_hub_spot_event(
        "New User Created",
        "New user has been created",
        user_email,
        {"success": success, "status": "Invited"},
    )


def existing_user_logged_in_event(user_email: str, success: bool):
    _send_hub_spot_event(
        "Existing User Logged In",
        "Existing user has logged in",
        user_email,
        {"success": success},
    )


def non_user_attempt_log_in_event(user_email: str, success: bool):
    _send_hub_spot_event(
        "Non User Log In",
        "Non user has attempted to log in",
        user_email,
        {"success": success},
    )


def send_magic_link_event(user_email: str, success: bool):
    _send_hub_spot_event(
        "Magic-Login",
        "User attempted to login with magic link",
        user_email,
        {"success": success, "status": "Active"},
    )
