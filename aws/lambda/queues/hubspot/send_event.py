import json
import os

import hubspot
from hubspot.events.send import ApiException, BehavioralEventHttpCompletionRequest

client = hubspot.Client.create(access_token=os.getenv("HUBSPOT_ACCESS_TOKEN"))


def handler(event, context):
    try:
        print(event["Records"][0]["body"])
        event_data = (
            json.loads(event.decode("utf-8")) if isinstance(event, bytes) else event
        )
        event_record = event_data["Records"][0]
        body = json.loads(event_record["body"])
        message_id = event_record["attributes"]["MessageGroupId"]
        occurred_at = body["timestamp"]
        event_name = body["name"]
        properties = body["attributes"]

        event = BehavioralEventHttpCompletionRequest(
            occurred_at=occurred_at,
            event_name=event_name,
            uuid=message_id,
            email=body["email"],
            properties=properties,
        )
    # Uncomment the following lines to send the event to HubSpot
    #     api_response = client.events.send.behavioral_events_tracking_api.send(
    #         behavioral_event_http_completion_request=event
    #     )
    except ApiException as e:
        print(f"APIException when calling send: {e}")
    except Exception as e:
        print(f"Exception when calling send: {e}")
