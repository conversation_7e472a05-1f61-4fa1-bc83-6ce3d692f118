import json
import time
import traceback
from boto3 import client
from os import environ
from helpers.functions import logger
from botocore.exceptions import ClientError

def retry_wrapper(attempts: int, delay: int = 0):
    def retry_func(func):
        def wrapper(*args, **kwargs):
            for _ in range(attempts):
                try:
                    val = func(*args, **kwargs)
                    return val
                except ClientError as e:
                    print(f"ClientError occurred: {e}")
                    time.sleep(delay)
            raise Exception(f"Failed after {attempts} attempts")
        return wrapper
    return retry_func

@retry_wrapper(3, 1)
def kickoff_textract(
    bucket_name: str, 
    object_key: str, 
    textract_bucket_name: str, 
    document_uuid: str, 
    user_uuid: str, 
    attempt_number: int
):
    # create t-client
    tclient = client("textract")
    # create response with tclient.start_document_analysis
    output_config_path = f"{user_uuid}/{document_uuid}"

    logger.info(f"Starting document analysis for {object_key}, attempt number: {attempt_number}")
    response = tclient.start_document_analysis(
        DocumentLocation={"S3Object": {"Bucket": bucket_name, "Name": object_key}},
        FeatureTypes=["LAYOUT"],
        JobTag="text-extraction",
        NotificationChannel={
            "SNSTopicArn": environ["TOPIC_ARN"],
            "RoleArn": environ["SNS_ARN"],
        },
        OutputConfig={
            "S3Bucket": textract_bucket_name,
            "S3Prefix": output_config_path,
        },
    )
    return response["JobId"]

def handle_records(record):
    event_body = json.loads(record["body"])
    attempt_number = 1

    # Check if the required keys exist in event_body
    required_keys = ["bucket_name", "object_key", "textract_bucket_name", "document_uuid", "user_uuid"]
    for key in required_keys:
        if key not in event_body:
            raise KeyError(f"Missing required key: {key}")
    
    job_id = kickoff_textract(
        event_body["bucket_name"],
        event_body["object_key"],
        event_body["textract_bucket_name"],
        event_body["document_uuid"],
        event_body["user_uuid"],
        attempt_number
    )
    logger.info(f"Textract initaiated for: {event_body['object_key']}")
    logger.info(f"With job_jd: {job_id}")

def handler(event, context):
    try:
        for record in event["Records"]:
            handle_records(record)
            
    except json.JSONDecodeError as e:
        print("Error decoding JSON:", e)
        print(traceback.format_exc())
    except KeyError as e:
        print("Key error:", e)
        print(traceback.format_exc())
    except Exception as e:
        print("Exception when calling send:", e)
        print(traceback.format_exc())