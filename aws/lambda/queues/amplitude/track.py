import json
from os import environ
from amplitude import Amplitude, BaseEvent

AMPLITUDE_PLATFORM = "Tailo-API"


def handler(event, context):
    try:
        client = Amplitude(environ.get("AMPLITUDE_KEY"))
        event_data = (
            json.loads(event.decode("utf-8")) if isinstance(event, bytes) else event
        )
        for record in event_data["Records"]:
            body = json.loads(record["body"])
            tracking_id = body["tracking_id"]
            event_name = body["event_name"]
            record_event_data = body["event_data"]

            base_event = BaseEvent(
                event_type=event_name,
                user_id=tracking_id,
                event_properties=record_event_data,
                platform=AMPLITUDE_PLATFORM,
            )
            client.track(base_event)

        client.flush()
        client.shutdown()
    except Exception as e:
        print(f"Exception when calling send: {e}")
