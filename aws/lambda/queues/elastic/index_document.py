import json
import traceback
from boto3 import client
from database import Document
from database.instance import DB
from helpers.wrap import session
from datetime import datetime, timezone
from document_search.indexer import Indexer


def index_document(document_uuid, user_uuid, content):
    # Initialise the elastic indexer.
    indexer = Indexer()

    index = 0

    # Prepare the sections for response.
    for section in content["sections"].values():
        for block in section["content"]:
            # If the block is of type "paragraph"/"list".
            if block["type"] in ["paragraph"]:
                if "text" in block and "title" in section and block["text"] != "":
                    index += 1

                    # Index the block content
                    indexer.index_block(
                        user_uuid=user_uuid,
                        document_uuid=document_uuid,
                        section_id=section["id"],
                        section_title=section["title"],
                        block_id=block["id"],
                        block_type=block["type"],
                        index=index,
                        content=block["text"],
                    )

            # If the block is of type "paragraph"/"list".
            # if block["type"] in ["list"]:
            #     if "items" in block and len(block["items"]) > 0:
            #         # Index the block content
            #         indexer.index_block(
            #             user_uuid=user_uuid,
            #             document_uuid=document_uuid,
            #             section_id=section["id"],
            #             section_title=section["title"],
            #             block_id=block["id"],
            #             block_type=block["type"],
            #             content=block["items"]
            #         )


def handler(event, context):
    try:
        event_data = (
            json.loads(event.decode("utf-8")) if isinstance(event, bytes) else event
        )

        for record in event_data["Records"]:
            body = json.loads(record["body"])

            with DB() as db_session:
                document = Document.by_uuid(db_session, body["document_uuid"])

                if document is None:
                    return

                extracted_url = document.extracted_path

                s3_client = client("s3", region_name=session.region_name)

                file_object = s3_client.get_object(
                    Bucket=body["bucket_name"], Key=extracted_url
                )

                if file_object is None:
                    return

                file_contents = json.loads(file_object["Body"].read())

                if file_contents is None:
                    return

                if "document_uuid" in body and "user_uuid" in body:
                    index_document(
                        body["document_uuid"], body["user_uuid"], file_contents
                    )

                # Update the document's indexed state.
                document.indexed = True
                document.date_modified = datetime.now(tz=timezone.utc)
                db_session.commit()

    except Exception as e:
        print(traceback.format_exc())
        print(f"Exception when calling send: {e}")
