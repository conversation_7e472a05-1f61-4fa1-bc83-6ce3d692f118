import json
import traceback
import hashlib
from os import environ
from elasticsearch import Elasticsearch

api_key = environ["ELASTIC_API_KEY"]
endpoint = environ["ELASTIC_ENDPOINT"]

def index_block(block):
    client = Elasticsearch(endpoint, api_key=api_key)

    if "block_id" in block:
        # Generate a unique idenfifier for the block.
        identifiers = block["block_id"] + block["section_id"] + block["document_uuid"]
        id = hashlib.md5(identifiers.encode()).hexdigest()

        # Index the document block.
        client.index(index="document-blocks", id=id, document=block)

def handler(event, context):
    try:
        event_data = (
            json.loads(event.decode("utf-8")) if isinstance(event, bytes) else event
        )

        for record in event_data["Records"]:
            block = json.loads(record["body"])
            
            if block:
                index_block(block)
    except Exception as e:
        print(traceback.format_exc())
        print(f"Exception when calling send: {e}")
