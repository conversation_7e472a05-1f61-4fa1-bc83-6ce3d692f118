import hashlib
import io
import json
import math
import re
import traceback
import zipfile
from os import environ
from random import randint
from typing import Union

import boto3
from database import Document, User
from database.instance import DB
from database.services import commit as commit_document
from extractor import DocumentType
from extractor.error_codes import ExtractorError
from extractor.exceptions import (
    EncryptedFileExtractionException,
    ExtractionException,
    PageExtractionException,
    UnknownExtractionException,
)
from extractor.extraction_wrapper import ExtractionWrapper, get_extractor_wrapper
from extractor.progress_tracking import report_error
from fitz import Document as FitzDocument
from fitz import Page
from fitz import open as open_pdf
from helpers.amplitude import AmplitudeEvent, Track
from helpers.environment import is_local
from helpers.functions import logger, session
from helpers.sentry import sentry_init
from mypy_boto3_s3 import S3Client
from mypy_boto3_s3.type_defs import GetObjectOutputTypeDef
from pymupdf import TextPage
from typing_extensions import Any

sentry_init(is_local())

s3_client: S3Client = boto3.client("s3", region_name=session.region_name)
bucket_name: str = environ["BUCKET_NAME"]


def get_page_count(content_type: str, document: Any):
    """
    Gets the number of pages for the document
    """
    if content_type == "application/pdf":
        document = open_pdf(stream=document, filetype="pdf")
        return document.page_count

    # At this point this will be a docx since this is the only other file we support
    # Because none of the docx libraries have the ability to extract the information for some reason
    # I am having to parse into a zipfile and extract the xml data. Beautiful stuff I know.
    # TODO: Search for library that would handle this for us as Microsoft could add or alter items that would break regex
    try:
        docx_object = zipfile.ZipFile(io.BytesIO(document))
        docx_property_file_data = docx_object.read("docProps/app.xml").decode()
        # Get pages from docx properties. If missing, return 1 as default
        pages = re.search(r"<Pages>(\d+)</Pages>", docx_property_file_data)
        if pages is None:
            return 1
        page_count = pages.group(1)
        return int(page_count)
    except Exception:
        return 1


# Get file object from S3
# Pulled out as function to allow easier abstraction later
def get_file_object(document: Document) -> GetObjectOutputTypeDef:
    return s3_client.get_object(Bucket=bucket_name, Key=document.path)


# Extract meta-data from file and set against document
def extract_metadata_into_doc(
    document: Document,
    file_object: GetObjectOutputTypeDef,
    stream: bytes,
    extract_type: DocumentType,
    amplitude_event_data: dict,
) -> None:
    selected_file: str = file_object["ContentType"]
    logger.info(f"document path: {document.path}")

    file_hash: str = hashlib.sha256(stream).hexdigest()
    logger.info(f"file hash: {file_hash}")

    file_size: int = file_object["ContentLength"]
    logger.info(f"file size: {file_size}")
    amplitude_event_data["file_size"] = f"{file_size} bytes"

    page_count = get_page_count(selected_file, stream)
    logger.info(f"page_count: {page_count}")
    amplitude_event_data["page_count"] = page_count

    amplitude_event_data["extraction_type"] = extract_type.value

    if document.meta is None:
        document.meta = {}
    document.meta["extraction_type"] = extract_type.value
    document.hash = file_hash
    document.filesize = file_size
    document.page_count = page_count


# Get document and user IDs from event and retrieve entities from DB
def get_user_and_document(event: dict) -> tuple[User, Document]:
    # Extract document and user IDs from event
    event_data = json.loads(event.decode("utf-8")) if isinstance(event, bytes) else event
    document_id: int = event_data["detail"]["document_id"]
    user_uuid: str = event_data["detail"]["user_uuid"]
    # Retrieve user and document entities from DB
    with DB() as db_session:
        document = Document.by_id(session=db_session, id=document_id)

        if document is None:
            raise Exception("Unable to find uploaded document database item")

        user = User.get_by_estendio_id(session=db_session, uuid=user_uuid)

        if user is None:
            raise Exception("Unable to find user by uuid")

        if document.owner_user_id != user.id:
            raise Exception("User is not the owner of the document")

        db_session.expunge(document)
        db_session.expunge(user)

    return user, document


# Print out trace and error details, track to amplitude, report to user
# and update document in database
def handleExtractionException(
    document: Union[Document, None],
    user: Union[User, None],
    amplitude_event_data: dict,
    exception: Exception,
    label: str,
    error_type: str,
):
    # Direct printing of errors
    print(traceback.format_exc(limit=-1))
    print(f"{label}: {exception}")
    # Amplitude tracking
    amplitude_event_data["status"] = "error"
    amplitude_event_data["error_type"] = error_type
    Track(
        tracking_id=document.user_tracking_id if document is not None else "UNTRACKED",
        event=AmplitudeEvent.DOC_PROCESSED,
        event_data=amplitude_event_data,
    )
    # Report to user and update database for document
    if isinstance(document, Document):
        report_error(document, user, {"code": -1, "message": str(exception)})


# Gets random pages from the document so we don't loop through all
#    pages in a huge document which would cause performance issues
def _get_random_pages(pdf_contents: FitzDocument, num_of_pages: int = 0) -> list[Page]:
    try:
        extract_count = num_of_pages if num_of_pages > 0 else 5
        page_indexes: list[int] = []
        pages = []
        if pdf_contents.page_count <= extract_count:
            for page in pdf_contents:
                pages.append(page)

            return pages

        while len(page_indexes) < extract_count:
            index = randint(0, pdf_contents.page_count - 1)
            if index in page_indexes:
                continue

            page_indexes.append(index)
            page_indexes.sort()

        for index in page_indexes:
            pages.append(pdf_contents[index])

        return pages
    except Exception as e:
        print(e)
        raise PageExtractionException(str(e)) from e


# Determine if the document can be opened by PyMuPDF or needs OCR
def _does_contain_text(stream: bytes) -> bool:
    # Load PDF from stream
    pdf_contents: FitzDocument = open_pdf(stream=stream, filetype="pdf")
    try:
        pdf_contents.load_page(0)
    except ValueError as e:
        raise EncryptedFileExtractionException from e
    except Exception as e:
        raise UnknownExtractionException from e

    # check if document has text (OCR check)
    random_count: int = (
        math.floor(pdf_contents.page_count / 2)
        if pdf_contents.page_count > 5
        else pdf_contents.page_count
    )
    # TODO: Should we set a seed to make deterministic for testing?
    rnd_pages: list[Page] = _get_random_pages(pdf_contents, random_count)
    # If there is any non-stripped text in these pages, we can confirm there is text
    for page in rnd_pages:
        text_page: TextPage = page.get_textpage()
        stripped_text: str = text_page.extractText().strip()
        if len(stripped_text) > 0:
            return True

    # No text in selected pages, so assume only images
    return False


# Determine if the document is a word document
def _is_word_doc(document_type: str) -> bool:
    return document_type.endswith(
        "docx" and "vnd.openxmlformats-officedocument.wordprocessingml.document",
    )


# Determine the extraction type of document from the content type
def determine_document_type(document: Document, stream: bytes) -> DocumentType:
    print(f"document.type: {document.type}")
    if document.type.endswith("pdf"):
        if _does_contain_text(stream):
            return DocumentType.TEXTUAL_PDF
        return DocumentType.IMAGE_BASED_PDF
    if _is_word_doc(document.type):
        return DocumentType.DOCX
    raise ExtractionException(
        ExtractorError.INVALIDFILETYPE,
        "Invalid document type for extraction",
    )


# Update meta-data in database and kick off or perform extraction
def main(event: dict, context: dict) -> None:
    logger.info("FILE UPLOADED HANDLER STARTED")
    amplitude_event_data = {
        "type": "unknown",
        "character_count": -1,
    }
    user: User
    document: Document
    user, document = get_user_and_document(event)
    doc_data: GetObjectOutputTypeDef = get_file_object(document)
    try:
        doc_stream: bytes = doc_data["Body"].read()
        
        # Detect functional file type (docx, pdf or ocr)
        extract_type: DocumentType = determine_document_type(document, doc_stream)

        # Pull out meta-data and save to database
        extract_metadata_into_doc(
            document,
            doc_data,
            doc_stream,
            extract_type,
            amplitude_event_data
        )
        commit_document(document)

        # Get relevant extractor wrapper based on functional file type
        extractor_class: type[ExtractionWrapper] = get_extractor_wrapper(extract_type)
        extractor = extractor_class(doc_stream, user, document)

        # Extraction may be synchronous or asynchronous (like textract)
        extractor.start_extraction(amplitude_event_data)
    except ExtractionException as ee:
        handleExtractionException(
            document,
            user,
            amplitude_event_data,
            exception=ee,
            label="Extraction Exception",
            error_type=ee.message,
        )
    except Exception as e:
        handleExtractionException(
            document,
            user,
            amplitude_event_data,
            exception=e,
            label="ERROR",
            error_type="-1",  # UNKNOWN
        )
