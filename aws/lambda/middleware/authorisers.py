from database.instance import DB
from database.models import User, Document
from typing import OrderedDict, <PERSON><PERSON>
from os import environ
import jwt as JwtLib
import boto3  # type: ignore
from cryptography.hazmat.primitives import serialization

kms_client = boto3.client("kms")


def validate_jwt(event) -> Tuple[OrderedDict, User]:
    split_auth = str(event["headers"]["authorization"]).split(" ")
    if len(split_auth) != 2:
        raise Exception("Invalid token format")

    if split_auth[0] != "Bearer":
        raise Exception("Invalid Bearer tag")

    jwt = split_auth[1]
    public_key = kms_client.get_public_key(KeyId=environ["KMS_ARN"])
    key_string = serialization.load_der_public_key(public_key["PublicKey"])

    payload: OrderedDict = JwtLib.decode(jwt, key_string, ["PS256"])

    if not payload:
        raise Exception("Unable to get payload")

    expiry = payload.get("exp", -1)

    if not expiry:
        raise Exception("No expiry in token")

    __payload_is_authorised(payload)

    user = validate_user(payload["userId"])

    return payload, user


def __payload_is_authorised(payload: OrderedDict, throw: bool = True) -> bool:
    result = True

    if not ("license" in payload and "id" in payload["license"]):
        result = False
        if throw:
            raise Exception("Unauthorised Payload")

    return result


def validate_user(user_uuid: str):
    with DB() as db_session:
        user = User.get_by_estendio_id(session=db_session, uuid=user_uuid)

        if not user:
            raise Exception("User not found")

        db_session.expunge(user)

    return user


def validate_document(user: User, document_uuid: str):
    with DB() as db_session:
        db_session.add(user)
        document = Document.by_uuid(session=db_session, uuid=document_uuid)

        if not document:
            raise Exception("Document not found")

        if not user.can_read_doc(session=db_session, document=document):
            raise Exception("Unauthorised to view document")

        db_session.expunge(user)
        db_session.expunge(document)

    return document
