import json
import traceback
import time
from helpers.logging import get_tailo_logger
from .authorisers import validate_jwt, validate_document
from .utils import get_path_parameters, get_query_strings, get_body
from .schemas import (
    RequestContext,
    AuthoriseDocumentRequestContext,
    AuthoriseUserRequestContext,
    AuthContext,
    UserFeatureConfig,
)

logger = get_tailo_logger()


def __unauthorised_response():
    return {
        "statusCode": 401,
        "body": json.dumps({"message": "Unauthorised"}),
        "headers": {"content-type": "application/json"},
    }


def __internal_server_error_response():
    return {
        "statusCode": 500,
        "body": json.dumps({"message": "An unknown error occured"}),
        "headers": {"content-type": "application/json"},
    }


def __get_request_context(event, context) -> RequestContext:
    query_strings = get_query_strings(event)
    body = get_body(event)

    return RequestContext(
        event=event,
        context=context,
        http_method=event["requestContext"]["http"]["method"],
        http_path=event["requestContext"]["http"]["path"],
        query_strings=query_strings,
        body=body,
    )


def anonymous_user(func):
    def anonymous_user_func(*args):
        try:
            start_time = time.time()
            event, context = args
            # Request data

            request_context = __get_request_context(event, context)

            auth_time = time.time()
            logger.info(f"Auth time: {round(auth_time - start_time, 2)}")

            result = func(request_context)
            logger.info(f"Func time: {round(time.time() - auth_time, 2)}")
            logger.info(f"Overall time {round(time.time() - start_time, 2)}")
            return result

        except Exception as e:
            print(e)
            print(traceback.format_exc())
            return __internal_server_error_response()

    return anonymous_user_func


def authorise_user(func):
    def authoriser_func(*args):
        try:
            start_time = time.time()
            event, context = args
            payload, user = validate_jwt(event)

            features_configs = {
                feature["id"]: UserFeatureConfig.from_json(feature)
                for feature in payload.get("features")
            }

            auth_context = AuthContext(
                user_id=payload["userId"],
                permissions=payload["permissions"],
                features=features_configs,
                tracking_id=payload["trackingId"],
            )

            request_context = AuthoriseUserRequestContext(
                token_content=auth_context,
                user=user,
                **__get_request_context(event, context).asdict(),
            )

            auth_time = time.time()
            logger.info(f"Auth time: {round(auth_time - start_time, 2)}")

            result = func(request_context)
            logger.info(f"Func time: {round(time.time() - auth_time, 2)}")
            logger.info(f"Overall time {round(time.time() - start_time, 2)}")
            return result
        except Exception as e:
            print(e)
            print(traceback.format_exc())
            return __unauthorised_response()

    return authoriser_func


def authorise_document(func):
    def authoriser_func(*args):
        try:
            start_time = time.time()
            event, context = args
            payload, user = validate_jwt(event)

            path_parameters = get_path_parameters(event)

            if path_parameters is None and "body" in event:
                from .utils.license import payload_is_authorised

                payload_is_authorised(payload)
                document = None

            if path_parameters is not None:
                if "document_extension" in path_parameters:
                    document = None

                if "id" in path_parameters:
                    document_uuid = path_parameters["id"]
                    document = validate_document(user, document_uuid)

            features_configs = {
                feature["id"]: UserFeatureConfig.from_json(feature)
                for feature in payload.get("features")
            }

            auth_context = AuthContext(
                user_id=payload["userId"],
                permissions=payload["permissions"],
                features=features_configs,
                tracking_id=payload["trackingId"],
            )

            request_context = AuthoriseDocumentRequestContext(
                token_content=auth_context,
                user=user,
                document=document,
                **__get_request_context(event, context).asdict(),
            )

            auth_time = time.time()
            logger.info(f"Auth time: {round(auth_time - start_time, 2)}")

            result = func(request_context)
            logger.info(f"Func time: {round(time.time() - auth_time, 2)}")
            logger.info(f"Overall time {round(time.time() - start_time, 2)}")
            return result
        except Exception as e:
            print(e)
            print(traceback.format_exc())
            return __unauthorised_response()

    return authoriser_func
