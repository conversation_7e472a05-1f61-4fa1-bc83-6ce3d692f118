from datetime import timedelta
from dataclasses import dataclass, asdict
from database.models.Document import Document
from database.models.User import User
from typing import Any, Union
from .features import UserFeatureConfig, FeatureUnits


@dataclass
class AuthContext:
    user_id: str
    tracking_id: str
    features: dict[str, UserFeatureConfig]
    permissions: list  # TODO: Refine this to it's own dataclass

    def tts_exceeded(self, time_used: timedelta, feature_id: str) -> bool:
        config = self.features.get(feature_id)

        if config is None:
            raise Exception("Invalid config")
        if config.config.unit == FeatureUnits.HOURS:
            return (time_used.seconds // 3600) >= config.config.value
        elif config.config.unit == FeatureUnits.MINUTES:
            return (time_used.seconds // 60) % 60 >= config.config.value

        # All else fails assume true for now to be safe
        return True


@dataclass
class RequestContext:
    event: dict  # Don't like -> this request context should ideally just abstract the event data
    http_path: str
    http_method: str
    query_strings: dict
    body: dict
    context: Any
    asdict = asdict


@dataclass
class AuthoriseUserRequestContext(RequestContext):
    user: User
    token_content: AuthContext


@dataclass
class AuthoriseDocumentRequestContext(AuthoriseUserRequestContext):
    document: Document
