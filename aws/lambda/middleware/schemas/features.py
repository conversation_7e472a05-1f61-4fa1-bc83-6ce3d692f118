from dataclasses import dataclass
from enum import Enum

# TODO: This is a copy from data/features.py, delete data file once all lambdas moved to middleware

# Constants
DOCUMENT_UPLOAD_STANDARD = "document_upload_standard"
TTS_STANDARD = "tts_standard"


# Enums
class FeatureIntervals(Enum):
    DAILY = "DAILY"
    WEEKLY = "WEEKLY"
    MONTHLY = "MONTHLY"
    YEARLY = "YEARLY"


class FeatureUnits(Enum):
    HOURS = "HOURS"
    MINUTES = "MINUTES"
    PAGES = "PAGES"


class FeatureTypes(Enum):
    TIME_BASED = "TIME_BASED"


# Dataclasses
@dataclass(frozen=True)
class FeatureConfig:
    value: int
    unit: FeatureUnits
    repeat: bool
    interval: FeatureIntervals

    @classmethod
    def from_json(cls, json: dict):
        value = json.get("value", 0)
        unit = json.get("unit", FeatureUnits.MINUTES.value)
        repeat = json.get("repeat", False)
        interval = json.get("interval", FeatureIntervals.YEARLY.value)

        return cls(
            value=value,
            unit=FeatureUnits(unit),
            repeat=repeat,
            interval=FeatureIntervals(interval),
        )


@dataclass(frozen=True)
class UserFeatureConfig:
    id: str
    type: FeatureTypes
    config: FeatureConfig

    @classmethod
    def from_json(cls, json: dict):
        id = json.get("id", "")

        json_config: dict = json.get("config", {})
        if not json_config:
            raise Exception("Invalid User Feature JSON")

        config_configuration: dict = json_config.get("config", {})

        if not config_configuration:
            raise Exception("Invalid User Feature JSON")

        return cls(
            id=id,
            type=FeatureTypes(json_config.get("type", FeatureTypes.TIME_BASED.value)),
            config=FeatureConfig.from_json(config_configuration),
        )
