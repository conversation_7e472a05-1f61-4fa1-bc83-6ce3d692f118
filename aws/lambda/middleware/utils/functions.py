import json
from base64 import b64decode
from typing import Any, Dict


def get_query_strings(event: Dict) -> Dict[str, Any]:
    """
    Query strings arrive in a solid string like `key1=val1&key2=val2`.
    This function helps split them up into a dictionary:
    ```
    {
        "key1":"val1",
        "key2":"val2"
    }
    ```
    """
    response: Dict[str, Any] = {}

    if "rawQueryString" not in event or event["rawQueryString"].strip() == "":
        return response

    data = event["rawQueryString"]

    queries = data.split("&")

    for query in queries:
        split_qry = query.split("=")
        response[split_qry[0]] = None if len(split_qry) == 1 else split_qry[1]

    return response


def get_body(event) -> Dict:
    response = {}

    if "body" not in event or event["body"].strip() == "":
        return {}

    data = event["body"]

    if "isBase64Encoded" in event and event["isBase64Encoded"] is True:
        data = str(b64decode(event["body"]))

    try:
        response = json.loads(data)
        return response
    except Exception:
        return response
