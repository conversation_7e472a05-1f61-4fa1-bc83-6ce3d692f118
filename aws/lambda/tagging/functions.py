import re
from uuid import UUI<PERSON>
from sqlalchemy.orm import Session
from database.models import User, TaggableType, Taggable, Tag
from typing import List, Dict, Tuple, Union
from collections import Counter
from .exceptions import (
    InvalidUUIDException,
    TagsNotFoundException,
    TagsAlreadyExistsException,
    InvalidTagNameException,
)
from .constants import MAX_TAG_VALUE_LENGTH, MIN_TAG_VALUE_LENGTH


def _strip_tag(tag_value: str):
    # Trim multiple spaces
    processed_value = re.sub(r" +", " ", tag_value.strip())

    return processed_value


def _split_tags(tags: List[Dict[str, str]]) -> Tuple[List[str], List[str]]:
    """
    Splits the list of tags between new and existing to be actioned at a later point while
    also removing duplicates.
    """
    new_tags: set[str] = set()
    tag_uuids: set[str] = set()

    for tag in tags:
        tag_value = tag.get("value", "").strip()
        if tag_value == "":
            # Labels will be validated at a later point
            label = tag.get("label", "")
            new_tags.add(_strip_tag(label))

            continue

        try:
            UUID(tag_value, version=4)
        except ValueError:
            raise InvalidUUIDException(tag_value)

        tag_uuids.add(tag_value)

    return list(new_tags), list(tag_uuids)


def _delete_orphaned_tags(db_session: Session, user: User):
    """
    Will search for any of the users tags that have no taggable links and delete them
    from the tags table.
    """
    orphaned_tags = Tag.get_orphaned_tags(db_session, user.id)
    if len(orphaned_tags) > 0:
        Tag.bulk_delete(db_session, orphaned_tags)


def sync_item_tags(
    db_session: Session,
    tags: List[Dict[str, str]],
    user: User,
    tag_type: TaggableType,
    item_id: int,
) -> List[Tag]:
    """
    Synchronises the provided tag data with the specified item
    """
    # Get all taggables that exist currently for the item
    existing_taggables = Taggable.by_taggable_id(db_session, item_id, tag_type)
    new_tags, tag_uuids = _split_tags(tags)

    # Tags that don't exist in the existing uuids should be removed
    taggables_to_delete = [
        taggable
        for taggable in existing_taggables
        if str(taggable.tag.uuid) not in tag_uuids
    ]

    created_tags = []
    existing_tags = []

    # Grab all the existing tags that need to be applied to the tag
    if len(tag_uuids) > 0:
        existing_tags = Tag.get_by_uuids(db_session, tag_uuids)

        # If the list of tags returned doesn't match the ones that have been provided
        # then one of the UUIDS is invalid and we should report to the user
        if len(existing_tags) != len(tag_uuids):
            existing_tag_uuids = [str(tag.uuid) for tag in existing_tags]
            missing_uuids = list(
                filter(lambda uuid: uuid not in existing_tag_uuids, tag_uuids)
            )
            raise TagsNotFoundException(missing_uuids)

    # Create all tags that don't exist
    if len(new_tags) > 0:
        # Validate tag names
        _validate_tag_names(db_session, new_tags, user.id)

        created_tags = Tag.bulk_create(
            db_session,
            user,
            new_tags,
        )

    # Run bulk tag based off
    all_tags = existing_tags + created_tags
    if len(all_tags) > 0:
        all_tag_ids = [tag.id for tag in all_tags]
        Taggable.bulk_tag(db_session, all_tag_ids, tag_type, item_id)

    # Bulk remove
    rows_affected = Taggable.bulk_remove(db_session, taggables_to_delete)
    if rows_affected > 0:
        _delete_orphaned_tags(db_session, user)

    taggables = Taggable.by_taggable_id(db_session, item_id, tag_type)

    tag_ids = [taggable.tag_id for taggable in taggables]

    comment_tags = Tag.get_by_ids(db_session, tag_ids)

    return comment_tags


def _validate_tag_names(db_session: Session, names: List[str], user_id: int):
    """
    Validates the tag names against a set of rules to make sure they are valid before creation
    """
    # Ensure tag names follow min and max length
    invalid_names = []
    for name in names:
        name_length = len(name)
        if name_length < MIN_TAG_VALUE_LENGTH:
            invalid_names.append(
                {
                    "name": name,
                    "reason": f"too short. min: {MIN_TAG_VALUE_LENGTH} characters",
                }
            )
        if name_length > MAX_TAG_VALUE_LENGTH:
            invalid_names.append(
                {
                    "name": name,
                    "reason": f"too long. max: {MAX_TAG_VALUE_LENGTH} characters",
                }
            )

    # Ensure tag names aren't duplicated in the list
    unique = set()
    duplicates = set()
    for name in names:
        lowercase_name = name.lower()
        if lowercase_name in unique:
            duplicates.add(name)
            continue

        unique.add(lowercase_name)

    if len(duplicates) > 0:
        invalid_names.extend(
            [{"name": name, "reason": "duplicate value in body"} for name in duplicates]
        )

    if len(invalid_names) > 0:
        raise InvalidTagNameException(invalid_names)

    # Ensure there are no tags with the same names already for that user
    similar_tags = Tag.get_by_names(db_session, names, user_id)
    if len(similar_tags) > 0:
        raise TagsAlreadyExistsException([tag.name for tag in similar_tags])
