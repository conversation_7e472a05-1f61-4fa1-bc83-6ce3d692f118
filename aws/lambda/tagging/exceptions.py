from typing import List, Dict, Union
from helpers.constants import TailoError


class TaggingException(Exception):
    def __init__(self, *args: object):
        super().__init__(args)

    @property
    def error_code(self):
        return TailoError.INVALID_VALUE

    @property
    def data(self) -> Union[Dict, None]:
        return None


class InvalidTagNameException(TaggingException):
    def __init__(self, names: List[Dict]):
        super().__init__(names)
        self.names = names

    def __str__(self):
        return "Invalid tag names provided"

    @property
    def error_code(self):
        return TailoError.INVALID_VALUE

    @property
    def data(self):
        return self.names


class InvalidUUIDException(TaggingException):
    def __init__(self, uuid: str):
        super().__init__(uuid)
        self.uuid = uuid

    def __str__(self):
        return f"Invalid UUID provided: '{self.uuid}'"

    @property
    def error_code(self):
        return TailoError.INVALID_TAG_UUID

    @property
    def data(self):
        return super().data


class TagsNotFoundException(TaggingException):
    def __init__(self, uuids: List[str]):
        super().__init__(uuids)
        self.uuids = uuids

    def __str__(self):
        uuid_string = ", ".join(f"'{id}'" for id in self.uuids)
        return f"Invalid Tags with ids: {uuid_string}"

    @property
    def error_code(self):
        return TailoError.INVALID_VALUE

    @property
    def data(self):
        return super().data


class TagsAlreadyExistsException(TaggingException):
    def __init__(self, names: List[str]):
        super().__init__(names)
        self.names = names

    def __str__(self):
        names_str = ", ".join(f"'{name}'" for name in self.names)
        return f"Tags with names already exist: {names_str}"

    @property
    def error_code(self):
        return TailoError.INVALID_VALUE

    @property
    def data(self):
        return super().data
