# Start from AWS Python 3.9 base image
FROM public.ecr.aws/lambda/python:3.9

RUN yum -y install gcc
RUN yum -y install mariadb-devel mysql

# Install the dependencies
COPY summarisations/requirements.txt .
RUN pip3 install -r requirements.txt --target "${LAMBDA_TASK_ROOT}"

# Copy our function code
COPY summarisations/handler.py ${LAMBDA_TASK_ROOT}
COPY summarisations/exceptions "${LAMBDA_TASK_ROOT}/exceptions"
COPY data "${LAMBDA_TASK_ROOT}/data" 
COPY database "${LAMBDA_TASK_ROOT}/database"
COPY extractor "${LAMBDA_TASK_ROOT}/extractor"
COPY helpers "${LAMBDA_TASK_ROOT}/helpers"
COPY websocket_utils "${LAMBDA_TASK_ROOT}/websocket_utils"

# Set the handler function
CMD [ "handler.main" ]