from datetime import datetime, timezone

from database.instance import DB
from database import BetaEmail, User
from helpers.wrap import (
    <PERSON><PERSON><PERSON>rror,
    get_authorizer_context,
    logger,
    tailo_response,
)
from helpers.sentry import sentry_wrapper

@sentry_wrapper("delete_user_handler")
def handler(event, context):
    try:
        token_content = get_authorizer_context(event)

        user_uuid = token_content.user_id

        with DB() as session:
            user = User.get_by_estendio_id(session, user_uuid, True)
            if user is None:
                return tailo_response(TailoError.MISSING_USER, "User not found")

            if user.date_deleted is not None:
                return tailo_response(TailoError.INVALID_VALUE, "User already deleted")

            user.date_deleted = datetime.now(tz=timezone.utc)

            # soft delete associated beta email record if it exists
            # TODO: THIS WILL FAIL, WE NO LONGER STORE EMAILS HAVING SAID THAT WE ARE LIKELY REMOVING BETA EMAILS
            beta_email = BetaEmail.get_by_email(session, user.email)
            if beta_email is not None:
                beta_email.date_deleted = datetime.now(tz=timezone.utc)
                beta_email.deleted_by = user.id

            session.commit()

            return tailo_response(Tai<PERSON><PERSON>rror.OK, "User deleted")

    except Exception as e:
        logger.exception(f"Error in initial_access: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
