from os import environ

from database.instance import DB
from database import User
from helpers.wrap import (
    Tai<PERSON><PERSON>rror,
    set_user_licensing_attributes,
    get_authorizer_context,
    get_body,
    logger,
    tailo_response,
    get_authorization_token,
)
from helpers.validators import validate_req_arg
from requests import request
from helpers.sentry import sentry_wrapper, trace

@trace
def update_user_settings(attributes: dict, user_uuid: str, auth_token: str):
    with DB() as session:
        user = User.get_by_estendio_id(session, user_uuid)

        if user is None:
            return tailo_response(TailoError.MISSING_USER, "User not found.")

        license_response = request(
            "PUT",
            f"{environ['LICENSE_URL']}/user/attributes",
            headers={"Authorization": f"Bearer {auth_token}"},
            json={"attributes": attributes},
        )

        response_json = license_response.json()

        if not license_response.ok:
            return tailo_response(
                TailoError.UNKNOWN,
                (
                    response_json["message"]
                    if "message" in response_json
                    else "An error occured updating user settings"
                ),
            )

    return tailo_response(TailoError.OK, "OK")


@sentry_wrapper("update_user_settings_handler")
def handler(event, context):
    try:
        body = get_body(event)
        body_result = {}
        error, message = validate_req_arg(body, body_result, "settings", dict)

        if error != TailoError.OK:
            return tailo_response(error, message)

        new_settings = dict(body_result["settings"])

        token_content = get_authorizer_context(event)
        user_uuid = token_content.user_id
        auth_token = get_authorization_token(event)

        user_attributes = set_user_licensing_attributes(settings=new_settings)

        return update_user_settings(user_attributes, user_uuid, auth_token)

    except Exception as e:
        logger.exception(f"Error in user settings: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
