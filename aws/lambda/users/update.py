from database.instance import DB
from database import User
from helpers.wrap import (
    Tai<PERSON>Error,
    get_authorizer_context,
    get_body,
    logger,
    tailo_response,
)
from validator_collection import checkers
from helpers.sentry import sentry_wrapper, trace

@trace
def is_name_valid(name):
    if checkers.is_none(name):
        return False
    if not checkers.is_string(name):
        return False
    if not checkers.has_length(name, 1, 255):
        return False
    if name.isnumeric():
        return False
    return True

@sentry_wrapper("update_user_handler")
def handler(event, context):
    body = get_body(event)

    if not body:
        return tailo_response(TailoError.MISSING_PARAM, "Require at least one argument")

    token_content = get_authorizer_context(event)

    user_uuid = token_content.user_id
    logger.info(f"user_uuid {user_uuid}")

    try:
        with DB() as session:
            user = User.get_by_estendio_id(session, user_uuid)
            user_details = {}

            if user is None:
                return tailo_response(Tai<PERSON><PERSON>rror.MISSING_USER, "User not found.")
            # Update name
            if "name" in body:
                name = body["name"]
                if is_name_valid(name):
                    user.set_name(session=session, name=name)
                    user_details["name"] = name
                else:
                    return tailo_response(
                        TailoError.INVALID_VALUE, "Require valid name"
                    )
            # Update surname
            if "surname" in body:
                surname = body["surname"]
                if is_name_valid(surname):
                    user.set_surname(session=session, surname=surname)
                    user_details["surname"] = surname
                else:
                    return tailo_response(
                        TailoError.INVALID_VALUE, "Require valid surname"
                    )

            return tailo_response(TailoError.OK, "OK", {"user_details": user_details})

    except Exception as e:
        logger.exception("Exception in create: ", e)
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
