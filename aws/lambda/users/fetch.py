from os import environ

from helpers.functions import tailo_http_response
from database import User
from database.instance import DB
from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    logger,
    tailo_response,
    get_authorization_token,
)
from requests import request


def fetch_user(user_uuid: str, auth_token: str):
    with DB() as session:
        user = User.get_by_estendio_id(session, user_uuid)

        if user is None:
            return tailo_http_response(TailoError.MISSING_USER, "User not found.")

        license_response = request(
            "GET",
            f"{environ['LICENSE_URL']}/user/attributes",
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        response_json = license_response.json()

        if not license_response.ok:
            return tailo_http_response(
                TailoError.DENIED,
                (
                    response_json["message"]
                    if "message" in response_json
                    else "An error occurred fetching user settings"
                ),
            )

    data = response_json["data"]

    return tailo_response(TailoError.OK, "OK", data)


def handler(event, context):
    try:
        token_content = get_authorizer_context(event)
        user_uuid = token_content.user_id
        auth_token = get_authorization_token(event)

        return fetch_user(user_uuid, auth_token)

    except Exception as e:
        logger.exception(f"Error in user settings: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
