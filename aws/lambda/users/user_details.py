from os import environ

from helpers.functions import tailo_http_response
from middleware.schemas import AuthoriseUserRequestContext
from database import User
from database.instance import DB
from middleware import wrappers
from helpers.functions import tailo_http_response
from helpers.wrap import (
    TailoError,
    logger,
    get_authorization_token,
)
from requests import request


def fetch_user_details(user_uuid: str, auth_token: str):
    with DB() as session:
        user = User.get_by_estendio_id(session, user_uuid)

        if user is None:
            return tailo_http_response(TailoError.MISSING_USER, "User not found.")

        license_response = request(
            "GET",
            f"{environ['LICENSE_URL']}/user",
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        response_json = license_response.json()

        if not license_response.ok:
            return tailo_http_response(
                TailoError.DENIED,
                (
                    response_json["message"]
                    if "message" in response_json
                    else "An error occurred fetching user settings"
                ),
            )

    data = response_json["data"]

    return tailo_http_response(TailoError.OK, "OK", data)


@wrappers.authorise_user
def handler(request_context: AuthoriseUserRequestContext):
    try:
        user_uuid = request_context.token_content.user_id
        auth_token = get_authorization_token(request_context.event)

        return fetch_user_details(user_uuid, auth_token)

    except Exception as e:
        logger.exception(f"Error in user settings: {str(e)}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
