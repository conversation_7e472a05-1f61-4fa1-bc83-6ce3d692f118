from datetime import datetime, timezone

from helpers.emails import (
    send_feedback_email,
)
from helpers.sentry import sentry_wrapper
from helpers.validators import validate_args
from helpers.wrap import (
    TailoError,
    get_body,
    logger,
    tailo_response,
)


@sentry_wrapper("feedback_handler")
def handler(event, context):
    try:
        body = get_body(event)

        user_rating = ""
        document_type = ""

        body_result = {
            "user": "",
            "user_uuid": "",
            "subject": "",
            "location_in_app": "",
            "feedback": "",
            "reply_consent": False,
            "user_rating": "No rating provided",
            "document_type": "No document type provided",
        }

        error, message = validate_args(
            body,
            body_result,
            user=str,
            user_uuid=str,
            subject=str,
            location_in_app=str,
            feedback=str,
            reply_consent=bool,
            user_rating=str,
            document_type=str,
        )

        if error != TailoError.OK:
            return tailo_response(error, message)

        user = body_result["user"]
        user_uuid = body_result["user_uuid"]
        subject = body_result["subject"]
        location_in_app = body_result["location_in_app"]
        feedback = body_result["feedback"]
        reply_consent = body_result["reply_consent"]

        if body_result["subject"] == "user_rating":
            user_rating = body_result["user_rating"]
            document_type = body_result["document_type"]

        date_sent = datetime.now(tz=timezone.utc)

        send_feedback_email(
            user,
            user_uuid,
            subject,
            location_in_app,
            feedback,
            reply_consent,
            date_sent.strftime("%d/%m/%Y, %H:%M"),
            user_rating,
            document_type,
        )

        return tailo_response(TailoError.OK, "OK")

    except Exception as e:
        logger.exception(f"Error in user settings: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
