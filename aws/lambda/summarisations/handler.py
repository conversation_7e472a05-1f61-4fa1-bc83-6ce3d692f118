import concurrent.futures
import json
from concurrent.futures import as_completed
from enum import Enum
from logging import getLogger
from os import environ
from uuid import uuid4

import tiktoken
from boto3 import client
from database.instance import DB
from database import Document, Summarisation, SummarisationStatus, User
from exceptions import (
    ChunkSummariesException,
    CombineChunksException,
    RecommendedMethodException,
    StuffSummarisationException,
    SummarisationException,
)
from helpers.wrap import session
from langchain import PromptTemplate
from langchain.callbacks import get_openai_callback
from langchain.chains.summarize import load_summarize_chain
from langchain.chat_models import ChatOpenAI
from langchain.docstore.document import Document as LangchainDocument
from langchain.text_splitter import RecursiveCharacterTextSplitter
from websocket_utils import (
    MessageTarget,
    MessageType,
    WebsocketMessage,
    build_message,
)

logger = getLogger()

s3_client = client("s3", region_name=session.region_name)

bucket_name = environ["BUCKET_NAME"]

dynamodb_client = client("dynamodb")
api_client = None


class SummarisationMethod(Enum):
    STUFF = "stuff"
    CHUNKING = "chunking"


models = {
    "gpt-3.5-turbo": {
        "name": "gpt-3.5-turbo",
        "token_limit": 4097,
    },
    "gpt-3.5-turbo-16k": {"name": "gpt-3.5-turbo-16k", "token_limit": 16385},
}

model = models["gpt-3.5-turbo"]
encoding = tiktoken.encoding_for_model(model["name"])


def get_concat_doc_text(document_extracted: dict):
    text = ""
    for page in document_extracted["pages"]:
        for content_item in page["content"]:
            if "text" in content_item:
                text += content_item["text"] + "\n"
            if "header" in content_item:
                text += content_item["header"] + "\n"
    return text


def get_recommended_summarisation_type(prompt, doc_text, word_limit=False):
    try:
        token_limit_per_request = model["token_limit"]
        prompt_token_count = len(encoding.encode(prompt))
        doc_token_count = len(encoding.encode(doc_text))
        summarisation_method = SummarisationMethod.CHUNKING.value
        logger.info(f"===========================")
        logger.info(f"max token length: {model['token_limit']}")
        logger.info(f"prompt token length: {prompt_token_count}")
        logger.info(f"doc token length: {doc_token_count}")
        logger.info(f"===========================")

        if word_limit:
            word_limit_as_tokens = round(word_limit * 1.25)
            if (
                prompt_token_count + doc_token_count + word_limit_as_tokens
            ) <= token_limit_per_request:
                summarisation_method = SummarisationMethod.STUFF.value

        else:
            if (
                prompt_token_count + (doc_token_count * 2)
            ) <= token_limit_per_request or prompt_token_count + doc_token_count <= (
                token_limit_per_request / 2
            ):
                summarisation_method = SummarisationMethod.STUFF.value

        return summarisation_method

    except Exception as e:
        print(e)
        raise RecommendedMethodException(str(e))


def combine_chunks(summaries, doc_text, original_prompt, word_limit=None):
    try:
        summaries_doc = LangchainDocument(page_content=summaries)

        summaries_token_count = len(encoding.encode(summaries))

        if word_limit == None:
            word_limit = str(round(len(doc_text.split()) / 4))

        reduce_template = """ The following is a set of summaries:
        '''{text}'''
        These summaries were created by your response to the following prompt:
        '''%s'''
        Take these and distill it into one coherent summary. Please provide an answer of around %s words. Use the original prompt as guidance when producing the summary. Avoid any unnecessary repetition in your answer.
        SUMMARY:
        """
        reduce_template = reduce_template % (original_prompt, word_limit)
        reduce_template_token_count = len(encoding.encode(reduce_template))
        reduce_prompt = PromptTemplate(
            template=reduce_template, input_variables=["text"]
        )

        combine_max_tokens = (
            model["token_limit"] - (reduce_template_token_count + summaries_token_count)
        ) - 100
        if (
            summaries_token_count
            + len(encoding.encode(reduce_template))
            + (int(word_limit) * 1.25)
            > model["token_limit"]
        ):
            logger.info("super long document, using 16k model")
            combine_max_tokens = (
                models["gpt-3.5-turbo-16k"]["token_limit"]
                - (reduce_template_token_count + summaries_token_count)
            ) - 100

        combine_llm = ChatOpenAI(
            model_name=models["gpt-3.5-turbo-16k"]["name"],
            temperature=0,
            max_tokens=combine_max_tokens,
        )

        reduce_chain = load_summarize_chain(
            llm=combine_llm, chain_type="stuff", prompt=reduce_prompt, verbose=True
        )

        with get_openai_callback() as cb:
            output = reduce_chain(
                {"input_documents": [summaries_doc]}, return_only_outputs=True
            )
            logger.info(cb)
            return output

    except Exception as e:
        print(e)
        raise CombineChunksException(str(e))


def chunk_summarise(doc, prompt, llm, index):
    # this function is called by each thread so we can summarise in parallel
    map_chain = load_summarize_chain(llm, chain_type="stuff", prompt=prompt)

    chunk_summary = map_chain.run([doc])
    logger.info(
        f"Summary tokens: {len(encoding.encode(chunk_summary))} Summary Preview: {chunk_summary[:250]} \n"
    )
    return {"summary": chunk_summary, "index": index}


def chunk_summarise_all(prompt, doc_text, original_prompt, word_limit):
    try:
        chunk_size_tokens = model["token_limit"] / 2
        prompt_token_count = len(encoding.encode(prompt))

        max_tokens = (
            model["token_limit"] - (prompt_token_count + chunk_size_tokens)
        ) - 200  # to limit chance of irregular chunk size hitting max token limit
        llm = ChatOpenAI(model_name=model["name"], temperature=0, max_tokens=max_tokens)

        chunk_size_chars = (
            chunk_size_tokens * 4
        )  # multiplied by 4 for rough conversion between tokens and chars
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size_chars, chunk_overlap=40
        )

        texts = text_splitter.split_text(doc_text)
        docs = [LangchainDocument(page_content=t) for t in texts]

        summary_list = [None] * len(docs)
        chunk_prompts = []
        for i, doc in enumerate(docs):
            chunk_words = len(texts[i].split())
            this_prompt = (
                f"Please provide an answer of around {str(round(chunk_words / 3))} words. "
                + prompt
            )
            chunk_prompts.append(
                PromptTemplate(template=this_prompt, input_variables=["text"])
            )

        # create a thread for each chunk for parallel summarising - faster
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(docs)) as executor:
            futures = [
                executor.submit(
                    chunk_summarise, docs[index], chunk_prompts[index], llm, index
                )
                for index, item in enumerate(docs)
            ]

        for future in as_completed(futures):
            # when each thread finishes, add
            # summary responses to list at correct index to preserve order
            result = future.result()
            index = result["index"]
            summary = result["summary"]
            summary_list[index] = summary

        summaries = "\n\n".join(summary_list)
        logger.info(
            f"Summary chunks generated, total word length of summaries: {len(summaries.split())}"
        )

        output = combine_chunks(
            summaries=summaries,
            doc_text=doc_text,
            original_prompt=original_prompt,
            word_limit=word_limit,
        )

        return output

    except Exception as e:
        print(e)
        raise ChunkSummariesException(str(e))


def stuff_summarise(prompt, doc_text):
    try:

        prompt_token_count = len(encoding.encode(prompt))
        doc_token_count = len(encoding.encode(doc_text))
        max_tokens = (
            model["token_limit"] - (prompt_token_count + doc_token_count)
        ) - 100

        logger.info(f"recommened max tokens: {max_tokens}")
        llm = ChatOpenAI(model_name=model["name"], temperature=0, max_tokens=max_tokens)

        prompt = PromptTemplate(template=prompt, input_variables=["text"])
        chain = load_summarize_chain(
            llm,
            chain_type="stuff",
            prompt=prompt,
            verbose=True,
        )

        doc = LangchainDocument(page_content=doc_text)

        output = chain({"input_documents": [doc]}, return_only_outputs=True)

        return output
    except Exception as e:
        print(e)
        raise StuffSummarisationException(str(e))


def main(event, context):
    try:
        event_data = (
            json.loads(event.decode("utf-8")) if type(event) is bytes else event
        )

        with DB() as db_session:
            summarisation = Summarisation.by_id(
                session=db_session, id=event_data["detail"]["id"]
            )
            if summarisation is None:
                raise Exception("Unable to find summarisation")

            document = Document.by_id(session=db_session, id=summarisation.document_id)
            if document is None:
                raise Exception("Unable to find document")

            user = User.get_by_id(session=db_session, id=document.owner_user_id)
            if user is None:
                raise Exception("Unable to find user")

            summarisation.set_status(db_session, SummarisationStatus.PROCESSING)
            db_session.commit()
            message = build_message(
                MessageType.SUMMARY_STATUS,
                document.uuid,
                MessageTarget.SUMMARY,
                summarisation.uuid,
                SummarisationStatus.PROCESSING.value,
            )

            send_notification(user.estendio_id, message)

            form_fields = json.loads(summarisation.form_fields)

            user_word_limit = None
            if "word_limit" in form_fields and form_fields["word_limit"] != "":
                user_word_limit = form_fields["word_limit"]

            file_object = s3_client.get_object(
                Bucket=bucket_name, Key=document.extracted_path
            )
            file_contents = json.loads(file_object["Body"].read())

            concat_doc_text = get_concat_doc_text(file_contents)

            prompt = """
            %s
            "{text}"
            SUMMARY:
            """
            original_prompt = summarisation.prompt
            prompt = prompt % original_prompt

            recommended_summarisation_type = get_recommended_summarisation_type(
                prompt, concat_doc_text, user_word_limit
            )

            with get_openai_callback() as cb:
                if recommended_summarisation_type is SummarisationMethod.STUFF.value:
                    output = stuff_summarise(prompt, concat_doc_text)
                else:
                    output = chunk_summarise_all(
                        prompt,
                        concat_doc_text,
                        original_prompt=original_prompt,
                        word_limit=user_word_limit,
                    )

                meta_data = {
                    "total_tokens": cb.total_tokens,
                    "total_cost": cb.total_cost,
                }

            summarisation_uuid = uuid4()
            upload_path = (
                f"{user.estendio_id}/{document.uuid}/extracted/{summarisation_uuid}.txt"
            )

            s3_client.put_object(
                Bucket=bucket_name,
                Key=upload_path,
                Body=output["output_text"].strip(),
            )
            summarisation.path = upload_path
            summarisation.meta = json.dumps(meta_data)
            summarisation.set_status(db_session, SummarisationStatus.READY)
            db_session.commit()
            logger.info("SUMMARISATION:")
            logger.info(output["output_text"].strip())

            message = build_message(
                MessageType.SUMMARY_STATUS,
                document.uuid,
                MessageTarget.SUMMARY,
                summarisation.uuid,
                SummarisationStatus.READY.value,
            )

            send_notification(user.estendio_id, message)

    except SummarisationException as se:
        if "event_data" in locals():
            summarisation_id = event_data["detail"]["id"]
            report_error(summarisation_id, se.as_dict())
    except Exception as e:
        if "event_data" in locals():
            summarisation_id = event_data["detail"]["id"]
            report_error(summarisation_id, {"code": 0, "message": str(e)})
        logger.exception(f"Exception in summarisation handler: {e}")


# Report summary error via websocket
def report_error(summarisation_id: int, data: dict = {}):
    with DB() as db_session:
        summarisation = Summarisation.by_id(session=db_session, id=summarisation_id)

        if summarisation is not None:
            summarisation.set_status(db_session, SummarisationStatus.ERROR, data)
            document = Document.by_id(session=db_session, id=summarisation.document_id)

        user = User.get_by_id(db_session, document.owner_user_id)

        if user is not None and document is not None and summarisation is not None:
            message = build_message(
                MessageType.SUMMARY_STATUS,
                document.uuid,
                MessageTarget.SUMMARY,
                summarisation.uuid,
                "error",
                json.dumps(data),
            )
            send_notification(user.estendio_id, message)
    pass


# TODO these ws related functions are duplicated in file_uploaded handler, move to shared file
def format_query_results(results):
    ret_item = []
    for item in results["Items"]:
        row_item = {}
        for key, value in item.items():
            if "S" in value:
                row_item[key] = str(value["S"])
        ret_item.append(row_item)

    return ret_item


def get_user_sessions(user_uuid: str):
    user_sessions = []
    response = dynamodb_client.query(
        ExpressionAttributeValues={":v1": {"S": user_uuid}},
        KeyConditionExpression="userId = :v1",
        TableName=environ["SOCKETS_TABLE"],
    )
    items = format_query_results(response)

    for item in items:
        user_sessions.append(item["sessionId"])

    return user_sessions


def send_notification(user_uuid: str, message: WebsocketMessage):
    global api_client
    user_sessions = get_user_sessions(user_uuid)

    message_dict = message.dict()
    message_bytes = json.dumps(message_dict).encode("utf-8")

    if api_client is None:
        api_client = client(
            "apigatewaymanagementapi",
            endpoint_url=f"https://{environ['WEBSOCKET_URL']}",
        )

    for session in user_sessions:
        try:
            logger.info(f"Calling id {session}")
            response = api_client.post_to_connection(
                Data=message_bytes, ConnectionId=session
            )
            logger.info(response)
        except api_client.exceptions.GoneException as e:
            logger.info(f"Removing session: {session}")
            dynamodb_client.delete_item(
                Key={"userId": {"S": user_uuid}, "sessionId": {"S": session}},
                TableName=environ["SOCKETS_TABLE"],
            )
        except Exception as e:
            logger.error(e)
    pass
