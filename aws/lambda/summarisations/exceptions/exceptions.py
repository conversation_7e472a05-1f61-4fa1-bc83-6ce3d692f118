from .error_codes import SummarisationError


# TODO make a base TailoException class for both summarisation and extraction exceptions to derive from
class SummarisationException(Exception):
    code = 0
    message = ""

    def __init__(self, code: int, message: str) -> None:
        self.code = code
        self.message = message
        super().__init__(message)

    def as_dict(self):
        return {"code": self.code, "message": self.message}


class UnknownSummarisationException(SummarisationException):
    def __init__(self, message="An unknown summarisation error occured") -> None:
        super().__init__(SummarisationError.UNKNOWN, message)


class StuffSummarisationException(SummarisationException):
    def __init__(self, message="Error performing stuff summarisation on document text") -> None:
        super().__init__(SummarisationError.STUFFSUMMMARISATION, message)


class RecommendedMethodException(SummarisationException):
    def __init__(self, message="Error calculating recommended summarisation method") -> None:
        super().__init__(SummarisationError.RECOMMENDEDMETHOD, message)

class ChunkSummariesException(SummarisationException):
    def __init__(self, message="Error getting summarise of chunks") -> None:
        super().__init__(SummarisationError.CHUNKSUMMARIES, message)

class CombineChunksException(SummarisationException):
    def __init__(self, message="Error getting a combined summary of chunks") -> None:
        super().__init__(SummarisationError.COMBINECHUNKS, message)        super().__init__(SummarisationError.COMBINECHUNKS, message)