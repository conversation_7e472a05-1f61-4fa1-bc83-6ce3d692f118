from database.instance import DB
from database import BetaEmail
from helpers.wrap import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    logger,
    tailo_response,
)
from helpers.sentry import sentry_wrapper


@sentry_wrapper("read_all_beta_emails_handler")
def handler(event, context):
    global db

    try:
        with DB() as session:
            beta_emails = BetaEmail.get_all(session=session)
            emails = []
            for beta_email in beta_emails:
                emails.append(beta_email.to_response_json(session=session))

            return tailo_response(TailoError.OK, "OK", {"beta_emails": emails})

    except Exception as e:
        logger.exception(f"Error in initial_access: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
