from datetime import datetime, timezone

from database.instance import DB
from database import BetaEmail, User
from helpers.wrap import (
    Tai<PERSON><PERSON>rror,
    get_authorizer_context,
    get_body,
    logger,
    tailo_response,
)
from helpers.sentry import sentry_wrapper, trace


@trace
def get_update_data(email, date_deleted, deleted_by):
    return {"email": email, "date_deleted": date_deleted, "deleted_by": deleted_by}


@sentry_wrapper("delete_beta_emails_handler")
def handler(event, context):
    try:
        body = get_body(event)

        token_content = get_authorizer_context(event)

        user_uuid = token_content.user_id

        if "emails" not in body:
            return tailo_response(TailoError.MISSING_PARAM, "Require email")

        emails = body["emails"]

        if not isinstance(emails, list):
            return tailo_response(TailoError.INVALID_VALUE, "Emails must be an array")

        if len(emails) < 1:
            return tailo_response(
                TailoError.INVALID_VALUE, "Emails array must contain at least 1 email"
            )

        # remove duplicates from passed in emails list
        emails = list(dict.fromkeys(emails))

        data = {}

        with DB() as session:
            user = User.get_by_estendio_id(session, user_uuid)
            if user is None:
                return tailo_response(TailoError.INVALID_VALUE, "User not found")

            # remove non matching emails
            matching_records_query = session.query(
                BetaEmail.email, BetaEmail.deleted_by
            ).filter(BetaEmail.email.in_(emails))
            matching_records = matching_records_query.all()
            matching_emails = [record[0] for (record) in matching_records]
            if matching_records:
                not_found = [email for email in emails if email not in matching_emails]
                emails = [email for email in emails if email not in not_found]
                if emails and not_found:
                    data["not_found"] = not_found
            else:
                return tailo_response(TailoError.INVALID_VALUE, "No emails found")

            # remove already deleted emails
            already_deleted_records = [
                email for email in matching_records if email[1] != None
            ]
            already_deleted_emails = [record[0] for (record) in already_deleted_records]
            if already_deleted_emails:
                emails = [
                    email for email in emails if email not in already_deleted_emails
                ]
                data["already_deleted"] = already_deleted_emails

            if not emails and already_deleted_emails and not_found:
                return tailo_response(
                    TailoError.INVALID_VALUE,
                    "All emails already deleted or not found",
                    data,
                )
            elif not emails and already_deleted_emails:
                return tailo_response(
                    TailoError.INVALID_VALUE, "All emails already deleted"
                )
            elif emails:
                # if there are emails remaining, mark as deleted
                date_deleted = datetime.now(tz=timezone.utc)
                update_data = [
                    get_update_data(email, date_deleted, user.id) for email in emails
                ]
                session.bulk_update_mappings(BetaEmail, update_data)
                session.commit()
                data["deleted_emails"] = emails

            return tailo_response(TailoError.OK, "Beta emails deleted", data)

    except Exception as e:
        logger.exception(f"Error in initial_access: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
