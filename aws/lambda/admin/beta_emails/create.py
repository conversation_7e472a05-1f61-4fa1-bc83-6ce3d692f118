from datetime import datetime, timezone
from os import environ

from database import BetaEmail, User
from database.instance import DB
from helpers.wrap import (
    Tailo<PERSON>rror,
    get_authorizer_context,
    get_body,
    logger,
    tailo_response,
)
from queues.hubspot import new_user_created_event
from sqlalchemy import MetaData, Table, exc
from validator_collection import checkers
from helpers.sentry import sentry_wrapper, trace


@trace
def get_insert_data(email, date_created, added_by):
    return {"email": email, "date_created": date_created, "added_by": added_by}


@sentry_wrapper("create_beta_emails_handler")
def handler(event, context):
    try:
        token_content = get_authorizer_context(event)

        user_uuid = token_content.user_id

        body = get_body(event)
        logger.info(f"Body: {body}")

        if not "emails" in body:
            logger.info("Missing emails array")
            return tailo_response(TailoError.MISSING_PARAM, "Require emails array")

        emails = body["emails"]

        if not isinstance(emails, list):
            return tailo_response(TailoError.INVALID_VALUE, "Emails must be an array")

        if len(emails) < 1:
            return tailo_response(
                TailoError.INVALID_VALUE, "Emails array must contain at least 1 email"
            )

        data = {"added_emails": []}

        valid_emails = []
        invalid_emails = []

        for email in emails:
            if checkers.is_email(email):
                valid_emails.append(email)
            else:
                invalid_emails.append(email)

        if len(invalid_emails):
            data["invalid_emails"] = invalid_emails

        with DB() as session:
            user = User.get_by_estendio_id(session, user_uuid)

            if user == None:
                return tailo_response(TailoError.INVALID_VALUE, "User not found")

            duplicate_emails_query = session.query(BetaEmail.email).filter(
                BetaEmail.email.in_(valid_emails)
            )
            duplicate_emails = [email for (email,) in duplicate_emails_query.all()]

            if duplicate_emails:
                valid_emails = [
                    email for email in valid_emails if email not in duplicate_emails
                ]
                data["duplicate_emails"] = duplicate_emails

            if len(valid_emails):
                date_created = datetime.now(tz=timezone.utc)
                insert_data = [
                    get_insert_data(email, date_created, user.id)
                    for email in valid_emails
                ]
                for email in insert_data:
                    new_user_created_event(email.get("email"), True)

                metadata_obj = MetaData()
                BETA_EMAILS = Table(
                    "beta_emails", metadata_obj, autoload_with=db.engine
                )
                session.execute(BETA_EMAILS.insert().values(insert_data))
                session.commit()

                data["added_emails"] = valid_emails
            else:
                if duplicate_emails:
                    return tailo_response(
                        TailoError.INVALID_VALUE, "All submitted emails are duplicates"
                    )
                else:
                    return tailo_response(
                        TailoError.INVALID_VALUE, "All submitted emails are invalid"
                    )

            return tailo_response(TailoError.OK, "OK", data)

    except Exception as e:
        logger.exception(f"Error in initial_access: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
