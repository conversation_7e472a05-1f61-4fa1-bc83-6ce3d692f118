from .prompt import Prompt, PromptFor, PromptFormat, SummaryType


class PromptBuilder:
    def __init__(self):
        self.prompt = Prompt()  # int

    def set_reason(self, reason: str):
        self.prompt.set_reason(reason)
        return self

    def set_summary_for(self, summary_for: str):
        self.prompt.set_summary_for(summary_for)
        return self

    def set_student(self, is_student: bool):
        self.prompt.set_student(is_student)
        return self

    def set_field(self, field: str):
        self.prompt.set_field(field)
        return self

    def set_learning_differences(self, learning_differences: str):
        self.prompt.set_learning_differences(learning_differences)
        return self

    def set_format(self, format: PromptFormat):
        self.prompt.set_format(format)
        return self

    def set_focus_on(self, focus_on: str):
        self.prompt.set_focus_on(focus_on)
        return self

    def set_summary_type(self, summary_type: SummaryType):
        self.prompt.set_summary_type(summary_type)
        return self

    def set_summary_tone(self, summary_tone: str):
        self.prompt.set_summary_tone(summary_tone)
        return self

    def set_word_limit(self, word_limit: int):
        if word_limit == 0:
            raise Exception("Invalid word limit set")
        self.prompt.set_word_limit(word_limit)
        return self

    def build(self):
        """Builds the prompts based on the values passed into the builder"""

        return self.prompt
