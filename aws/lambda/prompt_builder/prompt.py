import json
from dataclasses import asdict, dataclass, is_dataclass
from enum import Enum


class PromptFor(Enum):
    ME = "me"
    SOMEONEELSE = "someone else"


class PromptFormat(Enum):
    BULLETS = "bullet points"
    PARAGRAPHS = "paragraphs"


class SummaryType(Enum):
    ABSTRACTIVE = "abstractive"
    EXECUTIVE = "executive"
    DESCRIPTIVE = "descriptive"
    EXTRACTIVE = "extractive"
    OTHER = "other"


"""
Summarise my document, {reason}

This summary is for {summary_for}, {student} {studying/working} {field} 
with {differences, comma separated}

This summary is for someone else, they have ADHD, Dyslexia, and Parkinsons...

Format the summary in {format} with a focus on {focus_on}

I want an {summary_type} summary in a {formal_tone} tone and {word_limit} words

"""


class PromptJSONEncoder(json.JSONEncoder):
    def default(self, o):
        if is_dataclass(o):
            return asdict(o)
        return super().default(o)


@dataclass()
class PromptStyle:
    type: str
    text: str
    dict = asdict


class Prompt:
    def __init__(self):
        self.reason = None  # str
        self.summary_for = None  # str
        self.student = None  # bool
        self.field = None  # str
        self.learning_differences = None  # str
        self.format = None  # PromptFormat
        self.focus_on = None  # str
        self.summary_type = None  # SummaryType
        self.summary_tone = None  # str
        self.word_limit = None  # int

    def set_reason(self, reason: str):
        self.reason = reason

    def set_summary_for(self, summary_for: str):
        self.summary_for = summary_for

    def set_student(self, is_student: bool):
        self.student = is_student

    def set_field(self, field: str):
        self.field = field

    def set_learning_differences(self, learning_differences: str):
        self.learning_differences = learning_differences

    def set_format(self, format: PromptFormat):
        self.format = format.value

    def set_focus_on(self, focus_on: str):
        self.focus_on = focus_on

    def set_summary_type(self, summary_type: SummaryType):
        self.summary_type = summary_type.value

    def set_summary_tone(self, summary_tone: str):
        self.summary_tone = summary_tone

    def set_word_limit(self, word_limit: int):
        self.word_limit = word_limit

    def as_dict(self):
        ret_data = {
            "start": json.loads(
                json.dumps(self.__get_start_dict(), cls=PromptJSONEncoder)
            ),
            "about": json.loads(
                json.dumps(self.__get_about_dict(), cls=PromptJSONEncoder)
            ),
            "format": json.loads(
                json.dumps(self.__get_format_dict(), cls=PromptJSONEncoder)
            ),
            "tone": json.loads(
                json.dumps(self.__get_tone_dict(), cls=PromptJSONEncoder)
            ),
        }

        return ret_data

    def __str__(self) -> str:
        json_data = self.as_dict()

        start_text = [item["text"] for item in json_data["start"]]
        about_text = [item["text"] for item in json_data["about"]]
        format_text = [item["text"] for item in json_data["format"]]
        tone_text = [item["text"] for item in json_data["tone"]]

        ret_str = "".join(start_text)
        if len(about_text) > 0:
            ret_str += "\n"
            ret_str += "".join(about_text)

        if len(format_text) > 0:
            ret_str += "\n"
            ret_str += "".join(format_text)

        if len(tone_text) > 0:
            ret_str += "\n"
            ret_str += "".join(tone_text)

        return ret_str

    def __get_about_dict(self):
        """
        This summary is for {summary_for}, {student} {studying/working} {field}
        with {differences, comma separated}
        """
        ret_list = []
        pronoun = (
            "I"
            if self.summary_for is None or self.summary_for == PromptFor.ME.value
            else "they"
        )
        pronoun_desc = (
            "I am"
            if self.summary_for is None or self.summary_for == PromptFor.ME.value
            else "they are"
        )

        # format about
        if self.summary_for is not None:
            ret_list.extend(
                [
                    PromptStyle("text", "This summary is for "),
                    PromptStyle("text", self.summary_for),
                ]
            )

        if self.learning_differences is not None:
            split_diff = self.learning_differences.split(",")

            # trim split diff of any blank values

            idx_trim = []
            for idx, diff in enumerate(split_diff):
                trimmed_diff = diff.strip()

                if trimmed_diff == "":
                    idx_trim.append(idx)

            # Loop through idx in reverse so we remove the highest indexes first
            for idx in reversed(idx_trim):
                split_diff.pop(idx)

            ret_list.append(
                PromptStyle(
                    "text",
                    f", {pronoun} have " if len(ret_list) > 0 else f"{pronoun} have ",
                ),
            )
            split_length = len(split_diff)

            for idx, diff in enumerate(split_diff):
                trimmed_diff = diff.strip()
                ret_list.append(PromptStyle("argument", trimmed_diff))

                is_end = (idx + 1) == split_length

                if is_end:
                    break

                remaining = split_length - (idx + 1)

                ret_list.append(
                    PromptStyle("text", ", ")
                    if remaining > 1
                    else PromptStyle("text", ", and ")
                )

        if self.student is not None:
            if len(ret_list) > 1:
                ret_list.append(PromptStyle("text", ". "))

            ret_list.extend(
                [
                    PromptStyle(
                        "text",
                        f"{pronoun_desc.capitalize()} a student"
                        if self.student
                        else f"{pronoun_desc.capitalize()} a worker",
                    )
                ]
            )

        if self.field is not None:
            delimeter = ", " if self.student is not None else f" and {pronoun_desc} "
            ret_list.append(
                PromptStyle(
                    "text",
                    delimeter if len(ret_list) > 0 else f"{pronoun_desc} ",
                )
            )
            ret_list.extend(
                [
                    PromptStyle("text", "studying " if self.student else "working in "),
                    PromptStyle("text", self.field),
                ]
            )

        if len(ret_list) > 0:
            ret_list.append(PromptStyle("text", ". "))

        return ret_list

    def __get_format_dict(self):
        ret_list = []
        if self.format is not None:
            ret_list.extend(
                [
                    PromptStyle("text", "Format the summary in "),
                    PromptStyle("argument", self.format),
                ]
            )

        if self.focus_on is not None:
            ret_list.extend(
                [
                    PromptStyle(
                        "text",
                        " with a focus on " if self.format is not None else "Focus on ",
                    ),
                    PromptStyle("argument", self.focus_on),
                ]
            )

        if len(ret_list) > 0:
            ret_list.append(PromptStyle("text", ". "))

        return ret_list

    def __get_tone_dict(self):
        """
        I want an {summary_type} summary in a {formal_tone} tone and {word_limit} words
        """
        ret_list = []
        pronoun = (
            "I"
            if self.summary_for is None or self.summary_for == PromptFor.ME.value
            else "They"
        )

        if self.summary_type is not None:
            ret_list.extend(
                [
                    PromptStyle("text", f"{pronoun} want an "),
                    PromptStyle("argument", self.summary_type),
                    PromptStyle("text", " summary"),
                ]
            )

        if self.summary_tone is not None:
            ret_list.extend(
                [
                    PromptStyle(
                        "text",
                        " in a "
                        if self.summary_type is not None
                        else f"{pronoun} want this in a ",
                    ),
                    PromptStyle("argument", self.summary_tone),
                    PromptStyle("text", " tone"),
                ]
            )

        if self.word_limit is not None:
            ret_list.extend(
                [
                    PromptStyle(
                        "text",
                        " and in " if len(ret_list) > 0 else f"{pronoun} want this in ",
                    ),
                    PromptStyle("argument", str(self.word_limit)),
                    PromptStyle("text", " words"),
                ]
            )

        if len(ret_list) > 0:
            ret_list.append(PromptStyle("text", ". "))

        return ret_list

    def __get_start_dict(self):
        ret_list = []
        ret_list.append(PromptStyle("text", "Summarise my document"))

        # format start
        if self.reason is not None:
            ret_list.extend(
                [PromptStyle("text", ", "), PromptStyle("argument", self.reason)]
            )

        ret_list.append(PromptStyle("text", ". "))

        return ret_list
