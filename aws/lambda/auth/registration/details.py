from os import environ
from requests import request  # type: ignore
from helpers.functions import get_path_parameters, tailo_http_response, TailoError  # type: ignore
from helpers.logging import get_tailo_logger  # type: ignore
from helpers.sentry import sentry_wrapper  # type: ignore

logger = get_tailo_logger()


@sentry_wrapper("registration_details_handler")
def handler(event, context):
    try:
        path_parameters = get_path_parameters(event)

        if not path_parameters:
            return tailo_http_response(TailoError.MISSING_PARAM, "Missing ticket ID")

        ticket_id = path_parameters.get("id", "").strip()

        if not ticket_id:
            return tailo_http_response(TailoError.MISSING_PARAM, "Missing ticket ID")

        response = request(
            "GET",
            f"{environ['LICENSE_URL']}/auth/register/details/{ticket_id}",
        )

        if not response.ok:
            return tailo_http_response(TailoError.INVALID_VALUE, "Invalid Ticket")

        response_json = response.json()

        if "data" not in response_json:
            return tailo_http_response(TailoError.INVALID_VALUE, "Missing ticket ID")

        return tailo_http_response(TailoError.OK, "OK", response_json["data"])
    except Exception as e:
        logger.exception(f"Exception in registerDetails: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
