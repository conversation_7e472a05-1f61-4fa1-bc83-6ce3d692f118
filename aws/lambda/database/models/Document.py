from os import environ
from datetime import datetime, timezone
from enum import Enum
from typing import List
from boto3 import client

from sqlalchemy import (
    JSON,
    VARCHAR,
    DateTime,
    ForeignKey,
    Text,
    BigInteger,
    Integer,
    Boolean,
)
from sqlalchemy.orm import Mapped, Session, mapped_column

from .Base import Base
from .DocumentUser import DocumentUser
from .Summarisation import Summarisation

from helpers.wrap import (
    session,
)


class DocumentStatus(Enum):
    UPLOADING = "Uploading"
    CHECKING_DOCUMENT = "Checking document"
    STARTING_EXTRACTION = "Starting content extraction"
    EXTRACTING = "Extracting"
    GENERATING_SUMMARY = "Generating Summary"
    FINISHING_UP = "Finishing up"
    COMPLETE = "Complete"
    ERROR = "Error"


expiry_time = 60


class Document(Base):
    __tablename__ = "documents"

    __mapper_args__ = {
        "polymorphic_identity": "document",
        "concrete": True,
    }

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255))
    owner_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    filename: Mapped[str] = mapped_column(VARCHAR(255))
    type: Mapped[str] = mapped_column(VARCHAR(255))
    path: Mapped[str] = mapped_column(Text)
    hash: Mapped[str] = mapped_column(VARCHAR(255))
    user_tracking_id: Mapped[str] = mapped_column(VARCHAR(255))
    filesize: Mapped[int] = mapped_column(BigInteger)
    extracted_path: Mapped[str] = mapped_column(Text)
    page_count: Mapped[int] = mapped_column(Integer)
    indexed: Mapped[bool] = mapped_column(Boolean)
    status: Mapped[str] = mapped_column(VARCHAR(255))
    status_message: Mapped[dict] = mapped_column(JSON)
    extracted_title: Mapped[str] = mapped_column(VARCHAR(255))
    display_name: Mapped[str] = mapped_column(VARCHAR(255))
    meta: Mapped[dict] = mapped_column(JSON)
    last_read_at: Mapped[datetime] = mapped_column(DateTime)
    date_created: Mapped[datetime] = mapped_column(DateTime)
    date_modified: Mapped[datetime] = mapped_column(DateTime)
    date_deleted: Mapped[datetime] = mapped_column(DateTime)

    def __repr__(self) -> str:
        return f"Document(id={self.id!r}, uuid={self.uuid!r}, owner_user_id={self.uuid!r}, name={self.filename!r}, path={self.path!r}, status={self.status!r}, date_created={self.date_created!r}, date_deleted={self.date_deleted!r})"

    @classmethod
    def create(cls, session: Session, **kw):
        docobj = cls(**kw)
        session.add(docobj)
        session.commit()
        session.refresh(docobj)

        DocumentUser.create(
            session=session, document_id=docobj.id, user_id=docobj.owner_user_id
        )

        return docobj

    @classmethod
    def by_id(cls, session: Session, id: int):
        return session.query(cls).filter(cls.id == id).first()

    @classmethod
    def by_path(cls, session: Session, path: str):
        return session.query(cls).filter(cls.path == path).first()

    @classmethod
    def by_uuid(cls, session: Session, uuid: str):
        return session.query(cls).filter(cls.uuid == uuid).first()

    @classmethod
    def by_uuids(cls, session: Session, uuids: List[str]):
        return session.query(cls).filter(cls.uuid.in_(uuids)).all()

    @classmethod
    def is_unindexed(cls, session: Session):
        return session.query(cls).filter(cls.indexed != True).all()

    @classmethod
    def is_indexed(cls, session: Session):
        return session.query(cls).filter(cls.indexed == True).all()

    def to_response_json(self, user_id):
        data = {
            "id": self.uuid,
            "filename": self.filename,
            "display_name": (
                self.display_name
                if self.display_name is not None and self.display_name.strip() != ""
                else self.filename  # For older documents that don't have a display_name set
            ),
            "extracted_title": self.extracted_title,
            "type": self.type,
            "status": self.status,
            "filesize": self.filesize,
            "last_read_at": (
                str(self.last_read_at.replace(tzinfo=timezone.utc))
                if self.last_read_at
                else None
            ),
            "date_created": str(self.date_created.replace(tzinfo=timezone.utc)),
            "last_modified": str(self.date_modified.replace(tzinfo=timezone.utc)),
            "is_owner": self.owner_user_id == user_id,
        }

        if self.status == DocumentStatus.ERROR.value:
            data["error_details"] = self.status_message

        return data

    def to_response_json_with_content(self, user_id, user_uuid, query_parameters):
        data = self.to_response_json(user_id)

        bucket_name = environ.get("BUCKET_NAME", "")

        if not bucket_name or bucket_name.strip() == "":
            return data

        s3_client = client("s3", region_name=session.region_name)

        # Generate a presigned URL to the extracted JSON contents.
        extracted_presigned_url = s3_client.generate_presigned_url(
            "get_object",
            Params={
                "Bucket": bucket_name,
                "Key": self.path + "/extracted.json",
            },
            ExpiresIn=expiry_time,
        )

        # Generate a presigned URL to the document state JSON contents.
        state_presigned_params = {
            "Bucket": bucket_name,
            "Key": f"{user_uuid}/{self.uuid}/state.json",
        }

        if "version" in query_parameters:
            state_presigned_params["VersionId"] = query_parameters["version"]

        state_presigned_url = s3_client.generate_presigned_url(
            "get_object",
            Params=state_presigned_params,
            ExpiresIn=expiry_time,
        )

        # Fetch all versions of the document state JSON file.
        state_versions_response = s3_client.list_object_versions(
            Bucket=bucket_name, Prefix=f"{user_uuid}/{self.uuid}/state.json"
        )
        state_versions = state_versions_response.get("Versions", [])

        data["extraction"] = {"url": extracted_presigned_url}

        data["state"] = {
            "versions": [
                {
                    "id": version.get("VersionId"),
                    "modified": datetime.timestamp(version.get("LastModified")),
                }
                for version in state_versions
            ],
            "url": state_presigned_url,
        }

        return data

    def to_json_state_response(self, user_uuid, query_parameters):
        bucket_name = environ.get("BUCKET_NAME", "")

        if not bucket_name or bucket_name.strip() == "":
            return

        s3_client = client("s3", region_name=session.region_name)

        # Generate a presigned URL to the document state JSON contents.
        state_presigned_params = {
            "Bucket": bucket_name,
            "Key": f"{user_uuid}/{self.uuid}/state.json",
        }

        if "version" in query_parameters:
            state_presigned_params["VersionId"] = query_parameters["version"]

        state_presigned_url = s3_client.generate_presigned_url(
            "get_object",
            Params=state_presigned_params,
            ExpiresIn=expiry_time,
        )

        # Fetch all versions of the document state JSON file.
        state_versions_response = s3_client.list_object_versions(
            Bucket=bucket_name, Prefix=f"{user_uuid}/{self.uuid}/state.json"
        )
        state_versions = state_versions_response.get("Versions", [])

        data = {
            "versions": [
                {
                    "id": version.get("VersionId"),
                    "modified": datetime.timestamp(version.get("LastModified")),
                }
                for version in state_versions
            ],
            "url": state_presigned_url,
        }

        return data

    def set_status(self, session: Session, status: DocumentStatus, message: dict = {}):
        self.status = status.value
        if len(message) > 0:
            self.status_message = message
        session.commit()

    def get_summarisations(self, session: Session):
        summarisations: List[Summarisation] = (
            session.query(Summarisation)
            .filter(Summarisation.document_id == self.id)
            .all()
        )

        return summarisations
