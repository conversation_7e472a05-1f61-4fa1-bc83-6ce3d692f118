from sqlalchemy import <PERSON><PERSON><PERSON>, VARCHAR
from sqlalchemy.orm import Mapped, Session, mapped_column

from .Base import Base

class TTSVoice(Base):
    __tablename__ = "tts_voices"

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255))
    name: Mapped[str] = mapped_column(VARCHAR(255))
    external_id: Mapped[str] = mapped_column(VARCHAR(255))

    @classmethod
    def by_name(cls, session: Session, name: str):
        return session.query(cls).filter(cls.name == name).first()

    @classmethod
    def by_external_id(cls, session: Session, external_id: str):
        return session.query(cls).filter(cls.external_id == external_id).first()
