from datetime import datetime
from hashlib import sha256

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, Session, mapped_column

from .Base import Base


class UserSession(Base):
    __tablename__ = "user_sessions"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    refresh_token: Mapped[str] = mapped_column(VARCHAR(255))
    issued_at: Mapped[datetime] = mapped_column(DateTime())
    expires_at: Mapped[datetime] = mapped_column(DateTime())

    def __init__(self, user_id, refresh_token, issued_at, expires_at):
        self.user_id = user_id
        self.refresh_token = sha256(refresh_token.encode("utf-8")).hexdigest()
        self.issued_at = issued_at
        self.expires_at = expires_at

    def __repr__(self) -> str:
        return f"UserSession(id={self.id!r}, user_id={self.user_id!r}, refresh_token={self.refresh_token!r}, issued_at={self.issued_at!r}, expires_at={self.expires_at!r})"

    @classmethod
    def create(cls, session: Session, **kw):
        obj = cls(**kw)
        session.add(obj)
        session.commit()
        session.refresh(obj)
        return obj
