from datetime import datetime, timezone
from typing import List

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, Session, mapped_column

from .Base import Base
from .User import User


class BetaEmail(Base):
    __tablename__ = "beta_emails"

    email: Mapped[str] = mapped_column(VARCHAR(255), primary_key=True)
    added_by: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    deleted_by: Mapped[int] = mapped_column(ForeignKey("users.id"))
    claimed_by: Mapped[int] = mapped_column(ForeignKey("users.id"))
    date_created: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    date_deleted: Mapped[datetime] = mapped_column(DateTime)

    def __repr__(self) -> str:
        return f"BetaEmail(email={self.email!r}, added_by={self.added_by!r}, deleted_by={self.deleted_by!r}, claimed_by={self.claimed_by!r}, date_created={self.date_created!r}, date_deleted={self.date_deleted!r})"

    @classmethod
    def create(cls, session: Session, **kw):
        obj = cls(**kw)
        session.add(obj)
        session.commit()
        session.refresh(obj)

        return obj

    @classmethod
    def get_by_email(cls, session: Session, email: str):
        return session.query(cls).filter(cls.email == email).first()

    @classmethod
    def get_all(cls, session: Session):
        emails: List[BetaEmail] = session.query(cls).all()
        return emails

    def set_claimed_by(self, session: Session, claimed_by: int):
        self.claimed_by = claimed_by
        session.commit()

    def to_response_json(self, session: Session):
        added_by = User.get_full_name_or_email_by_id(session, self.added_by)
        deleted_by = User.get_full_name_or_email_by_id(session, self.deleted_by)
        claimed_by = User.get_full_name_or_email_by_id(session, self.claimed_by)
        date_created = str(self.date_created.replace(tzinfo=timezone.utc))
        date_deleted = (
            str(self.date_deleted.replace(tzinfo=timezone.utc))
            if self.date_deleted
            else None
        )

        data = {
            "email": self.email,
            "added_by": added_by,
            "deleted_by": deleted_by,
            "claimed_by": claimed_by,
            "date_created": date_created,
            "date_deleted": date_deleted,
        }
        return data
