from typing import List
from datetime import datetime
from sqlalchemy.orm import Mapped, Session, mapped_column
from sqlalchemy import ForeignKey, VARCHAR, Text, DateTime, INT

from .Base import Base


class DocumentAudio(Base):
    __tablename__ = "document_audio"

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255))
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"))
    owner_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    block_id: Mapped[str] = mapped_column(VARCHAR(255))
    voice_id: Mapped[str] = mapped_column(VARCHAR(255))
    path: Mapped[str] = mapped_column(Text)
    duration: Mapped[int] = mapped_column(INT)
    date_created: Mapped[datetime] = mapped_column(DateTime)
    date_modified: Mapped[datetime] = mapped_column(DateTime)
    date_deleted: Mapped[datetime] = mapped_column(DateTime)

    def __repr__(self) -> str:
        return f"DocumentAudio(id={self.id!r}, document_id={self.document_id!r}, owner_user_id={self.owner_user_id!r})"

    @classmethod
    def create(cls, session: Session, **kw):
        obj = cls(**kw)
        session.add(obj)
        session.commit()
        session.refresh(obj)
        return obj

    @classmethod
    def by_document_id(cls, session: Session, document_id: int):
        res: List[DocumentAudio] = (
            session.query(cls).filter(cls.document_id == document_id).all()
        )
        return res

    @classmethod
    def by_block_id(cls, session: Session, document_id: str, block_id: str, voice_id: str):
        res: DocumentAudio = (
            session.query(cls)
            .filter(cls.document_id == document_id)
            .filter(cls.block_id == block_id)
            .filter(cls.voice_id == voice_id)
            .first()
        )
        return res

    @classmethod
    def by_owner_user_id(cls, session: Session, owner_user_id: int):
        res: List[DocumentAudio] = (
            session.query(cls).filter(cls.owner_user_id == owner_user_id).all()
        )
        return res

    @classmethod
    def by_path(cls, session: Session, path: int):
        res: List[DocumentAudio] = session.query(cls).filter(cls.path == path).all()
        return res
