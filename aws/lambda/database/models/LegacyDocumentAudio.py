import calendar
from typing import List
from datetime import datetime, timezone
from sqlalchemy import Foreign<PERSON>ey, VARCHAR, Text, DateTime, JSON
from sqlalchemy.orm import Mapped, Session, mapped_column
from data.features import FeatureIntervals

from .Base import Base


class LegacyDocumentAudio(Base):
    __tablename__ = "legacy_document_audio"

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255))
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"))
    section_id: Mapped[str] = mapped_column(VARCHAR(255))
    paragraph_id: Mapped[str] = mapped_column(VARCHAR(255))
    owner_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    external_task_id: Mapped[str] = mapped_column(VARCHAR(255))
    tts_voice_id: Mapped[int] = mapped_column(ForeignKey("tts_voices.id"))
    name: Mapped[str] = mapped_column(VARCHAR(255))
    type: Mapped[str] = mapped_column(VARCHAR(255))
    path: Mapped[str] = mapped_column(Text)
    status: Mapped[str] = mapped_column(VARCHAR(255))
    file_metadata: Mapped[dict] = mapped_column(JSON)
    date_created: Mapped[datetime] = mapped_column(DateTime)
    date_modified: Mapped[datetime] = mapped_column(DateTime)
    date_deleted: Mapped[datetime] = mapped_column(DateTime)

    def __repr__(self) -> str:
        return f"LegacyDocumentAudio(id={self.id!r}, document_id={self.document_id!r}, owner_user_id={self.owner_user_id!r})"

    @classmethod
    def create(cls, session: Session, **kw):
        obj = cls(**kw)
        session.add(obj)
        session.commit()
        session.refresh(obj)
        return obj

    @classmethod
    def by_external_task_id(cls, session: Session, external_task_id: int):
        res: List[LegacyDocumentAudio] = (
            session.query(cls).filter(cls.external_task_id == external_task_id).all()
        )
        return res

    @classmethod
    def by_document_id(cls, session: Session, document_id: int):
        res: List[LegacyDocumentAudio] = (
            session.query(cls).filter(cls.document_id == document_id).all()
        )
        return res

    @classmethod
    def by_section_id(cls, session: Session, document_id: str, section_id: str):
        res: LegacyDocumentAudio = (
            session.query(cls)
            .filter(cls.document_id == document_id)
            .filter(cls.section_id == section_id)
            .all()
        )
        return res

    @classmethod
    def by_paragraph_id(
        cls,
        session: Session,
        document_id: int,
        section_id: str,
        paragraph_id: str,
        tts_voice_id: int,
    ):
        res: LegacyDocumentAudio = (
            session.query(cls)
            .filter(cls.document_id == document_id)
            .filter(cls.section_id == section_id)
            .filter(cls.paragraph_id == paragraph_id)
            .filter(cls.tts_voice_id == tts_voice_id)
            .first()
        )
        return res

    @classmethod
    def by_owner_user_id(cls, session: Session, owner_user_id: int):
        res: List[LegacyDocumentAudio] = (
            session.query(cls).filter(cls.owner_user_id == owner_user_id).all()
        )
        return res

    @classmethod
    def by_path(cls, session: Session, path: int):
        res: List[LegacyDocumentAudio] = session.query(cls).filter(cls.path == path).all()
        return res
