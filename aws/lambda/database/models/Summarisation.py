import json
from datetime import datetime, timezone
from enum import Enum
from os import environ

from boto3 import Session as AwsSession
from boto3 import client
from sqlalchemy import <PERSON>SO<PERSON>, VARCHAR, DateTime, ForeignKey, Text
from sqlalchemy.orm import Mapped, Session, mapped_column

from .Base import Base


class SummarisationStatus(Enum):
    QUEUED = "queued"
    PROCESSING = "processing"
    READY = "ready"
    ERROR = "error"


class SummarisationType(Enum):
    WHOLE_DOC = "whole doc"
    EXCERPT = "excerpt"


class BuilderMethod(Enum):
    BUILDER = "builder"
    CUSTOM = "custom"


class Summarisation(Base):
    __tablename__ = "summarisations"

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255))
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"))
    name: Mapped[str] = mapped_column(VARCHAR(255))
    type: Mapped[str] = mapped_column(VARCHAR(255))
    path: Mapped[str] = mapped_column(Text)
    prompt: Mapped[str] = mapped_column(Text)
    status: Mapped[str] = mapped_column(VARCHAR(255))
    status_message: Mapped[dict] = mapped_column(JSON)
    date_created: Mapped[datetime] = mapped_column(DateTime)
    date_modified: Mapped[datetime] = mapped_column(DateTime)
    date_deleted: Mapped[datetime] = mapped_column(DateTime)
    form_fields: Mapped[str] = mapped_column(Text)
    meta: Mapped[str] = mapped_column(Text)

    def __repr__(self) -> str:
        return f"Summarisation(id={self.id!r}, uuid={self.uuid!r}, document_id={self.document_id!r}, name={self.name!r}, document_id={self.document_id!r}, type={self.type!r}, path={self.path!r}, status={self.status!r}, date_created={self.date_created!r}, date_deleted={self.date_deleted!r}, form_fields={self.form_fields!r})"

    @classmethod
    def create(cls, session: Session, **kw):
        sumobj = cls(**kw)
        session.add(sumobj)
        session.commit()
        session.refresh(sumobj)

        return sumobj

    @classmethod
    def by_id(cls, session: Session, id: int):
        return session.query(cls).filter(cls.id == id).first()

    @classmethod
    def by_path(cls, session: Session, path: str):
        return session.query(cls).filter(cls.path == path).first()

    @classmethod
    def by_uuid(cls, session: Session, uuid: str):
        return session.query(cls).filter(cls.uuid == uuid).first()

    def to_response_json(self, session: AwsSession, include_summary=False):
        data = {
            "id": self.uuid,
            "prompt": self.prompt,
            "builder_method": self.get_builder_method(),
            "status": self.status,
            "date_created": str(self.date_created.replace(tzinfo=timezone.utc)),
        }

        if include_summary:
            s3_client = client("s3", region_name=session.region_name)
            file_object = s3_client.get_object(
                Bucket=environ["BUCKET_NAME"], Key=self.path
            )
            file_contents = file_object["Body"].read().decode("utf-8")
            data["summary"] = file_contents

        if self.status == SummarisationStatus.ERROR.value:
            data["error_details"] = self.status_message

        return data

    def set_status(
        self, session: Session, status: SummarisationStatus, message: dict = {}
    ):
        self.status = status.value
        if len(message) > 0:
            self.status_message = message
        session.commit()

    def get_builder_method(self):
        form_fields = json.loads(self.form_fields)
        if "freeTextPrompt" in form_fields and form_fields["freeTextPrompt"] != "":
            return BuilderMethod.CUSTOM.value

        return BuilderMethod.BUILDER.value
