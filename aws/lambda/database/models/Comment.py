from datetime import datetime
from typing import List
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Integer, ForeignKey, Text, DateTime, select
from sqlalchemy.orm import Mapped, Session, mapped_column, relationship

from .Tag import Tag
from .Base import Base


class Comment(Base):
    __tablename__ = "comments"

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255), unique=True)
    thread_id: Mapped[int] = mapped_column(
        ForeignKey("comment_threads.id"), nullable=False
    )
    owner_user_id: Mapped[int] = mapped_column(Integer, nullable=False)
    text: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    deleted_at: Mapped[datetime] = mapped_column(DateTime)

    thread = relationship("CommentThread", back_populates="comments")

    tags: Mapped[List["Tag"]] = relationship(
        "Tag",
        primaryjoin="and_(Comment.id==Taggable.taggable_id, Taggable.taggable_type=='comment')",
        secondary="taggables",
        secondaryjoin="Taggable.tag_id==Tag.id",
        back_populates="comments",
        overlaps="taggable",
    )

    def __repr__(self) -> str:
        return (
            f"Comment(id={self.id!r}, uuid={self.uuid!r}, thread_id={self.thread_id!r}, "
            f"owner_user_id={self.owner_user_id!r}, text={self.text!r}, created_at={self.created_at!r}, "
            f"updated_at={self.updated_at!r}, deleted_at={self.deleted_at!r})"
        )

    @classmethod
    def create(cls, session: Session, **kwargs) -> "Comment":
        """Create a new Comment instance and add it to the session."""
        comment = cls(**kwargs)
        session.add(comment)
        session.flush()
        return comment

    @classmethod
    def by_uuid(cls, session: Session, uuid: str) -> "Comment":
        """Retrieve a Comment instance by its UUID."""
        return session.query(cls).filter(cls.uuid == uuid).first()

    @classmethod
    def by_thread_id(cls, session: Session, thread_id: int):
        """Retrieve all Comment instances by thread_id."""
        return (
            session.query(cls)
            .filter(cls.thread_id == thread_id, cls.deleted_at == None)
            .all()
        )

    def to_response_json(self) -> dict:
        """Convert the Comment instance to a JSON-serializable dictionary, including tags if present."""
        return {
            "comment_id": self.uuid,
            "text": self.text,
            "created_at": str(self.created_at),
            "updated_at": str(self.updated_at),
            "tags": [{"id": str(tag.uuid), "value": tag.name} for tag in self.tags],
        }
