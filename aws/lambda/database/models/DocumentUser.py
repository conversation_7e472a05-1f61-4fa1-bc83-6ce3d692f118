from typing import List

from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy.orm import Mapped, Session, mapped_column

from .Base import Base


class DocumentUser(Base):
    __tablename__ = "document_users"

    id: Mapped[int] = mapped_column(primary_key=True)
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"))
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))

    def __repr__(self) -> str:
        return f"DocumentUser(id={self.id!r}, document_id={self.document_id!r}, user_id={self.user_id!r})"

    @classmethod
    def create(cls, session: Session, **kw):
        obj = cls(**kw)
        session.add(obj)
        session.commit()
        session.refresh(obj)
        return obj

    @classmethod
    def by_user_id(cls, session: Session, user_id: int):
        res: List[DocumentUser] = (
            session.query(cls).filter(cls.user_id == user_id).all()
        )
        return res
