from sqlalchemy import VARCHAR
from sqlalchemy.orm import Mapped, mapped_column

from .Base import Base


class EarlyAccess(Base):
    __tablename__ = "early_access"

    id: Mapped[int] = mapped_column(primary_key=True)
    identifier: Mapped[str] = mapped_column(VARCHAR(255))
    password: Mapped[str] = mapped_column(VARCHAR(255))

    def __repr__(self) -> str:
        return f"EarlyAccess(id={self.id!r}, identifier={self.identifier!r}, password={self.password!r})"
