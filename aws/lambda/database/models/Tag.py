from .Taggable import Taggable
from .User import User
from typing import List
from datetime import datetime
from sqlalchemy import VARCHAR, ForeignKey, Text, DateTime, insert, delete, select, or_
from sqlalchemy.orm import Mapped, Session, mapped_column, relationship, joinedload
from .Base import Base


class Tag(Base):
    __tablename__ = "tags"

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255), unique=True)
    name: Mapped[str] = mapped_column(Text, nullable=False)
    owner_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    taggable = relationship("Taggable", back_populates="tag")

    comments = relationship(
        "Comment",
        primaryjoin="and_(Tag.id==Taggable.tag_id, Taggable.taggable_type=='comment')",
        secondary="taggables",
        secondaryjoin="Comment.id==Taggable.taggable_id",
        back_populates="tags",
        overlaps="taggable,tag",
    )

    def __repr__(self) -> str:
        return (
            f"Tag(id={self.id!r}, uuid={self.uuid!r}, name={self.name!r}, "
            f"owner_user_id={self.owner_user_id!r}, created_at={self.created_at!r})"
        )

    @classmethod
    def bulk_create(
        cls,
        session: Session,
        owner_user: User,
        values: List[str],
    ) -> list["Tag"]:
        """
        Bulk creates tags with the given list of tag names
        """
        tag_args = [{"name": name, "owner_user_id": owner_user.id} for name in values]

        tags = session.scalars(insert(Tag).returning(Tag), tag_args)

        return list(tags.all())

    @classmethod
    def get_by_ids(cls, session: Session, ids: List[int]) -> List["Tag"]:
        """
        Retrieve Tags based on a list of tag IDs.
        """
        return session.query(cls).filter(cls.id.in_(ids)).all()

    @classmethod
    def get_by_uuids(cls, session: Session, uuids: List[str]) -> List["Tag"]:
        """
        Get's all tags based on the list of uuids provided
        """
        return list(session.query(cls).filter(cls.uuid.in_(uuids)).all())

    @classmethod
    def get_by_names(
        cls,
        session: Session,
        names: List[str],
        user_id: int,
    ) -> List["Tag"]:
        """
        Gets tags by name limited to the owner user
        """
        or_operator = or_(*[cls.name.ilike(name) for name in names])
        return list(
            session.query(cls).filter(cls.owner_user_id == user_id, or_operator).all()
        )

    @classmethod
    def get_users_linked_tags(cls, session: Session, user: User) -> list["Tag"]:
        """
        Returns all users tags that have a link in the `taggables` table
        """
        res = (
            session.query(cls)
            .filter(cls.owner_user_id == user.id)
            .order_by(cls.created_at.desc())
            .options(joinedload(cls.taggable, innerjoin=True))
            .all()
        )
        return list(res)

    def to_response_json(self):
        """
        Formats the tag to valid JSON response for users
        """
        return {
            "id": str(self.uuid),
            "value": self.name,
            "created_at": str(self.created_at),
        }

    @classmethod
    def get_orphaned_tags(cls, session: Session, user_id: int) -> List["Tag"]:
        """
        Will get all of the users tags that don't have a link in the `taggables` table
        """
        subq = select(Taggable).where(cls.id == Taggable.tag_id).exists()
        result = session.query(cls).where(cls.owner_user_id == user_id, ~subq).all()
        return list(result)

    @classmethod
    def bulk_delete(
        cls,
        session: Session,
        tags: List["Tag"],
    ):
        """
        Bulk deletes the provided tags from the `tags` table
        """
        tag_ids = [tag.id for tag in tags]
        cls.bulk_remove_by_ids(session, tag_ids)

    @classmethod
    def bulk_remove_by_ids(cls, session: Session, tag_ids: List[int]):
        """
        Bulk deletes tags from the `tags` table that match the list of provided ids
        """
        delete_stmt = delete(cls).where(cls.id.in_(tag_ids))
        session.execute(delete_stmt)
        session.flush()
