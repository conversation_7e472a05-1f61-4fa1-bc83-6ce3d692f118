from sqlalchemy import <PERSON>RC<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.orm import Mapped, Session, mapped_column

from .Base import Base


class UserSetting(Base):
    __tablename__ = "user_settings"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    key: Mapped[str] = mapped_column(VARCHAR(255))
    str_value: Mapped[str] = mapped_column(VARCHAR(255))
    int_value: Mapped[int] = mapped_column(Integer)
    bool_value: Mapped[bool] = mapped_column(Boolean)

    def __init__(self, user_id: int, key: str, value):
        self.user_id = user_id
        self.key = key
        self.set_value(value)

    def set_value(self, value):
        value_type = type(value)

        if value_type is int:
            self.int_value = value
            self.str_value = None
            self.bool_value = None
        elif value_type is bool:
            self.bool_value = value
            self.str_value = None
            self.int_value = None
        else:  # All else fails string it
            self.str_value = str(value)
            self.bool_value = None
            self.int_value = None

    def get_value(self):
        return self.bool_value or self.int_value or self.str_value

    @classmethod
    def create(cls, session: Session, **kw):
        obj = cls(**kw)
        session.add(obj)
        session.commit()
        session.refresh(obj)
        session.expunge(obj)
        return obj

    def to_json(self):
        return {self.key: self.get_value()}
