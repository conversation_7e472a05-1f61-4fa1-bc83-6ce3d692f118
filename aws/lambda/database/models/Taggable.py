from typing import List
from datetime import datetime
from sqlalchemy import Integer, ForeignKey, Text, DateTime, delete
from sqlalchemy.orm import Mapped, Session, mapped_column, relationship, joinedload
from sqlalchemy.dialects.postgresql import insert
from .Base import Base
from enum import Enum


# ? Overkill? Maybe but good basis for going forward
class TaggableType(Enum):
    COMMENT = "comment"


class Taggable(Base):
    __tablename__ = "taggables"

    id: Mapped[int] = mapped_column(primary_key=True)
    tag_id: Mapped[int] = mapped_column(ForeignKey("tags.id"), nullable=False)
    taggable_type: Mapped[str] = mapped_column(Text, nullable=False)
    taggable_id: Mapped[str] = mapped_column(Integer, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    tag = relationship(
        "Tag",
        back_populates="taggable",
        primaryjoin="and_(Taggable.tag_id == Tag.id)",
        overlaps="comment,tags",
    )

    def __repr__(self) -> str:
        return (
            f"Taggable(id={self.id!r}, tag_id={self.tag_id!r}, taggable_type={self.taggable_type!r}, "
            f"taggable_id={self.taggable_id!r}, created_at={self.created_at!r})"
        )

    @classmethod
    def bulk_tag(
        cls,
        session: Session,
        tag_ids: List[int],
        taggable_type: TaggableType,
        taggable_id: int,
    ) -> List["Taggable"]:
        """
        Bulk assigns tags to the provided taggable item
        """
        taggable_args = [
            {
                "tag_id": id,
                "taggable_type": taggable_type.value,
                "taggable_id": taggable_id,
            }
            for id in tag_ids
        ]

        taggables = session.scalars(
            insert(cls)
            .on_conflict_do_nothing(constraint="unique_taggable_item")
            .returning(cls),
            taggable_args,
        )

        return list(taggables.all())

    @classmethod
    def bulk_remove(
        cls,
        session: Session,
        taggables: List["Taggable"],
    ) -> int:
        """
        Bulk removes the provided taggable items from the `taggables` table
        """
        taggable_ids = [taggable.id for taggable in taggables]
        return cls.bulk_remove_by_ids(session, taggable_ids)

    @classmethod
    def bulk_remove_by_ids(cls, session: Session, taggable_ids: List[int]) -> int:
        """
        Bulk removes taggable items that have the provided ids from the `taggables` table
        """
        delete_stmt = delete(cls).where(cls.id.in_(taggable_ids))
        result = session.execute(delete_stmt)
        affected_rows: int = result.rowcount  # type: ignore
        print(affected_rows)
        session.flush()
        return affected_rows

    @classmethod
    def by_taggable_id(
        cls, session: Session, taggable_id: int, taggable_type: TaggableType
    ) -> list["Taggable"]:
        """
        Retrieve all Taggable instances by taggable_id
        """
        return (
            session.query(cls)
            .filter(
                cls.taggable_id == taggable_id, cls.taggable_type == taggable_type.value
            )
            .options(joinedload(cls.tag))
            .all()
        )

    @classmethod
    def by_taggable_ids(
        cls, session: Session, taggable_id: List[int], taggable_type: TaggableType
    ):
        """
        Grabs all taggable records by the provided ids
        """
        return (
            session.query(cls)
            .where(
                cls.taggable_id.in_(taggable_id),
                cls.taggable_type == taggable_type.value,
            )
            .options(joinedload(cls.tag))
            .all()
        )
