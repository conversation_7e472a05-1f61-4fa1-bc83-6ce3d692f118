from sqlalchemy import JSO<PERSON>, VARCHAR
from sqlalchemy.orm import Mapped, Session, mapped_column

from .Base import Base


class Role(Base):
    __tablename__ = "roles"

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(VARCHAR(255))
    definition: Mapped[dict] = mapped_column(JSON())

    def __repr__(self) -> str:
        return (
            f"Role(id={self.id!r}, name={self.name!r}, definition={self.definition!r})"
        )

    @classmethod
    def by_name(cls, session: Session, name: str):
        return session.query(cls).filter(cls.name == name).first()
