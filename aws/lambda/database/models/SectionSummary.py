from typing import List
from datetime import datetime
from sqlalchemy import ForeignKey, VARCHAR, Text, DateTime
from sqlalchemy.orm import Mapped, Session, mapped_column
from sqlalchemy.dialects.postgresql import ARRAY

from .Base import Base

class SectionSummary(Base):
    __tablename__ = "section_summary"

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255))
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"))
    section_id: Mapped[str] = mapped_column(VARCHAR(255))
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    summary_response: Mapped[str] = mapped_column(ARRAY(Text))
    date_created: Mapped[datetime] = mapped_column(DateTime)
    date_modified: Mapped[datetime] = mapped_column(DateTime)
    date_deleted: Mapped[datetime] = mapped_column(DateTime)

    def __repr__(self) -> str:
        return f"SectionSummary(id={self.id!r}, uuid={self.uuid!r}, document_id={self.document_id!r}, section_id={self.section_id!r}, user_id={self.user_id!r}), summary_response={self.summary_response!r}, date_created={self.date_created!r}, date_modified={self.date_modified!r}, date_deleted={self.date_deleted!r}"

    @classmethod
    def create(cls, session: Session, **kw):
        obj = cls(**kw)
        session.add(obj)
        session.commit()
        session.refresh(obj)
        return obj

    @classmethod
    def by_external_task_id(cls, session: Session, external_task_id: int):
        res: List[SectionSummary] = (
            session.query(cls).filter(cls.external_task_id == external_task_id).all()
        )
        return res

    @classmethod
    def by_document_id(cls, session: Session, document_id: int):
        res: List[SectionSummary] = (
            session.query(cls).filter(cls.document_id == document_id).all()
        )
        return res

    @classmethod
    def by_section_id(cls, session: Session, document_id: int, section_id: str):
        res: SectionSummary = (
            session.query(cls)
            .filter(cls.document_id == document_id)
            .filter(cls.section_id == section_id)
            .first()
        )
        return res

    @classmethod
    def by_user_id(cls, session: Session, user_id: int):
        res: List[SectionSummary] = (
            session.query(cls).filter(cls.user_id == user_id).all()
        )
        return res

    @classmethod
    def by_path(cls, session: Session, path: int):
        res: List[SectionSummary] = session.query(cls).filter(cls.path == path).all()
        return res
