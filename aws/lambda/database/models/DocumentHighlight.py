from typing import List
from datetime import datetime
from sqlalchemy.orm import Mapped, Session, mapped_column
from sqlalchemy import ForeignKey, VARCHAR, JSON, DateTime

from .Base import Base

class DocumentHighlight(Base):
    __tablename__ = "document_highlights"

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255))
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"))
    owner_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    meta: Mapped[dict] = mapped_column(JSON)
    date_created: Mapped[datetime] = mapped_column(DateTime)
    date_modified: Mapped[datetime] = mapped_column(DateTime)

    def __repr__(self) -> str:
        return f"DocumentHighlight(id={self.id!r}, document_id={self.document_id!r}, owner_user_id={self.owner_user_id!r})"

    @classmethod
    def create(cls, session: Session, **kw):
        obj = cls(**kw)
        session.add(obj)
        session.commit()
        session.refresh(obj)
        return obj

    @classmethod
    def by_uuid(cls, session: Session, uuid: str):
        res: DocumentHighlight = (
            session.query(cls).filter(cls.uuid == uuid).first()
        )
        return res
    
    @classmethod
    def by_document_id(cls, session: Session, document_id: int):
        res: List[DocumentHighlight] = (
            session.query(cls).filter(cls.document_id == document_id).all()
        )
        return res

    @classmethod
    def by_owner_user_id(cls, session: Session, owner_user_id: int):
        res: List[DocumentHighlight] = (
            session.query(cls).filter(cls.owner_user_id == owner_user_id).all()
        )
        return res
