from typing import List
from datetime import datetime
from .Comment import Comment

from sqlalchemy import JSON, VARCHAR, DateTime, Integer
from sqlalchemy.orm import Mapped, Session, joinedload, mapped_column, relationship

from .Base import Base


class CommentThread(Base):
    __tablename__ = "comment_threads"

    id: Mapped[int] = mapped_column(primary_key=True)
    uuid: Mapped[str] = mapped_column(VARCHAR(255), unique=True)
    threadable_id: Mapped[int] = mapped_column(Integer, nullable=False)
    threadable_type: Mapped[str] = mapped_column(VARCHAR(255), nullable=False)
    meta: Mapped[dict] = mapped_column(JSON)
    created_at: Mapped[datetime] = mapped_column(DateTime)
    updated_at: Mapped[datetime] = mapped_column(DateTime)
    deleted_at: Mapped[datetime] = mapped_column(DateTime)

    comments: Mapped[List[Comment]] = relationship(
        "Comment",
        back_populates="thread",
        primaryjoin="and_(Comment.thread_id == CommentThread.id, Comment.deleted_at == None)",
    )

    def __repr__(self) -> str:
        return (
            f"CommentThread(id={self.id!r}, uuid={self.uuid!r}, threadable_id={self.threadable_id!r}, "
            f"threadable_type={self.threadable_type!r}, meta={self.meta!r}, created_at={self.created_at!r}, "
            f"updated_at={self.updated_at!r}, deleted_at={self.deleted_at!r})"
        )

    @classmethod
    def create(cls, session: Session, **kwargs) -> "CommentThread":
        """Create a new CommentThread instance and add it to the session."""
        comment_thread = cls(**kwargs)
        session.add(comment_thread)
        session.commit()
        session.refresh(comment_thread)
        return comment_thread

    @classmethod
    def by_threadable_id(
        cls, session: Session, threadable_id: int
    ) -> list["CommentThread"]:
        """Retrieve all CommentThread instances by threadable_id."""
        return (
            session.query(cls)
            .filter(cls.threadable_id == threadable_id, cls.deleted_at == None)
            .options(joinedload(CommentThread.comments))
            .all()
        )

    @classmethod
    def by_uuid(cls, session: Session, uuid: str) -> "CommentThread":
        """Retrieve a CommentThread instance by its UUID."""
        return session.query(cls).filter(cls.uuid == uuid).first()

    def to_response_json(self) -> dict:
        """Convert the CommentThread instance and its comments to a JSON-serializable dictionary."""
        return {
            "id": self.uuid,
            "meta": self.meta,
            "created_at": str(self.created_at),
            "updated_at": str(self.updated_at),
        }
