from datetime import datetime, timezone, timedelta
from typing import List, Union

from sqlalchemy import VA<PERSON>HAR, DateTime, extract
from sqlalchemy.orm import Mapped, Session, mapped_column

from data.features import FeatureIntervals

from .Base import Base
from .Document import Document
from .DocumentUser import DocumentUser
from .Role import Role
from .UserRole import User<PERSON><PERSON>
from .DocumentAudio import DocumentAudio
from .LegacyDocumentAudio import LegacyDocumentAudio


class User(Base):
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(primary_key=True)
    estendio_id: Mapped[str] = mapped_column(VARCHAR(255), unique=True, nullable=False)
    role: Mapped[str] = mapped_column(VARCHAR(255))
    created_at: Mapped[datetime] = mapped_column(DateTime)
    modified_at: Mapped[datetime] = mapped_column(DateTime)
    deleted_at: Mapped[datetime] = mapped_column(DateTime)

    def __repr__(self) -> str:
        return f"User(id={self.id!r}, estendio_id={self.estendio_id!r} role={self.role!r}, created_at={self.created_at!r}, modified_at={self.modified_at!r}, deleted_at={self.deleted_at!r})"

    def __init__(
        self,
        date_created,
        date_modified,
        estendio_id,
    ):
        self.estendio_id = estendio_id
        self.created_at = date_created
        self.modified_at = date_modified

    @classmethod
    def create_user(cls, session: Session, **kw):
        # TODO: Is there a better way to do this? We ideally don't want to commit within the classmethod
        obj = cls(**kw)
        session.add(obj)
        session.commit()
        session.refresh(obj)
        session.expunge(obj)
        return obj

    @classmethod
    def get_by_id(cls, session: Session, id: int, include_deleted: bool = False):
        res = None
        if include_deleted:
            res = session.query(cls).filter(cls.id == id).first()
        else:
            res = (
                session.query(cls).filter(cls.id == id, cls.deleted_at == None).first()
            )
        return res

    @classmethod
    def get_by_estendio_id(
        cls, session: Session, uuid: str, include_deleted: bool = False
    ):
        res = None
        if include_deleted:
            res = session.query(cls).filter(cls.estendio_id == uuid).first()
        else:
            res = (
                session.query(cls)
                .filter(cls.estendio_id == uuid, cls.deleted_at == None)
                .first()
            )
        return res

    def get_roles(self, session: Session):
        res: List[Role] = (
            session.query(Role)
            .join(UserRole, UserRole.role_id == Role.id)
            .filter(UserRole.user_id == self.id)
            .all()
        )

        return res

    def get_all_permissions(self, session: Session):
        res: List[dict] = []
        user_roles = self.get_roles(session=session)
        for role in user_roles:
            res = res + role.definition["permissions"]

        return res

    def switch_role(self, session: Session, old_role: int, new_role: int):
        user_role_item = (
            session.query(UserRole)
            .filter(UserRole.user_id == self.id, UserRole.role_id == old_role)
            .first()
        )

        if user_role_item is None:
            return

        user_role_item.role_id = new_role
        session.commit()

    def get_documents(
        self,
        session: Session,
        page: int = 1,
        limit: int = 1000,
        get_deleted: bool = False,
    ):
        if page < 1:
            page = 1
        calculated_offset = (page - 1) * limit
        # If page = 1 then take away by one and times by limit to get offset
        # e.g. page = 1, limit = 1000, offset = 0 getting the first 1000 results.
        # page = 2, limit = 1000, offset = 1000 getting the next 1000 results and so on

        document_deleted_filter = (
            Document.date_deleted != None
            if get_deleted
            else Document.date_deleted == None
        )

        user_documents: List[Document] = (
            session.query(Document)
            .filter(DocumentUser.user_id == self.id, document_deleted_filter)
            .join(DocumentUser, Document.id == DocumentUser.document_id)
            .order_by(Document.date_modified.desc())
            .offset(calculated_offset)
            .limit(limit)
            .all()
        )
        return user_documents

    def get_documents_by_uuids(self, session: Session, ids: List[str]):
        user_documents: List[Document] = (
            session.query(Document)
            .filter(DocumentUser.user_id == self.id, Document.uuid.in_(ids))
            .join(DocumentUser, Document.id == DocumentUser.document_id)
            .order_by(Document.date_modified.desc())
            .all()
        )
        return user_documents

    def can_read_doc(self, session: Session, document: Document):
        results = (
            session.query(DocumentUser)
            .filter(
                DocumentUser.document_id == document.id, DocumentUser.user_id == self.id
            )
            .first()
        )

        # True if there is a record, false if nothing is returned
        return results is not None

    def set_name(self, session: Session, name: str):
        self.name = name
        self.date_modified = datetime.now(tz=timezone.utc)
        session.commit()

    def set_surname(self, session: Session, surname: str):
        self.surname = surname
        self.date_modified = datetime.now(tz=timezone.utc)
        session.commit()

    def __get_document_interval_filter(
        self,
        cls: Union[type[Document], type[LegacyDocumentAudio], type[DocumentAudio]],
        interval: FeatureIntervals,
    ):
        ret_val: tuple = ()
        curdate = datetime.now(tz=timezone.utc)
        if interval == FeatureIntervals.DAILY:
            ret_val = (
                extract("day", cls.date_created) == curdate.day,
                extract("month", cls.date_created) == curdate.month,
                extract("year", cls.date_created) == curdate.year,
            )
        elif interval == FeatureIntervals.WEEKLY:
            ret_val = (
                extract("week", cls.date_created) == curdate.today().isocalendar().week,
                extract("month", cls.date_created) == curdate.month,
                extract("year", cls.date_created) == curdate.year,
            )
        elif interval == FeatureIntervals.MONTHLY:
            ret_val = (
                extract("month", cls.date_created) == curdate.month,
                extract("year", cls.date_created) == curdate.year,
            )
        elif interval == FeatureIntervals.YEARLY:
            ret_val = (extract("year", cls.date_created) == curdate.year,)

        return ret_val

    def get_document_for_interval(
        self,
        session: Session,
        interval: FeatureIntervals,
        date: Union[datetime, None] = None,
    ) -> List[Document]:
        if date is None:
            date = datetime.now(tz=timezone.utc)

        interval_filter = self.__get_document_interval_filter(Document, interval)

        user_documents: List[Document] = (
            session.query(Document)
            .filter(DocumentUser.user_id == self.id, *interval_filter)
            .join(DocumentUser, Document.id == DocumentUser.document_id)
            .order_by(Document.date_modified.desc())
            .all()
        )

        return user_documents

    def get_total_pages_for_interval(
        self,
        session: Session,
        interval: FeatureIntervals,
        date: Union[datetime, None] = None,
    ) -> int:
        if date is None:
            date = datetime.now(tz=timezone.utc)

        user_documents = self.get_document_for_interval(session, interval, date)

        # TODO: Ideally should be Postgres SUM but had issues with SqlAlchemy. Needs investigating further but this works for now
        total = 0
        for document in user_documents:
            if document.page_count is None:
                continue
            total += document.page_count

        return total

    def get_tts_for_interval(
        self,
        session: Session,
        interval: FeatureIntervals,
        date: Union[datetime, None] = None,
    ) -> List[DocumentAudio]:
        if date is None:
            date = datetime.now(tz=timezone.utc)

        interval_filter = self.__get_document_interval_filter(DocumentAudio, interval)

        user_documents: List[DocumentAudio] = (
            session.query(DocumentAudio)
            .filter(DocumentAudio.owner_user_id == self.id, *interval_filter)
            .all()
        )

        return user_documents

    def get_legacy_tts_for_interval(
        self,
        session: Session,
        interval: FeatureIntervals,
        date: Union[datetime, None] = None,
    ) -> List[LegacyDocumentAudio]:
        if date is None:
            date = datetime.now(tz=timezone.utc)

        interval_filter = self.__get_document_interval_filter(
            LegacyDocumentAudio, interval
        )

        user_documents: List[LegacyDocumentAudio] = (
            session.query(LegacyDocumentAudio)
            .filter(LegacyDocumentAudio.owner_user_id == self.id, *interval_filter)
            .all()
        )

        return user_documents

    def calculate_legacy_tts_quota(
        self,
        session: Session,
        interval: FeatureIntervals,
        date: Union[datetime, None] = None,
    ) -> timedelta:
        if date is None:
            date = datetime.now(tz=timezone.utc)

        document_audio = self.get_legacy_tts_for_interval(session, interval, date)

        tts_used = 0

        for audio in document_audio:
            metadata = audio.file_metadata

            # Check if we have metadata and a duration value.
            if metadata and "duration" in metadata:
                # Add up all metadata seconds.
                tts_used += metadata["duration"]

        ret_val = timedelta(seconds=tts_used)
        return ret_val

    def calculate_tts_quota(
        self,
        session: Session,
        interval: FeatureIntervals,
        date: Union[datetime, None] = None,
    ) -> timedelta:
        if date is None:
            date = datetime.now(tz=timezone.utc)

        document_audio = self.get_tts_for_interval(session, interval, date)

        used = 0

        for audio in document_audio:
            # Add up all metadata milliseconds.
            if audio.duration is not None:
                used += audio.duration

        ret_val = timedelta(seconds=used / 1000)

        return ret_val
