from os import environ
from typing import Union

from boto3 import Session  # type: ignore
from helpers.environment import is_local
from helpers.logging import get_tailo_logger
from pymysql import install_as_MySQLdb  # type: ignore
from sqlalchemy import URL, create_engine
from sqlalchemy.orm import Session as DBSession
from sqlalchemy.orm import sessionmaker

from .get_secret import get_secret_pass

install_as_MySQLdb()

__session = Session()

_logger = get_tailo_logger()


def get_dsn_url() -> URL:
    local = is_local()
    rds_host = environ["RDS_HOST"] if not local else environ["LOCAL_DB_HOST"]
    rds_port = int(environ["RDS_DB_PORT"]) if not local else int(environ["LOCAL_DB_PORT"])
    db_name = environ["RDS_DB_NAME"] if not local else environ["LOCAL_DB_NAME"]

    username, password = (
        (None, None)
        if local
        else get_secret_pass(session=__session, secret_name=environ["SECRET_NAME"])
    )
    if local:
        username = environ["LOCAL_DB_USER"]

    return URL.create(
        "postgresql",
        host=rds_host,
        port=rds_port,
        username=username,
        password=password,
        database=db_name,
    )


_engine = create_engine(get_dsn_url())
_session = sessionmaker(_engine)


class DB:
    __session: Union[DBSession, None] = None
    __expire_on_commit: bool

    def __init__(self, expire_on_commit=True):
        self.__expire_on_commit = expire_on_commit

    def __enter__(self):
        self.__session = _session()
        self.__session.begin()
        self.__session.expire_on_commit = self.__expire_on_commit
        return self.__session

    def __close_and_dispose(self):
        self.__session.close()
        _engine.pool.dispose()  # This will only dispose connections classed as idle which at this point the current one should be set as such

    def __exit__(self, exception_type, exception_value, exception_traceback):
        # Exception, print, rollback and return
        if exception_type is not None:
            self.__session.rollback()
            _logger.error("type %s", exception_type)
            _logger.error("value %s", exception_value)
            _logger.error("traceback %s", exception_traceback)
            self.__close_and_dispose()
            return

        self.__session.commit()
        self.__close_and_dispose()
