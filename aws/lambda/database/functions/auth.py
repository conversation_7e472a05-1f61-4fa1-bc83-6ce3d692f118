from os import environ
from datetime import datetime, timezone
from typing import Union, Tuple, List
from ..models import Role, UserR<PERSON>, User, DocumentUser, Document
from data import UserDetails


def _clean_document_uuids(uuids: str):
    return [uuid.strip() for uuid in uuids.split(",") if uuid.strip()]


def _get_roles(
    session,
    is_admin: bool,
) -> Union[Role, None]:
    role = Role.by_name(
        session,
        environ["ROLE_ADMIN_USER"] if is_admin else environ["ROLE_GENERAL_USER"],
    )

    return role


def _attach_preloaded_documents(session, user: User, is_assessor: bool):
    # Fetch and clean up UUIDs.
    assessor_doc_uuids = _clean_document_uuids(environ["PRELOADED_ASSESSOR_DOCUMENTS"])
    student_doc_uuids = _clean_document_uuids(environ["PRELOADED_STUDENT_DOCUMENTS"])

    # Fetch preloaded document UUIDs.
    uuids = assessor_doc_uuids if is_assessor else student_doc_uuids

    # If there are no UUIDs, ignore.
    if uuids is None:
        return

    # Fetch the documents by UUID.
    documents = Document.by_uuids(session=session, uuids=uuids)

    # Attach the documents to the user.
    for document in documents:
        DocumentUser.create(
            session=session,
            document_id=document.id,
            user_id=user.id,
        )


def create_user_and_role(
    session,
    details: UserDetails,
) -> Union[Tuple[User, List[Role]], None]:
    now = datetime.now(tz=timezone.utc)

    role = _get_roles(session, details.email.endswith("presentpal.co.uk"))

    # TODO: Ideally we should throw an exception that is tailo specific and caught in the handlers try catch
    if role is None:
        return None

    user = User.create_user(
        session=session,
        estendio_id=details.id,
        date_created=now,
        date_modified=now,
    )

    if user is None:
        return None

    UserRole.create(
        session=session,
        user_id=user.id,
        role_id=role.id,
    )

    is_assessor = details.occupation != "Studying"

    # Attach the preloaded documents to the newly created user.
    _attach_preloaded_documents(session=session, user=user, is_assessor=is_assessor)

    return user, [role]


def get_or_create_user(
    session,
    user_details: UserDetails,
) -> Union[tuple[User, List[Role]], None]:
    user = User.get_by_estendio_id(session=session, uuid=user_details.id)

    if user is None:
        return create_user_and_role(session, user_details)

    roles = user.get_roles(session=session)

    # TODO: Ideally we should throw an exception that is tailo specific and caught in the handlers try catch
    if roles is None:
        return None

    return user, roles
