"""
Document service module for database operations related to documents.
This module provides functions for persisting and managing documents in the database.
"""

from database.instance import DB
from database import Document

# Persists a document to the database.
def commit(document: Document) -> None:
    with DB(False) as db_session:
        db_session.add(document)
        db_session.commit()
        db_session.refresh(document)  # Just to be safe
        db_session.expunge_all()
