from datetime import datetime, timedelta, timezone
from os import environ

from boto3 import client
from database.instance import DB
from database import Document
from helpers.wrap import session
from helpers.logging import get_tailo_logger

logger = get_tailo_logger()

def handler(event, context):
    try:
        delete_day_limit = int(environ["DELETE_DAY_LIMIT"])

        comparison = datetime.now(tz=timezone.utc) - timedelta(days=delete_day_limit)
        logger.info(str(comparison))
        # Create the database items to link user to document
        with DB() as db_session:
            all_deleted = (
                db_session.query(Document)
                .filter(
                    Document.date_deleted != None, Document.date_deleted <= comparison
                )
                .all()
            )

            logger.info(len(all_deleted))
            if len(all_deleted) == 0:
                return

            bucket_name = environ["BUCKET_NAME"]
            s3_client = client("s3", region_name=session.region_name)

            ids = []
            for document in all_deleted:
                logger.info(f"Removing file {document.path}")
                response = s3_client.delete_object(
                    Bucket=bucket_name, Key=document.path
                )
                ids.append(document.id)

            db_session.query(Document).filter(Document.id.in_(ids)).delete()
            db_session.commit()
            logger.info(f"deleted {ids}")

    except Exception as e:
        logger.exception("Exception in deleted_checker: ", e)
