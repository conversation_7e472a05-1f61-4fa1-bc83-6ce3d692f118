from os import environ
from helpers.logging import get_tailo_logger
from database.instance import DB
from database import Document, User
from document_search.indexer import Indexer

bucket_name = environ["BUCKET_NAME"]
logger = get_tailo_logger()

def handler(event, context):
    try:
        # Fetch all documents that are not indexed
        with DB() as db_session:
          documents = Document.is_unindexed(session=db_session)

          for document in documents:
            # Fetch the user so we can attribute the indexed document.
            user = User.get_by_id(session=db_session, id=document.owner_user_id)

            if user is None:
              logger.exception(
                "There was a problem fetching the user for this document: ",
                document
              )

              break

            indexer = Indexer()
            indexer.index_document(
                user_uuid=user.estendio_id,
                document_uuid=document.uuid,
                bucket_name=bucket_name,
            )

    except Exception as e:
        logger.exception("Exception in index_scheduler: ", e)
