import pytest
from prompt_builder import <PERSON><PERSON><PERSON><PERSON><PERSON>, PromptFor, PromptFormat, SummaryType


def test_blank_prompt():
    assert PromptBuilder().build()
    prompt = PromptBuilder().build()

    assert str(prompt) == "Summarise my document."
    assert prompt.as_dict()


def test_reason():
    reason = "I cannot be arsed"
    assert PromptBuilder().set_reason(reason).build()

    prompt = PromptBuilder().set_reason(reason).build()

    assert str(prompt) == f"Summarise my document, {reason}."


def test_summary_for():
    base_statement = "Summarise my document.\n"
    me_statement = "This summary is for me"
    someoneelse_statement = "This summary is for someone else"

    prompt = PromptBuilder().set_summary_for(PromptFor.ME.value).build()

    assert str(prompt) == f"{base_statement}{me_statement}."
    assert prompt.as_dict()

    prompt = PromptBuilder().set_summary_for(PromptFor.SOMEONEELSE.value).build()

    assert str(prompt) == f"{base_statement}{someoneelse_statement}."
    assert prompt.as_dict()


def test_learning_differences():
    learning_differences_one_list = "ADHD,"
    learning_differences_one_out = "ADHD"
    learning_differences_six_list = (
        "ADHD, Dyslexia, Test2, Test3, Test4, a reading age of 12"
    )
    learning_differences_six_out = (
        "ADHD, Dyslexia, Test2, Test3, Test4, and a reading age of 12"
    )
    base_statement = "Summarise my document.\n"
    me_statement = "This summary is for me"
    someoneelse_statement = "This summary is for someone else"

    prompt = (
        PromptBuilder().set_learning_differences(learning_differences_one_list).build()
    )
    assert str(prompt) == f"{base_statement}I have {learning_differences_one_out}."

    prompt = (
        PromptBuilder().set_learning_differences(learning_differences_six_list).build()
    )
    assert str(prompt) == f"{base_statement}I have {learning_differences_six_out}."

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.SOMEONEELSE.value)
        .set_learning_differences(learning_differences_six_list)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}{someoneelse_statement}, they have {learning_differences_six_out}."
    )

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.ME.value)
        .set_learning_differences(learning_differences_six_list)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}{me_statement}, I have {learning_differences_six_out}."
    )


def test_student():
    learning_differences_one_list = "ADHD,"
    learning_differences_one_out = "ADHD"
    base_statement = "Summarise my document.\n"
    me_statement = "This summary is for me"
    someoneelse_statement = "This summary is for someone else"

    prompt = PromptBuilder().set_student(True).build()
    assert str(prompt) == f"{base_statement}I am a student."

    prompt = (
        PromptBuilder().set_summary_for(PromptFor.ME.value).set_student(True).build()
    )
    assert str(prompt) == f"{base_statement}{me_statement}. I am a student."

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.SOMEONEELSE.value)
        .set_student(True)
        .build()
    )
    assert (
        str(prompt) == f"{base_statement}{someoneelse_statement}. They are a student."
    )

    prompt = (
        PromptBuilder()
        .set_learning_differences(learning_differences_one_list)
        .set_student(True)
        .build()
    )

    assert (
        str(prompt)
        == f"{base_statement}I have {learning_differences_one_out}. I am a student."
    )

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.SOMEONEELSE.value)
        .set_learning_differences(learning_differences_one_list)
        .set_student(True)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}{someoneelse_statement}, they have {learning_differences_one_out}. They are a student."
    )

    prompt = PromptBuilder().set_student(False).build()
    assert str(prompt) == f"{base_statement}I am a worker."

    prompt = (
        PromptBuilder().set_summary_for(PromptFor.ME.value).set_student(False).build()
    )
    assert str(prompt) == f"{base_statement}{me_statement}. I am a worker."

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.SOMEONEELSE.value)
        .set_student(False)
        .build()
    )
    assert str(prompt) == f"{base_statement}{someoneelse_statement}. They are a worker."


def test_field():
    learning_differences_one_list = "ADHD,"
    learning_differences_one_out = "ADHD"
    field = "Linguistics"
    base_statement = "Summarise my document.\n"
    me_statement = "This summary is for me"
    someoneelse_statement = "This summary is for someone else"
    me_desc = "I am a"
    someoneelse_desc = "They are a"

    prompt = PromptBuilder().set_field(field).build()
    assert str(prompt) == f"{base_statement}I am working in {field}."

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.SOMEONEELSE.value)
        .set_field(field)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}{someoneelse_statement} and they are working in {field}."
    )

    prompt = (
        PromptBuilder().set_summary_for(PromptFor.ME.value).set_field(field).build()
    )
    assert str(prompt) == f"{base_statement}{me_statement} and I am working in {field}."

    prompt = PromptBuilder().set_student(True).set_field(field).build()
    assert str(prompt) == f"{base_statement}{me_desc} student, studying {field}."

    prompt = PromptBuilder().set_student(False).set_field(field).build()
    assert str(prompt) == f"{base_statement}{me_desc} worker, working in {field}."

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.SOMEONEELSE.value)
        .set_student(True)
        .set_field(field)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}{someoneelse_statement}. {someoneelse_desc} student, studying {field}."
    )

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.SOMEONEELSE.value)
        .set_student(False)
        .set_field(field)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}{someoneelse_statement}. {someoneelse_desc} worker, working in {field}."
    )

    prompt = (
        PromptBuilder()
        .set_learning_differences(learning_differences_one_list)
        .set_field(field)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}I have {learning_differences_one_out} and I am working in {field}."
    )


def test_prompt_format():
    base_statement = "Summarise my document.\n"

    prompt = PromptBuilder().set_format(PromptFormat.BULLETS).build()
    assert (
        str(prompt)
        == f"{base_statement}Format the summary in {PromptFormat.BULLETS.value}."
    )

    prompt = PromptBuilder().set_format(PromptFormat.PARAGRAPHS).build()
    assert (
        str(prompt)
        == f"{base_statement}Format the summary in {PromptFormat.PARAGRAPHS.value}."
    )


def test_prompt_focus():
    focus = "Social Economics"
    base_statement = "Summarise my document.\n"

    prompt = PromptBuilder().set_focus_on(focus).build()
    assert str(prompt) == f"{base_statement}Focus on {focus}."

    prompt = (
        PromptBuilder().set_format(PromptFormat.BULLETS).set_focus_on(focus).build()
    )
    assert (
        str(prompt)
        == f"{base_statement}Format the summary in {PromptFormat.BULLETS.value} with a focus on {focus}."
    )


def test_extraction_type():
    base_statement = "Summarise my document.\n"
    someoneelse_statement = "This summary is for someone else.\n"

    prompt = PromptBuilder().set_summary_type(SummaryType.ABSTRACTIVE).build()
    print(str(prompt))
    assert (
        str(prompt)
        == f"{base_statement}I want an {SummaryType.ABSTRACTIVE.value} summary."
    )

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.SOMEONEELSE.value)
        .set_summary_type(SummaryType.EXECUTIVE)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}{someoneelse_statement}They want an {SummaryType.EXECUTIVE.value} summary."
    )


def test_extraction_tone():
    base_statement = "Summarise my document.\n"
    someoneelse_statement = "This summary is for someone else.\n"
    tone = "Formal"

    prompt = PromptBuilder().set_summary_tone(tone).build()
    assert str(prompt) == f"{base_statement}I want this in a {tone} tone."

    prompt = (
        PromptBuilder()
        .set_summary_for(PromptFor.SOMEONEELSE.value)
        .set_summary_tone(tone)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}{someoneelse_statement}They want this in a {tone} tone."
    )

    prompt = (
        PromptBuilder()
        .set_summary_tone(tone)
        .set_summary_type(SummaryType.ABSTRACTIVE)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}I want an {SummaryType.ABSTRACTIVE.value} summary in a {tone} tone."
    )


def test_word_limit():
    base_statement = "Summarise my document.\n"
    someoneelse_statement = "This summary is for someone else.\n"
    tone = "Formal"

    prompt = PromptBuilder().set_word_limit(500).build()
    assert str(prompt) == f"{base_statement}I want this in 500 words."

    # Ensure an error is raised if the limit is too low
    with pytest.raises(Exception):
        prompt = PromptBuilder().set_word_limit(0).build()

    prompt = (
        PromptBuilder()
        .set_summary_type(SummaryType.EXECUTIVE)
        .set_word_limit(500)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}I want an {SummaryType.EXECUTIVE.value} summary and in 500 words."
    )

    prompt = PromptBuilder().set_summary_tone(tone).set_word_limit(500).build()
    assert (
        str(prompt) == f"{base_statement}I want this in a {tone} tone and in 500 words."
    )

    prompt = (
        PromptBuilder()
        .set_summary_type(SummaryType.DESCRIPTIVE)
        .set_summary_tone(tone)
        .set_word_limit(500)
        .build()
    )
    assert (
        str(prompt)
        == f"{base_statement}I want an {SummaryType.DESCRIPTIVE.value} summary in a {tone} tone and in 500 words."
    )
