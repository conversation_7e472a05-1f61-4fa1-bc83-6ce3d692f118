# Answer inspired from https://stackoverflow.com/a/73769968
# Start from AWS Python 3.9 base image
FROM public.ecr.aws/h5o0w5r0/python-3.9-tesseract-ocr-x86-64:latest

# Install the dependencies
COPY file_uploaded/requirements.txt .
RUN pip3 install -r requirements.txt --target "${LAMBDA_TASK_ROOT}"

# Copy our function code
COPY textract/analysis.py ${LAMBDA_TASK_ROOT}
COPY data "${LAMBDA_TASK_ROOT}/data"
COPY database "${LAMBDA_TASK_ROOT}/database"
COPY extractor "${LAMBDA_TASK_ROOT}/extractor"
COPY summariser "${LAMBDA_TASK_ROOT}/summariser"
COPY helpers "${LAMBDA_TASK_ROOT}/helpers"
COPY websocket_utils "${LAMBDA_TASK_ROOT}/websocket_utils"
COPY document_search "${LAMBDA_TASK_ROOT}/document_search"

# Set the handler function
CMD [ "analysis.handler" ]