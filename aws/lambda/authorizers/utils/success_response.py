import json
from authorizer_data.AllowedMethod import AllowedMethod
from authorizer_data.AuthPolicy import AuthPolicy


def success_response(payload, allowed_methods: list[AllowedMethod], event):
    user_id = payload["userId"]
    permissions = payload["permissions"]
    features = payload["features"]
    tracking_id = payload["trackingId"]

    principalId = f"user|{user_id}"

    tmp = event["methodArn"].split(":")
    apiGatewayArnTmp = tmp[5].split("/")
    awsAccountId = tmp[4]

    policy = AuthPolicy(principalId, awsAccountId)
    policy.restApiId = apiGatewayArnTmp[0]
    policy.region = tmp[3]
    policy.stage = apiGatewayArnTmp[1]

    for method in allowed_methods:
        policy.allowMethod(verb=method.verb, resource=method.resource),

    # Finally, build the policy
    authResponse = policy.build()

    # new! -- add additional key-value pairs associated with the authenticated principal
    # these are made available by APIGW like so: $context.authorizer.<key>
    # additional context is cached
    context = {
        "user_uuid": user_id,  # $context.authorizer.key -> value
        "tracking_id": tracking_id,
        "permissions": json.dumps(permissions),
        "features": json.dumps(features),
    }
    authResponse["context"] = context

    return authResponse
