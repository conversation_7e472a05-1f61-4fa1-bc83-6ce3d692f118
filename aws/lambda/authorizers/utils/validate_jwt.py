import json
import base64
from requests import request
from utils.failure_exception import AuthFailureException


def validate_jwt(url, jwt: str):
    license_response = request("GET", url, json={"jwt": jwt}).json()

    if "data" not in license_response:
        raise AuthFailureException("Unauthorized")

    valid: bool = license_response["data"]["valid"]

    if not valid:
        raise AuthFailureException("Unauthorized")
    split_jwt = jwt.split(".")
    decoded_header = json.loads(base64.urlsafe_b64decode(split_jwt[0] + "=="))
    decoded_payload = json.loads(base64.urlsafe_b64decode(split_jwt[1] + "=="))

    return decoded_header, decoded_payload
