"""
Based heavily on the example found here: 
https://github.com/awslabs/aws-apigateway-lambda-authorizer-blueprints/blob/master/blueprints/python/api-gateway-authorizer-python.py
"""

from logging import ERROR as LOGGING_ERROR
from logging import INFO as LOG<PERSON>NG_INFO
from logging import getLogger
from os import environ
from authorizer_data import AllowedMethod
from utils.licence import payload_is_authorised
from utils.validate_jwt import validate_jwt
from authorizer_data import HttpVerb
from utils import success_response


is_local = environ.get("IS_LOCAL") == "true"
logger = getLogger()
logger.setLevel(level=LOGGING_INFO if is_local else LOGGING_ERROR)

# Ignore this error if you have typechecking on, need to find better way
# of handling this variable


def handler(event, context):
    """Do not print the auth token unless absolutely necessary"""
    auth_token = str(event["authorizationToken"]).split(" ")[1]

    header, payload = validate_jwt(f"{environ['LICENSE_URL']}/validate", auth_token)

    payload_is_authorised(payload)

    allowed_methods = [
        AllowedMethod(verb=HttpVerb.POST, resource="/documents"),
        AllowedMethod(verb=HttpVerb.GET, resource="/documents/*"),
        AllowedMethod(verb=HttpVerb.GET, resource="/documents"),
        AllowedMethod(verb=HttpVerb.PATCH, resource="/documents/*"),
        AllowedMethod(verb=HttpVerb.DELETE, resource="/documents/*"),
        AllowedMethod(verb=HttpVerb.GET, resource="/validation/*"),
        AllowedMethod(verb=HttpVerb.GET, resource="/dictionary"),
        AllowedMethod(verb=HttpVerb.GET, resource="/quotas"),
        AllowedMethod(verb=HttpVerb.POST, resource="/documents/*/tts"),
        AllowedMethod(verb=HttpVerb.GET, resource="/documents/search"),
        AllowedMethod(verb=HttpVerb.POST, resource="/documents/*/explain"),
        AllowedMethod(verb=HttpVerb.POST, resource="/documents/*/summary/*"),
        AllowedMethod(verb=HttpVerb.GET, resource="/documents/*/summary"),

        # Version 2 endpoints.
        AllowedMethod(verb=HttpVerb.GET, resource="/v2/documents/*"),
        AllowedMethod(verb=HttpVerb.GET, resource="/v2/documents/*/state-upload-url"),
        AllowedMethod(verb=HttpVerb.POST, resource="/v2/documents/*/media"),
        AllowedMethod(verb=HttpVerb.POST, resource="/v2/documents/*/audio")
    ]

    return success_response(payload, allowed_methods, event)
