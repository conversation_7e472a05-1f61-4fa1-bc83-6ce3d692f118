"""
Based heavily on the example found here: 
https://github.com/awslabs/aws-apigateway-lambda-authorizer-blueprints/blob/master/blueprints/python/api-gateway-authorizer-python.py
"""

import json
import boto3  # type: ignore
from datetime import datetime, timezone
from logging import ERROR as LOG<PERSON>NG_ERROR
from logging import INFO as LOGGING_INFO
from logging import getLogger
from os import environ

is_local = environ.get("IS_LOCAL") == "true"
logger = getLogger()
logger.setLevel(level=LOGGING_INFO if is_local else LOGGING_ERROR)

dynamodb_client = boto3.client("dynamodb")


def format_query_results(results):
    ret_item = []
    for item in results["Items"]:
        row_item = {}
        for key, value in item.items():
            if "S" in value:
                row_item[key] = str(value["S"])
        ret_item.append(row_item)

    return ret_item


def handler(event, context):
    try:
        """Do not print the auth token unless absolutely necessary"""
        if "queryStringParameters" not in event:
            raise Exception("Ticket not in table")

        params = event["queryStringParameters"]

        if "ticket" not in params:
            raise Exception("Ticket not in table")

        ticket_id = params["ticket"]

        ticket_query = dynamodb_client.query(
            ExpressionAttributeValues={":v1": {"S": ticket_id}},
            KeyConditionExpression="ticketId = :v1",
            TableName=environ["TICKETS_TABLE"],
        )

        items = format_query_results(ticket_query)

        if len(items) == 0:
            raise Exception("No tokens found in table")

        item = items[0]

        # TODO: Needs more security before live, add checks for IP and ticket lifetime
        if "consumed" in item and len(item["consumed"]) > 0:
            raise Exception("Token already used")

        user_uuid = item["userId"]

        dynamodb_client.put_item(
            Item={
                "userId": {"S": user_uuid},
                "sessionId": {"S": event["requestContext"]["connectionId"]},
            },
            TableName=environ["SOCKETS_TABLE"],
        )

        update_response = dynamodb_client.update_item(
            ExpressionAttributeNames={
                "#C": "consumed",
            },
            ExpressionAttributeValues={
                ":c": {
                    "S": str(datetime.now(tz=timezone.utc)),
                },
            },
            Key={
                "ticketId": {
                    "S": ticket_id,
                },
                "userId": {"S": user_uuid},
            },
            TableName=environ["TICKETS_TABLE"],
            UpdateExpression="SET #C = :c",
        )

        # Principal ID has to be a string for it to be valid
        principal_id = json.dumps(
            {
                "user": user_uuid,
                "ticket": ticket_id,
            }
        )

        # Finally, build the policy
        authResponse = {}
        condition = {}

        condition["IpAddress"] = {}

        logger.info(json.dumps(authResponse, indent=4))

        # TODO: Alot more work is needed to add in a generate disallow and
        # extra security
        return generate_allow(principal_id, event["methodArn"])
    except Exception as e:
        logger.exception(f"Error in guest_auth: {str(e)}")
        # The reason we have to raise an exception in here
        # is because AWS Authorizers will only know it's
        # unauthorised through exceptions
        raise Exception("Unauthorized")


def generate_policy(principal_id: str, effect: str, resource: str):
    auth_response = {}
    auth_response["principalId"] = principal_id

    if effect and resource:
        policy_document = {}
        policy_document["Version"] = "2012-10-17"
        policy_document["Statement"] = []

        statement_one = {}
        statement_one["Action"] = "execute-api:Invoke"
        statement_one["Effect"] = effect
        statement_one["Resource"] = resource
        policy_document["Statement"].append(statement_one)
        auth_response["policyDocument"] = policy_document

    return auth_response


def generate_allow(principal_id: str, resource: str):
    return generate_policy(principal_id, "Allow", resource)
