"""
Based heavily on the example found here: 
https://github.com/awslabs/aws-apigateway-lambda-authorizer-blueprints/blob/master/blueprints/python/api-gateway-authorizer-python.py
"""

from authorizer_data.AllowedMethod import AllowedMethod
from logging import ERROR as LOGGING_ERROR
from logging import INFO as LOG<PERSON>NG_INFO
from logging import getLogger
from os import environ
from utils.validate_jwt import validate_jwt
from utils.licence import payload_is_authorised
from authorizer_data.HttpVerbs import HttpVerb
from utils.success_response import success_response


is_local = environ.get("IS_LOCAL") == "true"
logger = getLogger()
logger.setLevel(level=LOGGING_INFO if is_local else LOGGING_ERROR)

# Ignore this error if you have typechecking on, need to find better way
# of handling this variable


def handler(event, context):
    """Do not print the auth token unless absolutely necessary"""
    auth_token = str(event["authorizationToken"]).split(" ")[1]

    header, payload = validate_jwt(f"{environ['LICENSE_URL']}/validate", auth_token)

    # Check this is a valid license
    payload_is_authorised(payload)

    allowed_methods = [
        AllowedMethod(verb=HttpVerb.POST, resource="/summarisations"),
        AllowedMethod(verb=HttpVerb.POST, resource="/summarisations/custom"),
        AllowedMethod(verb=HttpVerb.GET, resource="/summarisations/*"),
        AllowedMethod(verb=HttpVerb.GET, resource="/documents/*/summarisations"),
        AllowedMethod(verb=HttpVerb.POST, resource="/summarisations/view-prompt"),
    ]

    return success_response(payload, allowed_methods, event)
