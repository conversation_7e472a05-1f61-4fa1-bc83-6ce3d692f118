from os import environ
from database.instance import DB
from database import User

from helpers.functions import tailo_http_response
from helpers.wrap import (
    Tai<PERSON><PERSON>rror,
    get_authorizer_context,
    logger,
)
from data.features import TTS_STANDARD, DOCUMENT_UPLOAD_STANDARD
from helpers.sentry import sentry_wrapper

bucket_name = environ["BUCKET_NAME"]


@sentry_wrapper("read_tts_quotas_handler")
def handler(event, context):
    try:
        auth_context = get_authorizer_context(event)

        if auth_context is None:
            return tailo_http_response(
                TailoError.DENIED,
                "Unable to find user auth context",
            )

        tts_limit = auth_context.features.get(TTS_STANDARD, None)
        if tts_limit is None:
            return tailo_http_response(
                TailoError.DENIED,
                "Invalid TTS Limit",
            )

        document_limit = auth_context.features.get(DOCUMENT_UPLOAD_STANDARD, None)
        if document_limit is None:
            return tailo_http_response(
                TailoError.DENIED,
                "Invalid Document Upload Limit",
            )

        with DB() as db_session:
            user = User.get_by_estendio_id(db_session, auth_context.user_id)
            if user is None:
                return tailo_http_response(TailoError.DENIED, "Unable to find user")

            quotas = {
                # * This currently returns seconds to work with current FE implementation.
                # * Need discussion to figure out how the FE should handle this data and what it needs
                "tts": {
                    "used": (
                        user.calculate_legacy_tts_quota(
                            db_session,
                            tts_limit.config.interval,
                            tts_limit.config.unit,
                        ).seconds
                        // 60
                    )
                    % 60,
                    "allowance": tts_limit.config.value,
                    "interval": tts_limit.config.interval.value.capitalize(),
                    "unit": tts_limit.config.unit.value.capitalize(),
                },
                "document_upload": {
                    "used": user.get_total_pages_for_interval(
                        db_session,
                        document_limit.config.interval,
                    ),
                    "allowance": document_limit.config.value,
                    "interval": document_limit.config.interval.value.capitalize(),
                    "unit": document_limit.config.unit.value.capitalize(),
                },
            }

            return tailo_http_response(TailoError.OK, "OK", quotas)
    except Exception as e:
        logger.exception(f"error in read: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
