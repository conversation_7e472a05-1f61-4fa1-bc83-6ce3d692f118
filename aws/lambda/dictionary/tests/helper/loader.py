import glob
from os import path
import os
from pathlib import Path
from typing import List, Union


def get_script_dir() -> str:
    return path.dirname(path.realpath(__file__))


def read_file(path: str) -> str:
    f = open(path)
    content = f.read()
    f.close()
    return content


def get_input_list(source: str, ext: str = "xml") -> List[str]:
    dir = get_script_dir()
    test_dir = path.realpath(f"{dir}/../data/{source}")
    # result = [f.absolute for f in glob(f"{test_dir}/*.{ext}")]
    result = glob.glob(f"{test_dir}/*.{ext}")
    print("Found these", result)
    return result


def get_word_from_input_path(source: str) -> Union[str, None]:
    return Path(source).stem if os.path.isfile(source) else None


def get_expected_from_input_path(source: str) -> Union[str, None]:
    expected = Path(source).with_suffix(".json").absolute().as_posix()

    return expected if os.path.isfile(expected) else None


def get_test_details_from_input_path(source: str):
    word = get_word_from_input_path(source)
    expected = get_expected_from_input_path(source)

    return {"word": word, "expected": expected, "input": source}
