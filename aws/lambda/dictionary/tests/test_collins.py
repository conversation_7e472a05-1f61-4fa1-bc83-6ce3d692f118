from json import dumps
import json
from pathlib import Path
import pytest

from dictionary.tests.helper.loader import read_file
from dictionary.dictionary_collins_handler import handleDictionaryResponse
from dictionary.tests.fixtures.data import collins_test_list


def test_loading(collins_test_list):
    result = handleDictionaryResponse(
        collins_test_list["word"], read_file(collins_test_list["input"])
    )

    if collins_test_list["expected"]:
        file = open(collins_test_list["expected"], "r")
        expected = json.load(file)
        file.close()

        assert result == expected

    else:
        assert result is not None
