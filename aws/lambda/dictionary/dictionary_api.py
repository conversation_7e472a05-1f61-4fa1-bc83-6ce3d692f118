# dictionary_api.py

import requests
from os import environ
from dictionary.dictionary_collins_handler import handleDictionaryResponse

MAX_DEPTH = 1 # Maximum depth for recursion when searching for reference targets

def build_url(word, source):
    dictionary_api = {
        "lingua_robot": {
            "url": "https://lingua-robot.p.rapidapi.com/language/v1/entries/en/",
        },
        "collins": {
            "url": "https://api.collinsdictionary.com/api/v1/dictionaries/english/search/first/?q=",
        },
    }
    return f"{dictionary_api[source]['url']}{word}&format=xml"


def get_definition(word: str, source: str, depth: int = 0) -> str:
    """
    Function to get a dictionary definition of the given word.
    If a reference target is found, it performs a second search on that word.
    The search will be limited to one level of depth to avoid further recursion.
    """
    headers = {
        "accessKey": environ.get("COLLINS_API_KEY"),
        "Content-Type": "application/json",
    }

    try:
        # Build the dictionary API URL for the given word
        url = build_url(word, source)

        response = requests.get(url, headers=headers)

        # Check if the response is valid
        if response.status_code != 200:
            raise Exception(f"API request failed with status code {response.status_code}")

        # The API might return JSON or XML
        content_type = response.headers.get("Content-Type", "")
        if "application/json" in content_type:
            json_response = response.json()
            xml_to_convert = json_response.get("entryContent", None)
            if not xml_to_convert:
                raise Exception(f"Invalid JSON response structure: {json_response}")
        else:
            xml_to_convert = response.text  # Assuming XML is directly returned

        # Pass both the word and the xml_to_convert to handleDictionaryResponse
        if (depth == MAX_DEPTH):
            # If this is the second search, return the result directly
            return handleDictionaryResponse(word, xml_to_convert, depth)
          
        result = handleDictionaryResponse(word, xml_to_convert)

        if isinstance(result, str):
            # Increment depth for the second search
            second_result = get_definition(result, source, depth + 1)
            # Return the result of the second search, which will be the final output
            return second_result

        # Return the result of the initial search
        return result

    except Exception as e:
        print(f"An error occurred: {e}")
        return None  # Return None or an appropriate message on error
      