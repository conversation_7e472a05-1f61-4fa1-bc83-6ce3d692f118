from json import dumps
import re
import traceback
import xmltodict
from typing import Union, List
from collections import OrderedDict
from helpers.logging import get_tailo_logger
from dictionary.dictionary_structure import (
    WordDefinitionDefinitions,
    WordDefinitionMeanings,
    WordDefinitionPhonetics,
    WordDefinitionResponse,
)

logger = get_tailo_logger()


def handleDictionaryResponse(word: str, xml_to_convert, depth=0) -> Union[str, WordDefinitionResponse]:
        
    dict_data = xmltodict.parse(xml_to_convert, dict_constructor=OrderedDict)
    

    data = dict_data["entry"]

    # Word not found
    if len(data) == 0:
        return None

    phonetics_list: List[WordDefinitionPhonetics] = []
    word_definition_response: WordDefinitionResponse = []
    word_definition_list: List[WordDefinitionMeanings] = []
    
    # Assuming the parsing logic is correct and it extracts a reference word like 'matrix'. But only if we haven't already searched for a reference word
    if (depth == 0):
        reference_target = extractReferenceTarget(data)

        if reference_target:
            return reference_target  # Return only the reference target as a string
    
    # Continue processing the current word if no singular reference is found
    for item in data:
        if item == "form":
            phons_list = extractCollinsForm(data[item])
            if phons_list:
                phonetics_list.extend(phons_list)

        elif item == "hom":
            word_def_list, phonetics = extractCollinsMeanings(data[item])
            if word_def_list:
                word_definition_list.extend(word_def_list)

            if len(phonetics) > 0:
                phonetics_list.extend(phonetics)

        else:
            logger.info("Unhandled key")
            logger.info(dumps(item))

    word_definition_response.append(
        WordDefinitionResponse(
            word=word,
            phonetics=phonetics_list,
            meanings=word_definition_list,
        )
    )

    return list(map(lambda x: x.to_dict(), word_definition_response))
        
def extractCollinsForm(
    form_data: Union[List, OrderedDict]
) -> List[WordDefinitionPhonetics]:

    list: List[WordDefinitionPhonetics] = []

    if isinstance(form_data, List):
        for form_item in form_data:
            if "pron" in form_item and isinstance(form_item, OrderedDict):

                phons: [WordDefinitionPhonetics] = extractCollinsForm(form_item)

                if phons:
                    list.extend(phons)

                break
    elif "pron" in form_data and isinstance(form_data["pron"], OrderedDict):
        phon: WordDefinitionPhonetics = extractCollinsPronunciation(form_data)

        if phon:
            list.append(phon)

    return list


def extractCollinsPronunciation(
    data: OrderedDict,
) -> Union[WordDefinitionPhonetics, None]:
    result = None

    try:

        if "pron" in data and isinstance(data, OrderedDict) and "#text" in data["pron"]:
            # It's the pronunciation and audio
            phon: WordDefinitionPhonetics = {}

            phon["text"] = data["pron"]["#text"]  # item["pron"]["audio"]["@title"]

            phon["audio"] = ""

            if "audio" in data["pron"]:
                phon["audio"] = data["pron"]["audio"]["source"]["@src"]

            result = phon
    except Exception as e:
        logger.error("Pronunciation Extraction Failed")
        logger.error(traceback.format_exc())
        logger.error("Record")
        logger.error(dumps(data))

    return result


def extractCollinsMeanings(
    data: Union[OrderedDict, List]
) -> tuple[List[WordDefinitionMeanings], List[WordDefinitionPhonetics]]:
    list: List[WordDefinitionMeanings] = []
    phonetics: List[WordDefinitionPhonetics] = []
    partList: dict = {}

    if isinstance(data, List):
        # Loop the collections
        for data_part in data:
            def_part, def_phons = extractCollinsMeanings(data_part)

            if def_part:
                list.extend(def_part)

            if def_phons:
                phonetics.extend(def_phons)

    else:

        rootGrammarGroup: str = extractCollinsGrammarGroup(data["gramGrp"])

        if "sense" in data:

            if "def" in data["sense"]:
                # singular definition
                entry_def, entry_part = extractCollinsDefinition(
                    data["sense"], rootGrammarGroup
                )
                addCollinsDefinition(partList, list, entry_part, entry_def)

            elif isinstance(data["sense"], List):
                for definition in data["sense"]:
                    if isinstance(definition, OrderedDict):
                        # Extract this definition into the entry
                        entry_def, entry_part = extractCollinsDefinition(
                            definition, rootGrammarGroup
                        )

                        addCollinsDefinition(partList, list, entry_part, entry_def)

        if "form" in data:
            # Sometimes phonetics are hidden in here
            phons = extractCollinsForm(data["form"])
            if len(phons) > 0:
                phonetics.extend(phons)

    return list, phonetics


def addCollinsDefinition(
    partList: dict,
    list: List[WordDefinitionMeanings],
    entry_part: str,
    entry_def: Union[WordDefinitionDefinitions, None],
):

    if entry_def:
        entry: WordDefinitionMeanings = None
        entry_index = partList.get(entry_part, None)

        if entry_index is None:
            # This grammar of the item has not occurred yet
            entry = {}
            entry["part_of_speech"] = entry_part
            entry["definitions"] = []
            partList[entry_part] = len(list)
            list.append(entry)
        else:
            # A part is already registered fetch that
            entry = list[entry_index]

        entry["definitions"].append(entry_def)


def extractCollinsGrammarGroup(data: Union[OrderedDict, List]) -> str:
    result = ""

    if isinstance(data, List):
        for group in data:
            result = extractCollinsGrammarGroup(group)
            if result != "":
                break
    elif isinstance(data, OrderedDict):
        if "pos" in data:
            result = data["pos"]

    return result


def extractCollinsDefinition(
    data: OrderedDict, rootGrammarGroup: str
) -> tuple[Union[WordDefinitionDefinitions, None], str]:
    definition: WordDefinitionDefinitions = {}
    entry_part = rootGrammarGroup  # Set as default for now

    if "gramGrp" in data:
        temp_part = extractCollinsGrammarGroup(data["gramGrp"])
        if temp_part != "":
            entry_part = temp_part

    # Seems to get some definition that are very nuanced, skipping those for now
    if "def" in data:
        definition["definition"] = extractCollinsDefinitionField(data["def"])
        definition["example"] = []

        if "cit" in data and "quote" in data["cit"]:
            definition["example"].extend(extractCollinsQuote(data["cit"]["quote"]))

    else:
        definition = None

    return definition, entry_part


def extractCollinsQuote(data: Union[OrderedDict, List]) -> Union[str, None]:
    result: List[str] = []

    if isinstance(data, List):
        for quote in data:
            if "#text" in quote:
                result.append(trimCollinsNewlines(quote["#text"]))

    elif "#text" in data:
        result.append(trimCollinsNewlines(data["#text"]))

    return result


def extractCollinsDefinitionField(data: Union[str, OrderedDict]) -> str:
    result: str = ""

    if isinstance(data, str):
        result = data
    elif "#text" in data and "hi" in data and "#text" in data["hi"]:
        result = data["hi"]["#text"] + ": " + data["#text"]
    elif "#text" in data and "hi" in data and isinstance(data["hi"], List):
        result = extractCollinsDefinitionFieldRender(data)
    else:
        logger.info("+++ Unhandled render challenge")

    # Remove newline and whitespace
    if result != "":
        result = trimCollinsNewlines(result)

    return result


def trimCollinsNewlines(result: str) -> str:
    return re.sub(r"\n\W+", " ", result)


def createRenderReplaceFunction(render_items: List):
    idx = 0

    def regexMatcher(match_obj: re.Match) -> str:
        nonlocal idx

        first = render_items[idx]["#text"]
        idx += 1
        second = render_items[idx]["#text"]
        idx += 1

        replace = f"{first}{match_obj.group(2)}{match_obj.group(3)}{second}{match_obj.group(4)}"

        return replace

    return regexMatcher


def extractCollinsDefinitionFieldRender(data: OrderedDict) -> str:
    result = ""

    if "#text" in data and "hi" in data and isinstance(data["hi"], List):
        result = re.sub(
            r"(\W)(\(see)(\W)(\))",
            createRenderReplaceFunction(data["hi"]),
            data["#text"],
        )

    return result

def extractReferenceTarget(data: Union[OrderedDict, list]) -> Union[str, None]:
    """
    Extracts the referenced word from the <ref> tag, if available.
    Looks for <ref target="..."> and returns the 'target' word.
    """
    # If 'data' is a list, recursively check each item
    if isinstance(data, list):
        for item in data:
            result = extractReferenceTarget(item)  # Recursively check each item in the list
            if result:
                return result
        return None

    # Ensure 'data' is an OrderedDict, otherwise return None
    if not isinstance(data, OrderedDict):
        return None

    # Extract 'hom', which can also be a list
    hom = data.get("hom")
    if isinstance(hom, list):
        for item in hom:
            result = extractReferenceTarget(item)  # Recursively check each item in the list
            if result:
                return result
        return None

    if not hom:
        return None

    # Extract 'sense', which can also be a list
    senses = hom.get("sense")
    if isinstance(senses, list):
        for sense in senses:
            result = extractReferenceTarget(sense)  # Recursively check each sense
            if result:
                return result
        return None

    if not senses:
        return None

    # Ensure 'senses' is an OrderedDict for uniform handling
    if isinstance(senses, OrderedDict):
        senses = [senses]

    for sense in senses:
        xr = sense.get("xr")
        if not xr:
            continue

        ref_data = xr.get("ref")
        if isinstance(ref_data, OrderedDict) and "#text" in ref_data:
            return ref_data["#text"]

    return None

