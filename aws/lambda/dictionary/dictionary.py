from os import environ, path
from typing import List
from collections import OrderedDict
from xmljson import Badger<PERSON>ish  # import the class

from dictionary.dictionary_structure import (
    WordDefinitionDefinitions,
    WordDefinitionMeanings,
    WordDefinitionPhonetics,
)
from dictionary.dictionary_api import get_definition
from helpers.wrap import TailoError
from helpers.functions import get_query_strings, tailo_http_response
from helpers.validators import validate_req_arg
from helpers.sentry import sentry_wrapper

dictionary_api = {
    "lingua_robot": {
        "url": "https://lingua-robot.p.rapidapi.com/language/v1/entries/en/",
    },
    "collins": {
        "url": "https://api.collinsdictionary.com/api/v1/dictionaries/english/search/first/?q=",
    },
}

bf = BadgerFish(dict_type=OrderedDict)


def build_url(word, source):
    return f"{dictionary_api[source]['url']}{word}&format=xml"


def get_phonetics(phonetics):
    phonetics_list: List[WordDefinitionPhonetics] = []

    for item in phonetics:
        phonetics_item = {}
        if "transcriptions" in item and item["transcriptions"][0]:
            phonetics_item["text"] = item["transcriptions"][0]["transcription"]
        if "audio" in item:
            phonetics_item["audio"] = item["audio"]["url"]
        phonetics_list.append(WordDefinitionPhonetics(**phonetics_item))

    return phonetics_list


def get_meanings(meanings):
    word_definition_list: List[WordDefinitionMeanings] = []
    definition_list = []
    meanings_list: List[WordDefinitionMeanings] = []

    for key, value in meanings.items():
        synonyms_list = []
        antonyms_list = []
        examples_list = []

        if key == "gramGrp":
            for key, entry in value.items():
                if "pos" in key:
                    part_of_speech = entry
                    meanings_list.append(part_of_speech)

        if key == "sense":
            for entry in value:
                if "def" in entry:
                    definition_list.append(value["def"])

                if "usageExamples" in entry:
                    examples_list.append(value["usageExamples"])

                if "synonyms" in entry:
                    synonyms_list.append(value["synonyms"])

                if "antonyms" in entry:
                    antonyms_list.append(value["antonyms"])

    meanings_list.append(
        WordDefinitionDefinitions(
            definition=definition_list,
            example=examples_list,
            synonyms=synonyms_list,
            antonyms=antonyms_list,
        )
    )

    if part_of_speech != "":
        word_definition_list.append(
            WordDefinitionMeanings(
                part_of_speech=part_of_speech,
                definitions=meanings_list,
            )
        )

    return word_definition_list

# test multiple words being sent
@sentry_wrapper("dictionary_handler")
def handler(event, context):
    query_strings = get_query_strings(event)

    body_args = {"word": ""}

    error, message = validate_req_arg(
        query_strings,
        body_args,
        "word",
        str,
    )
    if error != TailoError.OK:
        return tailo_http_response(error, message)

    try:
        dictionary_result = get_definition(body_args["word"], "collins")

        if dictionary_result is None:
            return tailo_http_response(TailoError.NOT_FOUND, "Word not found")

        return tailo_http_response(TailoError.OK, dictionary_result)

    except Exception as e:
        raise TailoError(f"An error occurred: {e}")


def captureXml(dictionary: str, word: str, content):
    script_dir = path.dirname(path.realpath(__file__))
    f = open(f"{script_dir}/tests/data/{dictionary}/{word}.xml", "w")
    f.write(content)
    f.close()
