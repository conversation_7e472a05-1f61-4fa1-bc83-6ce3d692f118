from dataclasses import asdict, dataclass
from typing import List, Optional


@dataclass
class WordDefinitionPhonetics:
    text: Optional[str] = ""
    audio: Optional[str] = ""


@dataclass
class WordDefinitionDefinitions:
    definition: str
    example: List[str]
    synonyms: List[str]
    antonyms: List[str]


@dataclass
class WordDefinitionMeanings:
    part_of_speech: str
    definitions: List[WordDefinitionDefinitions]


@dataclass
class WordDefinitionResponse:
    to_dict = asdict
    word: Optional[str]
    phonetics: List[WordDefinitionPhonetics]
    meanings: List[WordDefinitionMeanings]
    origin: Optional[str] = ""
