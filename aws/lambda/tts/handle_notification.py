import json
import time
import pusher
from boto3 import client
from urllib.parse import unquote
from os import environ, remove as fsRemove
from database.instance import DB
from database import User, Document, LegacyDocumentAudio, TTSVoice
from helpers.wrap import TailoError, tailo_response, logger, session

bucket_name = environ["BUCKET_NAME"]

s3_client = client("s3", region_name=session.region_name)

pusher_client = pusher.Pusher(
    app_id=environ["PUSHER_APP_ID"],
    key=environ["PUSHER_KEY"],
    secret=environ["PUSHER_SECRET"],
    cluster=environ["PUSHER_CLUSTER"],
    ssl=True,
)


def handler(event, context):
    try:
        event_data = (
            json.loads(event.decode("utf-8")) if isinstance(event, bytes) else event
        )

        event_record = event_data["Records"][0]
        event_s3 = event_record["s3"]

        # AWS inserts '+' for spaces into the key value, these need removing
        # as these are not in the bucket name
        key = unquote(event_s3["object"]["key"].replace("+", " "))

        # Ignore the handling if this is not for an audio file.
        if ".mp3" not in key:
            return

        # Split out the IDs to retrieve document ID, section ID and paragraph ID.
        (
            user_id,
            document_id,
            *middle,
            section_id,
            paragraph_id,
            external_voice_id,
            audio_file_name,
        ) = key.split("/")

        # Update DB record status.
        with DB() as database_session:
            document = Document.by_uuid(session=database_session, uuid=document_id)

            if document is None:
                raise Exception("Unable to find uploaded document database item")

            user = User.get_by_estendio_id(session=database_session, uuid=user_id)

            if user is None:
                raise Exception("Unable to find user by uuid")

            if document.owner_user_id != user.id:
                raise Exception("User is not the owner of the document")

            # Load and map the voice from storage to external identifier.
            tts_voice = TTSVoice.by_external_id(
                session=database_session, external_id=external_voice_id
            )

            if tts_voice is None:
                raise Exception("No voice could be found for that identifier.")

            record = LegacyDocumentAudio.by_paragraph_id(
                session=database_session,
                document_id=document.id,
                section_id=section_id,
                paragraph_id=paragraph_id,
                tts_voice_id=tts_voice.id,
            )
            
            if record is None:
                return

            audio_file_path = s3_client.generate_presigned_url(
                ClientMethod="get_object",
                Params={"Bucket": bucket_name, "Key": key},
                ExpiresIn=900,
            )

            # Define a temporary file path for the audio.
            tmp_local_audio_file = "/tmp/" + str(time.time()) + ".mp3"

            # Download from S3 to the temporary location.
            s3_client.download_file(bucket_name, key, tmp_local_audio_file)

            # Process the audio metadata to retrieve the duration.
            # PLACEHOLDER: As we cannot use Audio-Metadata package which is out of date
            #              we are just using 0 for all values
            file_metadata: dict[str, int] = {
                "filesize": 0,
                "bitrate": 0,
                "channels": 0,
                "duration": 0,
                "sample_rate": 0,
            }

            # Clean up and delete the temporary file.
            fsRemove(tmp_local_audio_file)

            # Update the database record with the metadata.
            metadata = {}
            metadata["filesize"] = file_metadata.filesize
            metadata["bitrate"] = file_metadata.streaminfo.bitrate
            metadata["channels"] = file_metadata.streaminfo.channels
            metadata["duration"] = file_metadata.streaminfo.duration
            metadata["sample_rate"] = file_metadata.streaminfo.sample_rate

            record.file_metadata = metadata
            record.status = "ready"

            meta = {}
            meta["document_id"] = document_id
            meta["section_id"] = section_id
            meta["paragraph_id"] = paragraph_id
            meta["audio_file"] = audio_file_path
            meta["status"] = "ready"

            pusher_client.trigger(user_id, "tts-audio-created", meta)

            database_session.commit()
    except Exception as e:
        logger.exception(f"error in dispatch_job: {e}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
