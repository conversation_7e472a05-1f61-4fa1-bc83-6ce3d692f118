from os import environ
from boto3 import client
from helpers.wrap import (
    TailoError,
    tailo_response,
    session,
    logger,
)
from database.instance import DB
from database import User, Document, LegacyDocumentAudio
from helpers.sentry import sentry_wrapper

bucket_name = environ["BUCKET_NAME"]
s3_client = client("s3", region_name=session.region_name)


@sentry_wrapper("fetch_section_audio_handler")
def handler(event, context):
    try:
        path_params = event["pathParameters"]

        document_id = path_params["document_id"]
        section_id = path_params["section_id"]

        # Establish database connection and retrieve document in question.
        with DB() as database_session:
            document = Document.by_uuid(session=database_session, uuid=document_id)

            if document is None:
                raise Exception("Unable to find uploaded document database item")

            user = User.get_by_id(database_session, document.owner_user_id)

            if user is None:
                raise Exception("Unable to find user by uuid")

            if document.owner_user_id != user.id:
                raise Exception("User is not the owner of the document")

            # Find audio for this query.
            document_audio_records = LegacyDocumentAudio.by_section_id(
                session=database_session, document_id=document.id, section_id=section_id
            )

            if document_audio_records is None:
                return tailo_response(TailoError.NOT_FOUND, "Not found")

            data = {}

            status = "processing"

            if not any(
                audio_records.status == "processing"
                for audio_records in document_audio_records
            ):
                status = "ready"

            # Provide a count of how many records we have for this section.
            data["count"] = len(document_audio_records)

            # Set an overall status for the section audio.
            data["status"] = status

            # Build up an array of audio for the section.
            data["audio"] = []

            # Run through each record and build up an S3 URL.
            for record in document_audio_records:
                audio_file_path = s3_client.generate_presigned_url(
                    ClientMethod="get_object",
                    Params={"Bucket": bucket_name, "Key": record.path + record.name},
                    ExpiresIn=900,
                )

                audio_record = {}
                audio_record["document_id"] = document.uuid
                audio_record["section_id"] = record.section_id
                audio_record["paragraph_id"] = record.paragraph_id
                audio_record["status"] = record.status
                audio_record["audio_file"] = audio_file_path

                data["audio"].append(audio_record)

            database_session.commit()

            return tailo_response(TailoError.OK, "OK", data)
    except Exception as e:
        logger.exception(f"error in dispatch_job: {e}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
