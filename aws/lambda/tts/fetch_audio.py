import json
from os import environ
from boto3 import client
from helpers.wrap import Tai<PERSON><PERSON>rror, tailo_response, session, logger
from database import User, Document, LegacyDocumentAudio
from helpers.sentry import sentry_wrapper

bucket_name = environ["BUCKET_NAME"]
s3_client = client("s3", region_name=session.region_name)


@sentry_wrapper("fetch_audio_handler")
def handler(event, context):
    try:
        global db

        document_id = json.loads(event["pathParameters"]["document_id"])
        section_id = json.loads(event["queryStringParameters"]["section_id"])
        paragraph_id = json.loads(event["queryStringParameters"]["paragraph_id"])

        # Establish database connection and retrieve document in question.
        with db.get_session() as database_session:
            document = Document.by_uuid(session=database_session, uuid=document_id)

            if document is None:
                raise Exception("Unable to find uploaded document database item")

            user = User.get_by_id(database_session, document.owner_user_id)

            if user is None:
                raise Exception("Unable to find user by uuid")

            if document.owner_user_id != user.id:
                raise Exception("User is not the owner of the document")

            # Find audio for this query.
            audio = LegacyDocumentAudio.by_paragraph_id(
                session=database_session,
                document_id=document.id,
                section_id=section_id,
                paragraph_id=paragraph_id,
            )

            if audio is None:
                return tailo_response(TailoError.NOT_FOUND, "Not found")

            audio_file_path = s3_client.generate_presigned_url(
                ClientMethod="get_object",
                Params={"Bucket": bucket_name, "Key": audio.path + "/" + audio.name},
                ExpiresIn=900,
            )

            database_session.commit()

            audio.audio_file = audio_file_path

            return tailo_response(TailoError.OK, "OK", audio)
    except Exception as e:
        logger.exception(f"error in dispatch_job: {e}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
