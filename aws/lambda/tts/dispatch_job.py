import re
import json
from uuid import uuid4
from os import environ
from boto3 import client
from boto3 import Session
from datetime import datetime, timezone
from database.instance import DB
from database import User, Document, LegacyDocumentAudio, TTSVoice
from helpers.functions import tailo_http_response
from data.features import TTS_STANDARD
from helpers.functions import tts_exceeded
from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    logger,
    session,
)
from helpers.sentry import sentry_wrapper

s3_client = client("s3", region_name=session.region_name)

credentials = session.get_credentials()
current_credentials = credentials.get_frozen_credentials()

bucket_name = environ["BUCKET_NAME"]


@sentry_wrapper("tts_dispatch_job_handler")
def handler(event, context):
    try:
        auth_context = get_authorizer_context(event)

        if auth_context is None:
            return tailo_http_response(
                TailoError.DENIED,
                "Unable to find user auth context",
            )

        feature_limit = auth_context.features.get(TTS_STANDARD, None)
        if feature_limit is None:
            return tailo_http_response(
                TailoError.DENIED,
                "Invalid TTS Limit",
            )

        event_data = (
            json.loads(event.decode("utf-8")) if isinstance(event, bytes) else event
        )

        body = json.loads(event["body"])

        # Fetch the request body that was sent.
        section_id = body["sectionId"]
        paragraph_id = body["paragraphId"]

        # Set default settings if none are provided. TODO: Update this to dynamic later.
        settings = {"voice": "Brian", "language": "en"}

        # If we have settings provided, use them.
        if "settings" in body:
            settings["voice"] = body["settings"]["voice"]
            settings["language"] = body["settings"]["language"]

        # Establish database connection and retrieve document in question.
        with DB() as database_session:
            user = User.get_by_estendio_id(
                session=database_session,
                uuid=auth_context.user_id,
            )

            if user is None:
                return tailo_http_response(
                    TailoError.MISSING_USER,
                    "Unable to find user by uuid",
                )

            tts_used = user.calculate_tts_quota(
                database_session,
                feature_limit.config.interval,
                feature_limit.config.unit,
            )

            if tts_exceeded(
                tts_used,
                feature_limit.config.value,
                feature_limit.config.unit,
            ):
                return tailo_http_response(
                    TailoError.DENIED,
                    "Exceeded allowed TTS usage",
                )

            document = Document.by_uuid(
                session=database_session,
                uuid=event_data["pathParameters"]["document_id"],
            )

            if document is None:
                return tailo_http_response(
                    TailoError.INVALID_DOCUMENT,
                    "Unable to find uploaded document database item",
                )

            if document.owner_user_id != user.id:
                return tailo_http_response(
                    TailoError.DENIED,
                    "User is not the owner of the document",
                )

            # Load and map the voice from storage to external identifier.
            tts_voice = TTSVoice.by_name(
                session=database_session, name=settings["voice"]
            )

            if tts_voice is None:
                return tailo_http_response(
                    TailoError.NOT_FOUND,
                    "No voice could be found for that identifier.",
                )

            # Do not send a request to amazon if we already have this synthesized.
            exists = LegacyDocumentAudio.by_paragraph_id(
                session=database_session,
                document_id=document.id,
                section_id=section_id,
                paragraph_id=paragraph_id,
                tts_voice_id=tts_voice.id,
            )

            if exists:
                return tailo_http_response(
                    TailoError.DENIED,
                    "Denied",
                    "The audio already exists for this section",
                )

            database_session.commit()

            # Establish a new polly connection.
            polly = Session().client("polly")

            # Build up the path to store in external storage.
            path = (
                user.estendio_id
                + "/"
                + document.uuid
                + "/audio/"
                + section_id
                + "/"
                + paragraph_id
                + "/"
                + tts_voice.external_id
                + "/"
            )

            text = json.loads(event["body"])["text"]

            if text is None:
                return tailo_http_response(
                    TailoError.INVALID_VALUE,
                    "No text was supplied for the request",
                )

            # Replace semi-colons with line breaks.
            text = text.replace(";", "\n")

            # Strip spaces and unwanted characters out.
            transformed_path = path.replace(" ", "-")
            transformed_path = re.sub("’|'", "", transformed_path)

            # Dispatch a task to synthesize the requested text.
            response = polly.start_speech_synthesis_task(
                VoiceId=tts_voice.external_id,
                OutputS3BucketName=bucket_name,
                OutputS3KeyPrefix=transformed_path,
                OutputFormat="mp3",
                Text=text,
                Engine="standard",
            )

            # Fetch the task ID for later.
            task_id = response["SynthesisTask"]["TaskId"]

            now = datetime.now(tz=timezone.utc)

            file_name = "." + task_id + ".mp3"

            # Store the record in the database for later.
            LegacyDocumentAudio.create(
                session=database_session,
                uuid=uuid4(),
                document_id=document.id,
                section_id=section_id,
                paragraph_id=paragraph_id,
                owner_user_id=user.id,
                external_task_id=task_id,
                tts_voice_id=tts_voice.id,
                type="audio/mpeg",
                name=file_name,
                path=transformed_path,
                status="processing",
                date_created=now,
                date_modified=now,
            )

            return tailo_http_response(TailoError.OK, "OK", task_id)
    except Exception as e:
        logger.exception(f"error in dispatch_job: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
