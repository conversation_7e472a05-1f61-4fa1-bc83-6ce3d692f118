from os import environ  # type: ignore
from requests import request  # type: ignore
from middleware.schemas import RequestContext  # type: ignore
from helpers.functions import (  # type: ignore
    tailo_http_response,
    TailoError,
)
from helpers.logging import get_tailo_logger  # type: ignore

logger = get_tailo_logger()


def call_login(email: str):
    request_body = {"email": email, "productName": environ["PRODUCT_NAME"]}

    license_response = request(
        "POST",
        f"{environ['LICENSE_URL']}/auth/magic-link/login",
        json=request_body,
    )

    if not license_response.ok:
        return tailo_http_response(TailoError.UNKNOWN, message="Internal server error")

    response_json = license_response.json()

    return tailo_http_response(TailoError.OK, "OK", response_json.get("data", {}))


def login(request_context: RequestContext):
    try:
        if not request_context.body:
            return tailo_http_response(TailoError.MISSING_PARAM, "Require 'email'")

        email = request_context.body.get("email", " ")

        if email.strip() == "":
            return tailo_http_response(
                TailoError.INVALID_VALUE,
                "Invalid value for 'email'",
            )

        return call_login(email)

    except Exception as e:
        logger.exception(f"Error in initial_access: {str(e)}")
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
