from middleware.schemas import RequestContext  # type: ignore
from middleware.wrappers import anonymous_user  # type: ignore
from .login import login  # type: ignore
from .register import register  # type: ignore
from .verify_otp import verify_otp  # type: ignore
from .verify_ticket import verify_ticket  # type: ignore

LOGIN_PATH = "/v2/auth/login"


@anonymous_user
def handler(request_context: RequestContext):
    # If post then can either be login or submitting OTP
    if request_context.http_method == "POST":
        if request_context.http_path == LOGIN_PATH:
            # login path then can be either the registration flow or the login flow.
            # assess the registration flag and if not then then call login endpoint
            return (
                register(request_context)
                if request_context.body.get("registration", False)
                else login(request_context)
            )

        return verify_otp(request_context)
    if request_context.http_method == "GET":
        return verify_ticket(request_context)
