from os import environ  # type: ignore
from middleware.schemas import RequestContext  # type: ignore
from requests import request  # type: ignore
from database.instance import DB  # type: ignore
from database.functions.auth import get_or_create_user  # type: ignore
from queues.hubspot import send_magic_link_event  # type: ignore
from helpers.functions import (  # type: ignore
    tailo_http_response,
    TailoError,
    extract_session_details,
    format_session_response,
)
from validator_collection import checkers  # type: ignore
from http import HTTPStatus


def validate_otp(session: str, passcode: str):
    license_response = request(
        "POST",
        f"{environ['LICENSE_URL']}/auth/login/validate-otp",
        json={"passcode": passcode, "session": session},
    )

    response_json = license_response.json()

    print(response_json)

    if not license_response.ok:
        code = response_json.get("code", None)
        message = response_json.get("message", "Invalid session and/or passcode")

        response_code = TailoError.DENIED
        if code is not None and code == "SESSION_TIMED_OUT":
            response_code = TailoError.EXPIRED

        return tailo_http_response(
            response_code, message, status=HTTPStatus(license_response.status_code)
        )

    #  Get the ticket data
    user_details, auth_details = extract_session_details(response_json)

    # TODO: Pydantic could be good here to convert JSON to model. Infact Pydantic could be good in general.

    with DB() as session:
        # Get user's data
        user_result = get_or_create_user(session, user_details)

        if user_result is None:
            return tailo_http_response(
                TailoError.DENIED, "Unable to find or create user and/or roles"
            )

        _, user_roles = user_result
        user_role = user_roles[0]

        # Return user's data
        send_magic_link_event(user_details.email, True)
        return tailo_http_response(
            TailoError.OK,
            "OK",
            format_session_response(user_details, auth_details, user_role),
        )


def verify_otp(request_context: RequestContext):
    if not request_context.body:
        return tailo_http_response(
            TailoError.MISSING_PARAM,
            "Require 'session_id' and 'passcode'",
        )

    session = request_context.body.get("session", "")
    passcode = request_context.body.get("passcode", "")

    invalid_params = []
    if not checkers.is_string(session, minimum_length=1):
        invalid_params.append("session")

    if not checkers.is_string(passcode, minimum_length=6, maximum_length=6):
        invalid_params.append("passcode")

    if len(invalid_params) > 0:
        return tailo_http_response(
            TailoError.INVALID_VALUE,
            "Invalid values for " + ", ".join(f"'{param}'" for param in invalid_params),
        )

    return validate_otp(session, passcode)
