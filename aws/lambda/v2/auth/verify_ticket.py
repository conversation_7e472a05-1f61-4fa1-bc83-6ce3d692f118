from os import environ  # type: ignore
from requests import request  # type: ignore
from database.instance import DB  # type: ignore
from helpers.logging import get_tailo_logger  # type: ignore
from queues.hubspot import send_magic_link_event  # type: ignore
from database.functions.auth import get_or_create_user  # type: ignore
from helpers.functions import (  # type: ignore
    extract_session_details,
    format_session_response,
    TailoError,
    tailo_http_response,
)
from middleware.schemas import RequestContext  # type: ignore

logger = get_tailo_logger()


def validate_ticket(ticket: str):
    license_response = request(
        "POST",
        f"{environ['LICENSE_URL']}/auth/magic-link/validate",
        json={"ticketId": ticket},
    )

    response_json = license_response.json()

    if not license_response.ok:
        message = (
            response_json["message"]
            if "message" in response_json
            else "Unable to validate license"
        )
        return tailo_http_response(TailoError.DENIED, message)

    #  Get the ticket data
    user_details, auth_details = extract_session_details(response_json)

    # TODO: Pydantic could be good here to convert JSON to model. Infact Pydantic could be good in general.

    with DB() as session:
        # Get user's data
        user_result = get_or_create_user(session, user_details)

        if user_result is None:
            return tailo_http_response(
                TailoError.DENIED, "Unable to find or create user and/or roles"
            )

        _, user_roles = user_result
        user_role = user_roles[0]

        # Return user's data
        send_magic_link_event(user_details.email, True)
        return tailo_http_response(
            TailoError.OK,
            "OK",
            format_session_response(user_details, auth_details, user_role),
        )


def verify_ticket(request_context: RequestContext):
    try:
        ticket = request_context.query_strings.get("ticket", "")
        if ticket.strip() == "":
            return tailo_http_response(TailoError.MISSING_PARAM, "Missing 'ticket'")

        return validate_ticket(ticket)
    except Exception as e:
        logger.exception(f"Error in auth: {str(e)}")
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
