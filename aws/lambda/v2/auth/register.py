from database.instance import DB  # type: ignore
from database.functions.auth import get_or_create_user  # type: ignore
from requests import request  # type: ignore
from os import environ  # type: ignore
from typing import Union  # type: ignore
from middleware.schemas import RequestContext  # type: ignore
from helpers.functions import tailo_http_response, TailoError, set_user_licensing_attributes, extract_session_details, format_session_response  # type: ignore
from helpers.validators import validate_args  # type: ignore
from helpers.logging import get_tailo_logger  # type: ignore
from validator_collection import checkers  # type: ignore

logger = get_tailo_logger()


def call_registration(
    email: str,
    registration_ticket: Union[str, None] = None,
    device_id: Union[str, None] = None,
    **attrs,
):
    request_body = {
        "email": email,
        "productName": environ["PRODUCT_NAME"],
        "registration": True,
    }

    request_body["attributes"] = attrs

    if registration_ticket and registration_ticket.strip() != "":
        request_body["registrationTicket"] = registration_ticket

    if device_id and device_id.strip() != "":
        request_body["deviceId"] = device_id

    license_response = request(
        "POST",
        f"{environ['LICENSE_URL']}/auth/magic-link/login",
        json=request_body,
    )

    # TODO: We should assess what failed. If license related or invalid user then return OK otherwise error
    if not license_response.ok:
        return tailo_http_response(TailoError.UNKNOWN, message="Internal server error")

    response_json = license_response.json()

    data = response_json.get("data", {})
    if "session" in data:
        return tailo_http_response(0, "OK", data)

    user_details, auth_details = extract_session_details(response_json)

    with DB() as session:
        user_result = get_or_create_user(session, user_details)

        if user_result is None:
            return tailo_http_response(
                TailoError.DENIED, "Unable to find or create user and/or roles"
            )

        _, user_roles = user_result
        user_role = user_roles[0]

        response = format_session_response(user_details, auth_details, user_role)

    return tailo_http_response(TailoError.OK, "OK", response)


def register(request_context: RequestContext):
    try:
        if not request_context.body:
            return tailo_http_response(
                TailoError.MISSING_PARAM,
                "Require email address",
            )

        # Must be provided
        req_args = {
            "email": "",
            "tos_accepted": False,
            "first_name": "",
            "last_name": "",
            "occupation": "",
            "occupation_title": "",
            "gdpr_acceptance": False,
            "settings": {},
        }

        error, message = validate_args(
            request_context.body,
            req_args,
            True,
            email=str,
            registration=bool,
            settings=dict,
            first_name=str,
            last_name=str,
            occupation=str,
            occupation_title=str,
            tos_accepted=bool,
            gdpr_acceptance=bool,
        )

        if error != TailoError.OK:
            return tailo_http_response(error, message)

        # Specific to registration
        optional_args = {
            "device_id": "",
            "ticket": "",
        }

        error, message = validate_args(
            request_context.body,
            optional_args,
            False,
            ticket=str,
            device_id=str,
        )

        if error != TailoError.OK:
            return tailo_http_response(error, message)

        email = str(req_args["email"])
        device_id = optional_args["device_id"]
        registration_ticket = optional_args["ticket"]

        if not checkers.is_email(email):
            return tailo_http_response(
                TailoError.INVALID_VALUE,
                "Invalid email address",
            )

        attribute_keys = {
            "tos_accepted",
            "first_name",
            "last_name",
            "occupation",
            "occupation_title",
            "gdpr_acceptance",
        }

        attribute_args = {
            key: req_args[key] for key in req_args.keys() & attribute_keys
        }

        user_attributes = set_user_licensing_attributes(
            **attribute_args, settings=req_args["settings"]
        )

        return call_registration(
            email,
            registration_ticket,
            device_id,
            **user_attributes,
        )

    except Exception as e:
        logger.exception(f"Error in initial_access: {str(e)}")
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
