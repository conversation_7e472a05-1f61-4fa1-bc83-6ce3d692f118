import json
from os import environ
from uuid import uuid4
from middleware import wrappers
from database.instance import DB
from boto3 import Session, client
from data.features import TTS_STANDARD
from datetime import datetime, timezone
from helpers.sentry import sentry_wrapper, trace
from database import User, Document, DocumentAudio
from middleware.schemas import AuthoriseDocumentRequestContext
from helpers.functions import tailo_http_response, tts_exceeded

from helpers.wrap import (
    TailoError,
    get_path_parameters,
    get_authorizer_context,
    get_body,
    logger,
)

session = Session()
s3_client = client("s3", region_name=session.region_name)
credentials = session.get_credentials()
current_credentials = credentials.get_frozen_credentials()
bucket_name = environ["BUCKET_NAME"]

@trace
def create_if_empty(text: str, voice: str, path: str):
    polly = Session().client("polly")
    
    # We don't have audio, process it.
    audio_response = polly.synthesize_speech(
        Text=text,
        Engine="standard",
        OutputFormat="mp3",
        VoiceId=voice
    )

    timings_response = polly.synthesize_speech(
        Text=text,
        OutputFormat="json",
        Engine="standard",
        VoiceId=voice,
        TextType="text",
        SpeechMarkTypes=["word", "sentence"]
    )

    s3_client.put_object(
        Bucket=bucket_name,
        Key=path + "/block.mp3",
        Body=audio_response["AudioStream"].read(),
        ContentType="audio/mpeg"
    )

    timing_items = timings_response["AudioStream"].read().decode().strip().split('\n')

    s3_client.put_object(
        Bucket=bucket_name,
        Key=path + "/timings.json",
        Body=json.dumps([json.loads(line) for line in timing_items]),
        ContentType="application/json"
    )

@sentry_wrapper("create_document_audio_handler")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:
        feature_limit = request_context.token_content.features.get(TTS_STANDARD)

        if feature_limit is None:
            return tailo_http_response(
                TailoError.DENIED,
                "Invalid TTS Limit",
            )

        body = get_body(request_context.event)

        if not body:
            return tailo_http_response(
                TailoError.MISSING_PARAM,
                "Require ID, text, and voice",
            )

        if "id" not in body:
            return tailo_http_response(TailoError.MISSING_PARAM, "Require id")

        id = body["id"]

        if "text" not in body:
            return tailo_http_response(TailoError.MISSING_PARAM, "Require text")

        text = body["text"]

        if "voice" not in body:
            return tailo_http_response(TailoError.MISSING_PARAM, "Require voice")

        voice = body["voice"]

        with DB() as db_session:
            user = request_context.user
            document = request_context.document
            
            db_session.add(document)
            db_session.add(user)

            upload_path = f"{user.estendio_id}/{document.uuid}/audio/{id}/{voice}"

            # Check if we have audio (and timings) already for this identifier.
            try:
                s3_client.head_object(
                    Bucket=bucket_name,
                    Key=upload_path + "/block.mp3",
                )

                s3_client.head_object(
                    Bucket=bucket_name,
                    Key=upload_path + "/timings.json",
                )
            except:
                # Check if they have used their quota.
                used = user.calculate_tts_quota(
                    db_session,
                    feature_limit.config.interval,
                    feature_limit.config.unit,
                )

                if request_context.token_content.tts_exceeded(
                    used,
                    TTS_STANDARD
                ):
                    return tailo_http_response(
                        TailoError.FORBIDDEN,
                        "Exceeded allowed TTS usage",
                    )

                create_if_empty(text=text, voice=voice, path=upload_path)

            # Generate a presigned URL for the client to use.
            audio_url = s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": bucket_name, "Key": upload_path + "/block.mp3"},
                ExpiresIn=28800, # 8 hours
            )

            # Fetch the timings JSON data file.
            timings_object = s3_client.get_object(
                Bucket=bucket_name,
                Key=upload_path + "/timings.json",
            )
            
            # Fetch the contents of the timings file.
            timings_content = timings_object["Body"].read().decode("utf-8")
            
            # Check to see if we have a record in the database for this block.
            record = DocumentAudio.by_block_id(
                session=db_session,
                document_id=document.id,
                block_id=id,
                voice_id=voice
            )

            # If we don't, let's create a record for this.
            if record is None:
                now = datetime.now(tz=timezone.utc)
                
                duration = None

                # Check if we have timings.
                try:
                    if timings_content is not None:
                        timings = json.loads(timings_content)

                        # Grab the last timing (to calculate duration).
                        if timings[-1] is not None:
                            duration = timings[-1]["time"]
                except:
                    logger.exception("There was a problem unloading the JSON timings file")

                DocumentAudio.create(
                    session=db_session,
                    uuid=uuid4(),
                    document_id=document.id,
                    block_id=id,
                    voice_id=voice,
                    owner_user_id=user.id,
                    path=upload_path,
                    duration=duration,
                    date_created=now,
                    date_modified=now,
                )

            output = {
                "data": {
                    "id": id,
                    "file": audio_url,
                    "timings": None
                }
            }

            try:
                timings = json.loads(timings_content)
                output["data"]["timings"] = timings
            except:
                logger.exception("There was a problem unloading the JSON timings file")
    
        return tailo_http_response(TailoError.OK, "OK", output)
    except Exception as e:
        logger.exception(f"Exception in create: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
