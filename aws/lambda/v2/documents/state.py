from datetime import datetime, timezone
from database.instance import DB
from middleware import wrappers
from middleware.schemas import AuthoriseDocumentRequestContext
from helpers.wrap import (
    TailoError,
    get_query_strings,
    logger,
)

from helpers.sentry import sentry_wrapper
from helpers.functions import tailo_http_response

@sentry_wrapper("read_document_state_handler")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:        
        query_parameters = get_query_strings(request_context.event)

        with DB() as db_session:
            db_session.add(request_context.user)
            db_session.add(request_context.document)

            request_context.document.last_read_at = datetime.now(timezone.utc)
            db_session.commit()

            return tailo_http_response(TailoError.OK, "OK", {
                "data": request_context.document.to_json_state_response(
                    request_context.user.estendio_id,
                    query_parameters
                )
            })
    except Exception as e:
        logger.exception(f"error in read: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
