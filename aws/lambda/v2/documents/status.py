from database.instance import DB
from database.models import DocumentStatus
from helpers.functions import tailo_http_response
from helpers.logging import get_tailo_logger
from helpers.sentry import sentry_wrapper
from helpers.wrap import TailoError
from middleware import wrappers
from middleware.schemas import AuthoriseUserRequestContext

logger = get_tailo_logger()


@sentry_wrapper("document_status_handler")
@wrappers.authorise_user
def handler(request_context: AuthoriseUserRequestContext):
    try:
        ids = request_context.body.get("ids")

        if ids is None:
            return tailo_http_response(TailoError.INVALID_VALUE, "Missing 'ids'")

        with DB() as db_session:
            queried_documents = request_context.user.get_documents_by_uuids(
                db_session, ids
            )

            db_session.expunge_all()

        values = []
        for document in queried_documents:
            data = {"id": document.uuid, "status": document.status}
            if document.status == DocumentStatus.COMPLETE.value:
                data["name"] = (
                    document.display_name
                    if document.display_name is not None
                    else document.filename
                )
            values.append(data)

        return tailo_http_response(TailoError.OK, "OK", values)
    except Exception as e:
        logger.exception(f"error in read: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
