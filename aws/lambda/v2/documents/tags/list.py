from typing import List
from middleware.wrappers import authorise_document
from middleware.schemas import AuthoriseDocumentRequestContext
from database.models import CommentThread, Tag
from database.instance import DB
from helpers.functions import tailo_http_response, TailoError
from helpers.logging import get_tailo_logger

logger = get_tailo_logger()


@authorise_document
def handler(request: AuthoriseDocumentRequestContext):
    try:
        with DB() as db_session:
            threads = CommentThread.by_threadable_id(db_session, request.document.id)

            tags: List[Tag] = []
            for thread in threads:
                for comment in thread.comments:
                    tags.extend(comment.tags)

            return tailo_http_response(
                TailoError.OK, "OK", [tag.to_response_json() for tag in tags]
            )
    except Exception as e:
        logger.exception(f"error in read: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
