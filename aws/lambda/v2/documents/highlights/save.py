from datetime import datetime, timezone
from database import DocumentHighlight
from database.instance import DB
from middleware import wrappers
from helpers.wrap import (
    TailoError,
    get_body,
    logger,
)

from helpers.sentry import sentry_wrapper
from helpers.functions import tailo_http_response
from middleware.schemas import AuthoriseDocumentRequestContext

# Create or update a document highlight record.
def create_or_update_highlight(session, document_id, user_id, body):
    highlight = DocumentHighlight.by_uuid(session=session, uuid=body["id"])

    if highlight is None:
        DocumentHighlight.create(
            session=session,
            uuid=body["id"],
            document_id=document_id,
            owner_user_id=user_id,
            meta=body["meta"],
            date_created=datetime.now(timezone.utc),
            date_modified=datetime.now(timezone.utc),
        )

    if highlight is not None:
        highlight.uuid = body["id"]
        highlight.document_id = document_id
        highlight.owner_user_id = user_id
        highlight.meta = body["meta"]
        highlight.date_modified = datetime.now(timezone.utc)

    session.commit()

@sentry_wrapper("document_save_highlight")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:                
        body = get_body(request_context.event)

        if "id" not in body:
            return tailo_http_response(TailoError.INVALID_VALUE, "Please provide highlighted ID")

        if "meta" not in body:
            return tailo_http_response(TailoError.INVALID_VALUE, "Please provide metadata")

        with DB() as db_session:
            create_or_update_highlight(
                session=db_session,
                document_id=request_context.document.id,
                user_id=request_context.user.id,
                body=body,
            )

            return tailo_http_response(TailoError.OK, "OK", {
                "data": {}
            })
    except Exception as e:
        logger.info(f"{e}")
        logger.exception(f"error in read: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
