from database import DocumentHighlight
from database.instance import DB
from middleware import wrappers
from helpers.wrap import (
    TailoError,
    get_path_parameters,
    logger,
)

from helpers.sentry import sentry_wrapper
from helpers.functions import tailo_http_response
from middleware.schemas import AuthoriseDocumentRequestContext

# Find and delete highlight by ID.
def delete_highlight(session, id):
    highlight = DocumentHighlight.by_uuid(session=session, uuid=id)
    
    if highlight is None:
        return

    # Mark the highlight as deleted.
    session.delete(highlight)

@sentry_wrapper("document_delete_highlight")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:
        params = get_path_parameters(request_context.event)

        with DB() as db_session:
            delete_highlight(session=db_session, id=params["highlight_id"])

            return tailo_http_response(TailoError.OK, "OK", {
                "data": {}
            })
    except Exception as e:
        logger.exception(f"error in read: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
