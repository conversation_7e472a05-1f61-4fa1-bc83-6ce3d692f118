from os import environ
from boto3 import Session, client
from helpers.functions import tailo_http_response
from helpers.sentry import sentry_wrapper
from database import Document, User
from database.instance import DB
from helpers.wrap import (
    TailoError,
    get_path_parameters,
    get_authorizer_context,
    logger,
)

session = Session()
s3_client = client("s3", region_name=session.region_name)
credentials = session.get_credentials()
current_credentials = credentials.get_frozen_credentials()

bucket_name = environ["BUCKET_NAME"]

# TODO: pass in file size and compare to limit set as per Rules.py
@sentry_wrapper("document_state_upload_url_handler")
def handler(event, context):
    try:
        token_content = get_authorizer_context(event)

        path_parameters = get_path_parameters(event)

        document_uuid = path_parameters["id"]

        user_uuid = token_content.user_id

        with DB() as db_session:
            document = Document.by_uuid(session=db_session, uuid=document_uuid)

            if not document:
                return tailo_http_response(
                    TailoError.MISSING_USER, "Unable to find document"
                )
            
            user = User.get_by_estendio_id(db_session, user_uuid)

            if user is None:
                return tailo_http_response(
                    TailoError.MISSING_USER, "Unable to fetch user details"
                )
            
            if not user.can_read_doc(db_session, document):
                return tailo_http_response(TailoError.DENIED, "Denied")

            upload_path = f"{user_uuid}/{document_uuid}/state.json"

            response = {
                "upload_url": s3_client.generate_presigned_post(
                    bucket_name,
                    upload_path,
                    {
                        "acl": "private",
                        "Content-Type": 'application/json'
                    },
                    Conditions=[
                        {
                            "acl": "private"
                        },
                        {
                            "Content-Type": 'application/json'
                        }
                    ],
                ),
            }

            response["aws_access_key"] = current_credentials.access_key

            return tailo_http_response(TailoError.OK, "OK", response)
    except Exception as e:
        logger.exception(f"Exception in state_upload_url: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
