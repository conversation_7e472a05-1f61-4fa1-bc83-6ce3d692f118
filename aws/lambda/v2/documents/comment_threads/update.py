from database.instance import DB
from datetime import datetime, timezone
from database.models import CommentThread
from .error_handlers import (
    handle_comment_thread_not_found,
    handle_edit_deleted_comment_thread,
    handle_unknown_error
)


def handler(body: dict, path_parameters: dict):
    try:
        with DB() as db_session:
            comment_thread_uuid = path_parameters["thread_id"]
            comment_thread = CommentThread.by_uuid(db_session, comment_thread_uuid)
            
            if comment_thread is None:
                return handle_comment_thread_not_found()
            
            if comment_thread.deleted_at != None:
                return handle_edit_deleted_comment_thread()
            
            comment_thread.meta = body["meta"]
            comment_thread.updated_at = datetime.now(tz=timezone.utc)

            db_session.commit()

            updated_comment_thread_json = comment_thread.to_response_json()
        
        response = {
            "data": updated_comment_thread_json,
        }

        return response
    except Exception as e:
        return handle_unknown_error(e)