from helpers.sentry import sentry_wrapper
from helpers.wrap import (
    TailoError,
    get_body,
)
from helpers.functions import get_path_parameters, tailo_http_response
from middleware import wrappers
from middleware.schemas import AuthoriseDocumentRequestContext
from .list import handler as list_handler
from .create import handler as create_handler
from .delete import handler as delete_handler
from .update import handler as update_handler
from .error_handlers import is_invalid_request


@sentry_wrapper("comment_thread_handler")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    body = get_body(request_context.event)
    path_parameters = get_path_parameters(request_context.event)

    is_invalid = is_invalid_request(request_context.http_method, body, path_parameters)

    if is_invalid:
        return is_invalid

    if request_context.http_method == "GET":
        return list_handler(path_parameters)

    if request_context.http_method == "PUT":
        return create_handler(body, request_context.document.id)

    if request_context.http_method == "PATCH":
        return update_handler(body, path_parameters)

    if request_context.http_method == "DELETE":
        return delete_handler(path_parameters, request_context.user)

    return tailo_http_response(TailoError.NOT_FOUND, "Invalid route")
