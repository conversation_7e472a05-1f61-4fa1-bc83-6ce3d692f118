from database.models import Document
from database.models import CommentThread
from database.instance import DB
from .error_handlers import handle_unknown_error


def handler(path_parameters: dict):
    try:
        with DB() as db_session:
            document_uuid = path_parameters["id"]
            document_id = Document.by_uuid(db_session, document_uuid).id
            comment_threads = CommentThread.by_threadable_id(db_session, document_id)

            if len(comment_threads) == 0:
                return {"data": []}

            threads_response = []

            for thread in comment_threads:
                thread_data = thread.to_response_json()
                thread_data["comments"] = []

                for comment in thread.comments:
                    # TODO: I would just use `to_response_json` for the comment however, we seem to return `id` instead of `comment_id`
                    comment_data = {
                        "id": comment.uuid,
                        "text": comment.text,
                        "created_at": str(comment.created_at),
                        "updated_at": str(comment.updated_at),
                        "tags": [tag.to_response_json() for tag in comment.tags],
                    }
                    thread_data["comments"].append(comment_data)

                threads_response.append(thread_data)

            response = {"data": threads_response}

            return response

    except Exception as e:
        return handle_unknown_error(e)
