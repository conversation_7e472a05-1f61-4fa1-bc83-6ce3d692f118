from datetime import datetime, timezone
from uuid import uuid4
from psycopg2.errors import IntegrityError
from database.models import CommentThread
from helpers.constants import TailoError
from database.instance import DB
from helpers.logging import get_tailo_logger
from helpers.functions import tailo_http_response
from .error_handlers import (
    handle_integrity_error,
    handle_unknown_error
)

logger = get_tailo_logger()

def handler(body: dict, document_id: str):
    try:

        with DB() as db_session:
            comment_thread = create_comment_thread(
                db_session,
                document_id,
                body
            )

            comment_thread_json = comment_thread.to_response_json()

        response = {
            "data": comment_thread_json,
        }

        return tailo_http_response(TailoError.OK, "OK", response)
    except IntegrityError as e:
        return handle_integrity_error(e)
    except Exception as e:
        return handle_unknown_error(e)
    
def create_comment_thread(db_session: DB, document_id: int, body: dict) -> CommentThread:
    now = datetime.now(tz=timezone.utc)
    uuid = str(uuid4()) if "id" not in body["meta"] else body["meta"]["id"]
    return CommentThread.create(
        session=db_session,
        uuid=uuid,
        threadable_id=document_id,
        threadable_type="document",
        meta=body["meta"],
        created_at=now,
        updated_at=now,
        deleted_at=None
    )

