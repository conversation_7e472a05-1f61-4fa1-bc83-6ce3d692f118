from database.instance import DB
from datetime import datetime, timezone
from database.models import CommentThread, User, TaggableType
from .error_handlers import (
    handle_comment_thread_already_deleted,
    handle_unknown_error,
    handle_tagging_exception,
)
from tagging import sync_item_tags
from tagging.exceptions import TaggingException


def handler(path_parameters: dict, user: User):
    try:
        with DB() as db_session:
            comment_thread_uuid = path_parameters["thread_id"]
            comment_thread = CommentThread.by_uuid(db_session, comment_thread_uuid)

            db_session.add(comment_thread)

            if comment_thread.deleted_at is not None:
                return handle_comment_thread_already_deleted()

            now = datetime.now(tz=timezone.utc)
            comment_thread.deleted_at = now

            for comment in comment_thread.comments:
                comment.deleted_at = now
                # TODO: Currently comments are tied to one thread however, in the future they could be matched to several
                # so we need to only mark them as deleted if they are orphaned
                # We also need to handle multiple comments, currently a thread can only have 1 comment in the FE but we don't
                # want to do tag deletion in a for loop and get an N+1 query
                sync_item_tags(db_session, [], user, TaggableType.COMMENT, comment.id)

            db_session.commit()

        response = {
            "data": f"Comment Thread {comment_thread_uuid} has been successfully deleted",
        }

        return response

    except TaggingException as te:
        return handle_tagging_exception(te)
    except Exception as e:
        return handle_unknown_error(e)
