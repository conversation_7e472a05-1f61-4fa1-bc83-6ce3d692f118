from psycopg2.errors import UniqueViolation
from helpers.functions import get_tailo_logger, tailo_http_response
from helpers.constants import TailoError
from tagging.exceptions import TaggingException


logger = get_tailo_logger()


def handle_integrity_error(e):
    if isinstance(e.orig, UniqueViolation):
        error_message = "A Comment Thread with this ID already exists."
        logger.error(error_message)
        return tailo_http_response(TailoError.INVALID_COMMENT, error_message)
    else:
        logger.error(f"Integrity error: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An integrity error occurred.")


def handle_unknown_error(e):
    logger.error(f"Unexpected error: {e}")
    return tailo_http_response(TailoError.UNKNOWN, "An unknown error occurred")


def handle_comment_thread_already_deleted():
    return tailo_http_response(
        TailoError.INVALID_COMMENT_THREAD, "Comment Thread already deleted"
    )


def handle_edit_deleted_comment_thread():
    return tailo_http_response(
        TailoError.INVALID_COMMENT_THREAD, "Cannot edit a deleted comment thread"
    )


def handle_missing_comment_thread_id():
    return tailo_http_response(TailoError.MISSING_PARAM, "Require Comment Thread id")


def handle_comment_thread_not_found():
    return tailo_http_response(TailoError.INVALID_COMMENT, "Comment Thread not found")


def handle_missing_document_id():
    return tailo_http_response(TailoError.MISSING_PARAM, "Require Document id")


def validation_for_addedit_comment_thread(body: dict):
    if not body:
        return tailo_http_response(
            TailoError.MISSING_PARAM,
            "Require body",
        )

    if "meta" not in body:
        return tailo_http_response(TailoError.MISSING_PARAM, "Require meta")

    if "text" not in body["meta"]:
        return tailo_http_response(TailoError.MISSING_PARAM, "Meta Requires text")

    return False


def is_invalid_request(http_method: str, body: dict, path_parameters: dict):
    if "id" not in path_parameters or path_parameters["id"] == "":
        return handle_missing_document_id()

    if http_method == "GET":
        if "id" not in path_parameters:
            return handle_missing_document_id()

    if http_method == "DELETE":
        if "thread_id" not in path_parameters or path_parameters["thread_id"] == "":
            return handle_missing_comment_thread_id()

    if http_method == "PUT":
        is_invalid = validation_for_addedit_comment_thread(body)
        if is_invalid:
            return is_invalid

    if http_method == "PATCH":
        if "thread_id" not in path_parameters or path_parameters["thread_id"] == "":
            return handle_missing_comment_thread_id()

        is_invalid = validation_for_addedit_comment_thread(body)
        if is_invalid:
            return is_invalid

    return False


def handle_tagging_exception(e: TaggingException):
    return tailo_http_response(e.error_code, str(e), e.data)
