from typing import Dict, Any
from datetime import datetime, timezone
from database.instance import DB
from middleware import wrappers
from middleware.schemas import AuthoriseDocumentRequestContext
from helpers.logging import getLogger
from helpers.wrap import (
    TailoError,
)
from database.models import Document, User

from helpers.sentry import sentry_wrapper
from helpers.functions import tailo_http_response

logger = getLogger()


def get_reset_name(document: Document) -> str:
    """
    Grabs the most appropriate reset title based on whether the
    document title has been successfully extracted
    """
    return (
        document.extracted_title
        if document.extracted_title is not None
        else document.filename
    )


def valid_request_body(request_context: AuthoriseDocumentRequestContext) -> bool:
    # Make sure body isn't empty
    if len(request_context.body.keys()) == 0:
        return False

    # Make sure at least one of the required values is set
    if "new_name" not in request_context.body and "reset" not in request_context.body:
        return False

    return True


def update_document_name(
    new_name: str, document: Document, user: User
) -> Dict[str, Any]:
    # Create the database items to link user to document
    with DB() as db_session:
        db_session.add(document)
        logger.info(document)

        # Ensure user is the owner of the document
        if document.owner_user_id != user.id:
            return tailo_http_response(
                TailoError.DENIED, "User does not have permissions to edit document"
            )

        document.display_name = new_name
        document.date_modified = datetime.now(tz=timezone.utc)
        db_session.commit()

        new_details = document.to_response_json(user.id)

    return new_details


@sentry_wrapper("update_document_handler")
@wrappers.authorise_document
def handler(request_context: AuthoriseDocumentRequestContext):
    try:
        if not valid_request_body(request_context):
            return tailo_http_response(
                TailoError.MISSING_PARAM, "Require 'new_name' or 'reset'"
            )

        # Need to specify boolean so that other truthy values aren't entertained
        resetting = request_context.body.get("reset") is True

        # If extracted_title is empty get the filename as a fallback
        reset_name = get_reset_name(request_context.document)

        # If we're resetting then assign new_doc_name as the reset_name value else assign it the
        # new_name body value passed in
        new_doc_name = (
            reset_name if resetting else request_context.body.get("new_name", "")
        )

        if not resetting and len(new_doc_name) == 0:
            return tailo_http_response(TailoError.INVALID_VALUE, "New name invalid")

        new_details = update_document_name(
            new_doc_name,
            request_context.document,
            request_context.user,
        )

        return tailo_http_response(TailoError.OK, "OK", new_details)

    except Exception as e:
        logger.exception("Exception in create: ", e)
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
