import io
from PIL import Image
from os import environ
from boto3 import Session, client
from helpers.functions import tailo_http_response
from helpers.sentry import sentry_wrapper
from database.instance import DB
from database import Document, User
from helpers.wrap import (
    TailoError,
    get_body,
    get_authorizer_context,
    get_path_parameters,
    logger,
)

session = Session()
s3_client = client("s3", region_name=session.region_name)
credentials = session.get_credentials()
current_credentials = credentials.get_frozen_credentials()

bucket_name = environ["BUCKET_NAME"]

def get_image_dimensions(bucket_name, object_key):
    # Initialize a session using Amazon S3
    s3_client = client('s3')

    # Download the file into a BytesIO
    image_data = io.BytesIO()
    s3_client.download_fileobj(bucket_name, object_key, image_data)

    # Open the image and get dimensions
    image_data.seek(0)
    with Image.open(image_data) as img:
        width, height = img.size

    return width, height

@sentry_wrapper("media_presigned_url")
def handler(event, context):
    try:
        token_content = get_authorizer_context(event)

        path_parameters = get_path_parameters(event)

        document_uuid = path_parameters["id"]

        body = get_body(event)

        user_uuid = token_content.user_id

        if not body:
            return tailo_http_response(TailoError.MISSING_PARAM, "Require new_name")

        with DB() as db_session:
            document = Document.by_uuid(db_session, document_uuid)

            if document is None:
                return tailo_http_response(
                    TailoError.INVALID_DOCUMENT, "Unable to find specified document"
                )
            
            user = User.get_by_estendio_id(db_session, user_uuid)

            if user is None:
                return tailo_http_response(
                    TailoError.MISSING_USER, "Unable to fetch user details"
                )
            
            if not user.can_read_doc(db_session, document):
                return tailo_http_response(TailoError.DENIED, "Denied")

            width, height = get_image_dimensions(bucket_name, body["key"])

            response = {
                "url": s3_client.generate_presigned_url(
                    "get_object",
                    Params={"Bucket": environ["BUCKET_NAME"], "Key": body["key"]},
                    ExpiresIn=86400,
                ),
                "width": width,
                "height": height,
            }

        return tailo_http_response(TailoError.OK, "OK", response)
    except Exception as e:
        logger.exception(f"Exception in media_presigned_url: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
