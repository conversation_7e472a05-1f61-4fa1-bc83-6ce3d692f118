from database.instance import DB
from helpers.functions import tailo_http_response
from helpers.wrap import <PERSON><PERSON><PERSON><PERSON><PERSON>, logger
from data.features import TTS_STANDARD, DOCUMENT_UPLOAD_STANDARD
from helpers.sentry import sentry_wrapper
from middleware import wrappers
from middleware.schemas import AuthoriseUserRequestContext


@sentry_wrapper("read_tts_quotas_handler")
@wrappers.authorise_user
def handler(request_context: AuthoriseUserRequestContext):
    try:

        tts_limit = request_context.token_content.features.get(TTS_STANDARD, None)
        if tts_limit is None:
            return tailo_http_response(
                TailoError.DENIED,
                "Invalid TTS Limit",
            )

        document_limit = request_context.token_content.features.get(
            DOCUMENT_UPLOAD_STANDARD, None
        )
        if document_limit is None:
            return tailo_http_response(
                TailoError.DENIED,
                "Invalid Document Upload Limit",
            )

        with DB() as db_session:
            db_session.add(request_context.user)

            quotas = {
                "tts": {
                    "used": {
                        "value": (
                            request_context.user.calculate_tts_quota(
                                db_session,
                                tts_limit.config.interval,
                                tts_limit.config.unit,
                            ).seconds
                            // 60
                        )
                        % 60,
                        "unit": "Minutes",
                    },
                    "allowance": {
                        "value": tts_limit.config.value,
                        "interval": tts_limit.config.interval.value.capitalize(),
                        "unit": tts_limit.config.unit.value.capitalize(),
                    },
                },
                "document_upload": {
                    "used": {
                        "value": request_context.user.get_total_pages_for_interval(
                            db_session,
                            document_limit.config.interval,
                        ),
                        "unit": "Pages",
                    },
                    "allowance": {
                        "value": document_limit.config.value,
                        "interval": document_limit.config.interval.value.capitalize(),
                        "unit": document_limit.config.unit.value.capitalize(),
                    },
                },
            }

            return tailo_http_response(TailoError.OK, "OK", quotas)
    except Exception as e:
        logger.exception(f"error in read: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")
