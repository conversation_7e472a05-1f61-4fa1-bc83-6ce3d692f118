from middleware.wrappers import authorise_user
from middleware.schemas import AuthoriseUserRequestContext
from helpers.functions import tailo_http_response
from helpers.constants import TailoError
from database.models import Tag
from database.instance import DB


@authorise_user
def handler(request: AuthoriseUserRequestContext):
    with DB() as db_session:
        users_tags = Tag.get_users_linked_tags(db_session, request.user)
        response = [tag.to_response_json() for tag in users_tags]

    return tailo_http_response(TailoError.OK, "OK", response)
