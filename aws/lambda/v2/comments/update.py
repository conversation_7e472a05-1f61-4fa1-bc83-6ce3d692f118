from tagging import sync_item_tags
from tagging.exceptions import TaggingException
from helpers.functions import tailo_http_response
from helpers.constants import TailoError
from database.instance import DB
from database.models import Comment, User, TaggableType
from datetime import datetime, timezone
from .error_handlers import (
    handle_comment_not_found,
    handle_edit_deleted_comment,
    handle_unknown_error,
    handle_tagging_exception,
)


def handler(body: dict, path_parameters: dict, user: User):
    try:

        with DB() as db_session:
            comment_uuid = path_parameters["comment_id"]
            comment = Comment.by_uuid(db_session, comment_uuid)

            if comment is None:
                return handle_comment_not_found()

            if comment.deleted_at is not None:
                return handle_edit_deleted_comment()

            comment.text = body["text"]
            comment.updated_at = datetime.now(tz=timezone.utc)

            tag_data = body.get("tags", [])
            # ? Should this call be moved to the `Comment` model?
            # Answer: It should but not for this iteration
            sync_item_tags(
                db_session,
                tag_data,
                user,
                TaggableType.COMMENT,
                comment.id,
            )

            updated_comment_json = comment.to_response_json()

            db_session.commit()

        response = {
            "data": updated_comment_json,
        }

        return tailo_http_response(TailoError.OK, "OK", response)
    except TaggingException as te:
        return handle_tagging_exception(te)
    except Exception as e:
        return handle_unknown_error(e)
