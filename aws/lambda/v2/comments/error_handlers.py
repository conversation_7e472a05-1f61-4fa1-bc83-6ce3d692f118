from tagging.exceptions import TaggingException
from helpers.constants import TailoError
from helpers.functions import tailo_http_response, get_tailo_logger
from psycopg2.errors import UniqueViolation


logger = get_tailo_logger()


def handle_edit_deleted_comment():
    return tailo_http_response(
        TailoError.INVALID_COMMENT, "Cannot edit a deleted comment"
    )


def handle_comment_not_found():
    return tailo_http_response(TailoError.INVALID_COMMENT, "Comment not found")


def handle_already_deleted_comment():
    return tailo_http_response(TailoError.INVALID_COMMENT, "Comment already deleted")


def handle_comment_thread_not_found():
    return tailo_http_response(TailoError.INVALID_COMMENT, "Comment Thread not found")


def handle_unknown_error(e):
    logger.error(f"Unexpected error: {e}")
    return tailo_http_response(TailoError.UNKNOWN, "An unknown error occurred")


def handle_missing_thread_id():
    return tailo_http_response(TailoError.MISSING_PARAM, "Missing Comment Thread id")


def handle_missing_comment_id():
    return tailo_http_response(TailoError.MISSING_PARAM, "Missing Comment id")


def handle_comment_on_deleted_thread():
    return tailo_http_response(
        TailoError.INVALID_COMMENT, "Cannot comment on a deleted thread"
    )


def handle_tagging_exception(e: TaggingException):
    return tailo_http_response(e.error_code, str(e), e.data)


def handle_integrity_error(e):
    if isinstance(e.orig, UniqueViolation):
        error_message = "A Comment with this ID already exists."
        logger.error(error_message)
        return tailo_http_response(TailoError.INVALID_COMMENT, error_message)
    else:
        logger.error(f"Integrity error: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An integrity error occurred.")


def validation_for_addedit_comment(body: dict):
    if not body:
        return tailo_http_response(
            TailoError.MISSING_PARAM,
            "Require body",
        )

    if "text" not in body:
        return tailo_http_response(TailoError.MISSING_PARAM, "Require text")

    return False


def is_invalid_request(
    http_method: str, body: dict, path_parameters: dict, query_parameters: dict
):
    if http_method == "GET":
        if (
            query_parameters is None
            or "thread_id" not in query_parameters
            or query_parameters["thread_id"] == ""
        ):
            return handle_missing_thread_id()

    if http_method == "DELETE":
        if "comment_id" not in path_parameters or path_parameters["comment_id"] == "":
            return handle_missing_comment_id()

    if http_method == "PUT":
        is_invalid = validation_for_addedit_comment(body)
        if is_invalid:
            return is_invalid
        if "thread_id" not in body:
            return handle_missing_thread_id()

    if http_method == "PATCH":
        if "comment_id" not in path_parameters or path_parameters["comment_id"] == "":
            return handle_missing_comment_id()

    return False
