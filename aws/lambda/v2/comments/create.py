import traceback
from tagging import sync_item_tags
from tagging.exceptions import TaggingException
from typing import List, Dict
from datetime import datetime, timezone
from uuid import UUID, uuid4
from psycopg2 import IntegrityError
from database.models import CommentThread, Tag, Taggable, TaggableType, User
from helpers.functions import tailo_http_response
from helpers.constants import TailoError
from database.instance import DB
from database import Comment
from .error_handlers import (
    handle_integrity_error,
    handle_tagging_exception,
    handle_unknown_error,
    handle_comment_thread_not_found,
    handle_comment_on_deleted_thread,
)


def filter_empty(values: List[str]) -> List[str]:
    return list(filter(lambda value: value.strip() != "", values))


def handler(body: dict, user: User):
    try:
        thread_id = body["thread_id"]
        comment_text = body["text"]
        tags_data = body.get("tags", [])
        # TODO: Improve the checks against thread_id and comment_text

        with DB() as db_session:
            comment_thread = CommentThread.by_uuid(db_session, thread_id)
            if comment_thread is None:
                return handle_comment_thread_not_found()

            if comment_thread.deleted_at is not None:
                return handle_comment_on_deleted_thread()

            comment = create_comment(
                db_session,
                user.id,
                comment_thread,
                thread_id,
                comment_text,
            )

            sync_item_tags(
                db_session,
                tags_data,
                user,
                TaggableType.COMMENT,
                comment.id,
            )

            created_comment_json = comment.to_response_json()

            db_session.commit()

        response = created_comment_json

        return tailo_http_response(TailoError.OK, "OK", response)

    except IntegrityError as e:
        return handle_integrity_error(e)
    except TaggingException as te:
        return handle_tagging_exception(te)
    except Exception as e:
        print(traceback.format_exc())
        return handle_unknown_error(e)


def create_comment(
    db_session: DB,
    user_id: str,
    comment_thread: CommentThread,
    thread_id: int,
    text: str,
) -> Comment:
    now = datetime.now(tz=timezone.utc)

    thread_id = CommentThread.by_uuid(db_session, thread_id).id
    return Comment.create(
        session=db_session,
        uuid=str(uuid4()),
        thread_id=comment_thread.id,
        owner_user_id=user_id,
        text=text,
        created_at=now,
        updated_at=now,
        deleted_at=None,
    )
