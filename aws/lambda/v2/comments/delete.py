from database.models import Comment, User, TaggableType
from database.instance import DB
from datetime import datetime, timezone
from .error_handlers import handle_comment_not_found, handle_already_deleted_comment
from tagging import sync_item_tags
from tagging.exceptions import TaggingException
from .error_handlers import handle_unknown_error, handle_tagging_exception


def handler(path_parameters: dict, user: User):
    try:
        with DB() as db_session:
            comment_uuid = path_parameters["comment_id"]
            comment = Comment.by_uuid(db_session, comment_uuid)

            if comment is None:
                return handle_comment_not_found()

            db_session.add(comment)

            if comment.deleted_at is not None:
                return handle_already_deleted_comment()

            # Unlink all tags associated with the comment
            # ? Is there a scenario where a user can undelete a comment? If so then these may need to stay...somehow?
            sync_item_tags(db_session, [], user, TaggableType.COMMENT, comment.id)
            comment.deleted_at = datetime.now(tz=timezone.utc)

            db_session.commit()

        response = {
            "data": f"Comment {comment_uuid} has been successfully deleted",
        }

        return response

    except TaggingException as te:
        return handle_tagging_exception(te)
    except Exception as e:
        return handle_unknown_error(e)
