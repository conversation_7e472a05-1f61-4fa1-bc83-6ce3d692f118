from database.models import CommentThread, Comment
from database.instance import DB
from .error_handlers import(
    handle_comment_thread_not_found,
    handle_unknown_error
)

def handler(query_parameters: dict):
    try:
        with DB() as db_session:
            thread_uuid = query_parameters["thread_id"]
            thread = CommentThread.by_uuid(db_session, thread_uuid)

            if thread is None:
                return handle_comment_thread_not_found()
            
            comments = Comment.by_thread_id(db_session, thread.id)

            comments_json = [comment.to_response_json() for comment in comments]

        response = {
            "data": comments_json
        }

        return response
    except Exception as e:
        return handle_unknown_error(e)

