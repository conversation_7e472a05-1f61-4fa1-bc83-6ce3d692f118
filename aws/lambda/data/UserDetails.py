from dataclasses import dataclass


@dataclass
class UserDetails:
    id: str
    email: str
    first_name: str
    last_name: str
    occupation: str
    occupation_title: str
    settings: dict
    license: dict
    tracking_id: str

    @classmethod
    def from_json(cls, data: dict):
        return cls(
            id=data.get("id", ""),
            email=data.get("email", ""),
            first_name=data.get("firstName", ""),
            last_name=data.get("lastName", ""),
            occupation=data.get("occupation", ""),
            occupation_title=data.get("occupationTitle", ""),
            settings=data.get("settings", {}),
            license=data.get("license", {}),
            tracking_id=data.get("trackingId", ""),
        )
