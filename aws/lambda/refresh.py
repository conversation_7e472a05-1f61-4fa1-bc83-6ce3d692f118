# import database
from os import environ

from database import User
from database.instance import DB
from helpers.sentry import sentry_wrapper
from helpers.wrap import (
    Tai<PERSON>Error,
    get_body,
    logger,
    extract_session_details,
)
from helpers.functions import tailo_http_response
from requests import request


@sentry_wrapper("refresh_handler")
def handler(event, context):
    body = get_body(event)
    if not body:
        return tailo_http_response(TailoError.MISSING_PARAM, "Require refresh_token")

    if "refresh_token" not in body:
        return tailo_http_response(TailoError.MISSING_PARAM, "Require refresh_token")

    refresh_token = body.get("refresh_token", "")
    if not isinstance(refresh_token, str) or refresh_token.strip() == "":
        return tailo_http_response(TailoError.MISSING_PARAM, "Require refresh_token")

    if "user_id" not in body:
        return tailo_http_response(TailoError.MISSING_PARAM, "Require user_id")

    refresh_token = body["refresh_token"]
    user_uuid = body["user_id"]

    license_response = request(
        "POST",
        f"{environ['LICENSE_URL']}/auth/refresh",
        json={"refreshToken": refresh_token, "userId": user_uuid},
    )

    if not license_response.ok:
        return tailo_http_response(TailoError.EXPIRED, "Session has expired")

    user_details, auth_details = extract_session_details(license_response.json())

    try:
        with DB() as db_session:
            # TODO: Get user settings - should this be returned from the refresh endpoint?
            user = User.get_by_estendio_id(session=db_session, uuid=user_uuid)

            if user is None:
                return tailo_http_response(
                    TailoError.MISSING_USER, "Unable to fetch user details"
                )

            user_roles = user.get_roles(session=db_session)
            if user_roles is None:
                return tailo_http_response(
                    TailoError.MISSING_USER, "Unable to fetch user details"
                )

            # Updating the refresh_token expiry to match the client Next Auth session
            db_session.commit()

            user_permissions = user.get_all_permissions(session=db_session)

        details = {
            "id": user_uuid,
            "permissions": user_permissions,
        }
        user_settings = user_details.settings
        if user_details.first_name:
            details["first_name"] = user_details.first_name
        if user_details.last_name:
            details["last_name"] = user_details.last_name
        if user_details.email:
            details["email"] = user_details.email
        if len(user_settings.keys()) > 0:
            details["settings"] = user_settings
        if user_details.license:
            details["license"] = user_details.license
        if user_details.tracking_id:
            details["trackingId"] = user_details.tracking_id

        auth_data = {
            "auth": {
                "refresh_token": auth_details.refresh_token,
                "access_token": auth_details.access_token,
            },
            "details": {**details},
        }

        return tailo_http_response(TailoError.OK, "OK", auth_data)
    except Exception as e:
        logger.exception(f"Error in refresh: {str(e)}")
        return tailo_http_response(TailoError.UNKNOWN, "An unkown error occured")
