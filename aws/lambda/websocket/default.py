import boto3


def main(event, context):
    request_context = event["requestContext"]
    stage = event["requestContext"]["stage"]
    domain = event["requestContext"]["domainName"]
    connection_id = request_context["connectionId"]

    client = boto3.client(
        "apigatewaymanagementapi", endpoint_url=f"https://{domain}/{stage}"
    )

    body = event.get("body", "")

    if body == "ping":
        response = client.post_to_connection(
            Data="pong".encode("utf-8"), ConnectionId=connection_id
        )
        print(response)

    return {"statusCode": 200, "body": "Message sent"}