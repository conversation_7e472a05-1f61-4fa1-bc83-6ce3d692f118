import json
import boto3
import ast
from os import environ


dynamodb_client = boto3.client("dynamodb")


def format_query_results(results):
    ret_item = []
    for item in results["Items"]:
        row_item = {}
        for key, value in item.items():
            if "S" in value:
                row_item[key] = str(value["S"])
        ret_item.append(row_item)

    return ret_item


def main(event, context):
    request_context = event["requestContext"]
    incoming_request_id = request_context["requestId"]
    principal_id = json.loads(request_context["authorizer"]["principalId"])
    user_uuid = principal_id["user"]
    ticket_id = principal_id["ticket"]

    dynamodb_client.delete_item(
        Key={"userId": {"S": user_uuid}, "sessionId": {"S": incoming_request_id}},
        TableName=environ["SOCKETS_TABLE"],
    )

    dynamodb_client.delete_item(
        Key={"ticketId": {"S": ticket_id}, "userId": {"S": user_uuid}},
        TableName=environ["TICKETS_TABLE"],
    )

    # Delete ticket

    return {"statusCode": 200, "body": "Disconnected"}
