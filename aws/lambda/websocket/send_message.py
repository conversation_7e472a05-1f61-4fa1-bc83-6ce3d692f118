from os import environ
import ast
import json
import boto3

dynamodb_client = boto3.client("dynamodb")


def format_query_results(results):
    ret_item = []
    for item in results["Items"]:
        row_item = {}
        for key, value in item.items():
            if "S" in value:
                row_item[key] = str(value["S"])
        ret_item.append(row_item)

    return ret_item


def main(event, context):
    # For some inconceivable reason, some libraries send json with
    # single quotes ' instead of double ", use ast to eval the string
    # to return a dictionary
    print(json.dumps(event, indent=4))

    request_context = event["requestContext"]
    incoming_request_id = request_context["requestId"]
    principal_id = json.loads(request_context["authorizer"]["principalId"])
    user_id = principal_id["user"]

    result = ast.literal_eval(event["body"])
    message_data = result["data"]

    stage = event["requestContext"]["stage"]
    domain = event["requestContext"]["domainName"]

    response = dynamodb_client.query(
        ExpressionAttributeValues={":v1": {"S": user_id}},
        KeyConditionExpression="userId = :v1",
        TableName=environ["SOCKETS_TABLE"],
    )

    client = boto3.client(
        "apigatewaymanagementapi", endpoint_url=f"https://{domain}/{stage}"
    )

    items = format_query_results(response)

    for item in items:
        try:
            if item["sessionId"] == incoming_request_id:
                continue
            response = client.post_to_connection(
                Data=message_data.encode("utf-8"), ConnectionId=item["sessionId"]
            )
        except client.exceptions.GoneException:
            dynamodb_client.delete_item(
                Key={"userId": {"S": user_id}, "sessionId": {"S": item["sessionId"]}},
                TableName=environ["SOCKETS_TABLE"],
            )
        except Exception as e:
            print(e)

    return {"statusCode": 200, "body": "Message sent"}
