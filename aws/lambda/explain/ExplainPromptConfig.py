from enum import Enum


class LevelOfDetail(Enum):
    BRIEF = "Provide a single, concise sentence that captures the main concept clearly and directly."
    IN_DEPTH = """Where applicable, use the following structure whilst avoiding headings:
                 - Start with a concise paragraph detailing the main concept (20-30 words)
                 - If relevant, use bullet points to break down core points or essential details (up to 100 words)
                 - When helpful, include a brief closing sentence that reinforces or wraps up the explanation (10-20 words).
                """


class LanguageComplexity(Enum):
    SIMPLE = "Write this text to achieve a Flesch-Kincaid Grade Level of 5 or lower. Use only common, simple words and keep any sentences between 8-12 words. Avoid complex phrases or technical terms. Focus on clarity, using short, direct sentences and removing any non-essential details."
    MATCH_SOURCE = ""


class LevelOfKnowledge(Enum):
    BEGINNER = "Assume I have no prior knowledge of this subject. Use simple language and avoid technical jargon. For example, if the input is about “machine learning,” the beginner response might describe it as “a way for computers to learn from examples, like teaching a child with picture flashcards.”"
    INTERMEDIATE = "Assume I have a foundational level of knowledge of this subject but lack the depth of knowledge that an expert might have. For example, for “machine learning,” the intermediate response could cover key ideas like “training data,” “algorithms,” and “predictions” without overloading the response with technical specifics."
    EXPERT = "Assume I have an expert level of knowledge of this subject and understand the topic in great detail already. The response can be thorough, specific, and full of technical language. It should assume the user is comfortable with the details and will appreciate a deep dive into methods, underlying theories, and the “why” behind the “what.” For example, for “machine learning,” you could discuss specific algorithms, like neural networks or decision trees, and mention nuances like “overfitting” or “hyperparameter tuning.”"


class ExplainPromptConfig:
    def __init__(self):
        self.input_text = ""
        self.level_of_detail = LevelOfDetail.BRIEF
        self.language_complexity = LanguageComplexity.MATCH_SOURCE
        self.level_of_knowledge = LevelOfKnowledge.INTERMEDIATE

    def get_level_of_detail(self) -> str:
        return self.level_of_detail.value

    def get_language_complexity(self) -> str:
        return self.language_complexity.value

    def get_level_of_knowledge(self) -> str:
        return self.level_of_knowledge.value

    def __str__(self):
        configs = [
            self.input_text,
            self.get_level_of_detail(),
            self.get_language_complexity(),
            self.get_level_of_knowledge(),
            f"""
            Explain the following: {self.input_text} + {self.get_level_of_detail} + {self.get_language_complexity} + {self.get_level_of_knowledge}
            """,
        ]
        joined_configs = " ".join(config for config in configs if config)
        return f"Explain the following: {joined_configs}"


class ExplainPromptConfigBuilder:
    def __init__(self):
        self.explain_prompt_config = ExplainPromptConfig()

    def set_input_text(self, input_text):
        self.explain_prompt_config.input_text = input_text
        return self

    def set_level_of_detail(self, level_of_detail: str):
        if not level_of_detail:
            return self
        for detail in LevelOfDetail:
            if detail.name == level_of_detail.upper():
                self.explain_prompt_config.level_of_detail = detail
                break
        return self

    def set_language_complexity(self, language_complexity: str):
        if not language_complexity:
            return self
        for complexity in LanguageComplexity:
            if complexity.name == language_complexity.upper():
                self.explain_prompt_config.language_complexity = complexity
                break
        return self

    def set_level_of_knowledge(self, level_of_knowledge: str):
        if not level_of_knowledge:
            return self
        for knowledge in LevelOfKnowledge:
            if knowledge.name == level_of_knowledge.upper():
                self.explain_prompt_config.level_of_knowledge = knowledge
                break
        return self

    def build(self):
        return self.explain_prompt_config
