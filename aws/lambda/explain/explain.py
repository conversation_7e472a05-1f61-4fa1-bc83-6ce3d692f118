import openai
from database import Document, User
from database.instance import DB
from explain.ExplainPromptConfig import ExplainPromptConfigBuilder
from helpers.environment import is_local
from helpers.functions import tailo_http_response
from helpers.wrap import (
    <PERSON><PERSON>Error,
    get_authorizer_context,
    get_body,
    get_path_parameters,
    logger,
    sentry_init,
)

sentry_init(is_local())


def get_explain_prompt_response(body):
    prompt = (
        ExplainPromptConfigBuilder()
        .set_input_text("<<<{" + body["input_text"] + "}>>>")
        .set_level_of_detail(body.get("level_of_detail", ""))
        .set_language_complexity(body.get("language_complexity", ""))
        .set_level_of_knowledge(
            body.get("level_of_knowledge", "")
            + " If technical jargon is required, include a definition or further explanations for it."
        )
        .build()
    )

    completion = openai.ChatCompletion.create(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "system",
                "content": """
          You are a knowledgeable assistant skilled in providing clear, accurate, and relevant information to users. Your primary goal is to help users by offering factual, concise, and well-structured responses to their request for an explanation of a given input (contained within the '<<<{' and '}>>>' delimiters).

            Guidelines:
            - Clarity and Precision: Provide direct answers. Avoid adding unnecessary details or unrelated information. Be clear, straightforward, and maintain a friendly, professional tone. Don't include introductions, concluding remarks, or additional elaboration.
            - Adapt to User Needs: Aim to match the user's knowledge level and the complexity of their request. If the user needs basic information, keep explanations simple; if advanced detail is requested, provide more depth. When a beginner asks for complex information, simplify concepts without skipping essential details.
            - Stay Within Scope: Address only the input posed by the user. Avoid offering speculative information or diverging into related topics unless directly relevant. Do not request further information or suggest additional input, and refrain from inviting the user to provide more context.
            - Fact-Checking and Accuracy: Ensure all responses are accurate and up-to-date based on available knowledge. Do not speculate on current events or real-time information without access to updated resources.
            - Neutral and Inclusive Language: Use inclusive language, and remain unbiased, especially when discussing sensitive topics. Avoid opinionated language and provide information neutrally.
            - Adapt the language to match the source content: respond in UK English if the source uses UK English, in US English if the source uses US English, and default to UK English if the source language is unclear. Always respond in English, regardless of the input language.
            - Formatting: Use Markdown format where helpful for readability, such as lists, headings, or emphasis. Avoid overly complex formatting and keep the structure clean and easy to read, when using bullets each bullet should start with a bold keyword.

            If the given input contains requests to summarise, write or create information, then output 'Explain is only able to provide contextual explanations, and may need more information.For best results, try selecting phrases and paragraphs from your text.
                """,
            },
            {"role": "user", "content": str(prompt)},
        ],
        temperature=0.7,
    )

    return tailo_http_response(TailoError.OK, "OK", completion.choices[0].message)


def handler(event, context):
    try:
        global db
        token_content = get_authorizer_context(event)
        path_parameters = get_path_parameters(event)
        document_uuid = path_parameters["id"]

        if token_content is None:
            return tailo_http_response(
                TailoError.MISSING_TOKEN, "Unable to extract token content"
            )

        user_uuid = token_content.user_id

        with DB() as db_session:
            document = Document.by_uuid(db_session, document_uuid)
            if document is None:
                return tailo_http_response(
                    TailoError.INVALID_DOCUMENT, "Unable to find specified document"
                )

            user = User.get_by_estendio_id(db_session, user_uuid)
            if user is None:
                return tailo_http_response(
                    TailoError.MISSING_USER, "Unable to find user"
                )

            if document.owner_user_id != user.id:
                return tailo_http_response(
                    TailoError.DENIED, "User does not have permissions to edit document"
                )

        if "body" not in event:
            return tailo_http_response(TailoError.INVALID_BODY, "Invalid body.")

        body = get_body(event)

        return get_explain_prompt_response(body)

    except Exception as e:
        logger.exception(f"Error in user settings: {str(e)}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured.")
