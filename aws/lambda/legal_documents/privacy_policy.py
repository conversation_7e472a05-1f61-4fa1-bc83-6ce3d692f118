import json
from os import environ

import boto3
from helpers.wrap import <PERSON><PERSON><PERSON>rror, tailo_response
from helpers.sentry import sentry_wrapper, trace

dynamodb_client = boto3.client("dynamodb")

@trace
def format_query_results(results):
    ret_item = []
    for item in results["Items"]:
        row_item = {}
        for key, value in item.items():
            if "S" in value:
                row_item[key] = str(value["S"])
        ret_item.append(row_item)

    return ret_item


# TODO: Need to add in version return
@sentry_wrapper("privacy_policy_handler")
def handler(event, context):
    response = dynamodb_client.query(
        ExpressionAttributeValues={
            ":v1": {"S": environ["PRIVACY_KEY"]},
            ":v2": {"N": "1"},
        },
        KeyConditionExpression="title = :v1 and version = :v2",
        TableName=environ["LEGALS_TABLE"],
    )

    items = format_query_results(response)

    return tailo_response(TailoError.OK, "OK", json.loads(items[0]["content"]))
