from os import environ
from uuid import uuid4

import boto3  # type: ignore
from database import User
from database.instance import DB
from helpers.environment import is_local

from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    logger,
    sentry_init,
    tailo_response,
)

sentry_init(is_local())

dynamodb_client = boto3.client("dynamodb")


def handler(event, context):
    try:
        global db
        token_content = get_authorizer_context(event)

        if token_content is None:
            return tailo_response(
                TailoError.MISSING_TOKEN, "Unable to extract token content"
            )

        user_uuid = token_content.user_id

        with DB() as db_session:
            user = User.get_by_estendio_id(db_session, user_uuid)

            if user is None:
                return tailo_response(TailoError.MISSING_USER, "Unable to find user")

        # Create ticket
        ticket_id = uuid4().hex
        dynamodb_client.put_item(
            Item={
                "ticketId": {"S": ticket_id},
                "userId": {"S": user_uuid},
            },
            TableName=environ["TICKETS_TABLE"],
        )

        url = f"{environ['WEBSOCKET_URL']}?ticket={ticket_id}"
        return tailo_response(TailoError.OK, "OK", {"url": url})

    except Exception as e:
        logger.exception(f"Error in get_comms {e}")
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
