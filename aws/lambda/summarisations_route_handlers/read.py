from logging import getLogger
from os import environ

from database.instance import DB
from database import Document, Summarisation, SummarisationStatus, User
from helpers.sentry import sentry_wrapper
from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    get_path_parameters,
    logger,
    session,
    tailo_response,
)

logger = getLogger()
bucket_name = environ["BUCKET_NAME"]


@sentry_wrapper("read_summarisation_handler")
def handler(event, context):
    try:
        token_content = get_authorizer_context(event)

        path_parameters = get_path_parameters(event)

        summarisation_uuid = path_parameters["id"]

        user_uuid = token_content.user_id

        with DB() as db_session:
            summarisation = Summarisation.by_uuid(db_session, summarisation_uuid)

            if summarisation is None:
                return tailo_response(
                    TailoError.INVALID_SUMMARISATION,
                    "Unable to find specified summarisation",
                )

            if summarisation.status == SummarisationStatus.PROCESSING.value:
                return tailo_response(
                    TailoError.INVALID_SUMMARISATION,
                    "Summarisation has not yet been processed",
                )

            document = Document.by_id(db_session, summarisation.document_id)
            if document is None:
                return tailo_response(
                    TailoError.INVALID_DOCUMENT, "Unable to find associated document"
                )

            user = User.get_by_estendio_id(db_session, user_uuid)
            if user is None:
                return tailo_response(
                    TailoError.MISSING_USER, "Unable to fetch user details"
                )

            if not user.can_read_doc(db_session, document):
                return tailo_response(TailoError.DENIED, "Denied")

        response = summarisation.to_response_json(session, include_summary=True)

        return tailo_response(TailoError.OK, "OK", response)
    except Exception as e:
        logger.exception("error in read", e)
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
