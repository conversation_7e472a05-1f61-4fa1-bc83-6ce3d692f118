from typing import List

from database.instance import DB
from database import Document, Summarisation, User
from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    get_path_parameters,
    logger,
    tailo_response,
)
from helpers.sentry import sentry_wrapper

@sentry_wrapper("read_all_summarisations_handler")
def handler(event, context):
    logger.info("inside summarisations read all")
    try:

        token_content = get_authorizer_context(event)
        path_parameters = get_path_parameters(event)

        document_uuid = path_parameters["document_id"]

        user_uuid = token_content.user_id

        with DB() as db_session:
            document = Document.by_uuid(db_session, document_uuid)

            if document is None:
                return tailo_response(
                    TailoError.INVALID_DOCUMENT, "Unable to find specified document"
                )

            user = User.get_by_estendio_id(db_session, user_uuid)

            if user is None:
                return tailo_response(
                    TailoError.MISSING_USER, "Unable to fetch user details"
                )

            if not user.can_read_doc(db_session, document):
                return tailo_response(TailoError.DENIED, "Denied")

            summarisations: List[Summarisation] = document.get_summarisations(
                session=db_session
            )

            response = {"count": len(summarisations), "values": []}
            for summarisation in summarisations:
                response["values"].append(summarisation.to_response_json(db_session))

        return tailo_response(TailoError.OK, "OK", response)

    # get all summarisations belonging to a document
    except Exception as e:
        logger.exception("error in read all", e)
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
