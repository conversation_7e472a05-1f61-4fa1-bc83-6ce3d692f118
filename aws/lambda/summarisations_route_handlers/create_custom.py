import json
from datetime import datetime, timezone
from logging import getLogger
from os import environ
from uuid import uuid4

# import fitz
from boto3 import client
from database.instance import DB
from database import (
    Document,
    Summarisation,
    SummarisationStatus,
    SummarisationType,
    User,
)
from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    get_body,
    logger,
    session,
    tailo_response,
)
from helpers.sentry import sentry_wrapper

bucket_name = environ["BUCKET_NAME"]
bus_name = environ["BUS_NAME"]

logger = getLogger()

client = client("events", region_name=session.region_name)

@sentry_wrapper("create_custom_summerisation_handler")
def handler(event, context):
    # This handler is heavily based off of the sumarisations create handler
    # While internal testing is taking place, we should look at all the handlers and see what
    # can be moved to shared functions to avoid duplication. It is highly likely that this particular endpoint
    # will be removed once we reach beta, so was agreed that it's okay to leave this duplication in for now
    try:
        token_content = get_authorizer_context(event)

        body = get_body(event)
        document_uuid = body["document_id"]
        free_text_prompt = body["freeTextPrompt"].strip()

        if free_text_prompt is None or free_text_prompt == "":
            return tailo_response(TailoError.MISSING_PARAM, "Require free_text_prompt")

        user_uuid = token_content.user_id

        summarisation_type = SummarisationType.WHOLE_DOC.value
        summarisation_uuid = uuid4()

        with DB() as db_session:
            document = Document.by_uuid(db_session, document_uuid)
            if document is None:
                return tailo_response(
                    TailoError.INVALID_DOCUMENT, "Unable to find specified document"
                )
            summarisation_name = document.name + " - summary"
            user = User.get_by_estendio_id(db_session, user_uuid)

            if user is None:
                return tailo_response(
                    TailoError.MISSING_USER, "Unable to fetch user details"
                )

            form_values = {"freeTextPrompt": free_text_prompt}

            if not user.can_read_doc(db_session, document):
                return tailo_response(TailoError.DENIED, "Denied")
            now = datetime.now(tz=timezone.utc)
            created_summarisation = Summarisation.create(
                session=db_session,
                uuid=summarisation_uuid,
                name=summarisation_name,
                document_id=document.id,
                type=summarisation_type,
                status=SummarisationStatus.PROCESSING.value,
                date_created=now,
                date_modified=now,
                prompt=free_text_prompt,
                form_fields=json.dumps(form_values),
            )
            event_response = client.put_events(
                Entries=[
                    {
                        "Source": "summarisation.process",
                        "EventBusName": bus_name,
                        "DetailType": "summarisation-info",
                        "Detail": json.dumps(
                            {"id": created_summarisation.id, "prompt": free_text_prompt}
                        ),
                    }
                ]
            )
            logger.info("event response:")
            logger.info(event_response)

        response = created_summarisation.to_response_json(session)

        return tailo_response(TailoError.OK, "OK", response)

    except Exception as e:
        logger.exception("error in summarisation create", e)
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
