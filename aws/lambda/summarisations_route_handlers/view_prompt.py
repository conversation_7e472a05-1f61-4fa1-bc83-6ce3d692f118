import dataclasses
import json

from helpers.wrap import <PERSON><PERSON><PERSON>rror, get_body, logger, tailo_response
from prompt_builder import Pro<PERSON><PERSON><PERSON><PERSON>, PromptFor
from prompt_builder.prompt import PromptFormat, SummaryType
from helpers.sentry import sentry_wrapper

"""
"trying_to_do": "",
"for_me": False,
"who_for": "",
"student": False,
"field": "",
"learning_differences": "",
"bullet_points": False,
"focusing_on": "",
"specific_summary": "",  # Options like enum
"tone": "",
"word_limit": -1,
"""

@sentry_wrapper("view_prompt_handler")
def handler(event, context):
    logger.info("In view_prompt")

    body = get_body(event)
    logger.info(json.dumps(body, indent=4))

    builder = PromptBuilder()

    if body is None:
        prompt = builder.build()
        return tailo_response(TailoError.OK, "OK", prompt.as_dict())

    if "trying_to_do" in body and body["trying_to_do"] != "":
        builder.set_reason(body["trying_to_do"])

    if "for_me" in body and isinstance(body["for_me"], bool):
        builder.set_summary_for(
            PromptFor.ME.value if body["for_me"] else PromptFor.SOMEONEELSE.value
        )

    if "who_for" in body and body["who_for"] != "":
        builder.set_summary_for(body["who_for"])

    if "student" in body and body["student"] != "":
        builder.set_student(body["student"])

    if "field" in body and body["field"] != "":
        builder.set_field(body["field"])

    if "learning_differences" in body and body["learning_differences"] != "":
        builder.set_learning_differences(body["learning_differences"])

    if "bullet_points" in body and isinstance(body["bullet_points"], bool):
        builder.set_format(
            PromptFormat.BULLETS if body["bullet_points"] else PromptFormat.PARAGRAPHS
        )

    if "focusing_on" in body and body["focusing_on"] != "":
        builder.set_focus_on(body["focusing_on"])

    if "specific_summary" in body and body["specific_summary"] != "":
        builder.set_summary_type(SummaryType(body["specific_summary"]))

    if "tone" in body and body["tone"] != "":
        builder.set_summary_tone(body["tone"])

    if "word_limit" in body:
        if isinstance(body["word_limit"], str):
            body["word_limit"] = int(body["word_limit"])

        if isinstance(body["word_limit"], int) and body["word_limit"] > 0:
            builder.set_word_limit(body["word_limit"])

    prompt = builder.build()

    return tailo_response(TailoError.OK, "OK", prompt.as_dict())
