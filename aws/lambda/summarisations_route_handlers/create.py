import json
from datetime import datetime, timezone
from json import dumps
from logging import getLogger
from os import environ
from uuid import uuid4

# import fitz
from boto3 import client
from database.instance import DB
from database import (
    Document,
    Summarisation,
    SummarisationStatus,
    SummarisationType,
    User,
)
from helpers.wrap import (
    TailoError,
    get_authorizer_context,
    get_body,
    logger,
    session,
    tailo_response,
)
from prompt_builder import PromptBuilder, PromptFor
from prompt_builder.prompt import PromptFormat, SummaryType
from helpers.sentry import sentry_wrapper

bucket_name = environ["BUCKET_NAME"]
bus_name = environ["BUS_NAME"]

logger = getLogger()

client = client("events", region_name=session.region_name)

@sentry_wrapper("create_summerisation_handler")
def generate_prompt(body):
    # TODO this would be worth pulling out into a helper function since we do the same thing in view_prompt.py
    builder = PromptBuilder()

    # tyingToDo
    if "trying_to_do" in body and body["trying_to_do"] != "":
        builder.set_reason(body["trying_to_do"])

    if "for_me" in body and isinstance(body["for_me"], bool):
        builder.set_summary_for(
            PromptFor.ME.value if body["for_me"] else PromptFor.SOMEONEELSE.value
        )

    if "who_for" in body and body["who_for"] != "":
        builder.set_summary_for(body["who_for"])

    if "student" in body and body["student"] != "":
        builder.set_student(body["student"])

    if "field" in body and body["field"] != "":
        builder.set_field(body["field"])

    if "learning_differences" in body and body["learning_differences"] != "":
        builder.set_learning_differences(body["learning_differences"])

    if "bullet_points" in body and isinstance(body["bullet_points"], bool):
        builder.set_format(
            PromptFormat.BULLETS if body["bullet_points"] else PromptFormat.PARAGRAPHS
        )

    if "focusing_on" in body and body["focusing_on"] != "":
        builder.set_focus_on(body["focusing_on"])

    if "specific_summary" in body and body["specific_summary"] != "":
        builder.set_summary_type(SummaryType(body["specific_summary"]))

    if "tone" in body and body["tone"] != "":
        builder.set_summary_tone(body["tone"])

    if "word_limit" in body:
        if isinstance(body["word_limit"], str):
            body["word_limit"] = int(body["word_limit"])

        if isinstance(body["word_limit"], int) and body["word_limit"] > 0:
            builder.set_word_limit(body["word_limit"])

    return builder.build()


def handler(event, context):
    try:
        token_content = get_authorizer_context(event)

        body = get_body(event)
        document_uuid = body["document_id"]

        user_uuid = token_content.user_id

        prompt = generate_prompt(body)

        summarisation_type = SummarisationType.WHOLE_DOC.value
        summarisation_uuid = uuid4()

        with DB() as db_session:
            document = Document.by_uuid(db_session, document_uuid)
            if document is None:
                return tailo_response(
                    TailoError.INVALID_DOCUMENT, "Unable to find specified document"
                )
            summarisation_name = document.name + " - summary"
            user = User.get_by_estendio_id(db_session, user_uuid)

            if user is None:
                return tailo_response(
                    TailoError.MISSING_USER, "Unable to fetch user details"
                )

            if not user.can_read_doc(db_session, document):
                return tailo_response(TailoError.DENIED, "Denied")
            now = datetime.now(tz=timezone.utc)

            form_values = body
            del form_values["document_id"]

            created_summarisation = Summarisation.create(
                session=db_session,
                uuid=summarisation_uuid,
                name=summarisation_name,
                document_id=document.id,
                type=summarisation_type,
                status=SummarisationStatus.QUEUED.value,
                date_created=now,
                date_modified=now,
                prompt=prompt,
                form_fields=dumps(form_values),
            )

        event_response = client.put_events(
            Entries=[
                {
                    "Source": "summarisation.process",
                    "EventBusName": bus_name,
                    "DetailType": "summarisation-info",
                    "Detail": json.dumps(
                        {
                            "id": created_summarisation.id,
                        }
                    ),
                }
            ]
        )
        logger.info("event response:")
        logger.info(event_response)

        response = created_summarisation.to_response_json(session)

        return tailo_response(TailoError.OK, "OK", response)

    except Exception as e:
        logger.exception("error in summarisation create", e)
        return tailo_response(TailoError.UNKNOWN, "An unknown error occured")
