from helpers.wrap import (
    <PERSON><PERSON><PERSON>rror,
    tailo_response,
)


def handler(event, context):
    return tailo_response(TailoError.DENIED, "Denied")
    """
    global db
    body = get_body(event)
    private_key = serialization.load_pem_private_key(
        environ["PRIVATE_KEY"].encode("utf-8"),
        password=None,
        backend=default_backend(),
    )

    token_content = get_authorizer_context(event)

    if token_content is None:
        return tailo_response(TailoError.MISSING_TOKEN, "Missing Access Token")

    if not body:
        return tailo_response(Tailo<PERSON>rror.MISSING_PARAM, "Require user_id")

    if "user_id" not in body:
        return tailo_response(TailoError.MISSING_PARAM, "Require user_id")

    user_uuid = body["user_id"]

    if token_content["user_id"] != user_uuid:
        return tailo_response(TailoError.INVALID_VALUE, "user_id is invalid")

    if not user_uuid:
        return tailo_response(TailoError.MISSING_PARAM, "Require user_id")

    try:
        if db is None:
            logger.info("Creating from none")
            db = create_rds_instance()

        with db.get_session() as db_session:
            # Get user db id
            logger.debug("Running session")
            user = User.get_by_estendio_id(session=db_session, uuid=user_uuid)
            logger.debug(user)
            if user is None:
                return tailo_response(False, "User does not exist")

            user_roles = user.get_roles(session=db_session)

            if user_roles is None:
                return tailo_response(False, "user role not found")

            access_only_role: Role = None
            for role in user_roles:
                logger.info(role)
                if role.name == environ["ROLE_ACCESS_ONLY"]:
                    access_only_role = role
                    break

            if access_only_role is None:
                return tailo_response(
                    TailoError.ROLE_MISSING, "User does not have access_only role"
                )

            # extract permissions
            guest_role = Role.by_name(db_session, environ["ROLE_GUEST_USER"])
            if guest_role is None:
                return tailo_response(False, "No guest role found")

            user.switch_role(
                session=db_session, old_role=access_only_role.id, new_role=guest_role.id
            )

            access_token, access_expiry = generate_access_token(
                user_id=user.estendio_id,
                permissions=guest_role.definition["permissions"],
                private_key=private_key,
            )

            user_uuid = user.estendio_id
            user_permissions = user.get_all_permissions(session=db_session)

        auth_data = {
            "auth": {
                "access_token": access_token,
                "access_expiry": access_expiry.timestamp(),
            },
            "details": {
                "id": user_uuid,
                "permissions": user_permissions,
            },
        }

        return tailo_response(TailoError.OK, "OK", auth_data)
    except Exception as e:
        logger.exception(f"Error in guest_login: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
    """
