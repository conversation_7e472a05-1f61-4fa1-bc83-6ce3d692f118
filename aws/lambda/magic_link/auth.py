import boto3
from os import environ
from data import UserDetails
from requests import request
from database.instance import DB
from typing import Tuple, List, Union
from datetime import datetime, timezone
from helpers.validators import validate_req_arg
from queues.hubspot import send_magic_link_event
from helpers.sentry import sentry_wrapper, trace
from database import User, UserRole, Role, Document, DocumentUser
from helpers.functions import extract_session_details, format_session_response

from helpers.wrap import (
    TailoError,
    get_body,
    logger,
    tailo_response,
)

dynamodb_client = boto3.client("dynamodb")
sqs_client = boto3.client("sqs")


@trace
def _clean_document_uuids(uuids: str):
    return [uuid.strip() for uuid in uuids.split(",") if uuid.strip()]


@trace
def _get_roles(session, is_admin: bool) -> Union[Role, None]:
    role = Role.by_name(
        session,
        environ["ROLE_ADMIN_USER"] if is_admin else environ["ROLE_GENERAL_USER"],
    )

    return role


@trace
def _attach_preloaded_documents(session, user: User, is_assessor: bool):
    # Fetch and clean up UUIDs.
    assessor_doc_uuids = _clean_document_uuids(environ["PRELOADED_ASSESSOR_DOCUMENTS"])
    student_doc_uuids = _clean_document_uuids(environ["PRELOADED_STUDENT_DOCUMENTS"])

    # Fetch preloaded document UUIDs.
    uuids = assessor_doc_uuids if is_assessor else student_doc_uuids

    # If there are no UUIDs, ignore.
    if uuids is None:
        return

    # Fetch the documents by UUID.
    documents = Document.by_uuids(session=session, uuids=uuids)

    # Attach the documents to the user.
    for document in documents:
        DocumentUser.create(
            session=session,
            document_id=document.id,
            user_id=user.id,
        )


@trace
def _create_user(session, details: UserDetails) -> Union[Tuple[User, List[Role]], None]:
    now = datetime.now(tz=timezone.utc)

    role = _get_roles(session, details.email.endswith("presentpal.co.uk"))

    # TODO: Ideally we should throw an exception that is tailo specific and caught in the handlers try catch
    if role is None:
        return None

    user = User.create_user(
        session=session,
        estendio_id=details.id,
        date_created=now,
        date_modified=now,
    )

    if user is None:
        return None

    UserRole.create(
        session=session,
        user_id=user.id,
        role_id=role.id,
    )

    is_assessor = details.occupation != "Studying"

    # Attach the preloaded documents to the newly created user.
    _attach_preloaded_documents(session=session, user=user, is_assessor=is_assessor)

    return user, [role]


@trace
def _get_user(
    session, user_details: UserDetails
) -> Union[tuple[User, List[Role]], None]:
    user = User.get_by_estendio_id(session=session, uuid=user_details.id)

    if user is None:
        return _create_user(session, user_details)

    roles = user.get_roles(session=session)

    # TODO: Ideally we should throw an exception that is tailo specific and caught in the handlers try catch
    if roles is None:
        return None

    return user, roles


@trace
def validate_ticket(ticket: str):
    license_response = request(
        "POST",
        f"{environ['LICENSE_URL']}/auth/magic-link/validate",
        json={"ticketId": ticket},
    )

    response_json = license_response.json()

    if not license_response.ok:
        message = (
            response_json["message"]
            if "message" in response_json
            else "Unable to validate license"
        )
        return tailo_response(TailoError.DENIED, message)

    #  Get the ticket data
    user_details, auth_details = extract_session_details(response_json)

    # TODO: Pydantic could be good here to convert JSON to model. Infact Pydantic could be good in general.

    with DB() as session:
        # Get user's data
        user_result = _get_user(session, user_details)

        if user_result is None:
            return tailo_response(
                TailoError.DENIED, "Unable to find or create user and/or roles"
            )

        _, user_roles = user_result
        user_role = user_roles[0]

        # Return user's data
        send_magic_link_event(user_details.email, True)
        return format_session_response(user_details, auth_details, user_role)


@sentry_wrapper("magic_link_auth_handler")
def handler(event, context):
    try:
        body = get_body(event)
        global db

        if not body:
            return tailo_response(TailoError.MISSING_PARAM, "Require ticket")

        body_args = {"ticket": ""}
        error, message = validate_req_arg(body, body_args, "ticket", str)

        if error != TailoError.OK:
            return tailo_response(error, message)

        auth_data = validate_ticket(**body_args)
        return tailo_response(TailoError.OK, "OK", auth_data)

    except Exception as e:
        logger.exception(f"Error in auth: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
