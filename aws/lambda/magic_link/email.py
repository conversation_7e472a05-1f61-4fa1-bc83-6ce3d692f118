from os import environ
from requests import request  # type: ignore
from typing import Union
from database.instance import DB
from database.functions.auth import get_or_create_user

import boto3  # type: ignore
from helpers.functions import extract_session_details, format_session_response
from helpers.wrap import (  # type: ignore
    TailoError,
    get_body,
    logger,
    tailo_response,
    set_user_licensing_attributes,
)
from helpers.validators import validate_args, validate_req_arg  # type: ignore
from validator_collection import checkers  # type: ignore
from helpers.sentry import sentry_wrapper, trace

dynamodb_client = boto3.client("dynamodb")
sqs_client = boto3.client("sqs")

@trace
def handle_login_registration(
    email: str,
    registration: bool,
    registration_ticket: Union[str, None] = None,
    device_id: Union[str, None] = None,
    **attrs,
):
    request_body = {"email": email, "productName": environ["PRODUCT_NAME"]}

    if registration:
        request_body["attributes"] = attrs  # type: ignore

    if registration_ticket and registration_ticket.strip() != "":
        request_body["registrationTicket"] = registration_ticket

    if device_id and device_id.strip() != "":
        request_body["deviceId"] = device_id

    license_response = request(
        "POST",
        f"{environ['LICENSE_URL']}/auth/magic-link/login",
        json=request_body,
    )

    # TODO: We should assess what failed. If license related or invalid user then return OK otherwise error
    if not license_response.ok:
        return tailo_response(TailoError.OK, "OK")

    response_json = license_response.json()

    if "data" not in response_json:
        return tailo_response(0, "OK")

    user_details, auth_details = extract_session_details(response_json)

    with DB() as session:
        user_result = get_or_create_user(session, user_details)

        if user_result is None:
            return tailo_response(
                TailoError.DENIED, "Unable to find or create user and/or roles"
            )

        _, user_roles = user_result
        user_role = user_roles[0]

        response = format_session_response(user_details, auth_details, user_role)

    return tailo_response(TailoError.OK, "OK", response)

@sentry_wrapper("email_handler")
def handler(event, context):
    try:
        body = get_body(event)
        if not body:
            return tailo_response(TailoError.MISSING_PARAM, "Require email address")

        # Must be provided
        req_args = {"email": ""}

        # Specific to registration
        registration_args = {
            "registration": False,
            "settings": {},
            "ticket": "",
            "device_id": "",
        }

        # Attributes to send off to licensing, only done as part of registration
        attribute_args = {
            "tos_accepted": False,
            "first_name": "",
            "last_name": "",
            "occupation": "",
            "occupation_title": "",
            "gdpr_acceptance": False,
        }

        error, message = validate_req_arg(
            body,
            req_args,
            "email",
            str,
            lambda value: value.strip() != "" and checkers.is_email(value),
        )

        if error != TailoError.OK:
            return tailo_response(error, message)

        error, message = validate_args(
            body, registration_args, False, registration=bool, settings=dict, ticket=str, device_id=str
        )

        email = req_args["email"]
        registration = registration_args["registration"]
        settings = registration_args["settings"]
        registration_ticket = registration_args["ticket"]
        device_id = registration_args["device_id"]

        if error != TailoError.OK:
            return tailo_response(error, message)

        error, message = validate_args(
            body=body,
            res=attribute_args,
            required=registration,
            first_name=str,
            last_name=str,
            occupation=str,
            occupation_title=str,
            tos_accepted=bool,
            gdpr_acceptance=bool,
        )
        if error != TailoError.OK:
            return tailo_response(error, message)

        user_attributes = {}
        if registration:
            user_attributes = set_user_licensing_attributes(
                **attribute_args, settings=settings
            )

        return handle_login_registration(
            email,
            registration,
            registration_ticket,
            device_id,
            **user_attributes,
        )

    except Exception as e:
        logger.exception(f"Error in initial_access: {str(e)}")
        return tailo_response(TailoError.UNKNOWN, "An unkown error occured")
