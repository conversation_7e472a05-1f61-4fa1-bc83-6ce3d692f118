import openai
from helpers.environment import is_local
from helpers.functions import tailo_http_response
from helpers.wrap import (
    TailoError,
    sentry_init,
)

sentry_init(is_local())

break_short = 300
break_medium = 600

# Calculate the word count of the input prompt
def get_length_prompt(input_text):
    word_count = len(input_text.split())
    if word_count < break_short:
        return "Provide 1-3 bullets in your summary"
    elif word_count < break_medium:
        return "Provide 2-6 bullets in your summary"
    else:
        return "Provide 4-8 bullets in your summary"


def get_summary_prompt_response(prompt: str):

    length_prompt = get_length_prompt(prompt)

    user_prompt = f"{length_prompt}\nTL;DR;\n{prompt}"

    completion = openai.ChatCompletion.create(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "system",
                "content": """
          You are a professional summarizer who creates a concise and comprehensive summary of the provided text, be it an article, post, conversation, or passage, while adhering to these guidelines:
            - Use simple language.
            - Incorporate main ideas and essential information, eliminating extraneous language and focusing on critical aspects.
            - Rely strictly on the provided text, without including external information.
            - Use Markdown styling, only use bullets with a bold keyword or phrase at the start with a single short sentence under 25 words
            - Each sentence should also be complete and meaningful on its own, even without the bold header.
            - Match the given text English, use UK English for anything else
            - Do not include introductions or conclusions or any added context beyond the source text
            - Do not request further input or suggest additional actions.
            - Avoid inviting user feedback or prompting follow-up.
            - Ensure that all numerical values, such as time, dates, monetary amounts, and years, are preserved exactly as they appear in the original document, including formatting details like the placement of commas, decimals, and symbols.
                """,
            },
            {"role": "user", "content": str(user_prompt)},
        ],
        temperature=0.7,
    )

    return tailo_http_response(TailoError.OK, "OK", completion.choices[0].message)