from helpers.wrap import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_authorizer_context, logger, get_path_parameters
from helpers.functions import tailo_http_response
from helpers.sentry import sentry_wrapper
from section_summary.helper import get_section_summary_response

@sentry_wrapper("create_section_summary_handler")
def handler(event, context):
    try:
        token_content = get_authorizer_context(event)
        path_parameters = get_path_parameters(event)

        document_id = path_parameters["document_id"]
        section_id = path_parameters["section_id"]
        user_uuid = token_content.user_id

        summary_response = get_section_summary_response(document_id, user_uuid, section_id)

        return summary_response
    
    except Exception as e:
        logger.exception(f"error in section_summary_handler: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")