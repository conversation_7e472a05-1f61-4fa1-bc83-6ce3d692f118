from helpers.wrap import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_authorizer_context, logger, get_path_parameters
from helpers.functions import tailo_http_response
from helpers.sentry import sentry_wrapper
from section_summary.helper import fetch_existing_summaries_by_doc_id

@sentry_wrapper("fetch_document_summaries_handler")
def handler(event, context):
    try:
        token_content = get_authorizer_context(event)
        path_parameters = get_path_parameters(event)

        document_id = path_parameters["document_id"]
        user_uuid = token_content.user_id

        summary_responses = fetch_existing_summaries_by_doc_id(document_id, user_uuid)

        return summary_responses
    
    except Exception as e:
        logger.exception(f"error in fetch_document_summaries_handler: {e}")
        return tailo_http_response(TailoError.UNKNOWN, "An unknown error occured")