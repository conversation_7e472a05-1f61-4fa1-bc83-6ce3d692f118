import json
from os import environ
from typing import Dict, List
from database.instance import DB
from boto3 import client
from helpers.wrap import <PERSON><PERSON><PERSON><PERSON><PERSON>, session, logger
from helpers.functions import tailo_http_response
from database import User, Document, SectionSummary
from datetime import datetime, timezone
from uuid import uuid4
from section_summary.section_summary import get_summary_prompt_response

bucket_name = environ["BUCKET_NAME"]
s3_client = client("s3", region_name=session.region_name)


def normalize_text(text: str):
    """
    Normalizes text by removing extra spaces and converting to lowercase for deduplication.
    """
    return " ".join(text.lower().split())


def combine_text_segments(input_items: List[Dict], sections: Dict):
    """
    Combines text from extraction results, ensuring no duplication.

    Args:
        input_items (list): List of input items where each item is a dictionary
                            containing 'id', 'text', and other metadata.

    Returns:
        str: The combined, deduplicated text.
    """
    processed_ids = set()  # Track unique IDs of processed items
    combined_text_set = set()  # Track normalized text to avoid duplication
    combined_text = []  # Store combined text as a list for efficient concatenation

    for item in input_items:
        item_id = item.get("id")
        item_type = item.get("type")
        item_text = item.get("text", "").strip()

        if not item_type or item_id in processed_ids:
            continue

        if item_type == "paragraph":
            # Skip empty text or already processed items
            if not item_text:
                continue

            # Normalize the current text for comparison
            normalized_text = normalize_text(item_text)

            # Check if the text is already added
            if normalized_text in combined_text_set:
                continue

            # Add text to the combined list and track its normalized version and ID
            combined_text.append(item_text)
            combined_text_set.add(normalized_text)
            processed_ids.add(item_id)
        elif item_type == "list":
            for list_item in item["items"]:
                list_text = list_item["value"]
                normalized_list_text = normalize_text(list_text)

                combined_text.append(list_text)
                combined_text_set.add(normalized_list_text)

            processed_ids.add(item_id)
        elif item_type == "section":
            section_id = item["id"]  # Grab section id
            section_content = sections[section_id][
                "content"
            ]  # Grab section content from all sections
            section_text = combine_text_segments(
                section_content, sections
            )  # Grab the combined text from the section recursively
            combined_text.append(section_text)  # Append text to combined text

    # Join the combined text list into a single string
    return " ".join(combined_text)


def get_section_summary_response(document_id, user_uuid, section_id):
    # Establish database connection and retrieve document in question.
    with DB() as database_session:
        document = Document.by_uuid(session=database_session, uuid=document_id)

        if document is None:
            return tailo_http_response(
                TailoError.INVALID_DOCUMENT, "Unable to find specified document"
            )

        user = User.get_by_estendio_id(database_session, user_uuid)

        if user is None:
            return tailo_http_response(TailoError.MISSING_USER, "Unable to find user")

        if document.owner_user_id != user.id:
            return tailo_http_response(
                TailoError.DENIED, "User does not have permissions to edit document"
            )

        extracted_url = document.extracted_path
        file_object = s3_client.get_object(Bucket=bucket_name, Key=extracted_url)
        file_contents = json.loads(file_object["Body"].read())

        if section_id not in file_contents["sections"]:
            return tailo_http_response(TailoError.NOT_FOUND, "Section not found")

        # If there is already a summary for this document/section then return that instead
        existing_summary = SectionSummary.by_section_id(
            session=database_session,
            document_id=document.id,
            section_id=section_id,
        )

        if existing_summary:
            logger.info("returning existing summary")
            return tailo_http_response(
                TailoError.OK, "OK", existing_summary.summary_response
            )

        section_content = file_contents["sections"][section_id]["content"]
        combined_text = combine_text_segments(section_content, file_contents["sections"])

        logger.info("running section summary")

        output = get_summary_prompt_response(prompt=combined_text)

        body = json.loads(output["body"])
        content = body["data"]["content"]

        split_output = list(
            filter(lambda item: item.strip() != "", content.split("\n"))
        )
        cleaned_output = [item.lstrip("- ").strip() for item in split_output]

        now = datetime.now(tz=timezone.utc)

        SectionSummary.create(
            session=database_session,
            uuid=uuid4(),
            document_id=document.id,
            section_id=section_id,
            user_id=user.id,
            summary_response=cleaned_output,
            date_created=now,
            date_modified=now,
        )

        return tailo_http_response(TailoError.OK, "OK", cleaned_output)


def fetch_existing_summaries_by_doc_id(document_id: str, user_uuid: str):
    with DB() as database_session:
        document = Document.by_uuid(session=database_session, uuid=document_id)

        if document is None:
            return tailo_http_response(
                TailoError.INVALID_DOCUMENT, "Unable to find specified document"
            )

        user = User.get_by_estendio_id(database_session, user_uuid)

        if user is None:
            return tailo_http_response(TailoError.MISSING_USER, "Unable to find user")

        if document.owner_user_id != user.id:
            return tailo_http_response(
                TailoError.DENIED, "User does not have permissions to edit document"
            )

        summaries = SectionSummary.by_document_id(
            session=database_session,
            document_id=document.id,
        )

        formatted_summaries = [
            {
                "sectionId": summary.section_id,
                "summary": summary.summary_response,
            }
            for summary in summaries
        ]

        return tailo_http_response(TailoError.OK, "OK", formatted_summaries)
