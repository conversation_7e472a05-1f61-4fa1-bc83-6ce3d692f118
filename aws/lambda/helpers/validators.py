from typing import Any, Callable

from .constants import Tai<PERSON><PERSON>rror


def validate_args(body: dict, res: dict, required=False, **kwargs):
    for key in kwargs.keys():
        validator = validate_req_arg if required else validate_arg
        error_code, message = validator(body, res, key, kwargs[key])

        if error_code != 0:
            return error_code, message

    return TailoError.OK, "OK"


def validate_req_arg(
    body: dict,
    res: dict,
    key: str,
    value_type,
    validator: Callable[[Any], bool] = None,  # type: ignore
):
    if key not in body:
        return TailoError.MISSING_PARAM, f"Missing argument '{key}'"

    return validate_arg(body, res, key, value_type, validator)


def validate_arg(
    body: dict,
    res: dict,
    key: str,
    value_type,
    validator: Callable[[Any], bool] = None,  # type: ignore
):
    if key not in body:
        res[key] = res[key] if key in res else None
        return TailoError.OK, "OK"

    value = body[key]

    if not isinstance(value, value_type):
        return TailoError.INVALID_VALUE, f"Invalid value for '{key}'"

    result = (
        validator(value) if validator is not None else validate_value(value, value_type)
    )

    if not result:
        return TailoError.INVALID_VALUE, f"Invalid value for '{key}'"

    res[key] = value

    return TailoError.OK, "OK"


def validate_value(value: Any, value_type):
    if value_type is str:
        return validate_string(value)
    elif value_type is int:
        return True
    elif value_type is list:
        return validate_list(value)
    else:
        return True


def validate_string(value: str):
    return value.strip() != ""


def validate_list(value: list):
    return len(value) > 0
