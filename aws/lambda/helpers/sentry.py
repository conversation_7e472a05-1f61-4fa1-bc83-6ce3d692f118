from os import environ

import sentry_sdk
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from helpers.environment import is_local


def trace(func):
    return sentry_sdk.trace(func)


def sentry_wrapper(task: str, disabled=False):
    def wrapper(func):
        def start_transaction(*args):
            event, context = args
            transaction = sentry_sdk.continue_trace(event["headers"])
            sentry_init(is_local())

            # Initialize Sentry span with the trace ID
            with sentry_sdk.start_transaction(
                op="task", name=task, trace_id=transaction.trace_id
            ) as transaction:
                try:
                    return func(*args)
                finally:
                    transaction.finish()

        return start_transaction

    return wrapper


def sentry_init(disabled=False):
    if not disabled:
        sentry_sdk.init(
            dsn=environ["SENTRY_DNS"],
            integrations=[
                AwsLambdaIntegration(),
            ],
            # Set traces_sample_rate to 1.0 to capture 100%
            # of transactions for performance monitoring.
            # We recommend adjusting this value in production,
            enable_tracing=True,
            traces_sample_rate=1.0,
            environment="development" if is_local() else "production",
        )
