from enum import IntEnum
from http import HTTPStatus


class TailoError(IntEnum):
    UNKNOWN = -1
    OK = 0
    MISSING_PARAM = 1
    DENIED = 2
    ROLE_MISSING = 3
    MISSING_TOKEN = 4
    INVALID_VALUE = 5
    EXPIRED = 6
    MISSING_USER = 7
    INVALID_DOCUMENT = 8
    INVALID_SUMMARISATION = 9
    NOT_FOUND = 10
    FORBIDDEN = 11
    INVALID_COMMENT_THREAD = 12
    INVALID_COMMENT = 13
    INVALID_TAG_UUID = 14


class SafeHttpDict(dict):
    def __missing__(self, key):
        return HTTPStatus.INTERNAL_SERVER_ERROR


TailoErrorHttpMap = SafeHttpDict()
TailoErrorHttpMap[TailoError.UNKNOWN] = HTTPStatus.INTERNAL_SERVER_ERROR
TailoErrorHttpMap[TailoError.OK] = HTTPStatus.OK
TailoErrorHttpMap[TailoError.MISSING_PARAM] = HTTPStatus.BAD_REQUEST
TailoErrorHttpMap[TailoError.DENIED] = HTTPStatus.UNAUTHORIZED
TailoErrorHttpMap[TailoError.ROLE_MISSING] = HTTPStatus.FORBIDDEN
TailoErrorHttpMap[TailoError.FORBIDDEN] = HTTPStatus.FORBIDDEN
TailoErrorHttpMap[TailoError.INVALID_VALUE] = HTTPStatus.BAD_REQUEST
TailoErrorHttpMap[TailoError.EXPIRED] = HTTPStatus.UNAUTHORIZED
TailoErrorHttpMap[TailoError.MISSING_USER] = HTTPStatus.NOT_FOUND
TailoErrorHttpMap[TailoError.INVALID_DOCUMENT] = HTTPStatus.NOT_FOUND
TailoErrorHttpMap[TailoError.INVALID_SUMMARISATION] = HTTPStatus.NOT_FOUND
TailoErrorHttpMap[TailoError.NOT_FOUND] = HTTPStatus.NOT_FOUND
TailoErrorHttpMap[TailoError.INVALID_COMMENT_THREAD] = HTTPStatus.BAD_REQUEST
TailoErrorHttpMap[TailoError.INVALID_COMMENT] = HTTPStatus.BAD_REQUEST
