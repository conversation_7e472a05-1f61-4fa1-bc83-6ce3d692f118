import json
from base64 import b64decode
from datetime import datetime, timedelta, timezone
from json import loads
from .logging import get_tailo_logger
from typing import Dict, Union
from typing_extensions import Any
from helpers.constants import <PERSON><PERSON>Error, TailoErrorHttpMap
from data import AuthDetails, UserDetails
from data.features import UserFeatureConfig, FeatureUnits
from data.auth import AuthContext
from http import HTTPStatus

from boto3 import Session  # type: ignore
from jwt import encode

# TODO: Ideally the function itself should probably be called by lambdas instead of the variable
logger = get_tailo_logger()

session: Session = Session()


def tailo_response(code: TailoError, message: str, data: Union[dict, None] = None):
    response = {"code": code, "message": message}

    if data is not None:
        response["data"] = data

    return response


def tailo_http_response(
    code: TailoError,
    message: str,
    data: Union[dict, None] = None,
    status: Union[HTTPStatus, None] = None,
):
    response = tailo_response(code=code, message=message, data=data)

    http_status = TailoErrorHttpMap[code] if not status else status

    return {
        "statusCode": http_status,
        "headers": {"content-type": "application/json"},
        "body": json.dumps(response),
    }


def get_authorization_token(event):
    if "headers" not in event:
        return None

    headers = event["headers"]

    if "authorization" not in headers:
        return None

    auth_header = headers["authorization"]

    split_header = auth_header.split(" ")

    return split_header[1] if len(split_header) == 2 else split_header[0]


def get_authorizer_context(event) -> Union[AuthContext, None]:
    if "requestContext" not in event:
        return None

    request_context = event["requestContext"]

    if "authorizer" not in request_context:
        return None

    auth_context = request_context["authorizer"]["lambda"]

    features_list = json.loads(auth_context.get("features", []))
    features_configs = {
        feature["id"]: UserFeatureConfig.from_json(feature) for feature in features_list
    }

    return AuthContext(
        user_id=auth_context.get("user_uuid", ""),
        tracking_id=auth_context.get("tracking_id", ""),
        features=features_configs,
        permissions=json.loads(auth_context.get("permissions", "[]")),
    )


def get_body(event):
    if "body" not in event:
        return None

    response = {}
    data = event["body"]

    if "isBase64Encoded" in event and event["isBase64Encoded"] is True:
        data = str(b64decode(event["body"]))

    try:
        response = dict(loads(data))
        return response
    except Exception:
        return response


def generate_access_token(user_id: str, permissions: list, private_key: Any):
    access_expiry = datetime.now() + timedelta(hours=1)
    accessPayload = {
        "exp": access_expiry.timestamp(),
        "iat": datetime.now(tz=timezone.utc),
        "user_id": user_id,
        "permissions": permissions,
    }

    encodedAccessToken = encode(
        payload=accessPayload,
        key=private_key,
        algorithm="RS256",
    )

    return encodedAccessToken, access_expiry


def get_query_strings(event):
    """
    Query strings arrive in a solid string like `key1=val1&key2=val2`.
    This function helps split them up into a dictionary:
    ```
    {
        "key1":"val1",
        "key2":"val2"
    }
    ```
    """
    if "rawQueryString" not in event:
        return None

    response = {}
    data = event["rawQueryString"]

    queries = data.split("&")

    for query in queries:
        split_qry = query.split("=")
        response[split_qry[0]] = None if len(split_qry) == 1 else split_qry[1]

    return response


def get_path_parameters(event) -> Union[Dict[str, str], None]:
    if "pathParameters" not in event:
        return None

    response = event["pathParameters"]

    return response


def extract_session_details(session_response: dict):
    user_details = UserDetails.from_json(session_response["data"]["userDetails"])
    auth_details = AuthDetails.from_json(session_response["data"]["auth"])

    return user_details, auth_details


def set_user_licensing_attributes(**attrs):
    now = datetime.now(tz=timezone.utc)

    ret_val = {}

    if "first_name" in attrs:
        ret_val["firstName"] = {
            "value": attrs["first_name"],
            "exposed": True,
        }

    if "last_name" in attrs:
        ret_val["lastName"] = {
            "value": attrs["last_name"],
            "exposed": True,
        }

    if "occupation" in attrs:
        ret_val["occupation"] = {
            "value": attrs["occupation"],
            "exposed": True,
        }

    if "occupation_title" in attrs:
        ret_val["occupationTitle"] = {
            "value": attrs["occupation_title"],
            "exposed": True,
        }

    if "tos_accepted" in attrs:
        ret_val["tosAccepted"] = {
            "value": str(now) if attrs["tos_accepted"] else "",
            "exposed": False,
        }

    if "gdpr_acceptance" in attrs:
        ret_val["gdprAcceptedAt"] = {
            "value": str(now) if attrs["gdpr_acceptance"] else "",
            "exposed": False,
        }

    if "settings" in attrs:
        ret_val["settings"] = {"value": attrs["settings"], "exposed": True}

    return ret_val


def tts_exceeded(
    time_used: timedelta,
    config_value: int,
    config_unit: FeatureUnits,
) -> bool:
    if config_unit == FeatureUnits.HOURS:
        return (time_used.seconds // 3600) >= config_value
    elif config_unit == FeatureUnits.MINUTES:
        return (time_used.seconds // 60) % 60 >= config_value

    # All else fails assume true for now to be safe
    logger.info("Unsupported FeatureUnit so defaulting to exceeded")
    return True


def get_now() -> datetime:
    return datetime.now(tz=timezone.utc)


def format_session_response(
    session_user_details: UserDetails,
    session_auth_details: AuthDetails,
    user_role,
):
    user_settings = session_user_details.settings

    user_role

    # We currently dont update the users email being verified, so this should make it happen for all users, including those already in the system on next login
    details = {
        "id": session_user_details.id,
        "permissions": user_role.definition["permissions"],
    }

    if session_user_details.first_name:
        details["first_name"] = session_user_details.first_name
    if session_user_details.last_name:
        details["last_name"] = session_user_details.last_name
    if session_user_details.email:
        details["email"] = session_user_details.email
    if session_user_details.tracking_id:
        details["tracking_id"] = session_user_details.tracking_id
    if len(user_settings.keys()) > 0:
        details["settings"] = user_settings

    if session_user_details.license:
        details["license"] = session_user_details.license

    return {
        "auth": {
            "refresh_token": session_auth_details.refresh_token,
            "access_token": session_auth_details.access_token,
        },
        "details": {**details},
    }
