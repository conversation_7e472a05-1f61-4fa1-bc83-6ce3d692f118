import boto3
import json
from uuid import uuid4

from enum import Enum
from os import environ


__sqs_client = boto3.client("sqs")


class AmplitudeEvent(Enum):
    DOC_PROCESSED = "DocProcessed"


def Track(tracking_id: str, event: AmplitudeEvent, event_data: dict):
    __sqs_client.send_message(
        QueueUrl=environ["AMPLITUDE_EVENT_QUEUE_URL"],
        MessageBody=json.dumps(
            {
                "tracking_id": tracking_id,
                "event_name": event.value,
                "event_data": event_data,
            }
        ),
        MessageGroupId=uuid4().hex,
        MessageDeduplicationId=uuid4().hex,
    )
