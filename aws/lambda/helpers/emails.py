import json
from os import environ

import boto3

_ses_client = None


def _get_ses_client():
    global _ses_client
    if _ses_client is None:
        _ses_client = boto3.client("ses")

    return _ses_client


def get_logo_url():
    return f"https://{environ['EMAIL_BUCKET_URL']}/{environ['LOGO_IMG_KEY']}"


def send_feedback_email(
    user: str,
    user_uuid: str,
    subject: str,
    location_in_app: str,
    feedback: str,
    reply_consent: bool,
    date_sent: str,
    user_rating: str,
    document_type: str,
) -> bool:

    try:
        ses_client = _get_ses_client()
        ses_client.send_email(
            Destination={"ToAddresses": [environ["FEEDBACK_EMAIL_INBOX"]]},
            Message={
                "Body": {
                    "Text": {
                        "Charset": "UTF-8",
                        "Data": f"User: {user}\nUser UUID: {user_uuid}\nSubject: {subject}\nLocation Problem Occured: {location_in_app}\nFeedback: {feedback}\nReply Consent: {reply_consent}\nDate Sent: {date_sent}\nUser Rating: {user_rating}\nDocument Type: {document_type}",
                    }
                },
                "Subject": {"Charset": "UTF-8", "Data": "Feedback"},
            },
            Source=environ[
                "FEEDBACK_EMAIL_INBOX"
            ],  # TODO Change this when we decide how we want it sent
        )
        return True
    except Exception as e:
        print(f"Error sending emails, {e}")
        return False


def send_template(
    source: str, destinations: list[str], template: str, data: dict
) -> bool:
    try:
        print("Logo URL", get_logo_url())
        print("template", template)
        print("data", json.dumps(data, indent=4))
        print("Destinations", destinations)
        print("source", source)

        ses_client = _get_ses_client()
        res = ses_client.send_templated_email(
            Destination={"ToAddresses": destinations},
            Template=template,
            TemplateData=json.dumps(data),
            Source=source,
        )
        # HELLO_EMAIL_SOURCE
        print(res)
        return True
    except Exception as e:
        print(f"Error sending emails, {e}")
        return False
    pass
