from logging import INFO as LOGGING_INFO
from logging import Logger, getLogger
from os import environ


def get_tailo_logger() -> Logger:
    log_level = environ.get("LOG_LEVEL", "")
    level = LOGGING_INFO
    # if log_level == "DEBUG":
    #     level = LOGGING_DEBUG
    # else:
    #     level = LOGGING_INFO if is_local() else LOGGING_ERROR

    logger = getLogger()
    logger.setLevel(level)

    return logger
