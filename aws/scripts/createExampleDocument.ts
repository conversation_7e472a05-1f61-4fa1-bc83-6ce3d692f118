import {promises as fs} from 'fs';
import {Bucket} from 'sst/node/bucket';
import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
  NoSuchKey,
} from '@aws-sdk/client-s3';

const s3Client = new S3Client({});

async function uploadFile() {
  const exampleContents = await fs.readFile(
    `aws/scripts/data/${process.env.EXAMPLE_DOCUMENT_KEY}`,
    'utf-8',
  );

  const putObjectCommand = new PutObjectCommand({
    Bucket: Bucket.TailoDocumentBucket.bucketName,
    Key: process.env.EXAMPLE_DOCUMENT_KEY,
    Body: exampleContents,
  });

  await s3Client.send(putObjectCommand);
}

export async function handler() {
  // Ensure appropriate environment variable has been set
  if (!process.env.EXAMPLE_DOCUMENT_KEY) {
    throw new Error("'EXAMPLE_DOCUMENT_KEY' not set");
  }

  try {
    // Check if file exists
    const getObjectCmd = new GetObjectCommand({
      Bucket: Bucket.TailoDocumentBucket.bucketName,
      Key: process.env.EXAMPLE_DOCUMENT_KEY,
    });

    const result = await s3Client.send(getObjectCmd);
  } catch (error) {
    if (error instanceof NoSuchKey) {
      // If NoSuchKey then we need to upload the file
      return await uploadFile();
    } else {
      // Throw it because it's unknown
      throw error;
    }
  }
  await uploadFile();
  // If exists then exit
  // If doesn't exist then upload
}
