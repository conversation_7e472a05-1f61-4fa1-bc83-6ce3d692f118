import * as path from 'path';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileMigrationProvider, PostgresDialect} from 'kysely';
import {Pool} from 'pg';
import {promises as fs} from 'fs';
import {
  GetSecretValueCommand,
  SecretsManagerClient,
} from '@aws-sdk/client-secrets-manager';

interface SecretCredentials {
  username: string;
  password: string;
}

async function getSecretCredentials(
  secretName: string,
): Promise<SecretCredentials> {
  const client = new SecretsManagerClient();
  const response = await client.send(
    new GetSecretValueCommand({
      SecretId: secretName,
    }),
  );

  if (!response.SecretString) {
    throw new Error('Unable to retrieve secret information');
  }

  return JSON.parse(response.SecretString) as SecretCredentials;
}

export async function handler() {
  const migrationFolder = path.join(__dirname, '..', '..', 'db', 'migrations');

  const {username, password} = await getSecretCredentials(
    process.env.SECRET_NAME!,
  );

  // Verify migration folder
  try {
    const files = await fs.readdir(migrationFolder);
  } catch (error) {
    console.error('Error reading migration folder:', error);
    process.exit(1);
  }

  const db = new Kysely<any>({
    dialect: new PostgresDialect({
      pool: new Pool({
        host: process.env.RDS_HOST,
        port: parseInt(process.env.RDS_DB_PORT || '5432'),
        database: process.env.RDS_DB_NAME,
        user: username,
        password: password,
        connectionTimeoutMillis: 25000, // 25 seconds
      }),
    }),
  });

  const migrator = new Migrator({
    db,
    provider: new FileMigrationProvider({
      fs,
      path,
      migrationFolder: migrationFolder,
    }),
  });

  const nonMigrated = (await migrator.getMigrations()).filter(
    migration => !migration.executedAt,
  );
  const migrateCount = nonMigrated.length;

  if (migrateCount > 0) {
    console.log(
      `Running migrations for: ${nonMigrated
        .map(migration => migration.name)
        .join(', ')}`,
    );
  }

  for (let i = 0; i < migrateCount; i++) {
    const {error, results} = await migrator.migrateUp();
    if (error || !results) {
      console.error('failed to migrate');
      console.error(error);
      process.exit(1);
    }
    results.forEach(it => {
      if (it.status === 'Success') {
        console.log(
          `migration "${it.migrationName}" was executed successfully`,
        );
      } else if (it.status === 'Error') {
        console.error(`failed to execute migration "${it.migrationName}"`);
      }
    });
  }

  await db.destroy();
}
