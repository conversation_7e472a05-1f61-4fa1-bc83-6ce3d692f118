import * as fs from 'fs';
import {Table} from 'sst/node/table';
import {DynamoDBClient} from '@aws-sdk/client-dynamodb';
import {PutCommand, DynamoDBDocumentClient} from '@aws-sdk/lib-dynamodb';

const TOSFilePath = 'aws/scripts/data/terms_of_service_v2.json';
const PrivacyFilePath = 'aws/scripts/data/privacy_policy_tailo_v2.json';

const client = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(client);

export async function handler() {
  const TOSdata = fs.readFileSync(TOSFilePath, 'utf8');
  const TOSjsonData = JSON.parse(TOSdata);

  const Privacydata = fs.readFileSync(PrivacyFilePath, 'utf8');
  const PrivacyjsonData = JSON.parse(Privacydata);

  const TOSCommand = new PutCommand({
    TableName: Table.Legals.tableName,
    Item: {
      title: process.env.TOS_KEY,
      version: 1,
      content: JSON.stringify(TOSjsonData),
    },
  });
  const PrivacyCommand = new PutCommand({
    TableName: Table.Legals.tableName,
    Item: {
      title: process.env.PRIVACY_KEY,
      version: 1,
      content: JSON.stringify(PrivacyjsonData),
    },
  });
  await docClient.send(TOSCommand);
  await docClient.send(PrivacyCommand);
}
