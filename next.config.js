// github action failing due to lack of process.env so adding string backup
const bucketUrlInDev = process.env.NEXT_PUBLIC_BUCKET_URL || '';

/** @type {import('next').NextConfig} */
const nextConfig = {
  // // Comment out for debugging the Next app.
  //   webpack(config) {
  //     config.infrastructureLogging = {debug: /PackFileCache/};
  //     return config;
  //   },

  images: {
    domains: [
      bucketUrlInDev,
      'production-tailo-apistack-tailodocumentbucketd3c10-yewioad3ryjz.s3.amazonaws.com', // UK Production
      'production-us-tailo-apist-tailodocumentbucketd3c10-wzfev69fffex.s3.amazonaws.com', // US Production
      'staging-tailo-apistack-tailodocumentbucketd3c10378-oy7oip1jtes1.s3.amazonaws.com', // Staging
      'alpha-tailo-apistack-tailodocumentbucketd3c10378-0yq8zgbdnqj8.s3.amazonaws.com', // Alpha
      'experimental-tailo-apista-tailodocumentbucketd3c10-9ye8hr8jbvnu.s3.amazonaws.com', // Experimental
      'development-tailo-apistac-tailodocumentbucketd3c10-bqmmgmy5tctq.s3.amazonaws.com', // Development
    ],
  },
};

module.exports = nextConfig;

// Injected content via Sentry wizard below

const {withSentryConfig} = require('@sentry/nextjs');

module.exports = withSentryConfig(
  module.exports,
  {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options

    // Suppresses source map uploading logs during build
    silent: true,

    org: 'estendio',
    project: 'tailo',
  },
  {
    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Transpiles SDK to be compatible with IE11 (increases bundle size)
    transpileClientSDK: true,

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,
  },
);
