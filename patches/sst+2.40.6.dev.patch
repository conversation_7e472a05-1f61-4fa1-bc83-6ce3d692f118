diff --git a/node_modules/sst/support/python-runtime/Dockerfile b/node_modules/sst/support/python-runtime/Dockerfile
index 057512c..058ec10 100644
--- a/node_modules/sst/support/python-runtime/Dockerfile
+++ b/node_modules/sst/support/python-runtime/Dockerfile
@@ -4,6 +4,6 @@ ARG IMAGE=amazon/aws-sam-cli-build-image-python3.7
 FROM $IMAGE
 
 # Ensure rsync is installed
-RUN yum -q list installed rsync &>/dev/null || yum install -y rsync
+RUN yum -q list installed rsync &>/dev/null || yum install -y rsync || dnf repoquery --installed rsync &>/dev/null || dnf install -y rsync
 
 CMD [ "python" ]
diff --git a/node_modules/sst/support/python-runtime/Dockerfile.custom b/node_modules/sst/support/python-runtime/Dockerfile.custom
index aa23ba8..344347e 100644
--- a/node_modules/sst/support/python-runtime/Dockerfile.custom
+++ b/node_modules/sst/support/python-runtime/Dockerfile.custom
@@ -4,7 +4,7 @@ ARG IMAGE=amazon/aws-sam-cli-build-image-python3.7
 FROM $IMAGE
 
 # Ensure rsync is installed
-RUN yum -q list installed rsync &>/dev/null || yum install -y rsync
+RUN yum -q list installed rsync &>/dev/null || yum install -y rsync || dnf repoquery --installed rsync &>/dev/null || dnf install -y rsync
 
 # Upgrade pip (required by cryptography v3.4 and above, which is a dependency of poetry)
 RUN pip install --upgrade pip
diff --git a/node_modules/sst/support/python-runtime/Dockerfile.dependencies b/node_modules/sst/support/python-runtime/Dockerfile.dependencies
index 9828a7a..9e0d283 100644
--- a/node_modules/sst/support/python-runtime/Dockerfile.dependencies
+++ b/node_modules/sst/support/python-runtime/Dockerfile.dependencies
@@ -4,7 +4,7 @@ ARG IMAGE=amazon/aws-sam-cli-build-image-python3.7
 FROM $IMAGE
 
 # Ensure rsync is installed
-RUN yum -q list installed rsync &>/dev/null || yum install -y rsync
+RUN yum -q list installed rsync &>/dev/null || yum install -y rsync || dnf repoquery --installed rsync &>/dev/null || dnf install -y rsync
 
 # Upgrade pip (required by cryptography v3.4 and above, which is a dependency of poetry)
 RUN pip install --upgrade pip
