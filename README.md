## Set up notes

Please see the setup notes in [this Confluence page](https://estendio.atlassian.net/wiki/spaces/PP/pages/1971093505/<PERSON>lo+set+up+notes)

## Running locally once setup (assumes AWS SSO)

### Configure

Copy the root file `.cli-env.default` to `.cli-env` and set the `AWS_PROFILE` value to be your SSO profile name. For example if my AWS SSO profile was configured to be `Tailo-local` then I would change `.cli-env` to look like.

```
export AWS_PROFILE=Tailo-local
```

Once this is done then you can run the launch scripts successfully

### Run the scripts

Ensure the local SST instance is running, open a terminal and ensure you are in a venv then run

```
sh scripts/launch_aws.sh
```

With the local SST running launch the local Next.js instance with

```
sh scripts/launch_next.sh
```

### Pip installation script

To ensure you have all the Python packages installed correctly simply run

```
sh scripts/install.sh
```

This script iterates through all the `/aws/` sub-folders and executes `pip install -r requirements.txt` for any `requirements.txt` files.

## Unit Tests

### Backend

To run the backend unit tests locally, open a terminal and activate your `venv`. Once your `venv` is active run the following command in the root of the tailo directory

```
python -m pytest --verbose
```
