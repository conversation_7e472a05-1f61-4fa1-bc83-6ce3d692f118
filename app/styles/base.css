@layer base {
  /* Removes arrows from input number Chrome, Safari, Edge, Opera */
  /* Add textfield class to the component styling*/
  input.textfield::-webkit-outer-spin-button,
  input.textfield::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  /* Firefox */
  input.textfield[type='number'] {
    appearance: textfield;
    -moz-appearance: textfield;
  }
  :root {
    --shadow-sm-d: 0 1px 2px 0;
    --shadow-d: 0 1px 3px 0;
    --shadow-d-2: 0 1px 2px -1px;
    --shadow-md-d: 0 4px 6px -1px;
    --shadow-md-d-2: 0 2px 4px -2px;
    --shadow-lg-d: 0 10px 15px -3px;
    --shadow-lg-d-2: 0 4px 6px -4px;
    --shadow-xl-d: 0 20px 25px -5px;
    --shadow-xl-d-2: 0 8px 10px -6px;
    --shadow-2xl-d: 0 25px 50px -12px;
    --shadow-sm-u: 0 -1px 2px 0;
    --shadow-u: 0 -1px 3px 0;
    --shadow-u-2: 0 -1px 2px -1px;
    --shadow-md-u: 0 -4px 6px -1px;
    --shadow-md-u-2: 0 -2px 4px -2px;
    --shadow-lg-u: 0 -10px 15px -3px;
    --shadow-lg-u-2: 0 -4px 6px -4px;
    --shadow-xl-u: 0 -20px 25px -5px;
    --shadow-xl-u-2: 0 -8px 10px -6px;
    --shadow-2xl-u: 0 -25px 50px -12px;
    --shadow-l: -1px 0 3px 0;
    --shadow-l-2: -1px 0 2px -1px;
    --shadow-md-l: -4px 0 6px -1px;
    --shadow-md-l-2: -2px 0 4px -2px;
    --shadow-solid-d: 0 1px 0 0;
    --shadow-solid-md-d: 0 3px 0 0;
    --shadow-color-base: rgba(0, 0, 0, 0.1);
    --shadow-color-secondary: rgba(2, 6, 23, 0.1);
    --shadow-color-base-light: rgba(2, 6, 23, 0.1);
    --shadow-color-secondary-light: rgba(2, 6, 23, 0.05);
    --shadow-color-base-pale-blue: rgba(23, 37, 84, 0.1);
    --shadow-color-base-pale-green: rgba(26, 46, 5, 0.1);
    --shadow-color-base-pale-pink: rgba(76, 5, 25, 0.1);
    --shadow-color-base-peach: rgba(27, 26, 24, 0.1);
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  th {
    @apply font-semibold;
  }
}

@layer components {
  /* This won't be included in your compiled CSS unless you actually use it */
  .btn-base {
    @apply ring-4 ring-transparent focus:ring-4 focus-within:ring-0 ring-offset-0 outline-none flex flex-row justify-center items-center gap-2.5 rounded-md text-center font-semibold transition-colors;
  }

  /* Applies to all text and icon buttons (excluding FAB) */
  .btn {
    @apply font-semibold transition-colors border-2 border-transparent flex items-center gap-1.5
    focus:ring-3
    disabled:cursor-not-allowed;
  }

  /* Text-based buttons (sometimes with icons to left/right) */
  .btn-xs {
    @apply text-base p-1 px-2 rounded-md leading-4;
  }
  .btn-sm {
    @apply text-base p-4 rounded-md leading-4;
  }
  .btn-md {
    @apply text-xl py-5 px-6 rounded-lg leading-5;
  }
  .btn-lg {
    @apply text-xl py-6 px-10 rounded-lg leading-5;
  }

  .btn-full {
    @apply w-full justify-center;
  }

  .btn-xs svg,
  .btn-sm svg {
    @apply w-4 h-4;
  }
  .btn-md svg {
    @apply w-5 h-5;
  }
  .btn-lg svg {
    @apply w-6 h-6;
  }

  /* Icon buttons only (excluding FAB) → Doc tools & navbar icon buttons */
  .icon-btn-sm {
    @apply p-2 rounded-md;
  }
  .icon-btn-md {
    @apply p-2 rounded-md;
  }
  .icon-btn-lg {
    @apply p-2 rounded-lg;
  }

  .icon-btn-sm svg {
    @apply h-6 w-6;
  }
  .icon-btn-md svg {
    @apply h-8 w-8;
  }
  .icon-btn-lg svg {
    @apply h-10 w-10;
  }

  /* TEXT INPUTS */
  .input {
    @apply 
    w-full rounded-lg border-2 transition-colors bg-transparent
    focus:ring-3
    disabled:cursor-not-allowed;
  }
  .input:is(:-webkit-autofill, :autofill) {
    @apply ring-1 ring-offset-1;
  }
  .input-sm {
    @apply px-3.5 py-3 text-sm leading-3.5;
  }
  .input-md {
    @apply px-4 py-3.5 text-base leading-4;
  }
  .input-lg {
    @apply p-5 text-base leading-4;
  }

  /* TEXTAREA */
  .textarea {
    @apply input w-full;
  }
  .textarea-sm {
    @apply h-32 px-3.5 py-3 text-sm;
  }
  .textarea-md {
    @apply h-40 px-4 py-2 text-base;
  }
  .textarea-lg {
    @apply h-48 p-5 text-base;
  }
  .dropdown-input-item svg {
    @apply w-5 h-5;
  }
  .card .card-link {
    content: '';
    inset: 0;
    z-index: 1;
    @apply sm:text-xl font-semibold text-base sm:font-normal leading-tight
    before:absolute before:left-0 before:top-0 before:right-0 before:bottom-0 before:rounded-lg before:border before:border-transparent before:ring-transparent before:ring-4
    before:focus-visible:ring-4;
  }

  /* Document list card */
  .card-after {
    @apply w-0 h-0 overflow-hidden block outline-none after:absolute after:left-0 after:top-0 after:right-0 after:bottom-0 after:rounded-lg after:border-2 after:border-transparent after:ring-transparent after:ring-4;
  }

  /* The menu that appears when text is selected, in-document (Define, Explain this, etc) */
  .context-menu {
    @apply border rounded-lg;
  }
  .context-menu-options {
    @apply flex divide-x my-1;
  }
  .context-menu-options button {
    @apply mx-1;
  }
 
  .pill {
    @apply h-8 px-2 pr-2.5 py-1 bg-opacity-50 rounded-2xl justify-center items-center gap-1 inline-flex capitalize;
  }

  .reading-time {
    @apply flex gap-1.5 justify-start items-center p-1 px-2 text-base rounded-md font-semibold;
  }

  .dropdown-panel {
    @apply rounded-lg absolute right-0 shadow-xl border space-y-1 mt-1 p-2;
  }

  .read-time-label {
    @apply h-8 w-32 rounded-md flex text-base justify-center items-center gap-1;
  }
  /* Responsive Table layout  */
  .responsive-table {
    border-spacing: 0;
    /* Negative margin to accommodate for hidden thead in mobile view */
    @apply w-full border-collapse lg:border -mt-12 lg:mt-2;
  }

  /* Hides thead on mobiles but keeps it AT accessible*/
  .responsive-table thead {
    @apply invisible  lg:visible;
  }
  .responsive-table tr {
    @apply block lg:table-row mb-2 lg:mb-0 lg:p-2 border lg:border-none;
  }

  .responsive-table th,
  .responsive-table td {
    @apply lg:p-2 lg:border;
  }
  /* .tdBefore and grid for mobile layout */
  .responsive-table td {
    @apply grid grid-cols-3 gap-2 lg:table-cell;
  }
  .responsive-table .tdBefore {
    @apply font-bold lg:hidden;
  }

  .link {
    @apply font-semibold underline;
  }
  .desktop-onboarding-step-link {
    @apply focus-visible:outline-none;
  }
  .desktop-onboarding-step-link.disabled {
    @apply cursor-not-allowed;
  }
  /* Overview nav layout with grid */
  .grid-area-overview-app-nav {
    grid-area: app-nav;
  }
  .grid-area-overview-doc-nav {
    grid-area: doc-nav;
  }
  .grid-area-overview-doc-tools {
    grid-area: doc-tools;
  }
  .grid-area-overview-main {
    grid-area: main;
  }
  .overview-nav-desktop {
    /* 1fr ensures that teh main section of the page is stretched across with the nav width equivalent of w-16 for app nav and dev tools. When changing the width values, please adjust the paddings in DocumentOverviewNavigation file for scrolling animation. */
    grid-template-columns: 4rem 1fr 4rem;
    display: grid;
    grid-template-areas:
      'app-nav doc-nav doc-nav'
      'app-nav main doc-tools';
  }
  .overview-nav-mobile {
    display: grid;
    grid-template-areas:
      'app-nav'
      'main'
      'doc-nav'
      'doc-tools';
  }

  .active-tts-paragraph {
    padding-bottom: 0.5rem !important; /* Sorry */
    margin-bottom: 1rem;
  }

  .toc-items {
    @apply p-2 block;
  }
  .checkbox-label:has(input:checked) {
    @apply feedback-face-card-active;
  }
  
  .summary-expand-btn {
    /* Lift the button above the borders and separators, to avoid overlapping */
    @apply relative z-[1];
  }

  .active-toc-parent-text {
    @apply 
    font-bold border-2 border-transparent relative rounded-md
    before:content-['-'] before:absolute before:top-[8px] before:-left-[6px]
    focus-visible:border-2
    active:rounded-md active:no-underline;
  }
  .inactive-toc-parent-text {
    @apply 
    box-border border-2 border-transparent rounded-md relative
    hover:underline
    focus-visible:border-2
    focus-visible:underline
    active:rounded-md active:underline;
  }
  .inactive-toc-parent-text + ul a {
    @apply pl-5 hover:no-underline focus-visible:no-underline relative;
  }
  .active-toc-parent-text + ul a {
    @apply pl-5 hover:no-underline focus-visible:no-underline relative;
  }

}
