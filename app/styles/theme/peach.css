/* [data-theme*='peach'] .custom-class { }*/

:root {
  --scrollbar-track: transparent;
  --scrollbar-thumb: @apply tailo-peach-500;
}

@layer base {
}
@layer components {
  [data-theme*='peach'] .btn {
    @apply focus:border-tailo-peach-400 focus:ring-blue-700 focus:hover:border-tailo-peach-400;
  }

  [data-theme*='peach'] .btn-primary {
    @apply bg-tailo-peach-950 text-tailo-peach-50 
    hover:border-transparent hover:bg-tailo-peach-900 
    active:bg-tailo-peach-800 
    disabled:bg-tailo-peach-500 disabled:text-tailo-peach-900 
    data-[disabled=true]:bg-tailo-peach-400 data-[disabled=true]:text-tailo-peach-900;
  }

  [data-theme*='peach'] .btn-secondary {
    @apply border-2 border-tailo-peach-950 text-tailo-peach-950 
    hover:border-tailo-peach-800 
    hover:active:border-tailo-peach-950 
    active:border-tailo-peach-950 
    disabled:border-tailo-peach-500 disabled:text-tailo-peach-900 
    data-[disabled=true]:border-tailo-peach-500 data-[disabled=true]:text-tailo-peach-900
    disabled:active:border-tailo-peach-500;
  }

  [data-theme*='peach'] .btn-tertiary {
    @apply border-transparent text-tailo-peach-950 
    hover:bg-tailo-peach-500 
    hover:disabled:bg-transparent
    active:bg-tailo-peach-300 
    disabled:text-tailo-peach-900 
    data-[disabled=true]:text-tailo-peach-900;
  }

  [data-theme*='peach'] .btn-positive {
    @apply bg-green-600 text-white 
    hover:bg-green-700 
    active:bg-green-500 
    disabled:bg-green-100 disabled:text-green-700 
    data-[disabled=true]:bg-green-100 data-[disabled=true]:text-green-700;
  }

  [data-theme*='peach'] .btn-destructive {
    @apply bg-red-600 text-white 
    hover:bg-red-700 
    active:bg-red-500 
    disabled:bg-red-100 disabled:text-red-700 
    data-[disabled=true]:bg-red-100 data-[disabled=true]:text-red-700;
  }

  [data-theme*='peach'] .onboarding-page {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .pill-positive {
    @apply bg-green-400 text-green-950;
  }

  [data-theme*='peach'] .pill-negative {
    @apply bg-rose-300 text-rose-950;
  }

  [data-theme*='peach'] .pill-info {
    @apply bg-sky-400 text-sky-950;
  }

  [data-theme*='peach'] .pill-neutral {
    @apply bg-tailo-peach-950 text-tailo-peach-50;
  }

  [data-theme*='peach'] .input {
    @apply border-tailo-peach-950 text-tailo-peach-950 
    placeholder:text-tailo-peach-800
    hover:border-tailo-peach-800
    hover:active:border-tailo-peach-950 
    active:border-tailo-peach-950
    disabled:text-tailo-peach-900 disabled:border-tailo-peach-600 disabled:bg-tailo-peach-500
    disabled:active:border-tailo-peach-600    
    checked:border-tailo-peach-950 checked:bg-tailo-peach-950 checked:text-transparent 
    focus:border-tailo-peach-950 focus:ring-blue-700;
  }
  /* Native HTML <select> component options list bg */
  [data-theme*='peach'] .input optgroup,
  [data-theme*='peach'] .input option {
    @apply bg-tailo-peach-100;
  }
  /* Custom <select> component options list bg */
  [data-theme*='peach'] .form-dropdown-select {
    @apply border-tailo-peach-100 bg-tailo-peach-100 text-tailo-peach-950;
  }
  [data-theme*='peach'] .input:is(:-webkit-autofill, :autofill) {
    @apply ring-tailo-peach-950;
  }
  [data-theme*='peach'] .form-input-success {
    @apply border-green-600;
  }

  [data-theme*='peach'] .form-error {
    @apply bg-red-700;
  }

  [data-theme*='peach'] .form-error-border {
    @apply border-red-700;
  }

  [data-theme*='peach'] .context-menu {
    @apply bg-tailo-peach-400 border-tailo-peach-500;
  }
  [data-theme*='peach'] .context-menu-options {
    @apply divide-tailo-peach-500;
  }

  /* Checkbox Toggle Control */
  [data-theme*='peach'] input[type="checkbox"] ~ .toggle-control {
    @apply bg-tailo-peach-400 border-tailo-peach-900 after:bg-tailo-peach-950 after:border-tailo-peach-400;
  }
  [data-theme*='peach'] input[type="checkbox"]:checked ~ .toggle-control {
    @apply bg-tailo-peach-950 after:bg-tailo-peach-400;
  }    

  [data-theme*='peach'] .app-navbar-bg,
  [data-theme*='peach'] .highlighted-block {
    @apply bg-tailo-peach-300;
  }

  [data-theme*='peach'] .doc-bg {
    @apply bg-tailo-peach-400;
  }

  [data-theme*='peach'] .separator {
    @apply border-tailo-peach-500;
  }
  [data-theme*='peach'] .panel {
    @apply rounded-lg bg-tailo-peach-300;
  }
  [data-theme*='peach'] .dropdown-panel {
    @apply bg-tailo-peach-300 border-tailo-peach-500;
  }
  [data-theme*='peach'] .nav-item {
    @apply bg-stone-900 text-orange-200;
  }
  [data-theme*='peach'] .account-btn {
    @apply bg-stone-900 text-orange-200;
  }
  [data-theme*='peach'] .modal-bg {
    @apply bg-tailo-peach-50;
  }
  [data-theme*='peach'] .bottom-sheet-scrollbar {
    @apply mr-1.5 scrollbar scrollbar-thumb-tailo-peach-500 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='peach'] .doc-scrollbar {
    @apply scrollbar scrollbar-thumb-tailo-peach-500 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='peach'] .upload-area {
    @apply border-dashed border-tailo-peach-950 outline-none hover:border-tailo-peach-900 focus-visible:border-none focus-visible:ring-4 focus-visible:ring-blue-700;
  }
  [data-theme*='peach'] .upload-area-drag {
    @apply border-tailo-peach-900;
  }
  [data-theme*='peach'] .default-progress-bar {
    @apply border-tailo-peach-950 bg-tailo-peach-50;
  }
  [data-theme*='peach'] .default-progress-bar-upload {
    @apply border-tailo-peach-950 bg-tailo-peach-600;
  }
  [data-theme*='peach'] .default-progress-bar-icon {
    @apply text-tailo-peach-950;
  }
  [data-theme*='peach'] .message-bg-positive {
    @apply bg-green-300 text-green-950;
  }
  [data-theme*='peach'] .message-bg-negative {
    @apply bg-red-300 text-red-950;
  }
  [data-theme*='peach'] .message-bg-info {
    @apply bg-sky-200 text-sky-950;
  }
  [data-theme*='peach'] .card {
    @apply bg-tailo-peach-50 border-tailo-peach-500
    hover:bg-tailo-peach-100
    active:bg-tailo-peach-300 active:top-[3px] 
    transition-all duration-100;
  }
  [data-theme*='peach'] .card .card-link {
    @apply before:focus-visible:ring-blue-700 before:focus-visible:border-tailo-peach-400;
  }
  [data-theme*='peach'] .card-content {
    @apply text-tailo-peach-950;
  }
  [data-theme*='peach'] .card-content-subdued {
    @apply text-tailo-peach-800;
  }
  [data-theme*='peach'] .card-focus {
    @apply focus-visible:after:ring-blue-700;
  }
  [data-theme*='peach'] .card-icon-container {
    @apply bg-tailo-peach-950;
  }
  [data-theme*='peach'] .card-icon-body {
    @apply text-tailo-peach-50;
  }
  /* Document list item disabled style, inc. children styling */
  [data-theme*='peach'] .card.card-disabled {
    @apply border-tailo-peach-500 bg-tailo-peach-300 hover:border-tailo-peach-500 hover:bg-tailo-peach-300 active:top-0;
  }
  [data-theme*='peach'] .card-disabled .card-icon-container {
    @apply bg-stone-700;
  }
  [data-theme*='peach'] .card-disabled .card-icon-body {
    @apply text-tailo-peach-300;
  }
  [data-theme*='peach'] .card-disabled .card-content,
  [data-theme*='peach'] .card-disabled .card-content-subdued {
    @apply text-stone-700;
  }
  [data-theme*='peach'] .mobile-step-indicator {
    @apply bg-tailo-peach-950 text-tailo-peach-50;
  }
  [data-theme*='peach'] .mobile-step-indicator-inactive {
    @apply bg-tailo-peach-800 text-tailo-peach-50;
  }
  /* ONBOARDING DESKTOP STEP: default/current */
  [data-theme*='peach'] .desktop-onboarding-step-link .inner-outline {
    @apply fill-stone-900 stroke-stone-900;
  }
  [data-theme*='peach']
    .desktop-onboarding-step-link:focus-visible
    .outer-outline {
    @apply stroke-blue-700;
  }
  /* ONBOARDING DESKTOP STEP: disabled  */
  [data-theme*='peach'] .desktop-onboarding-step-link.disabled .inner-outline {
    @apply fill-none stroke-orange-300;
  }
  /* ONBOARDING DESKTOP STEP: completed  */
  [data-theme*='peach'] .desktop-onboarding-step-link.completed .inner-outline {
    @apply fill-none stroke-stone-900;
  }
  [data-theme*='peach']
    .desktop-onboarding-step-link.completed:hover
    .inner-outline {
    @apply stroke-stone-500;
  }
  [data-theme*='peach']
    .desktop-onboarding-step-link.completed:active
    .inner-outline {
    @apply stroke-stone-400;
  }
  [data-theme*='peach'] .nav-from-gradient {
    @apply from-tailo-peach-300;
  }
  [data-theme*='peach'] .verify-spinner {
    @apply text-tailo-peach-950;
  }
  [data-theme*='peach'] .toggle {
    @apply border-stone-700 bg-stone-900 focus:outline-2 focus:outline-blue-700;
    --tglbg: #fed7aa;
  }
  [data-theme*='peach'] .toggle:checked {
    @apply border-stone-700 bg-tailo-peach-400;
    --tglbg: #1c1917;
  }
  [data-theme*='peach'] .focus-outer {
    @apply outline-none focus-visible:border-2 focus-visible:border-blue-700;
  }
  [data-theme*='peach'] .definition-part-of-speech {
    @apply bg-tailo-peach-400 text-tailo-peach-950;
  }
  [data-theme*='peach'] .definition-example {
    @apply bg-tailo-peach-300 text-tailo-peach-900;
  }
  [data-theme*='peach'] .progress-bar-rail {
    @apply bg-tailo-peach-500;
  }
  [data-theme*='peach'] .progress-bar-rail::-webkit-progress-value {
    @apply bg-tailo-peach-950;
  }
  [data-theme*='peach'] .accordion:hover .accordion-title {
    @apply bg-tailo-peach-500;
  }
  [data-theme*='peach'] .accordion-title-active {
    @apply bg-tailo-peach-400;
  }
}
@layer utilities {
  [data-theme*='peach'] .app-bg,
  [data-theme*='peach'] .logo-fill-bg {
    @apply bg-tailo-peach-400 fill-tailo-peach-400;
  }
  [data-theme*='peach'] .app-bg-subdued {
    @apply bg-orange-100;
  }
  [data-theme*='peach'] .theme-border {
    @apply border border-tailo-peach-500;
  }
  [data-theme*='peach'] .theme-border-subdued {
    @apply border border-orange-200;
  }
  [data-theme*='peach'] .theme-border-t {
    @apply border-t-tailo-peach-500;
  }
  [data-theme*='peach'] .theme-border-l {
    @apply border-l border-l-tailo-peach-500;
  }
  [data-theme*='peach'] .theme-warning {
    @apply text-red-500;
  }
  [data-theme*='peach'] .theme-delete {
    @apply text-red-700;
  }

  [data-theme*='peach'] .opposite-bg {
    @apply bg-tailo-peach-950;
  }
  [data-theme*='peach'] .opposite-border,
  [data-theme*='peach'] .reading-marker-border {
    @apply border-tailo-peach-950;
  }
  [data-theme*='peach'] .onboarding-document-preview-container,
  [data-theme*='peach'] .expand-preview-btn-container > button {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .onboarding-document-preview {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .onboarding-doc-pop-out {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .selected-btn {
    @apply border-4 !border-tailo-peach-900;
  }
  [data-theme*='peach'] .svg-stroke {
    @apply stroke-tailo-peach-100;
  }
  [data-theme*='peach'] .doc-text-default {
    @apply text-tailo-peach-950;
  }
  [data-theme*='peach'] .doc-text-inactive {
    @apply text-tailo-peach-800;
  }

  [data-theme*='peach'] .doc-text-subdued {
    @apply text-tailo-peach-900;
  }
  [data-theme*='peach'] .doc-text-inverted {
    @apply text-tailo-peach-50;
  }
  [data-theme*='peach'] .theme-selected {
    @apply ring-blue-600;
  }
  [data-theme*='peach'] .selected {
    @apply ring-tailo-peach-950 ring-offset-2 ring-offset-tailo-peach-400;
  }
  [data-theme*='peach'] .bg-default {
    @apply bg-tailo-peach-50;
  }
  [data-theme*='peach'] .svg-default {
    @apply fill-tailo-peach-950;
  }
  [data-theme*='peach'] .doc-link-default {
    @apply bg-orange-200 hover:bg-tailo-peach-500 focus-visible:bg-tailo-peach-950 active:bg-tailo-peach-950 visited:bg-orange-200 text-tailo-peach-950 focus-visible:text-tailo-peach-50 active:text-tailo-peach-50 visited:text-tailo-peach-900 rounded-sm border-b border-tailo-peach-600 hover:border-tailo-peach-950 focus-visible:border-tailo-peach-950 active:border-tailo-peach-950 visited:border-tailo-peach-950 font-semibold outline-none focus-visible:ring ring-blue-700;
  }

  /* Shadows */
  [data-theme*='peach'] .shadow-sm-d {
    box-shadow: var(--shadow-sm-d) rgba(27, 26, 24, 0.05);
  }
  [data-theme*='peach'] .shadow-d {
    box-shadow: var(--shadow-d) var(--shadow-color-base-peach),
      var(--shadow-d-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-md-d {
    box-shadow: var(--shadow-md-d) var(--shadow-color-base-peach),
      var(--shadow-md-d-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-lg-d {
    box-shadow: var(--shadow-lg-d) var(--shadow-color-base-peach),
      var(--shadow-lg-d-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-xl-d {
    box-shadow: var(--shadow-xl-d) var(--shadow-color-base-peach),
      var(--shadow-xl-d-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-2xl-d {
    box-shadow: var(--shadow-2xl-d) rgba(27, 26, 24, 0.25);
  }
  [data-theme*='peach'] .shadow-sm-u {
    box-shadow: var(--shadow-sm-u) rgba(27, 26, 24, 0.05);
  }
  [data-theme*='peach'] .shadow-u {
    box-shadow: var(--shadow-u) var(--shadow-color-base-peach),
      var(--shadow-u-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-md-u {
    box-shadow: var(--shadow-md-u) var(--shadow-color-base-peach),
      var(--shadow-md-u-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-lg-u {
    box-shadow: var(--shadow-lg-u) var(--shadow-color-base-peach),
      var(--shadow-lg-u-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-xl-u {
    box-shadow: var(--shadow-xl-u) var(--shadow-color-base-peach),
      var(--shadow-xl-u-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-2xl-u {
    box-shadow: var(--shadow-2xl-u) rgba(27, 26, 24, 0.25);
  }
  [data-theme*='peach'] .shadow-l {
    box-shadow: var(--shadow-l) var(--shadow-color-base-peach),
      var(--shadow-l-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-md-l {
    box-shadow: var(--shadow-md-l) var(--shadow-color-base-peach),
      var(--shadow-md-l-2) var(--shadow-color-base-peach);
  }
  [data-theme*='peach'] .shadow-solid-d {
    box-shadow: var(--shadow-solid-d) #f2c38c; /*peach-500*/
  }
  [data-theme*='peach'] .shadow-solid-md-d {
    box-shadow: var(--shadow-solid-md-d) #f2c38c; /*peach-500*/
  }

  /* Table styling */

  [data-theme*='peach'] .table-header-bg {
    @apply bg-orange-300;
  }
  [data-theme*='peach'] .table-column-bg {
    @apply bg-orange-200;
  }
  [data-theme*='peach'] .table-cell-border {
    @apply border-orange-400;
  }
  /* Used on elements imitating focus ring - tabs nav on mobile */
  [data-theme*='peach'] .focus-border {
    @apply border-blue-700;
  }
  [data-theme*='peach'] .text-disabled {
    @apply text-stone-700;
  }
  [data-theme*='peach'] .app-bg-tertiary {
    @apply bg-orange-300;
  }
  [data-theme*='peach'] .break-line {
    @apply bg-orange-400 opacity-25;
  }
  [data-theme*='peach'] .bg-gradient {
    @apply bg-gradient-to-t from-orange-200;
  }
  [data-theme*='peach'] .panel-bg {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .drawer-bg {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .bottom-sheet-top-nav {
    @apply bg-tailo-peach-300 border-b border-tailo-peach-500;
  }
  /* Separate classes needed for the select-dropdown due to focus style beng set dynamically via internal props */
  [data-theme*='peach'] .select-ring {
    @apply ring-blue-700;
  }
  [data-theme*='peach'] .select-option-focus {
    @apply bg-tailo-peach-500;
  }
  [data-theme*='peach'] .focus-outline {
    @apply outline outline-[3px] outline-blue-700;
  }
  [data-theme*='peach'] .slider-text {
    @apply text-sm font-semibold text-tailo-peach-950;
  }
  [data-theme*='peach'] .slider-value {
    @apply mt-1 flex justify-between text-xs text-tailo-peach-950;
  }
  [data-theme*='peach'] .slider-dot {
    @apply bg-tailo-peach-800/75;
  }
  [data-theme*='peach'] .slider-dot-active {
    @apply border border-tailo-peach-950 bg-tailo-peach-950;
  }
  [data-theme*='peach'] .slider-handle {
    @apply border-tailo-peach-950 bg-tailo-peach-950 hover:bg-tailo-peach-950;
  }
  [data-theme*='peach'] .slider-handle-dragging {
    @apply border-none bg-tailo-peach-950 ring-4 ring-tailo-peach-950/25;
  }
  [data-theme*='peach'] .slider-handle-focus {
    @apply bg-tailo-peach-950 ring-4 ring-tailo-peach-950/25;
  }
  [data-theme*='peach'] .slider-rail {
    @apply bg-tailo-peach-500;
  }
  [data-theme*='peach'] .slider-track {
    @apply bg-tailo-peach-950;
  }
  [data-theme*='peach'] .tool-tip {
    @apply bg-tailo-peach-950 text-tailo-peach-50;
  }
  [data-theme*='peach'] .controls-container {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .text-highlight-word-definition {
    @apply bg-blue-700 text-stone-50;
  }
  [data-theme*='peach'] .active-tts-paragraph {
    @apply bg-tailo-peach-950 text-stone-50 px-2 pt-2 rounded-xl;
  }
  [data-theme*='peach'] .tts-progress-bar {
    @apply w-full [&::-webkit-progress-bar]:bg-tailo-peach-600
        [&::-webkit-progress-value]:bg-stone-900;
  }
  [data-theme*='peach'] .overview-gradient-border {
    @apply border border-tailo-peach-700 rounded-2xl p-[2px];
  }
  [data-theme*='peach'] .ai-gradient-border {
    @apply border-2 border-tailo-peach-700 rounded-md;
  }
  [data-theme*='peach'] .quick-summary-container {
    @apply h-full w-full bg-tailo-peach-400 rounded-2xl px-6 py-5;
  }
  [data-theme*='peach'] .ai-notice-container {
    @apply bg-transparent;
  }
  [data-theme*='peach'] .ai-underline {
    @apply underline decoration-tailo-peach-600;
  }
  [data-theme*='peach'] .reading-time {
    @apply bg-tailo-peach-500;
  }
  [data-theme*='peach'] .welcome-banner button {
    @apply border-tailo-peach-300 bg-tailo-peach-300
      hover:bg-tailo-peach-200 
      focus-visible:outline-blue-700 focus-visible:border-tailo-peach-950
      active:bg-tailo-peach-100;
  }
  [data-theme*='peach'] .document-wrapper {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .document-viewer-background {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .closed-right-drawer {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .summary-body {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .document-nav-bar {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .reading-environment-wrapper {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .toc-parent-text {
    @apply text-tailo-peach-950 font-bold;
  }
  [data-theme*='peach'] .toc-child-text {
    @apply text-tailo-peach-950;
  }
  [data-theme*='peach'] .toc-items-text {
    @apply text-tailo-peach-800;
  }
  [data-theme*='peach'] .toc-parent-line {
    @apply w-8 h-0.5 bg-tailo-peach-800;
  }
  [data-theme*='peach'] .toc-child-line {
    @apply w-6 h-0.5 bg-tailo-peach-800;
  }
  [data-theme*='peach'] .toc-grandcchild-line {
    @apply w-4 h-0.5 bg-tailo-peach-800;
  }
  [data-theme*='peach'] .toc-active-element {
    @apply bg-tailo-peach-950;
  }
  [data-theme*='peach'] .nav-border {
    @apply border-b-2 border-tailo-peach-500;
  }
  [data-theme*='peach'] .nav-bg {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .nav-underline {
    @apply bg-slate-950;
  }
  [data-theme*='peach'] .nav-content-styles {
    @apply bg-tailo-peach-200 border-t-2 border-tailo-peach-400 p-2 lg:relative lg:bg-tailo-peach-300 lg:border-none;
  }
  [data-theme*='peach'] .toc-background {
    @apply bg-tailo-peach-400/50 backdrop-blur-sm z-50 rounded-tr-lg rounded-br-lg;
  }
  [data-theme*='peach'] .dropdown-nav {
    @apply bg-tailo-peach-300 border-tailo-peach-600;
  }
  [data-theme*='peach'] .user-svg-icon {
    fill: #1b1a18;
  }
  [data-theme*='peach'] .dashboard-bg {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .documents-panel {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .mobile-dashboard-bg {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .mobile-welcome-panel {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .mobile-document-body {
    @apply bg-tailo-peach-400;
  }
  [data-theme*='peach'] .mobile-extraction-message {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .small-tailo-logo {
    @apply hover:text-slate-800 focus:ring-blue-700 
    focus:outline-2 focus:ring-2 focus:outline-blue-700 focus:border-slate-800
    active:border-slate-800 active:ring-2 active:ring-blue-700
    active:outline-2 active:outline-blue-700 active:text-stone-700;
  }

  /* The left border for the summary page expand/collapse button, to separate the text link from the button visually */
  [data-theme*='peach'] .summary-expand-btn {
    @apply border-l-tailo-peach-500;
  }

  [data-theme*='peach'] .active-toc-parent-text {
    @apply text-slate-950
    hover:bg-tailo-peach-500 hover:text-stone-950
    focus:border-blue-700 focus:bg-tailo-peach-950 focus:text-stone-50
    active:bg-tailo-peach-600 active:text-stone-950;
  }
  [data-theme*='peach'] .inactive-toc-parent-text {
    @apply text-slate-950 decoration-slate-950
    hover:bg-tailo-peach-500 hover:text-stone-950
    focus:border-blue-700 focus:bg-tailo-peach-950 focus:text-stone-50 focus:decoration-slate-50
    active:bg-tailo-peach-600 active:text-stone-950 active:decoration-stone-950;
  }
  [data-theme*='peach'] .inactive-toc-parent-text + ul a {
    @apply text-stone-950;
  }
  [data-theme*='peach'] .active-toc-parent-text + ul a {
    @apply text-stone-950;
  }
  [data-theme*='peach'] .tailo-icon-button-active {
    @apply bg-tailo-peach-200;
  }
  [data-theme*='peach'] .search-highlight {
    @apply bg-yellow-400 text-stone-950;
  }
  [data-theme*='peach'] .themed-spinner {
    @apply text-stone-950;
  }
  [data-theme*='peach'] .result-item-card {
    @apply bg-tailo-peach-100 border-2 border-transparent hover:bg-tailo-peach-500
    active:bg-tailo-peach-200 focus-visible:border-blue-700;
  }
  [data-theme*='peach'] .explain-this-dropdown {
    @apply bg-tailo-peach-300;
  }
  [data-theme*='peach'] .loading-response {
    @apply bg-tailo-peach-200;
  }
  [data-theme*='peach'] .divider {
    @apply bg-tailo-peach-500;
  }
  [data-theme*='peach'] .result-bg {
    @apply bg-tailo-peach-500;
  }
  [data-theme*='peach'] .result-bg {
    @apply text-tailo-peach-900;
  }
  [data-theme*='peach'] .feedback-faces {
    @apply stroke-stone-950;
  }
  [data-theme*='peach'] .feedback-face-card {
    @apply border-2 border-tailo-peach-500 hover:border-tailo-peach-700;
  }
  [data-theme*='peach'] .feedback-face-card-active {
    @apply ring-2 ring-blue-500 border-2 border-stone-950;
  }
}
