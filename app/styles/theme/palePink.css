/* [data-theme*='pale-pink'] .custom-class { }*/

:root {
  --scrollbar-track: transparent;
  --scrollbar-thumb: @apply rose-300;
}

@layer base {
}
@layer components {
  [data-theme*='pale-pink'] .btn {
    @apply focus:border-rose-100 focus:ring-blue-700 focus:hover:border-rose-100;
  }

  [data-theme*='pale-pink'] .btn-primary {
    @apply bg-rose-950 text-rose-50 
    hover:bg-rose-800 
    active:bg-rose-600 
    disabled:bg-rose-300 disabled:text-rose-900 
    data-[disabled=true]:bg-rose-300 data-[disabled=true]:text-rose-900;
  }

  [data-theme*='pale-pink'] .btn-secondary {
    @apply border-2 border-rose-950 text-rose-950 
    hover:border-transparent hover:border-rose-700 
    hover:active:border-rose-500 
    active:border-rose-500 
    disabled:border-rose-300 disabled:text-rose-700 
    data-[disabled=true]:border-rose-300 data-[disabled=true]:text-rose-700
    disabled:active:border-rose-300;
  }

  [data-theme*='pale-pink'] .btn-tertiary {
    @apply text-rose-950 
    hover:bg-rose-300 
    hover:disabled:bg-transparent
    active:bg-rose-400
    disabled:text-rose-700
    data-[disabled=true]:text-rose-700;
  }

  [data-theme*='pale-pink'] .btn-positive {
    @apply bg-green-600 text-white 
     hover:bg-green-700 
     active:bg-green-800 
     disabled:bg-green-200 disabled:text-green-800
     data-[disabled=true]:bg-green-200 data-[disabled=true]:text-green-800;
  }

  [data-theme*='pale-pink'] .btn-destructive {
    @apply bg-red-600 text-white 
    hover:bg-red-700 
    active:bg-red-800 
    disabled:bg-red-100 disabled:text-red-700 
    data-[disabled=true]:bg-red-100 data-[disabled=true]:text-red-700;
  }

  [data-theme*='pale-pink'] .onboarding-page {
    @apply bg-rose-50;
  }
  [data-theme*='pale-pink'] .pill-positive {
    @apply bg-green-400 text-green-950;
  }

  [data-theme*='pale-pink'] .pill-negative {
    @apply bg-rose-300 text-rose-950;
  }

  [data-theme*='pale-pink'] .pill-info {
    @apply bg-sky-400 text-sky-950;
  }

  [data-theme*='pale-pink'] .pill-neutral {
    @apply bg-rose-950 text-rose-50;
  }
  [data-theme*='pale-pink'] .input {
    @apply border-rose-700 text-rose-950 
    placeholder:text-rose-600
    hover:border-rose-800
    hover:active:border-rose-950 
    active:border-rose-950
    disabled:text-rose-800 disabled:border-rose-200 disabled:bg-rose-100
    disabled:active:border-rose-200    
    checked:border-rose-950 checked:bg-rose-950 checked:text-transparent 
    focus:border-rose-950 focus:ring-blue-700;
  }
  /* Native HTML <select> component options list bg */
  [data-theme*='pale-pink'] .input optgroup,
  [data-theme*='pale-pink'] .input option {
    @apply bg-rose-100;
  }
  /* Custom <select> component options list bg */
  [data-theme*='pale-pink'] .form-dropdown-select {
    @apply border-rose-100 bg-rose-100 text-rose-950;
  }
  [data-theme*='pale-pink'] .input:is(:-webkit-autofill, :autofill) {
    @apply ring-rose-950;
  }
  [data-theme*='pale-pink'] .form-input-success {
    @apply border-green-600;
  }

  [data-theme*='pale-pink'] .form-error {
    @apply bg-red-700;
  }

  [data-theme*='pale-pink'] .form-error-border {
    @apply border-red-700;
  }

  [data-theme*='pale-pink'] .context-menu {
    @apply bg-rose-200 border-rose-300;
  }
  [data-theme*='pale-pink'] .context-menu-options {
    @apply divide-rose-300;
  }

  /* Checkbox Toggle Control */
  [data-theme*='pale-pink'] input[type="checkbox"] ~ .toggle-control {
    @apply bg-rose-100 border-rose-700 after:bg-rose-950 after:border-rose-100;
  }
  [data-theme*='pale-pink'] input[type="checkbox"]:checked ~ .toggle-control {
    @apply bg-rose-950 after:bg-rose-100;
  }    

  [data-theme*='pale-pink'] .app-navbar-bg,
  [data-theme*='pale-pink'] .highlighted-block {
    @apply bg-rose-200;
  }

  [data-theme*='pale-pink'] .doc-bg {
    @apply bg-rose-200;
  }

  [data-theme*='pale-pink'] .separator {
    @apply border-rose-300;
  }
  [data-theme*='pale-pink'] .panel {
    @apply rounded-lg bg-rose-100;
  }
  [data-theme*='pale-pink'] .dropdown-panel {
    @apply bg-rose-100 border-rose-300;
  }
  [data-theme*='pale-pink'] .nav-item {
    @apply bg-rose-950 text-rose-200;
  }
  [data-theme*='pale-pink'] .account-btn {
    @apply bg-rose-950 text-rose-200;
  }
  [data-theme*='pale-pink'] .modal-bg {
    @apply bg-rose-50;
  }
  [data-theme*='pale-pink'] .bottom-sheet-scrollbar {
    @apply mr-1.5 scrollbar scrollbar-thumb-rose-300 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='pale-pink'] .doc-scrollbar {
    @apply scrollbar scrollbar-thumb-rose-300 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='pale-pink'] .upload-area {
    @apply border-dashed border-rose-950 hover:border-rose-800 focus:border-0 focus:ring-2 focus:ring-blue-700;
  }
  [data-theme*='pale-pink'] .upload-area-drag {
    @apply border-rose-800;
  }
  [data-theme*='pale-pink'] .default-progress-bar {
    @apply border-rose-700 bg-rose-50;
  }
  [data-theme*='pale-pink'] .default-progress-bar-upload {
    @apply border-rose-700 bg-rose-300;
  }
  [data-theme*='pale-pink'] .default-progress-bar-icon {
    @apply text-rose-950;
  }
  [data-theme*='pale-pink'] .message-bg-positive {
    @apply bg-green-400 text-green-950;
  }
  [data-theme*='pale-pink'] .message-bg-negative {
    @apply bg-rose-300 text-red-950;
  }
  [data-theme*='pale-pink'] .message-bg-info {
    @apply bg-sky-300 text-sky-950;
  }
  [data-theme*='pale-pink'] .card {
    @apply bg-rose-50 border-rose-500
    hover:bg-rose-100
    active:bg-rose-300 active:top-[3px]
    transition-all duration-100;
  }
  [data-theme*='pale-pink'] .card .card-link {
    @apply before:focus-visible:ring-blue-700 before:focus-visible:border-rose-200;
  }
  [data-theme*='pale-pink'] .card-content {
    @apply text-rose-950;
  }
  [data-theme*='pale-pink'] .card-content-subdued {
    @apply text-rose-800;
  }
  [data-theme*='pale-pink'] .card-focus {
    @apply focus-visible:after:ring-blue-700;
  }
  [data-theme*='pale-pink'] .card-icon-container {
    @apply bg-rose-950;
  }
  [data-theme*='pale-pink'] .card-icon-body {
    @apply text-rose-50;
  }
  /* Document list item disabled style, inc. children styling */
  [data-theme*='pale-pink'] .card.card-disabled {
    @apply border-rose-300 bg-rose-200 hover:border-rose-300 hover:bg-rose-200 active:top-0;
  }
  [data-theme*='pale-pink'] .card-disabled .card-icon-container {
    @apply bg-rose-900;
  }
  [data-theme*='pale-pink'] .card-disabled .card-icon-body {
    @apply text-rose-200;
  }
  [data-theme*='pale-pink'] .card-disabled .card-content,
  [data-theme*='pale-pink'] .card-disabled .card-content-subdued {
    @apply text-rose-900;
  }
  [data-theme*='pale-pink'] .mobile-step-indicator {
    @apply bg-rose-950 text-rose-50;
  }
  [data-theme*='pale-pink'] .mobile-step-indicator-inactive {
    @apply bg-rose-600 text-rose-50;
  }
  /* ONBOARDING DESKTOP STEP: default/current */
  [data-theme*='pale-pink'] .desktop-onboarding-step-link .inner-outline {
    @apply fill-rose-950 stroke-rose-950;
  }
  [data-theme*='pale-pink']
    .desktop-onboarding-step-link:focus-visible
    .outer-outline {
    @apply stroke-blue-700;
  }
  /* ONBOARDING DESKTOP STEP: disabled  */
  [data-theme*='pale-pink']
    .desktop-onboarding-step-link.disabled
    .inner-outline {
    @apply fill-none stroke-rose-400;
  }
  /* ONBOARDING DESKTOP STEP: completed  */
  [data-theme*='pale-pink']
    .desktop-onboarding-step-link.completed
    .inner-outline {
    @apply fill-none stroke-rose-950;
  }
  [data-theme*='pale-pink']
    .desktop-onboarding-step-link.completed:hover
    .inner-outline {
    @apply stroke-rose-700;
  }
  [data-theme*='pale-pink']
    .desktop-onboarding-step-link.completed:active
    .inner-outline {
    @apply stroke-rose-500;
  }
  [data-theme*='pale-pink'] .nav-from-gradient {
    @apply from-rose-50;
  }
  [data-theme*='pale-pink'] .verify-spinner {
    @apply text-rose-950;
  }
  [data-theme*='pale-pink'] .toggle {
    @apply border-rose-700 bg-pink-950 focus:outline-2 focus:outline-blue-700;
    --tglbg: #fce7f3;
  }
  [data-theme*='pale-pink'] .toggle:checked {
    @apply border-rose-700 bg-pink-100;
    --tglbg: #500724;
  }
  [data-theme*='pale-pink'] .focus-outer {
    @apply outline-none focus-visible:border-2 focus-visible:border-blue-700;
  }
  [data-theme*='pale-pink'] .definition-part-of-speech {
    @apply bg-rose-100 text-rose-950;
  }
  [data-theme*='pale-pink'] .definition-example {
    @apply bg-rose-200 text-rose-700;
  }
  [data-theme*='pale-pink'] .progress-bar-rail {
    @apply bg-rose-300;
  }
  [data-theme*='pale-pink'] .progress-bar-rail::-webkit-progress-value {
    @apply bg-rose-950;
  }
  [data-theme*='pale-pink'] .accordion:hover .accordion-title {
    @apply bg-rose-300;
  }
  [data-theme*='pale-pink'] .accordion-title-active {
    @apply bg-rose-400;
  }
}
@layer utilities {
  [data-theme*='pale-pink'] .app-bg {
    @apply bg-rose-200;
  }

  [data-theme*='pale-pink'] .logo-fill-bg {
    @apply bg-rose-100 fill-rose-100;
  }

  [data-theme*='pale-pink'] .app-bg-subdued {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .theme-border {
    @apply border border-rose-300;
  }
  [data-theme*='pale-pink'] .theme-border-subdued {
    @apply border border-rose-200;
  }
  [data-theme*='pale-pink'] .theme-border-t {
    @apply border-t-rose-300;
  }
  [data-theme*='pale-pink'] .theme-border-l {
    @apply border-l border-l-rose-300;
  }
  [data-theme*='pale-pink'] .theme-warning {
    @apply text-red-500;
  }
  [data-theme*='pale-pink'] .theme-delete {
    @apply text-red-950;
  }

  [data-theme*='pale-pink'] .opposite-bg {
    @apply bg-rose-950;
  }
  [data-theme*='pale-pink'] .opposite-border,
  [data-theme*='pale-pink'] .reading-marker-border {
    @apply border-rose-950;
  }
  [data-theme*='pale-pink'] .onboarding-document-preview-container,
  [data-theme*='pale-pink'] .expand-preview-btn-container > button {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .onboarding-document-preview {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .onboarding-doc-pop-out {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .selected-btn {
    @apply border-4 !border-rose-900;
  }
  [data-theme*='pale-pink'] .svg-stroke {
    @apply stroke-rose-100;
  }
  [data-theme*='pale-pink'] .doc-text-default {
    @apply text-rose-950;
  }
  [data-theme*='pale-pink'] .doc-text-inactive {
    @apply text-rose-600;
  }

  [data-theme*='pale-pink'] .doc-text-subdued {
    @apply text-rose-700;
  }
  [data-theme*='pale-pink'] .doc-text-inverted {
    @apply text-rose-50;
  }
  [data-theme*='pale-pink'] .theme-selected {
    @apply ring-blue-600;
  }
  [data-theme*='pale-pink'] .selected {
    @apply ring-rose-950 ring-offset-2 ring-offset-rose-100;
  }
  [data-theme*='pale-pink'] .bg-default {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .svg-default {
    @apply fill-rose-950;
  }
  [data-theme*='pale-pink'] .doc-link-default {
    @apply bg-rose-200 hover:bg-rose-300 focus-visible:bg-rose-950 active:bg-rose-950 visited:bg-rose-100 text-rose-950 focus-visible:text-rose-50 active:text-rose-50 visited:text-rose-700 rounded-sm border-b border-rose-600 hover:border-rose-950 focus-visible:border-rose-950 active:border-rose-950 visited:border-rose-950 font-semibold outline-none focus-visible:ring ring-blue-700;
  }

  /* Shadows */
  [data-theme*='pale-pink'] .shadow-sm-d {
    box-shadow: var(--shadow-sm-d) rgba(76, 5, 25, 0.05);
  }
  [data-theme*='pale-pink'] .shadow-d {
    box-shadow: var(--shadow-d) var(--shadow-color-base-pale-pink),
      var(--shadow-d-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-md-d {
    box-shadow: var(--shadow-md-d) var(--shadow-color-base-pale-pink),
      var(--shadow-md-d-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-lg-d {
    box-shadow: var(--shadow-lg-d) var(--shadow-color-base-pale-pink),
      var(--shadow-lg-d-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-xl-d {
    box-shadow: var(--shadow-xl-d) var(--shadow-color-base-pale-pink),
      var(--shadow-xl-d-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-2xl-d {
    box-shadow: var(--shadow-2xl-d) rgba(76, 5, 25, 0.25);
  }
  [data-theme*='pale-pink'] .shadow-sm-u {
    box-shadow: var(--shadow-sm-u) rgba(76, 5, 25, 0.05);
  }
  [data-theme*='pale-pink'] .shadow-u {
    box-shadow: var(--shadow-u) var(--shadow-color-base-pale-pink),
      var(--shadow-u-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-md-u {
    box-shadow: var(--shadow-md-u) var(--shadow-color-base-pale-pink),
      var(--shadow-md-u-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-lg-u {
    box-shadow: var(--shadow-lg-u) var(--shadow-color-base-pale-pink),
      var(--shadow-lg-u-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-xl-u {
    box-shadow: var(--shadow-xl-u) var(--shadow-color-base-pale-pink),
      var(--shadow-xl-u-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-2xl-u {
    box-shadow: var(--shadow-2xl-u) rgba(76, 5, 25, 0.25);
  }
  [data-theme*='pale-pink'] .shadow-l {
    box-shadow: var(--shadow-l) var(--shadow-color-base-pale-pink),
      var(--shadow-l-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-md-l {
    box-shadow: var(--shadow-md-l) var(--shadow-color-base-pale-pink),
      var(--shadow-md-l-2) var(--shadow-color-base-pale-pink);
  }
  [data-theme*='pale-pink'] .shadow-solid-d {
    box-shadow: var(--shadow-solid-d) #fda4af; /*rose-300*/
  }
  [data-theme*='pale-pink'] .shadow-solid-md-d {
    box-shadow: var(--shadow-solid-md-d) #fda4af; /*rose-300*/
  }
  /* Table styling */

  [data-theme*='pale-pink'] .table-header-bg {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .table-column-bg {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .table-cell-border {
    @apply border-rose-300;
  }
  /* Used on elements imitating focus ring - tabs nav on mobile */
  [data-theme*='pale-pink'] .focus-border {
    @apply border-blue-700;
  }
  [data-theme*='pale-pink'] .text-disabled {
    @apply text-rose-900;
  }
  [data-theme*='pale-pink'] .app-bg-tertiary {
    @apply bg-rose-300;
  }
  [data-theme*='pale-pink'] .break-line {
    @apply bg-rose-300 opacity-25;
  }
  [data-theme*='pale-pink'] .bg-gradient {
    @apply bg-gradient-to-t from-rose-200;
  }
  [data-theme*='pale-pink'] .panel-bg {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .drawer-bg {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .bottom-sheet-top-nav {
    @apply bg-rose-200 border-b border-rose-300;
  }
  /* Separate classes needed for the select-dropdown due to focus style beng set dynamically via internal props */
  [data-theme*='pale-pink'] .select-ring {
    @apply ring-blue-700;
  }
  [data-theme*='pale-pink'] .select-option-focus {
    @apply bg-rose-300;
  }
  [data-theme*='pale-pink'] .focus-outline {
    @apply outline outline-[3px] outline-blue-700;
  }
  [data-theme*='pale-pink'] .slider-text {
    @apply text-sm font-semibold text-rose-950;
  }

  [data-theme*='pale-pink'] .slider-value {
    @apply mt-1 flex justify-between text-xs text-rose-950;
  }
  [data-theme*='pale-pink'] .slider-dot {
    @apply bg-rose-800;
  }
  [data-theme*='pale-pink'] .slider-dot-active {
    @apply border border-rose-950 bg-rose-950;
  }
  [data-theme*='pale-pink'] .slider-handle {
    @apply border-rose-950 bg-rose-950 hover:bg-rose-950;
  }
  [data-theme*='pale-pink'] .slider-handle-dragging {
    @apply border-none bg-rose-950 ring-4 ring-rose-950/25;
  }
  [data-theme*='pale-pink'] .slider-handle-focus {
    @apply bg-rose-950 ring-4 ring-rose-950/25;
  }
  [data-theme*='pale-pink'] .slider-rail {
    @apply bg-rose-300;
  }
  [data-theme*='pale-pink'] .slider-track {
    @apply bg-rose-950;
  }
  [data-theme*='pale-pink'] .tool-tip {
    @apply bg-rose-950 text-rose-50;
  }
  [data-theme*='pale-pink'] .controls-container {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .text-highlight-word-definition {
    @apply bg-blue-700 text-rose-50;
  }
  [data-theme*='pale-pink'] .active-tts-paragraph {
    @apply bg-rose-950 text-rose-50 px-2 pt-2 rounded-xl;
  }
  [data-theme*='pale-pink'] .tts-progress-bar {
    @apply w-full [&::-webkit-progress-bar]:bg-rose-400
    [&::-webkit-progress-value]:bg-rose-950;
  }
  [data-theme*='pale-pink'] .overview-gradient-border {
    @apply border border-rose-600 rounded-2xl p-[2px];
  }
  [data-theme*='pale-pink'] .ai-gradient-border {
    @apply border-2 border-rose-600 rounded-md;
  }
  [data-theme*='pale-pink'] .quick-summary-container {
    @apply h-full w-full bg-rose-100 rounded-2xl px-6 py-5;
  }
  [data-theme*='pale-pink'] .ai-notice-container {
    @apply bg-transparent;
  }
  [data-theme*='pale-pink'] .ai-underline {
    @apply underline decoration-rose-600;
  }
  [data-theme*='pale-pink'] .reading-time {
    @apply bg-rose-300;
  }
  [data-theme*='pale-pink'] .welcome-banner button {
    @apply border-rose-100 bg-rose-100
      hover:bg-rose-50 
      focus-visible:outline-blue-700 focus-visible:border-rose-950
      active:bg-rose-300;
  }
  [data-theme*='pale-pink'] .document-wrapper {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .document-viewer-background {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .closed-right-drawer {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .summary-body {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .reading-environment-wrapper {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .toc-parent-text {
    @apply text-rose-950 font-bold;
  }
  [data-theme*='pale-pink'] .toc-child-text {
    @apply text-rose-950;
  }
  [data-theme*='pale-pink'] .toc-items-text {
    @apply text-rose-800;
  }
  [data-theme*='pale-pink'] .toc-parent-line {
    @apply w-8 h-0.5 bg-rose-600;
  }
  [data-theme*='pale-pink'] .toc-child-line {
    @apply w-6 h-0.5 bg-rose-600;
  }
  [data-theme*='pale-pink'] .toc-grandcchild-line {
    @apply w-4 h-0.5 bg-rose-600;
  }
  [data-theme*='pale-pink'] .toc-active-element {
    @apply bg-rose-950;
  }
  [data-theme*='pale-pink'] .nav-border {
    @apply border-b-2 border-rose-300;
  }
  [data-theme*='pale-pink'] .nav-bg {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .nav-underline {
    @apply bg-rose-500;
  }
  [data-theme*='pale-pink'] .nav-content-styles {
    @apply bg-rose-200 border-t-2 border-rose-300 p-2 lg:bg-rose-100 lg:relative lg:border-none;
  }
  [data-theme*='pale-pink'] .toc-background {
    @apply bg-rose-200/50 backdrop-blur-sm z-50 rounded-tr-lg rounded-br-lg;
  }
  [data-theme*='pale-pink'] .dropdown-nav {
    @apply bg-rose-100 border-rose-300;
  }
  [data-theme*='pale-pink'] .user-svg-icon {
    fill: #4c0519;
  }
  [data-theme*='pale-pink'] .dashboard-bg {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .documents-panel {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .mobile-dashboard-bg {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .mobile-welcome-panel {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .mobile-document-body {
    @apply bg-rose-200;
  }
  [data-theme*='pale-pink'] .mobile-extraction-message {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .small-tailo-logo {
    @apply hover:text-slate-800 focus:ring-blue-700 
    focus:outline-2 focus:ring-2 focus:outline-blue-700 focus:border-rose-950
    active:border-rose-950 active:ring-2 active:ring-blue-700
    active:outline-2 active:outline-blue-700 active:text-slate-700 active:text-rose-600;
  }

  /* The left border for the summary page expand/collapse button, to separate the text link from the button visually */
  [data-theme*='pale-pink'] .summary-expand-btn {
    @apply border-l-rose-300;
  }

  [data-theme*='pale-pink'] .active-toc-parent-text {
    @apply text-slate-950
    hover:bg-rose-300 hover:text-rose-950
    focus:border-blue-700 focus:bg-rose-950 focus:text-rose-50
    active:bg-rose-400 active:text-rose-950;
  }
  [data-theme*='pale-pink'] .inactive-toc-parent-text {
    @apply text-slate-950 decoration-slate-950
    hover:bg-rose-300 hover:text-rose-950
    focus:border-blue-700 focus:bg-rose-950 focus:text-rose-50 focus:decoration-rose-50
    active:bg-rose-400 active:text-rose-950 active:decoration-rose-950;
  }
  [data-theme*='pale-pink'] .inactive-toc-parent-text + ul a {
    @apply text-rose-950;
  }
  [data-theme*='pale-pink'] .active-toc-parent-text + ul a {
    @apply text-rose-950;
  }
  [data-theme*='pale-pink'] .tailo-icon-button-active {
    @apply bg-rose-400;
  }
  [data-theme*='pale-pink'] .search-highlight {
    @apply bg-yellow-400 text-rose-950;
  }
  [data-theme*='pale-pink'] .themed-spinner {
    @apply text-rose-950;
  }
  [data-theme*='pale-pink'] .result-item-card {
    @apply bg-rose-200 lg:bg-rose-50 border-2 border-transparent hover:bg-rose-300
    active:bg-rose-400 focus-visible:border-blue-500;
  }
  [data-theme*='pale-pink'] .explain-this-dropdown {
    @apply bg-rose-300;
  }
  [data-theme*='pale-pink'] .loading-response {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .divider {
    @apply bg-rose-300;
  }
  [data-theme*='pale-pink'] .result-bg {
    @apply bg-rose-100;
  }
  [data-theme*='pale-pink'] .result-text {
    @apply text-rose-800;
  }
  [data-theme*='pale-pink'] .feedback-faces {
    @apply stroke-rose-950;
  }
  [data-theme*='pale-pink'] .feedback-face-card {
    @apply border-2 border-rose-500 hover:border-rose-700;
  }
  [data-theme*='pale-pink'] .feedback-face-card-active {
    @apply ring-2 ring-blue-500 border-2 border-rose-950;
  }
}
