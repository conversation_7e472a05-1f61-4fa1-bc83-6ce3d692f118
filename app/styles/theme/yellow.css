/* The styling won't be included in your compiled CSS unless you actually use it */
/*  *= checks if attribute value contains this value; This way we can set the attribute value to  e.g."drk medium" to accommodate for fonts sizing as well:
   [data-theme*='medium'] .btn-primary {
    @apply text-2xl;
  }
   */

:root {
  --scrollbar-track: transparent;
  --scrollbar-thumb: @apply black;
}

@layer base {
}

@layer components {
  [data-theme*='yellow'] .btn {
    @apply focus:border-yellow-400 focus:ring-blue-700 focus:hover:border-yellow-400;
  }
  [data-theme*='yellow'] .btn-primary {
    @apply bg-black text-yellow-400 
    hover:border-transparent hover:bg-stone-900 
    active:bg-stone-700 
    disabled:bg-yellow-600 disabled:text-yellow-950 
    data-[disabled=true]:bg-yellow-500 data-[disabled=true]:text-yellow-950;
  }

  [data-theme*='yellow'] .btn-secondary {
    @apply border-2 border-black text-black 
    hover:border-yellow-800 
    hover:active:border-black 
    active:border-black 
    disabled:border-yellow-900 disabled:text-yellow-900
    data-[disabled=true]:border-yellow-900 data-[disabled=true]:text-yellow-900
    disabled:active:border-yellow-900;
  }

  [data-theme*='yellow'] .btn-tertiary {
    @apply text-black 
    hover:bg-yellow-300 
    hover:disabled:bg-transparent
    active:bg-yellow-600 active:text-yellow-950 
    disabled:text-yellow-900 
    data-[disabled=true]:text-yellow-900;
  }

  [data-theme*='yellow'] .btn-destructive {
    @apply bg-red-600 text-white 
    hover:bg-red-700
    active:bg-red-500 
    disabled:bg-red-100 disabled:text-red-700 
    data-[disabled=true]:bg-red-100 data-[disabled=true]:text-red-700;
  }

  [data-theme*='yellow'] .btn-positive {
    @apply bg-green-600 text-white 
    hover:bg-green-700 
    active:bg-green-500 
    disabled:bg-green-100 disabled:text-green-700 
    data-[disabled=true]:bg-green-100 data-[disabled=true]:text-green-700;
  }

  [data-theme*='yellow'] .app-navbar-bg {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .separator {
    @apply border-black;
  }
  [data-theme*='yellow'] .panel {
    @apply rounded-lg bg-yellow-400;
  }
  [data-theme*='yellow'] .dropdown-panel {
    @apply bg-yellow-400 border-black;
  }

  [data-theme*='yellow'] .card {
    @apply bg-yellow-300 border-black
    hover:bg-yellow-500
    active:top-[3px]
    transition-all duration-100;
  }
  [data-theme*='yellow'] .card .card-link {
    @apply before:focus-visible:ring-blue-700 before:focus-visible:border-yellow-400
    transition-all duration-100;
  }
  [data-theme*='yellow'] .card .card-link {
    @apply before:focus-visible:ring-blue-700 before:focus-visible:border-yellow-400;
  }
  [data-theme*='yellow'] .card-content {
    @apply text-black;
  }
  [data-theme*='yellow'] .card-content-subdued {
    @apply text-zinc-800;
  }
  [data-theme*='yellow'] .card-focus {
    @apply focus:after:ring-blue-700;
  }
  [data-theme*='yellow'] .card-icon-container {
    @apply bg-black;
  }
  [data-theme*='yellow'] .card-icon-body {
    @apply text-yellow-400;
  }
  /* Document list item disabled style, inc. children styling */
  [data-theme*='yellow'] .card.card-disabled {
    @apply border-yellow-500 bg-yellow-400 hover:border-yellow-500 hover:bg-yellow-400 active:top-0;
  }
  [data-theme*='yellow'] .card-disabled .card-icon-container {
    @apply bg-yellow-900;
  }
  [data-theme*='yellow'] .card-disabled .card-icon-body {
    @apply text-yellow-400;
  }
  [data-theme*='yellow'] .card-disabled .card-content,
  [data-theme*='yellow'] .card-disabled .card-content-subdued {
    @apply text-yellow-900;
  }
  [data-theme*='yellow'] .onboarding-page {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .selected-btn {
    @apply border-4 !border-black;
  }
  [data-theme*='yellow'] .selected-file-panel {
    @apply border-yellow-400 bg-yellow-400;
  }
  [data-theme*='yellow'] .account-btn {
    @apply border border-black bg-black text-yellow-400 focus:shadow active:border-stone-700 active:bg-stone-700;
  }
  [data-theme*='yellow'] .modal-bg {
    @apply border border-black bg-yellow-400 text-black;
  }
  [data-theme*='yellow'] .modal-overlay {
    background-color: rgba(0, 0, 0, 0.75);
  }
  [data-theme*='yellow'] .custom-srollbar {
    @apply scrollbar scrollbar-track-yellow-400 scrollbar-thumb-yellow-400;
  }
  [data-theme*='yellow'] .bottom-sheet-scrollbar {
    @apply mr-1.5 scrollbar scrollbar-thumb-black scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='yellow'] .doc-scrollbar {
    @apply scrollbar scrollbar-thumb-black scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='yellow'] .pill-positive {
    @apply bg-green-500 text-black;
  }
  [data-theme*='yellow'] .pill-negative {
    @apply bg-red-400 text-black;
  }
  [data-theme*='yellow'] .pill-info {
    @apply bg-sky-400 text-black;
  }
  [data-theme*='yellow'] .pill-neutral {
    @apply bg-black text-yellow-400;
  }

  [data-theme*='yellow'] .dashboard-placeholder {
    @apply rounded-lg border border-black;
  }
  [data-theme*='yellow'] .disabled-label-text {
    @apply text-black;
  }
  [data-theme*='yellow'] .message-bg-positive {
    @apply bg-green-200 text-green-950;
  }
  [data-theme*='yellow'] .message-bg-negative {
    @apply bg-rose-200 text-red-950;
  }
  [data-theme*='yellow'] .message-bg-info {
    @apply bg-blue-100 text-blue-950;
  }
  [data-theme*='yellow'] .input {
    @apply border-black text-black 
    placeholder:text-yellow-950
    hover:border-yellow-800
    hover:active:border-black 
    active:border-black
    disabled:text-black disabled:border-yellow-700 disabled:bg-yellow-600
    disabled:active:border-yellow-700    
    checked:border-black checked:bg-black checked:text-transparent 
    focus:border-black focus:ring-blue-700;
  }
  /* Native HTML <select> component options list bg */
  [data-theme*='yellow'] .input optgroup,
  [data-theme*='yellow'] .input option {
    @apply bg-yellow-200;
  }
  /* Custom <select> component options list bg */
  [data-theme*='yellow'] .form-dropdown-select {
    @apply border-yellow-200 bg-yellow-200 text-black;
  }
  [data-theme*='yellow'] .input:is(:-webkit-autofill, :autofill) {
    @apply ring-black;
  }
  [data-theme*='yellow'] .select-option-focus {
    @apply bg-black text-yellow-400;
  }
  [data-theme*='yellow'] .form-input-success {
    @apply border-tailo-green-600;
  }
  [data-theme*='yellow'] .form-error {
    @apply bg-red-800;
  }
  [data-theme*='yellow'] .form-error-border {
    @apply border-red-800;
  }

  [data-theme*='yellow'] .context-menu {
    @apply bg-yellow-400 border-black;
  }
  [data-theme*='yellow'] .context-menu-options {
    @apply divide-black;
  }

  /* Checkbox Toggle Control */
  [data-theme*='yellow'] input[type="checkbox"] ~ .toggle-control {
    @apply bg-yellow-400 border-black after:bg-black after:border-yellow-400;
  }
  [data-theme*='yellow'] input[type="checkbox"]:checked ~ .toggle-control {
    @apply bg-black after:bg-yellow-400;
  }    

  [data-theme*='yellow'] .nav-item {
    @apply bg-black text-yellow-400;
  }
  [data-theme*='yellow'] .upload-area {
    @apply border-dashed border-black outline-none hover:border-yellow-800 focus-visible:border-none focus-visible:ring-4 focus-visible:ring-blue-700;
  }
  [data-theme*='yellow'] .upload-area-drag {
    @apply border-yellow-800;
  }
  [data-theme*='yellow'] .default-progress-bar {
    @apply border-black;
  }
  [data-theme*='yellow'] .default-progress-bar-upload {
    @apply border-black bg-yellow-200;
  }
  [data-theme*='yellow'] .default-progress-bar-icon {
    @apply text-black;
  }
  [data-theme*='yellow'] .mobile-step-indicator {
    @apply bg-black text-yellow-400;
  }
  [data-theme*='yellow'] .mobile-step-indicator-inactive {
    @apply bg-yellow-700 text-yellow-400;
  }
  /* ONBOARDING DESKTOP STEP: default/current */
  [data-theme*='yellow'] .desktop-onboarding-step-link .inner-outline {
    @apply fill-black stroke-black;
  }
  [data-theme*='yellow']
    .desktop-onboarding-step-link:focus-visible
    .outer-outline {
    @apply stroke-blue-700;
  }
  /* ONBOARDING DESKTOP STEP: disabled  */
  [data-theme*='yellow'] .desktop-onboarding-step-link.disabled .inner-outline {
    @apply fill-none stroke-yellow-700;
  }
  /* ONBOARDING DESKTOP STEP: completed  */
  [data-theme*='yellow']
    .desktop-onboarding-step-link.completed
    .inner-outline {
    @apply fill-none stroke-black;
  }
  [data-theme*='yellow']
    .desktop-onboarding-step-link.completed:hover
    .inner-outline {
    @apply stroke-yellow-900;
  }
  [data-theme*='yellow']
    .desktop-onboarding-step-link.completed:active
    .inner-outline {
    @apply stroke-yellow-600;
  }
  [data-theme*='yellow'] .nav-from-gradient {
    @apply from-yellow-400;
  }
  [data-theme*='yellow'] .verify-spinner {
    @apply text-black;
  }
  [data-theme*='yellow'] .toggle {
    @apply border-black bg-black focus:outline-2 focus:outline-blue-700;
    --tglbg: #fde047;
  }
  [data-theme*='yellow'] .toggle:checked {
    @apply border-black bg-yellow-300;
    --tglbg: black;
  }
  [data-theme*='yellow'] .focus-outer {
    @apply outline-none focus-visible:border-2 focus-visible:border-blue-700;
  }
  [data-theme*='yellow'] .definition-part-of-speech {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .definition-example {
    @apply bg-yellow-400 text-zinc-800;
  }
  [data-theme*='yellow'] .progress-bar-rail {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .progress-bar-rail::-webkit-progress-value {
    @apply bg-black;
  }
  [data-theme*='yellow'] .accordion:hover .accordion-title {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .accordion-title-active {
    @apply bg-yellow-300;
  }
}

@layer utilities {
  [data-theme*='yellow'] .app-bg {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .logo-fill-bg {
    @apply bg-yellow-400 fill-yellow-400;
  }
  [data-theme*='yellow'] .app-bg-subdued,
  [data-theme*='yellow'] .highlighted-block {
    @apply bg-yellow-300;
  }
  [data-theme*='yellow'] .doc-bg {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .theme-border,
  [data-theme*='yellow'] .theme-border-subdued {
    @apply border border-black;
  }
  [data-theme*='yellow'] .theme-border-t {
    @apply border-t-black;
  }
  [data-theme*='yellow'] .theme-border-l {
    @apply border-l border-l-black;
  }
  [data-theme*='yellow'] .theme-warning {
    @apply text-red-500;
  } /* Used for delete text in dropdowns */
  [data-theme*='yellow'] .theme-delete {
    @apply text-black;
  }

  [data-theme*='yellow'] .icon-bg {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .opposite-bg {
    @apply bg-black;
  }
  [data-theme*='yellow'] .opposite-border,
  [data-theme*='yellow'] .reading-marker-border {
    @apply border-black;
  }
  [data-theme*='yellow'] .onboarding-document-preview-container,
  [data-theme*='yellow'] .expand-preview-btn-container > button {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .onboarding-document-preview {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .onboarding-doc-pop-out {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .doc-text-default {
    @apply text-black;
  }
  [data-theme*='yellow'] .doc-text-inactive {
    @apply text-yellow-700;
  }

  [data-theme*='yellow'] .doc-text-subdued {
    @apply text-zinc-800;
  }
  [data-theme*='yellow'] .doc-text-inverted {
    @apply text-yellow-400;
  }
  [data-theme*='yellow'] .default-progress-bar {
    @apply border-zinc-800 bg-yellow-400;
  }
  [data-theme*='yellow'] .uploadingBar {
    @apply bg-slate-200 bg-opacity-30;
  }
  [data-theme*='yellow'] .bg-default {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .svg-default {
    @apply fill-black;
  }
  [data-theme*='yellow'] .doc-link-default {
    @apply bg-yellow-500 hover:bg-black focus-visible:bg-black active:bg-black visited:bg-yellow-400 text-black hover:text-yellow-400 focus-visible:text-yellow-400 active:text-yellow-400 visited:text-amber-950 rounded-sm border-b border-black font-semibold outline-none focus-visible:ring ring-blue-700;
  }

  /* Applied only when theme is in focus */
  [data-theme*='yellow'] .theme-selected {
    @apply ring-sky-500;
  }

  /* Applied when current theme is in use */
  [data-theme*='yellow'] .selected {
    @apply ring-black ring-offset-2 ring-offset-yellow-400;
  }
  /* Table styling */
  [data-theme*='yellow'] .t-border {
    @apply border-yellow-800;
  }
  [data-theme*='yellow'] .th-bg {
    @apply bg-yellow-300;
  }
  [data-theme*='yellow'] .document-card-hover {
    @apply bg-zinc-800;
  }
  [data-theme*='yellow'] .svg-stroke {
    @apply stroke-yellow-400;
  }
  /* Shadows */
  [data-theme*='yellow'] .shadow-sm-d {
    box-shadow: var(--shadow-sm-d) var(--shadow-color-secondary);
  }
  [data-theme*='yellow'] .shadow-d {
    box-shadow: var(--shadow-d) var(--shadow-color-base),
      var(--shadow-d-2) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-md-d {
    box-shadow: var(--shadow-md-d) var(--shadow-color-base),
      var(--shadow-lg-d-2) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-lg-d {
    box-shadow: var(--shadow-lg-d) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-xl-d {
    box-shadow: var(--shadow-xl-d) var(--shadow-color-base),
      var(--shadow-xl-d-2) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-2xl-d {
    box-shadow: var(--shadow-2xl-d) rgba(2, 6, 23, 0.25);
  }
  [data-theme*='yellow'] .shadow-sm-u {
    box-shadow: var(--shadow-sm-u) var(--shadow-color-secondary);
  }
  [data-theme*='yellow'] .shadow-u {
    box-shadow: var(--shadow-u) var(--shadow-color-base),
      var(--shadow-u-2) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-md-u {
    box-shadow: var(--shadow-md-u) var(--shadow-color-base),
      var(--shadow-md-u-2) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-lg-u {
    box-shadow: var(--shadow-lg-u) var(--shadow-color-base),
      var(--shadow-lg-u-2) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-xl-u {
    box-shadow: var(--shadow-xl-u) var(--shadow-color-base),
      var(--shadow-xl-u-2) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-2xl-u {
    box-shadow: var(--shadow-2xl-u) rgba(2, 6, 23, 0.25);
  }
  [data-theme*='yellow'] .shadow-l {
    box-shadow: var(--shadow-l) var(--shadow-color-base),
      var(--shadow-l-2) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-md-l {
    box-shadow: var(--shadow-md-l) var(--shadow-color-base),
      var(--shadow-md-l-2) var(--shadow-color-base);
  }
  [data-theme*='yellow'] .shadow-solid-d {
    box-shadow: var(--shadow-solid-d) black;
  }
  [data-theme*='yellow'] .shadow-solid-md-d {
    box-shadow: var(--shadow-solid-md-d) black;
  }
  /* Table styling */

  [data-theme*='yellow'] .table-header-bg {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .table-column-bg {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .table-cell-border {
    @apply border-black;
  }
  /* Used on elements imitating focus ring - tabs nav on mobile */
  [data-theme*='yellow'] .focus-border {
    @apply border-blue-700;
  }
  [data-theme*='yellow'] .text-disabled {
    @apply text-yellow-900;
  }
  [data-theme*='yellow'] .app-bg-tertiary {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .break-line {
    @apply bg-black opacity-10;
  }
  [data-theme*='yellow'] .bg-gradient {
    @apply bg-gradient-to-t from-yellow-500;
  }
  [data-theme*='yellow'] .panel-bg {
    @apply bg-yellow-400;
  }
  /* Separate class needed for the select-dropdown due to focus style beng set dynamically via internal props */
  [data-theme*='yellow'] .select-ring {
    @apply ring-blue-700;
  }
  [data-theme*='yellow'] .focus-outline {
    @apply outline outline-[3px] outline-blue-700;
  }
  [data-theme*='yellow'] .slider-text {
    @apply text-sm font-semibold text-black;
  }

  [data-theme*='yellow'] .slider-value {
    @apply mt-1 flex justify-between text-xs text-black;
  }
  [data-theme*='yellow'] .slider-dot {
    @apply bg-black;
  }
  [data-theme*='yellow'] .slider-dot-active {
    @apply border border-black bg-black;
  }
  [data-theme*='yellow'] .slider-handle {
    @apply border-black bg-black hover:bg-black;
  }
  [data-theme*='yellow'] .slider-handle-dragging {
    @apply border-none bg-black ring-4 ring-black/25;
  }
  [data-theme*='yellow'] .slider-handle-focus {
    @apply bg-black ring-4 ring-black/25;
  }
  [data-theme*='yellow'] .slider-rail {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .slider-track {
    @apply bg-black;
  }
  [data-theme*='yellow'] .tool-tip {
    @apply bg-black text-yellow-400;
  }
  [data-theme*='yellow'] .controls-container {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .text-highlight-word-definition {
    @apply bg-black text-yellow-400;
  }
  [data-theme*='yellow'] .active-tts-paragraph {
    @apply bg-black text-yellow-400 px-2 pt-2 rounded-xl;
  }
  [data-theme*='yellow'] .tts-progress-bar {
    @apply w-full [&::-webkit-progress-bar]:bg-yellow-600
    [&::-webkit-progress-value]:bg-black;
  }
  [data-theme*='yellow'] .overview-gradient-border {
    @apply border border-black rounded-2xl p-[2px];
  }
  [data-theme*='yellow'] .ai-gradient-border {
    @apply border-2 border-black rounded-md;
  }
  [data-theme*='yellow'] .quick-summary-container {
    @apply h-full w-full bg-yellow-400 rounded-2xl px-6 py-5;
  }
  [data-theme*='yellow'] .ai-notice-container {
    @apply bg-transparent;
  }
  [data-theme*='yellow'] .ai-underline {
    @apply underline decoration-black;
  }
  [data-theme*='yellow'] .reading-time {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .welcome-banner button {
    @apply border-black bg-yellow-400
      hover:bg-yellow-500 
      focus-visible:outline-blue-700
      active:bg-yellow-300;
  }
  [data-theme*='yellow'] .document-wrapper {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .document-viewer-background {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .closed-right-drawer {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .summary-body {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .reading-environment-wrapper {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .toc-parent-text {
    @apply text-black font-bold;
  }
  [data-theme*='yellow'] .toc-child-text {
    @apply text-black;
  }
  [data-theme*='yellow'] .toc-items-text {
    @apply text-amber-950;
  }
  [data-theme*='yellow'] .toc-parent-line {
    @apply w-8 h-0.5 bg-yellow-700;
  }
  [data-theme*='yellow'] .toc-child-line {
    @apply w-6 h-0.5 bg-yellow-700;
  }
  [data-theme*='yellow'] .toc-grandcchild-line {
    @apply w-4 h-0.5 bg-yellow-700;
  }
  [data-theme*='yellow'] .toc-active-element {
    @apply bg-black;
  }
  [data-theme*='yellow'] .nav-border {
    @apply border-b-2 border-black;
  }
  [data-theme*='yellow'] .nav-bg {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .nav-underline {
    @apply bg-black;
  }
  [data-theme*='yellow'] .nav-content-styles {
    @apply bg-yellow-500 border-t-2 border-black p-2 lg:bg-yellow-400 lg:relative lg:border-none;
  }
  [data-theme*='yellow'] .toc-background {
    @apply bg-yellow-500/70 backdrop-blur-sm z-50 rounded-tr-lg rounded-br-lg;
  }
  [data-theme*='yellow'] .dropdown-nav {
    @apply bg-yellow-400 border-black;
  }
  [data-theme*='yellow'] .user-svg-icon {
    fill: #000000;
  }
  [data-theme*='yellow'] .dashboard-bg {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .documents-panel {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .mobile-dashboard-bg {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .mobile-welcome-panel {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .mobile-document-body {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .mobile-extraction-message {
    @apply bg-yellow-400;
  }

  [data-theme*='yellow'] .drawer-bg {
    @apply bg-yellow-500;
  }
  [data-theme*='yellow'] .bottom-sheet-top-nav {
    @apply bg-yellow-400 border-b border-black;
  }
  [data-theme*='yellow'] .small-tailo-logo {
    @apply hover:text-slate-800 focus:ring-blue-700 
    focus:outline-2 focus:ring-2 focus:outline-blue-700 focus:border-slate-800
    active:border-slate-800 active:ring-2 active:ring-blue-700
    active:outline-2 active:outline-blue-700 active:text-stone-700;
  }

  /* The left border for the summary page expand/collapse button, to separate the text link from the button visually */
  [data-theme*='yellow'] .summary-expand-btn {
    @apply border-l-black;
  }

  [data-theme*='yellow'] .active-toc-parent-text {
    @apply text-slate-950
    hover:bg-stone-900 hover:text-yellow-400
    focus:border-blue-700 focus:bg-yellow-400 focus:text-stone-900
    active:bg-stone-700 active:text-yellow-400;
  }
  [data-theme*='yellow'] .inactive-toc-parent-text {
    @apply text-amber-950 decoration-yellow-400
    hover:bg-stone-900 hover:text-yellow-400
    focus:border-blue-700 focus:bg-yellow-400 focus:text-stone-900 focus:decoration-black
    active:bg-stone-700 active:text-yellow-400 active:decoration-yellow-400;
  }
  [data-theme*='yellow'] .inactive-toc-parent-text + ul a {
    @apply text-black;
  }
  [data-theme*='yellow'] .active-toc-parent-text + ul a {
    @apply text-black;
  }
  [data-theme*='yellow'] .tailo-icon-button-active {
    @apply bg-yellow-600;
  }
  [data-theme*='yellow'] .search-highlight {
    @apply bg-yellow-50 text-black;
  }
  [data-theme*='yellow'] .themed-spinner {
    @apply text-slate-800;
  }
  [data-theme*='yellow'] .result-item-card {
    @apply bg-yellow-400 border-2 border-transparent hover:bg-yellow-300
    active:bg-yellow-300 focus-visible:border-blue-700;
  }
  [data-theme*='yellow'] .explain-this-dropdown {
    @apply bg-yellow-400;
  }
  [data-theme*='yellow'] .loading-response {
    @apply bg-yellow-300;
  }
  [data-theme*='yellow'] .divider {
    @apply bg-black;
  }
  [data-theme*='yellow'] .result-bg {
    @apply border-2 border-black;
  }
  [data-theme*='yellow'] .result-text {
    @apply text-black;
  }
  [data-theme*='yellow'] .feedback-faces {
    @apply stroke-black;
  }
  [data-theme*='yellow'] .feedback-face-card {
    @apply border-2 border-black;
  }
  [data-theme*='yellow'] .feedback-face-card-active {
    @apply ring-2 ring-blue-500 border-2 border-black;
  }
}
