/* The styling won't be included in your compiled CSS unless you actually use it */
/*  *= checks if attribute value contains this value; This way we can set the attribute value to  e.g."drk medium" to accommodate for fonts sizing as well:
   [data-theme*='medium'] .btn-primary {
    @apply text-2xl;
  }
   */

:root {
  --scrollbar-track: transparent;
  --scrollbar-thumb: @apply slate-400;
}

@layer base {
}

@layer components {
  [data-theme*='light'] .btn {
    @apply focus:border-white focus:ring-blue-700 focus:hover:border-white;
  }
  [data-theme*='light'] .btn-primary {
    @apply bg-slate-950 text-slate-50 
    hover:border-transparent hover:bg-slate-800 
    active:bg-slate-700 
    disabled:bg-slate-300
    data-[disabled=true]:bg-slate-300;
  }

  [data-theme*='light'] .btn-secondary {
    @apply border-2 border-slate-400 text-slate-950 
    hover:border-slate-200 
    hover:active:border-slate-950
    active:border-slate-950 
    disabled:border-slate-200 disabled:text-slate-500
    data-[disabled=true]:border-slate-200 data-[disabled=true]:text-slate-500
    disabled:active:border-slate-200;
  }

  [data-theme*='light'] .btn-tertiary {
    @apply border-transparent text-slate-950 
    hover:bg-slate-200
    hover:disabled:bg-transparent
    active:bg-slate-300 active:text-slate-700 
    disabled:text-slate-500 
    data-[disabled=true]:text-slate-500;
  }

  [data-theme*='light'] .btn-destructive {
    @apply border-transparent bg-red-600 text-white 
    hover:bg-red-700
    active:bg-red-800 
    disabled:bg-red-200 disabled:text-red-800
    data-[disabled=true]:bg-red-200 data-[disabled=true]:text-red-800;
  }

  [data-theme*='light'] .btn-positive {
    @apply border-transparent bg-green-600 text-white 
    hover:bg-green-700 
    active:bg-green-800 
    disabled:bg-green-200 disabled:text-green-950 
    data-[disabled=true]:bg-green-200 data-[disabled=true]:text-green-950;
  }

  [data-theme*='light'] .app-navbar-bg,
  [data-theme*='light'] .highlighted-block {
    @apply bg-slate-50;
  }
  [data-theme*='light'] .doc-bg {
    @apply bg-slate-50;
  }

  [data-theme*='light'] .separator {
    @apply border-slate-200;
  }
  [data-theme*='light'] .panel {
    @apply rounded-lg bg-white;
  }
  [data-theme*='light'] .dropdown-panel {
    @apply bg-slate-50 border-slate-200;
  }

  [data-theme*='light'] .card {
    @apply bg-white border-slate-300
    hover:bg-slate-100
    active:bg-slate-300 active:top-[3px]
    transition-all duration-100;
  }
  [data-theme*='dark'] .card .card-link {
    @apply before:focus-visible:ring-4 before:focus-visible:ring-blue-700 before:focus-visible:border-2 before:focus-visible:border-slate-950;
  }
  [data-theme*='light'] .card-content {
    @apply text-slate-950;
  }
  [data-theme*='light'] .card-content-subdued {
    @apply text-slate-700;
  }
  [data-theme*='light'] .card-focus {
    @apply focus-visible:after:ring-blue-700;
  }
  [data-theme*='light'] .card-icon-container {
    @apply bg-slate-200;
  }
  [data-theme*='light'] .card-icon-body {
    @apply text-slate-950;
  }
  /* Document list item disabled style, inc. children styling */
  [data-theme*='light'] .card.card-disabled {
    @apply border-slate-200 bg-slate-50 hover:border-slate-200 hover:bg-slate-50 active:top-0;
  }
  [data-theme*='light'] .card-disabled .card-icon-container {
    @apply bg-slate-500;
  }
  [data-theme*='light'] .card-disabled .card-icon-body {
    @apply text-slate-50;
  }
  [data-theme*='light'] .card-disabled .card-content,
  [data-theme*='light'] .card-disabled .card-content-subdued {
    @apply text-slate-500;
  }
  [data-theme*='light'] .onboarding-page {
    @apply bg-slate-50;
  }
  [data-theme*='light'] .selected-btn {
    @apply border-4 !border-slate-950;
  }
  [data-theme*='light'] .selected-file-panel {
    @apply border-stone-50 bg-stone-50;
  }
  [data-theme*='light'] .account-btn {
    @apply bg-slate-950 text-slate-50 focus:shadow active:bg-slate-700;
  }
  [data-theme*='light'] .modal-bg {
    @apply bg-white text-slate-950;
  }
  [data-theme*='light'] .modal-overlay {
    background-color: rgba(0, 0, 0, 0.75);
  }
  [data-theme*='light'] .custom-srollbar {
    @apply scrollbar scrollbar-track-white scrollbar-thumb-white;
  }
  [data-theme*='light'] .bottom-sheet-scrollbar {
    @apply mr-1.5 scrollbar scrollbar-thumb-slate-400 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='light'] .doc-scrollbar {
    @apply scrollbar scrollbar-thumb-slate-400 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='light'] .pill-positive {
    @apply bg-green-200 text-green-950;
  }
  [data-theme*='light'] .pill-negative {
    @apply bg-rose-200 text-rose-950;
  }
  [data-theme*='light'] .pill-info {
    @apply bg-sky-200 text-sky-950;
  }
  [data-theme*='light'] .pill-neutral {
    @apply bg-slate-950 text-slate-50;
  }

  [data-theme*='light'] .dashboard-placeholder {
    @apply bg-white;
  }
  [data-theme*='light'] .disabled-label-text {
    @apply text-slate-500;
  }

  [data-theme*='light'] .message-bg-positive {
    @apply bg-green-200 text-green-950;
  }
  [data-theme*='light'] .message-bg-negative {
    @apply bg-rose-200 text-red-950;
  }
  [data-theme*='light'] .message-bg-info {
    @apply bg-blue-100 text-blue-950;
  }
  [data-theme*='light'] .input {
    @apply border-slate-400 text-slate-950 
    placeholder:text-slate-600
    hover:border-slate-700
    hover:active:border-slate-950 
    active:border-slate-950
    disabled:text-slate-500 disabled:border-slate-300 disabled:bg-slate-200
    disabled:active:border-slate-300    
    checked:border-slate-950 checked:bg-slate-950 checked:text-transparent 
    focus:border-slate-950 focus:ring-blue-700;
  }
  /* Native HTML <select> component options list bg */
  [data-theme*='light'] .input optgroup,
  [data-theme*='light'] .input option {
    @apply bg-slate-50;
  }
  /* Custom <select> component options list bg */
  [data-theme*='light'] .form-dropdown-select {
    @apply border-slate-50 bg-slate-50 text-slate-950;
  }
  [data-theme*='light'] .input:is(:-webkit-autofill, :autofill) {
    @apply ring-slate-950;
  }
  [data-theme*='light'] .form-input-success {
    @apply border-tailo-green-600;
  }
  [data-theme*='light'] .form-error {
    @apply bg-red-700;
  }
  [data-theme*='light'] .form-error-border {
    @apply border-red-700;
  }

  [data-theme*='light'] .context-menu {
    @apply bg-slate-50 border-slate-200;
  }
  [data-theme*='light'] .context-menu-options {
    @apply divide-slate-200;
  }

  /* Checkbox Toggle Control */
  [data-theme*='light'] input[type="checkbox"] ~ .toggle-control {
    @apply bg-white border-slate-500 after:bg-slate-950 after:border-white;
  }
  [data-theme*='light'] input[type="checkbox"]:checked ~ .toggle-control {
    @apply bg-slate-950 after:bg-white;
  }  

  [data-theme*='light'] .nav-item {
    @apply bg-slate-950 text-slate-50;
  }
  [data-theme*='light'] .upload-area {
    @apply border-dashed border-slate-500 outline-none hover:border-slate-700 focus-visible:border-none focus-visible:ring-4 focus-visible:ring-blue-700;
  }
  [data-theme*='light'] .upload-area-drag {
    @apply border-slate-700;
  }
  [data-theme*='light'] .mobile-step-indicator {
    @apply bg-slate-950 text-blue-50;
  }

  [data-theme*='light'] .mobile-step-indicator-inactive {
    @apply bg-slate-600 text-blue-50;
  }
  /* ONBOARDING DESKTOP STEP: default/current */
  [data-theme*='light'] .desktop-onboarding-step-link .inner-outline {
    @apply fill-slate-950 stroke-slate-950;
  }
  [data-theme*='light']
    .desktop-onboarding-step-link:focus-visible
    .outer-outline {
    @apply stroke-blue-700;
  }
  /* ONBOARDING DESKTOP STEP: disabled  */
  [data-theme*='light'] .desktop-onboarding-step-link.disabled .inner-outline {
    @apply fill-none stroke-slate-300;
  }
  /* ONBOARDING DESKTOP STEP: completed  */
  [data-theme*='light'] .desktop-onboarding-step-link.completed .inner-outline {
    @apply fill-none stroke-slate-950;
  }
  [data-theme*='light']
    .desktop-onboarding-step-link.completed:hover
    .inner-outline {
    @apply stroke-slate-600;
  }
  [data-theme*='light']
    .desktop-onboarding-step-link.completed:active
    .inner-outline {
    @apply stroke-slate-400;
  }
  [data-theme*='light'] .nav-from-gradient {
    @apply from-slate-50;
  }
  [data-theme*='light'] .verify-spinner {
    @apply text-tailo-green-500;
  }
  [data-theme*='light'] .toggle {
    @apply border-slate-400 bg-slate-950 focus:outline-2 focus:outline-blue-700;
    --tglbg: white;
  }
  [data-theme*='light'] .toggle:checked {
    @apply border-slate-400 bg-white;
    --tglbg: #020617;
  }
  [data-theme*='light'] .focus-outer {
    @apply outline-none focus-visible:border-2 focus-visible:border-blue-700;
  }
  [data-theme*='light'] .definition-part-of-speech {
    @apply bg-white;
  }
  [data-theme*='light'] .definition-example {
    @apply bg-slate-50 text-slate-700;
  }
  [data-theme*='light'] .progress-bar-rail {
    @apply bg-slate-400;
  }
  [data-theme*='light'] .progress-bar-rail::-webkit-progress-value {
    @apply bg-slate-950;
  }
  [data-theme*='light'] .accordion:hover .accordion-title {
    @apply bg-slate-200;
  }
  [data-theme*='light'] .accordion-title-active {
    @apply bg-slate-300;
  }
}

@layer utilities {
  [data-theme*='light'] .theme-bg,
  [data-theme*='light'] .app-bg {
    @apply bg-slate-100;
  }
  [data-theme*='light'] .logo-fill-bg {
    @apply bg-white fill-white;
  }
  [data-theme*='light'] .app-bg-subdued {
    @apply bg-slate-200;
  }
  [data-theme*='light'] .theme-border {
    @apply border border-slate-200;
  }
  [data-theme*='light'] .theme-border-subdued {
    @apply border border-slate-200;
  }
  [data-theme*='light'] .theme-border-t {
    @apply border-t-slate-200;
  }
  [data-theme*='light'] .theme-border-l {
    @apply border-l border-l-slate-200;
  }
  [data-theme*='light'] .theme-text {
    @apply text-black;
  }
  [data-theme*='light'] .theme-warning {
    @apply text-red-500;
  }
  /* Used for delete text in dropdowns */
  [data-theme*='light'] .theme-delete {
    @apply text-red-600;
  }

  [data-theme*='light'] .icon-bg {
    @apply bg-stone-50;
  }
  [data-theme*='light'] .opposite-bg {
    @apply bg-slate-950;
  }
  [data-theme*='light'] .doc-text-default {
    @apply text-slate-950;
  }
  [data-theme*='light'] .doc-text-inactive {
    @apply text-slate-600;
  }
  [data-theme*='light'] .doc-text-subdued {
    @apply text-slate-700;
  }
  [data-theme*='light'] .doc-text-inverted {
    @apply text-blue-50;
  }
  [data-theme*='light'] .opposite-border,
  [data-theme*='light'] .reading-marker-border {
    @apply border-slate-950;
  }
  [data-theme*='light'] .onboarding-document-preview-container,
  [data-theme*='light'] .expand-preview-btn-container > button {
    @apply bg-slate-50;
  }
  [data-theme*='light'] .onboarding-document-preview {
    @apply bg-white;
  }
  [data-theme*='light'] .onboarding-doc-pop-out {
    @apply bg-white;
  }
  [data-theme*='light'] .default-progress-bar {
    @apply border-slate-300 bg-white;
  }
  [data-theme*='light'] .default-progress-bar-upload {
    @apply bg-slate-300;
  }
  [data-theme*='light'] .default-progress-bar-icon {
    @apply text-slate-950;
  }
  [data-theme*='light'] .bg-default {
    @apply bg-slate-50;
  }
  [data-theme*='light'] .svg-default {
    @apply fill-slate-950;
  }
  [data-theme*='light'] .doc-link-default {
    @apply bg-slate-100 hover:bg-slate-200 focus-visible:bg-slate-950 active:bg-slate-950 visited:bg-white text-slate-950 focus-visible:text-slate-50 active:text-slate-50 visited:text-slate-700 rounded-sm border-b border-slate-400 hover:border-slate-950 focus-visible:border-slate-950 active:border-slate-950 visited:border-slate-950 font-semibold outline-none focus-visible:ring ring-blue-700;
  }

  /* Applied only when theme is in focus */
  [data-theme*='light'] .theme-selected {
    @apply ring-tailo-green-500;
  }

  /* Applied when current theme is in use */
  [data-theme*='light'] .selected {
    @apply ring-black ring-offset-2 ring-offset-white;
  }

  [data-theme*='light'] .document-card-hover {
    @apply bg-slate-700;
  }
  [data-theme*='light'] .svg-stroke {
    @apply stroke-white;
  }
  /* Shadows */
  [data-theme*='light'] .shadow-sm-d {
    box-shadow: var(--shadow-sm-d) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-d {
    box-shadow: var(--shadow-d) var(--shadow-color-base-light),
      var(--shadow-d-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-md-d {
    box-shadow: var(--shadow-md-d) var(--shadow-color-base-light),
      var(--shadow-md-d-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-lg-d {
    box-shadow: var(--shadow-lg-d) var(--shadow-color-base-light),
      var(--shadow-lg-d-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-xl-d {
    box-shadow: var(--shadow-xl-d) var(--shadow-color-base-light),
      var(--shadow-xl-d-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-2xl-d {
    box-shadow: var(--shadow-2xl-d) rgba(0, 0, 0, 0.25);
  }
  [data-theme*='light'] .shadow-sm-u {
    box-shadow: var(--shadow-sm-u) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-u {
    box-shadow: var(--shadow-u) var(--shadow-color-base-light),
      var(--shadow-u-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-md-u {
    box-shadow: var(--shadow-md-u) var(--shadow-color-base-light),
      var(--shadow-md-u-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-lg-u {
    box-shadow: var(--shadow-lg-u) var(--shadow-color-base-light),
      var(--shadow-lg-u-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-xl-u {
    box-shadow: var(--shadow-xl-u) var(--shadow-color-base-light),
      var(--shadow-xl-u-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-2xl-u {
    box-shadow: var(--shadow-2xl-u) rgba(0, 0, 0, 0.25);
  }
  [data-theme*='light'] .shadow-l {
    box-shadow: var(--shadow-l) var(--shadow-color-base-light),
      var(--shadow-l-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-md-l {
    box-shadow: var(--shadow-md-l) var(--shadow-color-base-light),
      var(--shadow-md-l-2) var(--shadow-color-base-light);
  }
  [data-theme*='light'] .shadow-solid-d {
    box-shadow: var(--shadow-solid-d) #cbd5e1; /*slate-300*/
  }
  [data-theme*='light'] .shadow-solid-md-d {
    box-shadow: var(--shadow-solid-md-d) #cbd5e1; /*slate-300*/
  }
  /* Table styling */

  [data-theme*='light'] .table-header-bg {
    @apply bg-slate-50;
  }

  [data-theme*='light'] .table-column-bg {
    @apply bg-white;
  }
  [data-theme*='light'] .table-cell-border {
    @apply border-slate-300;
  }
  /* Used on elements imitating focus ring - tabs nav on mobile */
  [data-theme*='light'] .focus-border {
    @apply border-blue-700;
  }
  [data-theme*='light'] .text-disabled {
    @apply text-slate-500;
  }
  [data-theme*='light'] .app-bg-tertiary {
    @apply bg-slate-200;
  }
  [data-theme*='light'] .break-line {
    @apply bg-slate-200 opacity-25;
  }
  [data-theme*='light'] .bg-gradient {
    @apply bg-gradient-to-t from-slate-100;
  }
  [data-theme*='light'] .panel-bg {
    @apply bg-slate-50;
  }
  [data-theme*='light'] .drawer-bg {
    @apply bg-white;
  }
  [data-theme*='light'] .bottom-sheet-top-nav {
    @apply bg-slate-50 border-b border-slate-200;
  }
  /* Separate classes needed for the select-dropdown due to focus style beng set dynamically via internal props */
  [data-theme*='light'] .select-ring {
    @apply ring-blue-700;
  }
  [data-theme*='light'] .select-option-focus {
    @apply bg-slate-300;
  }
  [data-theme*='light'] .focus-outline {
    @apply outline outline-[3px] outline-blue-700;
  }
  [data-theme*='light'] .slider-text {
    @apply text-sm font-semibold text-slate-950;
  }

  [data-theme*='light'] .slider-value {
    @apply mt-1 flex justify-between text-xs text-slate-950;
  }
  [data-theme*='light'] .slider-dot {
    @apply bg-slate-700;
  }
  [data-theme*='light'] .slider-dot-active {
    @apply border border-slate-950 bg-slate-950;
  }
  [data-theme*='light'] .slider-handle {
    @apply border-slate-950 bg-slate-950 hover:bg-slate-950;
  }
  [data-theme*='light'] .slider-handle-dragging {
    @apply border-none bg-slate-950 ring-4 ring-slate-950/25;
  }
  [data-theme*='light'] .slider-handle-focus {
    @apply bg-slate-950 ring-4 ring-slate-950/25;
  }
  [data-theme*='light'] .slider-rail {
    @apply bg-slate-400;
  }
  [data-theme*='light'] .slider-track {
    @apply bg-slate-950;
  }
  [data-theme*='light'] .tool-tip {
    @apply bg-slate-950 text-slate-50;
  }
  [data-theme*='light'] .controls-container {
    @apply bg-white;
  }
  [data-theme*='light'] .text-highlight-word-definition {
    @apply bg-blue-300 text-slate-950;
  }
  [data-theme*='light'] .active-tts-paragraph {
    @apply bg-slate-950 text-slate-50 px-2 pt-2 rounded-xl;
  }
  [data-theme*='light'] .tts-progress-bar {
    @apply w-full [&::-webkit-progress-bar]:bg-slate-300
    [&::-webkit-progress-value]:bg-slate-950;
  }
  [data-theme*='light'] .overview-gradient-border {
    @apply bg-gradient-to-br from-lime-400 via-pink-400 to-blue-400 rounded-2xl p-[2px];
  }
  [data-theme*='light'] .ai-gradient-border {
    @apply bg-gradient-to-br from-lime-400 via-pink-400 to-blue-400 rounded-md p-[1px];
  }
  [data-theme*='light'] .ai-gradient-border.btn-secondary {
    /* Support button states whilst maintaining the rainbow border */
    @apply border-0 p-[2px];
  }
  [data-theme*='light'] .quick-summary-container {
    @apply h-full w-full bg-white rounded-2xl px-6 py-5;
  }
  [data-theme*='light'] .ai-notice-container {
    @apply bg-white;
  }
  [data-theme*='light'] .ai-underline {
    @apply underline decoration-slate-400;
  }
  [data-theme*='light'] .reading-time {
    @apply bg-slate-200;
  }
  [data-theme*='light'] .welcome-banner button {
    @apply border-white bg-white
      hover:bg-slate-100 hover:border-slate-300
      focus-visible:outline-blue-700 focus-visible:border-white
      active:bg-slate-200;
  }
  [data-theme*='light'] .welcome-banner ul svg {
    @apply text-tailo-green-500;
  }
  [data-theme*='light'] .document-wrapper {
    @apply bg-slate-50;
  }
  [data-theme*='light'] .document-viewer-background {
    @apply bg-slate-50;
  }
  [data-theme*='light'] .closed-right-drawer {
    @apply bg-slate-50;
  }
  [data-theme*='light'] .summary-body {
    @apply bg-white;
  }
  [data-theme*='light'] .reading-environment-wrapper {
    @apply bg-white;
  }
  [data-theme*='light'] .toc-parent-text {
    @apply text-slate-950 font-bold;
  }
  [data-theme*='light'] .toc-child-text {
    @apply text-slate-950;
  }
  [data-theme*='light'] .toc-items-text {
    @apply text-slate-500;
  }
  [data-theme*='light'] .toc-parent-line {
    @apply w-8 h-0.5 bg-slate-500;
  }
  [data-theme*='light'] .toc-child-line {
    @apply w-6 h-0.5 bg-slate-500;
  }
  [data-theme*='light'] .toc-grandcchild-line {
    @apply w-4 h-0.5 bg-slate-500;
  }
  [data-theme*='light'] .toc-active-element {
    @apply bg-slate-950;
  }
  [data-theme*='light'] .nav-border {
    @apply border-b-2 border-slate-300;
  }
  [data-theme*='light'] .nav-bg {
    @apply bg-white;
  }
  [data-theme*='light'] .nav-underline {
    @apply bg-slate-950;
  }
  [data-theme*='light'] .nav-content-styles {
    @apply bg-slate-100 border-t-2 border-slate-200 p-2 lg:bg-white lg:relative lg:border-none;
  }
  [data-theme*='light'] .dropdown-nav {
    @apply bg-white border-slate-200;
  }
  [data-theme*='light'] .toc-background {
    @apply bg-slate-50/70 backdrop-blur-sm z-50 rounded-tr-lg rounded-br-lg;
  }
  [data-theme*='light'] .user-svg-icon {
    fill: #020617;
  }
  [data-theme*='light'] .dashboard-bg {
    @apply bg-slate-100;
  }
  [data-theme*='light'] .documents-panel {
    @apply bg-slate-100;
  }
  [data-theme*='light'] .mobile-dashboard-bg {
    @apply bg-slate-100;
  }
  [data-theme*='light'] .mobile-welcome-panel {
    @apply bg-white;
  }
  [data-theme*='light'] .mobile-document-body {
    @apply bg-slate-100;
  }
  [data-theme*='light'] .mobile-extraction-message {
    @apply bg-white;
  }
  [data-theme*='light'] .small-tailo-logo {
    @apply hover:text-slate-800 focus:ring-blue-700 
    focus:outline-2 focus:ring-2 focus:outline-blue-700 focus:border-slate-800
    active:border-slate-800 active:ring-2 active:ring-blue-700
    active:outline-2 active:outline-blue-700 active:text-slate-700;
  }

  /* The left border for the summary page expand/collapse button, to separate the text link from the button visually */
  [data-theme*='light'] .summary-expand-btn {
    @apply border-l-slate-200;
  }

  [data-theme*='light'] .active-toc-parent-text {
    @apply text-slate-950
    hover:bg-slate-200 hover:text-slate-950
    focus:border-blue-700 focus:bg-slate-950
    focus:text-slate-50
    active:bg-slate-300 active:text-slate-950;
  }
  [data-theme*='light'] .inactive-toc-parent-text {
    @apply text-slate-950 decoration-slate-950
    hover:bg-slate-200 hover:text-slate-950
    focus:border-blue-700 focus:bg-slate-950 focus:text-slate-50 focus:decoration-lime-50
    active:bg-slate-300 active:text-slate-950 active:decoration-slate-950;
  }
  [data-theme*='light'] .inactive-toc-parent-text + ul a {
    @apply text-slate-950;
  }
  [data-theme*='light'] .active-toc-parent-text + ul a {
    @apply text-slate-950;
  }
  [data-theme*='light'] .tailo-icon-button-active {
    @apply bg-slate-300;
  }
  [data-theme*='light'] .search-highlight {
    @apply bg-yellow-400 text-slate-950;
  }
  [data-theme*='light'] .themed-spinner {
    @apply text-slate-500;
  }
  [data-theme*='light'] .result-item-card {
    @apply bg-slate-200 lg:bg-white border-2 border-transparent hover:bg-slate-200 
    focus-visible:border-blue-700 active:bg-slate-300;
  }
  [data-theme*='light'] .explain-this-dropdown {
    @apply bg-slate-200;
  }
  [data-theme*='light'] .loading-response {
    @apply bg-slate-100;
  }
  [data-theme*='light'] .divider {
    @apply bg-slate-200;
  }
  [data-theme*='light'] .result-bg {
    @apply bg-slate-200;
  }
  [data-theme*='light'] .result-text {
    @apply text-slate-500;
  }
  [data-theme*='light'] .explain-this-ai-container {
    @apply bg-white;
  }
  [data-theme*='light'] .feedback-faces {
    @apply stroke-slate-950;
  }
  [data-theme*='light'] .feedback-face-card {
    @apply border-2 border-slate-500 hover:border-slate-300;
  }
  [data-theme*='light'] .feedback-face-card-active {
    @apply ring-2 ring-blue-500 border-2 border-black;
  }
}
