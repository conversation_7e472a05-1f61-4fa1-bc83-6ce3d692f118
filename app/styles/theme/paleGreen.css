/* [data-theme*='pale-green'] .custom-class { }*/

:root {
  --scrollbar-track: transparent;
  --scrollbar-thumb: @apply lime-300;
}

@layer base {
}
@layer components {
  [data-theme*='pale-green'] .btn {
    @apply focus:border-lime-100 focus:ring-rose-600 focus:hover:border-lime-100;
  }

  [data-theme*='pale-green'] .btn-primary {
    @apply bg-lime-950 text-lime-50
    hover:border-transparent hover:bg-lime-800 
    active:bg-lime-600 
    disabled:bg-lime-300 disabled:text-lime-700 
    data-[disabled=true]:bg-lime-300 data-[disabled=true]:text-lime-700;
  }

  [data-theme*='pale-green'] .btn-secondary {
    @apply border-2 border-lime-950 text-lime-950 
    hover:border-transparent hover:border-lime-700
    hover:active:border-lime-500 
    active:border-lime-500 
    disabled:border-lime-400 disabled:text-lime-700 
    data-[disabled=true]:border-lime-400 data-[disabled=true]:text-lime-700
    disabled:active:border-lime-400;
  }

  [data-theme*='pale-green'] .btn-tertiary {
    @apply text-lime-950 
    hover:bg-lime-300 
    hover:disabled:bg-transparent
    active:bg-lime-400 
    disabled:text-lime-700 
    data-[disabled=true]:text-lime-700;
  }

  [data-theme*='pale-green'] .btn-positive {
    @apply bg-green-600 text-white 
    hover:bg-green-700 
    active:bg-green-800 
    disabled:bg-green-200 disabled:text-green-800 
    data-[disabled=true]:bg-green-200 data-[disabled=true]:text-green-800;
  }

  [data-theme*='pale-green'] .btn-destructive {
    @apply bg-red-600 text-white 
    hover:bg-red-700 
    active:bg-red-800 
    disabled:bg-red-100 disabled:text-red-700
    data-[disabled=true]:bg-red-100 data-[disabled=true]:text-red-700;
  }

  [data-theme*='pale-green'] .onboarding-page {
    @apply bg-lime-50;
  }
  [data-theme*='pale-green'] .pill-positive {
    @apply bg-green-400 text-green-950;
  }

  [data-theme*='pale-green'] .pill-negative {
    @apply bg-rose-300 text-rose-950;
  }

  [data-theme*='pale-green'] .pill-info {
    @apply bg-sky-400 text-sky-950;
  }

  [data-theme*='pale-green'] .pill-neutral {
    @apply bg-lime-950 text-lime-50;
  }

  [data-theme*='pale-green'] .input {
    @apply border-lime-600 text-lime-950 
    placeholder:text-lime-800
    hover:border-lime-800
    hover:active:border-lime-900 
    active:border-lime-900
    disabled:text-lime-800 disabled:border-lime-200 disabled:bg-lime-100
    disabled:active:border-lime-200    
    checked:border-lime-950 checked:bg-lime-950 checked:text-transparent 
    focus:border-lime-950 focus:ring-rose-600;
  }
  /* Native HTML <select> component options list bg */
  [data-theme*='pale-green'] .input optgroup,
  [data-theme*='pale-green'] .input option {
    @apply bg-lime-100;
  }
  /* Custom <select> component options list bg */
  [data-theme*='pale-green'] .form-dropdown-select {
    @apply border-lime-300 bg-lime-100 text-lime-950;
  }
  [data-theme*='pale-green'] .input:is(:-webkit-autofill, :autofill) {
    @apply ring-slate-950;
  }
  [data-theme*='pale-green'] .form-input-success {
    @apply border-green-600;
  }

  [data-theme*='pale-green'] .form-error {
    @apply bg-red-700;
  }

  [data-theme*='pale-green'] .form-error-border {
    @apply border-red-700;
  }

  [data-theme*='pale-green'] .context-menu {
    @apply bg-lime-200 border-lime-300;
  }
  [data-theme*='pale-green'] .context-menu-options {
    @apply divide-lime-300;
  }

  /* Checkbox Toggle Control */
  [data-theme*='pale-green'] input[type="checkbox"] ~ .toggle-control {
    @apply bg-lime-100 border-lime-600 after:bg-lime-950 after:border-lime-100;
  }
  [data-theme*='pale-green'] input[type="checkbox"]:checked ~ .toggle-control {
    @apply bg-lime-950 after:bg-lime-100;
  }  

  [data-theme*='pale-green'] .app-navbar-bg,
  [data-theme*='pale-green'] .highlighted-block {
    @apply bg-lime-200;
  }

  [data-theme*='pale-green'] .doc-bg {
    @apply bg-lime-200;
  }

  [data-theme*='pale-green'] .separator {
    @apply border-lime-300;
  }
  [data-theme*='pale-green'] .panel {
    @apply rounded-lg bg-lime-100;
  }
  [data-theme*='pale-green'] .dropdown-panel {
    @apply bg-lime-100 border-lime-300;
  }
  [data-theme*='pale-green'] .nav-item {
    @apply bg-lime-950 text-lime-200;
  }
  [data-theme*='pale-green'] .account-btn {
    @apply bg-lime-950 text-lime-200;
  }
  [data-theme*='pale-green'] .modal-bg {
    @apply bg-lime-50;
  }
  [data-theme*='pale-green'] .bottom-sheet-scrollbar {
    @apply mr-1.5 scrollbar scrollbar-thumb-lime-300 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='pale-green'] .doc-scrollbar {
    @apply scrollbar scrollbar-thumb-lime-300 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='pale-green'] .upload-area {
    @apply border-dashed border-lime-950 outline-none hover:border-lime-800 focus-visible:border-none focus-visible:ring-4 focus-visible:ring-rose-600;
  }
  [data-theme*='pale-green'] .upload-area-drag {
    @apply border-lime-800;
  }
  [data-theme*='pale-green'] .default-progress-bar {
    @apply border-lime-700 bg-lime-50;
  }
  [data-theme*='pale-green'] .default-progress-bar-upload {
    @apply border-lime-700 bg-lime-300;
  }
  [data-theme*='pale-green'] .default-progress-bar-icon {
    @apply text-lime-950;
  }
  [data-theme*='pale-green'] .table-of-contents-button {
    @apply bg-lime-100 fill-lime-950;
  }
  [data-theme*='pale-green'] .message-bg-positive {
    @apply bg-green-400 text-green-950;
  }
  [data-theme*='pale-green'] .message-bg-negative {
    @apply bg-red-300 text-red-950;
  }
  [data-theme*='pale-green'] .message-bg-info {
    @apply bg-sky-300 text-sky-950;
  }
  [data-theme*='pale-green'] .card {
    @apply bg-lime-50 border-lime-500
    hover:bg-lime-100
    active:bg-lime-300 active:top-[3px]
    transition-all duration-100;
  }
  [data-theme*='pale-green'] .card .card-link {
    @apply before:focus-visible:ring-rose-600 before:focus-visible:border-lime-200;
  }
  [data-theme*='pale-green'] .card-content {
    @apply text-lime-950;
  }
  [data-theme*='pale-green'] .card-content-subdued {
    @apply text-lime-800;
  }
  [data-theme*='pale-green'] .card-focus {
    @apply focus-visible:after:ring-rose-600;
  }
  [data-theme*='pale-green'] .card-icon-container {
    @apply bg-lime-950;
  }
  [data-theme*='pale-green'] .card-icon-body {
    @apply text-lime-50;
  }
  /* Document list item disabled style, inc. children styling */
  [data-theme*='pale-green'] .card.card-disabled {
    @apply border-lime-400 bg-lime-200 hover:border-lime-400 hover:bg-lime-200 active:top-0;
  }
  [data-theme*='pale-green'] .card-disabled .card-icon-container {
    @apply bg-lime-700;
  }
  [data-theme*='pale-green'] .card-disabled .card-icon-body {
    @apply text-lime-200;
  }
  [data-theme*='pale-green'] .card-disabled .card-content,
  [data-theme*='pale-green'] .card-disabled .card-content-subdued {
    @apply text-lime-700;
  }
  [data-theme*='pale-green'] .mobile-step-indicator {
    @apply bg-lime-950 text-lime-50;
  }
  [data-theme*='pale-green'] .mobile-step-indicator-inactive {
    @apply bg-lime-700 text-lime-50;
  }
  /* ONBOARDING DESKTOP STEP: default/current */
  [data-theme*='pale-green'] .desktop-onboarding-step-link .inner-outline {
    @apply fill-lime-950 stroke-lime-950;
  }
  [data-theme*='pale-green']
    .desktop-onboarding-step-link:focus-visible
    .outer-outline {
    @apply stroke-blue-700;
  }
  /* ONBOARDING DESKTOP STEP: disabled  */
  [data-theme*='pale-green']
    .desktop-onboarding-step-link.disabled
    .inner-outline {
    @apply fill-none stroke-lime-400;
  }
  /* ONBOARDING DESKTOP STEP: completed  */
  [data-theme*='pale-green']
    .desktop-onboarding-step-link.completed
    .inner-outline {
    @apply fill-none stroke-lime-950;
  }
  [data-theme*='pale-green']
    .desktop-onboarding-step-link.completed:hover
    .inner-outline {
    @apply stroke-lime-700;
  }
  [data-theme*='pale-green']
    .desktop-onboarding-step-link.completed:active
    .inner-outline {
    @apply stroke-lime-500;
  }
  [data-theme*='pale-green'] .nav-from-gradient {
    @apply from-lime-50;
  }
  [data-theme*='pale-green'] .verify-spinner {
    @apply text-lime-950;
  }
  [data-theme*='pale-green'] .toggle {
    @apply border-green-600 bg-lime-950 focus:outline-2 focus:outline-rose-600;
    --tglbg: #ecfccb;
  }
  [data-theme*='pale-green'] .toggle:checked {
    @apply border-green-600 bg-lime-100;
    --tglbg: #1a2e05;
  }
  [data-theme*='pale-green'] .focus-outer {
    @apply outline-none focus-visible:border-2 focus-visible:border-rose-600;
  }
  [data-theme*='pale-green'] .definition-part-of-speech {
    @apply bg-lime-100 text-lime-950;
  }
  [data-theme*='pale-green'] .definition-example {
    @apply bg-lime-200 text-lime-800;
  }
  [data-theme*='pale-green'] .progress-bar-rail {
    @apply bg-lime-500;
  }
  [data-theme*='pale-green'] .progress-bar-rail::-webkit-progress-value {
    @apply bg-lime-950;
  }
  [data-theme*='pale-green'] .accordion:hover .accordion-title {
    @apply bg-lime-300;
  }
  [data-theme*='pale-green'] .accordion-title-active {
    @apply bg-lime-400;
  }
}

@layer utilities {
  [data-theme*='pale-green'] .app-bg {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .logo-fill-bg {
    @apply bg-lime-100 fill-lime-100;
  }
  [data-theme*='pale-green'] .app-bg-subdued {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .theme-border {
    @apply border border-lime-300;
  }
  [data-theme*='pale-green'] .theme-border-subdued {
    @apply border border-lime-200;
  }
  [data-theme*='pale-green'] .theme-border-t {
    @apply border-t-lime-300;
  }
  [data-theme*='pale-green'] .theme-border-l {
    @apply border-l border-l-lime-300;
  }
  [data-theme*='pale-green'] .theme-warning {
    @apply text-red-500;
  }
  [data-theme*='pale-green'] .theme-delete {
    @apply text-red-800;
  }

  [data-theme*='pale-green'] .opposite-bg {
    @apply bg-lime-950;
  }
  [data-theme*='pale-green'] .opposite-border,
  [data-theme*='pale-green'] .reading-marker-border {
    @apply border-lime-950;
  }
  [data-theme*='pale-green'] .onboarding-document-preview-container,
  [data-theme*='pale-green'] .expand-preview-btn-container > button {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .onboarding-document-preview {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .onboarding-doc-pop-out {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .selected-btn {
    @apply border-4 !border-lime-900;
  }
  [data-theme*='pale-green'] .svg-stroke {
    @apply stroke-lime-100;
  }
  [data-theme*='pale-green'] .doc-text-default {
    @apply text-lime-950;
  }
  [data-theme*='pale-green'] .doc-text-inactive {
    @apply text-lime-700;
  }

  [data-theme*='pale-green'] .doc-text-subdued {
    @apply text-lime-800;
  }
  [data-theme*='pale-green'] .doc-text-inverted {
    @apply text-lime-50;
  }
  [data-theme*='pale-green'] .theme-selected {
    @apply ring-rose-600;
  }
  [data-theme*='pale-green'] .selected {
    @apply ring-lime-950 ring-offset-2 ring-offset-lime-100;
  }
  [data-theme*='pale-green'] .bg-default {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .svg-default {
    @apply fill-lime-950;
  }
  [data-theme*='pale-green'] .doc-link-default {
    @apply bg-lime-200 hover:bg-lime-300 focus-visible:bg-lime-950 active:bg-lime-950 visited:bg-lime-100 text-lime-950 focus-visible:text-lime-50 active:text-lime-50 visited:text-lime-900 rounded-sm border-b border-lime-600 hover:border-lime-950 focus-visible:border-lime-950 active:border-lime-950 visited:border-slate-950 font-semibold outline-none focus-visible:ring ring-rose-600;
  }

  /* Shadows */
  [data-theme*='pale-green'] .shadow-sm-d {
    box-shadow: var(--shadow-sm-d) rgba(26, 46, 5, 0.05);
  }
  [data-theme*='pale-green'] .shadow-d {
    box-shadow: var(--shadow-d) var(--shadow-color-base-pale-green),
      var(--shadow-d-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-md-d {
    box-shadow: var(--shadow-md-d) var(--shadow-color-base-pale-green),
      var(--shadow-md-d-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-lg-d {
    box-shadow: var(--shadow-lg-d) var(--shadow-color-base-pale-green),
      var(--shadow-lg-d-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-xl-d {
    box-shadow: var(--shadow-xl-d) var(--shadow-color-base-pale-green),
      var(--shadow-xl-d-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-2xl-d {
    box-shadow: var(--shadow-2xl-d) rgba(26, 46, 5, 0.25);
  }
  [data-theme*='pale-green'] .shadow-sm-u {
    box-shadow: var(--shadow-sm-u) rgba(26, 46, 5, 0.05);
  }
  [data-theme*='pale-green'] .shadow-u {
    box-shadow: var(--shadow-u) var(--shadow-color-base-pale-green),
      var(--shadow-u-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-md-u {
    box-shadow: var(--shadow-md-u) var(--shadow-color-base-pale-green),
      var(--shadow-md-u-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-lg-u {
    box-shadow: var(--shadow-lg-u) var(--shadow-color-base-pale-green),
      var(--shadow-lg-u-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-xl-u {
    box-shadow: var(--shadow-xl-u) var(--shadow-color-base-pale-green),
      var(--shadow-xl-u-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-2xl-u {
    box-shadow: var(--shadow-2xl-u) rgba(26, 46, 5, 0.25);
  }
  [data-theme*='pale-green'] .shadow-l {
    box-shadow: var(--shadow-l) var(--shadow-color-base-pale-green),
      var(--shadow-l-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-md-l {
    box-shadow: var(--shadow-md-l) var(--shadow-color-base-pale-green),
      var(--shadow-md-l-2) var(--shadow-color-base-pale-green);
  }
  [data-theme*='pale-green'] .shadow-solid-d {
    box-shadow: var(--shadow-solid-d) #a3e635; /*lime-400*/
  }
  [data-theme*='pale-green'] .shadow-solid-md-d {
    box-shadow: var(--shadow-solid-md-d) #a3e635; /*lime-400*/
  }

  /* Table styling */

  [data-theme*='pale-green'] .table-header-bg {
    @apply bg-lime-200;
  }

  [data-theme*='pale-green'] .table-column-bg {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .table-cell-border {
    @apply border-lime-300;
  }
  /* Used on elements imitating focus ring - tabs nav on mobile */
  [data-theme*='pale-green'] .focus-border {
    @apply border-blue-700;
  }
  [data-theme*='pale-green'] .text-disabled {
    @apply text-lime-700;
  }
  [data-theme*='pale-green'] .app-bg-tertiary {
    @apply bg-lime-300;
  }
  [data-theme*='pale-green'] .break-line {
    @apply bg-lime-300 opacity-50;
  }
  [data-theme*='pale-green'] .bg-gradient {
    @apply bg-gradient-to-t from-lime-200;
  }
  [data-theme*='pale-green'] .panel-bg {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .drawer-bg {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .bottom-sheet-top-nav {
    @apply bg-lime-200 border-b border-lime-300;
  }
  /* Separate classes needed for the select-dropdown due to focus style beng set dynamically via internal props */
  [data-theme*='pale-green'] .select-ring {
    @apply ring-rose-600;
  }
  [data-theme*='pale-green'] .select-option-focus {
    @apply bg-lime-300;
  }
  [data-theme*='pale-green'] .focus-outline {
    @apply outline outline-[3px] outline-rose-600;
  }
  [data-theme*='pale-green'] .slider-text {
    @apply text-sm font-semibold text-lime-950;
  }

  [data-theme*='pale-green'] .slider-value {
    @apply mt-1 flex justify-between text-xs text-lime-950;
  }
  [data-theme*='pale-green'] .slider-dot {
    @apply bg-lime-800;
  }
  [data-theme*='pale-green'] .slider-dot-active {
    @apply border border-lime-950 bg-lime-950;
  }
  [data-theme*='pale-green'] .slider-handle {
    @apply border-lime-950 bg-lime-950 hover:bg-lime-950;
  }
  [data-theme*='pale-green'] .slider-handle-dragging {
    @apply border-none bg-lime-950 ring-4 ring-lime-950/25;
  }
  [data-theme*='pale-green'] .slider-handle-focus {
    @apply bg-lime-950 ring-4 ring-lime-950/25;
  }
  [data-theme*='pale-green'] .slider-rail {
    @apply bg-lime-500;
  }
  [data-theme*='pale-green'] .slider-track {
    @apply bg-lime-950;
  }
  [data-theme*='pale-green'] .tool-tip {
    @apply bg-lime-950 text-lime-50;
  }
  [data-theme*='pale-green'] .controls-container {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .text-highlight-word-definition {
    @apply bg-rose-600 text-lime-50;
  }
  [data-theme*='pale-green'] .active-tts-paragraph {
    @apply bg-lime-950 text-lime-50 px-2 pt-2 rounded-xl;
  }
  [data-theme*='pale-green'] .tts-progress-bar {
    @apply w-full [&::-webkit-progress-bar]:bg-lime-400
    [&::-webkit-progress-value]:bg-lime-950;
  }
  [data-theme*='pale-green'] .overview-gradient-border {
    @apply border border-lime-600 rounded-2xl p-[2px];
  }
  [data-theme*='pale-green'] .ai-gradient-border {
    @apply border-2 border-lime-600 rounded-md;
  }
  [data-theme*='pale-green'] .quick-summary-container {
    @apply h-full w-full bg-lime-100 rounded-2xl px-6 py-5;
  }
  [data-theme*='pale-green'] .ai-notice-container {
    @apply bg-transparent;
  }
  [data-theme*='pale-green'] .ai-underline {
    @apply underline decoration-lime-600;
  }
  [data-theme*='pale-green'] .reading-time {
    @apply bg-lime-300;
  }
  [data-theme*='pale-green'] .welcome-banner button {
    @apply border-lime-100 bg-lime-100
      hover:bg-lime-50 
      focus-visible:outline-rose-600 focus-visible:border-lime-200
      active:bg-lime-300;
  }
  [data-theme*='pale-green'] .document-wrapper {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .document-viewer-background {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .closed-right-drawer {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .summary-body {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .underline-style {
    @apply underline decoration-sky-500;
  }
  [data-theme*='pale-green'] .reading-environment-wrapper {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .toc-parent-text {
    @apply text-lime-950 font-bold;
  }
  [data-theme*='pale-green'] .toc-child-text {
    @apply text-lime-950;
  }
  [data-theme*='pale-green'] .toc-items-text {
    @apply text-lime-800;
  }
  [data-theme*='pale-green'] .toc-parent-line {
    @apply w-8 h-0.5 bg-lime-600;
  }
  [data-theme*='pale-green'] .toc-child-line {
    @apply w-6 h-0.5 bg-lime-600;
  }
  [data-theme*='pale-green'] .toc-grandcchild-line {
    @apply w-4 h-0.5 bg-lime-600;
  }
  [data-theme*='pale-green'] .toc-active-element {
    @apply bg-lime-950;
  }
  [data-theme*='pale-green'] .nav-border {
    @apply border-b-2 border-lime-300;
  }
  [data-theme*='pale-green'] .nav-bg {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .nav-underline {
    @apply bg-lime-500;
  }
  [data-theme*='pale-green'] .nav-content-styles {
    @apply bg-lime-200 border-t-2 border-lime-300 p-2 lg:bg-lime-100 lg:relative lg:border-none;
  }
  [data-theme*='pale-green'] .toc-background {
    @apply bg-lime-200/50 backdrop-blur-sm z-50 rounded-tr-lg rounded-br-lg;
  }
  [data-theme*='pale-green'] .dropdown-nav {
    @apply bg-lime-100 border-lime-300;
  }
  [data-theme*='pale-green'] .user-svg-icon {
    fill: #1a2e05;
  }
  [data-theme*='pale-green'] .dashboard-bg {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .documents-panel {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .mobile-dashboard-bg {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .mobile-welcome-panel {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .mobile-document-body {
    @apply bg-lime-200;
  }
  [data-theme*='pale-green'] .mobile-extraction-message {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .small-tailo-logo {
    @apply hover:text-slate-800 focus:ring-rose-600 
    focus:outline-2 focus:ring-2 focus:outline-rose-600 focus:border-lime-950
    active:border-lime-950 active:ring-2 active:ring-rose-600
    active:outline-2 active:outline-rose-600 active:text-lime-600;
  }

  /* The left border for the summary page expand/collapse button, to separate the text link from the button visually */
  [data-theme*='pale-green'] .summary-expand-btn {
    @apply border-l-lime-300;
  }

  [data-theme*='pale-green'] .active-toc-parent-text {
    @apply text-lime-950 
    hover:bg-lime-300 hover:text-lime-950
    focus:border-rose-600 focus:bg-lime-950 focus:text-lime-50
    active:bg-lime-400 active:text-lime-950;
  }
  [data-theme*='pale-green'] .inactive-toc-parent-text {
    @apply text-slate-950 decoration-lime-950
    hover:bg-lime-300 hover:text-lime-950
    focus:border-rose-600 focus:bg-lime-950 focus:text-lime-50 focus:decoration-lime-50
    active:bg-lime-400 active:text-lime-950 active:decoration-lime-950;
  }
  [data-theme*='pale-green'] .inactive-toc-parent-text + ul a {
    @apply text-lime-950;
  }
  [data-theme*='pale-green'] .active-toc-parent-text + ul a {
    @apply text-lime-950;
  }
  [data-theme*='pale-green'] .tailo-icon-button-active {
    @apply bg-lime-400;
  }
  [data-theme*='pale-green'] .search-highlight {
    @apply bg-yellow-400 text-lime-950;
  }
  [data-theme*='pale-green'] .themed-spinner {
    @apply text-lime-800;
  }
  [data-theme*='pale-green'] .result-item-card {
    @apply bg-green-100 lg:bg-lime-50 border-2 border-transparent hover:bg-lime-300
     active:bg-lime-400 focus-visible:border-rose-600;
  }
  [data-theme*='pale-green'] .explain-this-dropdown {
    @apply bg-lime-300;
  }
  [data-theme*='pale-green'] .loading-response {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .divider {
    @apply bg-lime-300;
  }
  [data-theme*='pale-green'] .result-bg {
    @apply bg-lime-100;
  }
  [data-theme*='pale-green'] .result-text {
    @apply text-lime-800;
  }
  [data-theme*='pale-green'] .feedback-faces {
    @apply stroke-lime-950;
  }
  [data-theme*='pale-green'] .feedback-face-card {
    @apply border-2 border-lime-600 hover:border-lime-700;
  }
  [data-theme*='pale-green'] .feedback-face-card-active {
    @apply ring-2 ring-rose-500 border-2 border-black;
  }
}
