/* The styling won't be included in your compiled CSS unless you actually use it */
/*  *= checks if attribute value contains this value; This way we can set the attribute value to  e.g."drk medium" to accommodate for fonts sizing as well:
   [data-theme*='medium'] .btn-primary {
    @apply text-2xl;
  }
   */

:root {
  --scrollbar-track: transparent;
  --scrollbar-thumb: @apply slate-600;
}

@layer base {
}

@layer components {
  [data-theme*='dark'] .btn {
    @apply focus:border-slate-950 focus:ring-tailo-green-500 focus:hover:border-slate-950;
  }
  [data-theme*='dark'] .btn-primary {
    @apply 
    bg-slate-50 text-slate-900 
    hover:border-transparent hover:bg-slate-300 
    active:bg-slate-400 
    disabled:bg-slate-500 disabled:text-slate-950
    data-[disabled=true]:bg-slate-500 data-[disabled=true]:text-slate-950;
  }

  [data-theme*='dark'] .btn-secondary {
    @apply 
    border-2 border-slate-500 text-slate-50
    hover:border-slate-300
    hover:active:border-slate-50 
    active:border-slate-50
    disabled:text-slate-700  disabled:border-slate-700
    data-[disabled=true]:border-slate-700 data-[disabled=true]:text-slate-700
    disabled:active:border-slate-700;
  }

  [data-theme*='dark'] .btn-tertiary {
    @apply 
    text-slate-50 
    hover:bg-slate-700 
    hover:disabled:bg-transparent
    active:bg-slate-800 active:text-slate-300 
    disabled:text-slate-700 
    data-[disabled=true]:text-slate-700;
  }

  [data-theme*='dark'] .btn-destructive {
    @apply 
    bg-red-600 text-white 
    hover:bg-red-700
    active:bg-red-500 
    disabled:bg-red-300 disabled:text-red-950 
    data-[disabled=true]:bg-red-300 data-[disabled=true]:text-red-950;
  }

  [data-theme*='dark'] .btn-positive {
    @apply 
    bg-green-600 text-white 
    hover:bg-green-700 
    active:bg-green-500 
    disabled:bg-green-300 disabled:text-green-950 
    data-[disabled=true]:bg-green-300 data-[disabled=true]:text-green-950;
  }

  [data-theme*='dark'] .app-navbar-bg,
  [data-theme*='dark'] .highlighted-block {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .doc-bg {
    @apply bg-slate-900;
  }

  [data-theme*='dark'] .separator {
    @apply border-slate-800;
  }
  [data-theme*='dark'] .panel {
    @apply rounded-lg bg-slate-950;
  }
  [data-theme*='dark'] .login-panel {
    @apply rounded-lg bg-slate-900 border-none;
  }
  [data-theme*='dark'] .dropdown-panel {
    @apply bg-slate-950 border-none;
  }
  [data-theme*='dark'] .card {
    @apply bg-slate-800 border-slate-950
    hover:bg-slate-700
    active:bg-slate-950 active:top-[3px]
    transition-all duration-100;
  }
  [data-theme*='dark'] .card .card-link {
    @apply before:focus-visible:ring-4 before:focus-visible:ring-tailo-green-500 before:focus-visible:border-2 before:focus-visible:border-slate-950;
  }
  [data-theme*='dark'] .card-content {
    @apply text-slate-50;
  }
  [data-theme*='dark'] .card-content-subdued {
    @apply text-slate-300;
  }
  [data-theme*='dark'] .card-focus {
    @apply focus-visible:after:ring-tailo-green-500;
  }
  [data-theme*='dark'] .card-icon-container {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .card-icon-body {
    @apply text-slate-50;
  }

  /* Document list item disabled style, inc. children styling */
  [data-theme*='dark'] .card.card-disabled {
    @apply border-slate-800 bg-slate-900 hover:border-slate-800 hover:bg-slate-900 active:top-0;
  }
  [data-theme*='dark'] .card-disabled .card-icon-container {
    @apply bg-slate-700;
  }
  [data-theme*='dark'] .card-disabled .card-icon-body {
    @apply text-slate-900;
  }
  [data-theme*='dark'] .card-disabled .card-content,
  [data-theme*='dark'] .card-disabled .card-content-subdued {
    @apply text-slate-700;
  }
  [data-theme*='dark'] .onboarding-page {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .selected-btn {
    @apply border-4 !border-slate-50;
  }
  [data-theme*='dark'] .selected-file-panel {
    @apply border-slate-800 bg-slate-900;
  }
  [data-theme*='dark'] .account-btn {
    @apply bg-slate-50 text-slate-900 hover:bg-slate-300 active:bg-slate-400;
  }
  [data-theme*='dark'] .modal-bg {
    @apply bg-slate-900 text-white;
  }
  [data-theme*='dark'] .modal-overlay {
    background-color: rgba(0, 0, 0, 0.75);
  }
  [data-theme*='dark'] .custom-srollbar {
    @apply scrollbar scrollbar-track-slate-950 scrollbar-thumb-slate-950;
  }
  [data-theme*='dark'] .bottom-sheet-scrollbar {
    @apply mr-1.5 scrollbar scrollbar-thumb-slate-600 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='dark'] .doc-scrollbar {
    @apply scrollbar scrollbar-thumb-slate-600 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='dark'] .pill-positive {
    @apply bg-green-500 bg-opacity-50 text-green-50;
  }
  [data-theme*='dark'] .pill-negative {
    @apply bg-rose-500 bg-opacity-50 text-rose-50;
  }
  [data-theme*='dark'] .pill-processing {
    @apply bg-sky-500 bg-opacity-50 text-sky-50;
  }
  [data-theme*='dark'] .pill-info {
    @apply bg-sky-500 bg-opacity-50 text-sky-50;
  }
  [data-theme*='dark'] .pill-neutral {
    @apply bg-slate-50 text-slate-900;
  }

  [data-theme*='dark'] .dashboard-placeholder {
    @apply bg-slate-200 bg-opacity-10;
  }
  [data-theme*='dark'] .disabled-label-text {
    @apply text-slate-300;
  }

  [data-theme*='dark'] .message-panel {
    @apply bg-slate-200 bg-opacity-10;
  }
  [data-theme*='dark'] .message-bg-positive {
    @apply bg-green-800 text-green-50;
  }
  [data-theme*='dark'] .message-bg-negative {
    @apply bg-red-800 text-red-50;
  }
  [data-theme*='dark'] .message-bg-info {
    @apply bg-sky-800 text-sky-50;
  }
  [data-theme*='dark'] .input {
    @apply 
    border-slate-500 text-slate-50 
    placeholder:text-slate-400
    hover:border-slate-300
    hover:active:border-slate-50 
    active:border-slate-50
    disabled:text-slate-300 disabled:border-slate-600 disabled:bg-slate-700
    disabled:active:border-slate-600    
    checked:border-slate-50 checked:bg-slate-50 checked:text-transparent 
    focus:border-slate-50 focus:ring-tailo-green-500 
  }
  /* Native HTML <select> component options list bg */
  [data-theme*='dark'] .input optgroup,
  [data-theme*='dark'] .input option {
    @apply bg-slate-700
  }
  /* Custom <select> component options list bg */
  [data-theme*='dark'] .form-dropdown-select {
    @apply border-slate-700 bg-slate-700 text-slate-50;
  }
  [data-theme*='dark'] .input:is(:-webkit-autofill, :autofill) {
    @apply ring-slate-50;
  }
  [data-theme*='dark'] .input:is(:-webkit-autofill, :autofill) {
    @apply ring-slate-50;
  }
  [data-theme*='dark'] .form-input-success {
    @apply border-tailo-green-400;
  }
  [data-theme*='dark'] .form-error {
    @apply bg-red-700;
  }
  [data-theme*='dark'] .form-error-border {
    @apply border-red-700;
  }

  [data-theme*='dark'] .context-menu {
    @apply bg-slate-800 border-slate-700
  }
  [data-theme*='dark'] .context-menu-options {
    @apply divide-slate-700;
  }

  /* Checkbox Toggle Control */
  [data-theme*='dark'] input[type="checkbox"] ~ .toggle-control {
    @apply bg-slate-950 border-slate-500 after:bg-slate-100 after:border-slate-950;
  }
  [data-theme*='dark'] input[type="checkbox"]:checked ~ .toggle-control {
    @apply bg-slate-100 after:bg-slate-950;
  }

  [data-theme*='dark'] .nav-item {
    @apply bg-slate-50 text-slate-900;
  }
  [data-theme*='dark'] .upload-area {
    @apply border-dashed border-slate-500 outline-none hover:border-slate-300 focus-visible:border-none focus-visible:ring-4 focus-visible:ring-tailo-green-500;
  }
  [data-theme*='dark'] .upload-area-drag {
    @apply border-slate-300;
  }
  [data-theme*='dark'] .mobile-step-indicator {
    @apply bg-slate-50 text-slate-950;
  }
  [data-theme*='dark'] .mobile-step-indicator-inactive {
    @apply bg-slate-500 text-blue-50;
  }
  /* ONBOARDING DESKTOP STEP: default/current */
  [data-theme*='dark'] .desktop-onboarding-step-link .inner-outline {
    @apply fill-slate-50 stroke-slate-50;
  }
  [data-theme*='dark']
    .desktop-onboarding-step-link:focus-visible
    .outer-outline {
    @apply stroke-green-500;
  }
  /* ONBOARDING DESKTOP STEP: disabled  */
  [data-theme*='dark'] .desktop-onboarding-step-link.disabled .inner-outline {
    @apply fill-none stroke-slate-700;
  }
  /* ONBOARDING DESKTOP STEP: completed  */
  [data-theme*='dark'] .desktop-onboarding-step-link.completed .inner-outline {
    @apply fill-none stroke-slate-50;
  }
  [data-theme*='dark']
    .desktop-onboarding-step-link.completed:hover
    .inner-outline {
    @apply stroke-slate-400;
  }
  [data-theme*='dark']
    .desktop-onboarding-step-link.completed:active
    .inner-outline {
    @apply stroke-slate-600;
  }
  [data-theme*='dark'] .nav-from-gradient {
    @apply from-slate-900;
  }
  [data-theme*='dark'] .verify-spinner {
    @apply text-tailo-green-500;
  }
  [data-theme*='dark'] .toggle {
    @apply border-slate-500 bg-slate-50 focus:outline-2 focus:outline-tailo-green-500;
    --tglbg: #020617;
  }
  [data-theme*='dark'] .toggle:checked {
    @apply border-slate-500 bg-slate-950;
    --tglbg: #f8fafc;
  }
  [data-theme*='dark'] .focus-outer {
    @apply focus:border-4 focus:border-tailo-green-500 focus:bg-slate-50 focus:text-slate-900;
  }
  [data-theme*='dark'] .definition-part-of-speech {
    @apply bg-slate-800;
  }
  [data-theme*='dark'] .definition-example {
    @apply bg-slate-900 text-slate-300;
  }
  [data-theme*='dark'] .progress-bar-rail {
    @apply bg-slate-600;
  }
  [data-theme*='dark'] .progress-bar-rail::-webkit-progress-value {
    @apply bg-slate-50;
  }
  [data-theme*='dark'] .accordion:hover .accordion-title {
    @apply bg-slate-700;
  }
  [data-theme*='dark'] .accordion-title-active {
    @apply bg-slate-800;
  }
}

@layer utilities {
  [data-theme*='dark'] .app-bg {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .login-app-bg {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .logo-fill-bg {
    @apply bg-slate-950 fill-slate-950;
  }
  [data-theme*='dark'] .app-bg-subdued {
    @apply bg-slate-800;
  }
  [data-theme*='dark'] .theme-border {
    @apply border border-slate-800;
  }
  [data-theme*='dark'] .theme-border-subdued {
    @apply border border-slate-700;
  }
  [data-theme*='dark'] .theme-border-t {
    @apply border-t-slate-800;
  }
  [data-theme*='dark'] .theme-border-l {
    @apply border-l border-l-slate-800;
  }
  [data-theme*='dark'] .theme-warning {
    @apply text-red-500;
  }
  /* Used for delete text in dropdowns */
  [data-theme*='dark'] .theme-delete {
    @apply text-red-400;
  }
  [data-theme*='dark'] .icon-bg {
    @apply bg-slate-800;
  }
  [data-theme*='dark'] .opposite-bg {
    @apply bg-slate-50;
  }
  [data-theme*='dark'] .doc-text-default {
    @apply text-slate-50;
  }
  [data-theme*='dark'] .doc-text-inactive {
    @apply text-slate-500;
  }
  [data-theme*='dark'] .doc-text-subdued {
    @apply text-slate-300;
  }
  [data-theme*='dark'] .doc-text-inverted {
    @apply text-slate-950;
  }
  [data-theme*='dark'] .opposite-border,
  [data-theme*='dark'] .reading-marker-border {
    @apply border-slate-50;
  }
  [data-theme*='dark'] .onboarding-document-preview-container,
  [data-theme*='dark'] .expand-preview-btn-container > button {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .onboarding-document-preview {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .onboarding-doc-pop-out {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .default-progress-bar {
    @apply border-slate-700 bg-slate-900;
  }
  [data-theme*='dark'] .default-progress-bar-upload {
    @apply border-0 bg-slate-700;
  }
  [data-theme*='dark'] .default-progress-bar-icon {
    @apply text-slate-50;
  }
  [data-theme*='dark'] .bg-default {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .svg-default {
    @apply fill-slate-50;
  }
  [data-theme*='dark'] .doc-link-default {
    @apply bg-slate-800 hover:bg-slate-700 focus-visible:bg-slate-50 active:bg-slate-50
     visited:bg-slate-950 text-slate-50 focus-visible:text-slate-900 active:text-slate-900
    visited:text-slate-300 rounded-sm border-b border-slate-400 hover:border-slate-50 
    focus-visible:border-slate-50 active:border-slate-50 visited:border-slate-50 font-semibold
      outline-none focus-visible:ring  ring-tailo-green-500;
  }

  /* Applied only when theme is in focus */
  [data-theme*='dark'] .theme-selected {
    @apply ring-tailo-green-500;
  }

  /* Applied when current theme is in use */
  [data-theme*='dark'] .selected {
    @apply ring-white ring-offset-2 ring-offset-slate-900;
  }

  /* Table styling */

  [data-theme*='dark'] .table-header-bg {
    @apply bg-slate-800;
  }

  [data-theme*='dark'] .table-column-bg {
    @apply border-slate-950;
  }
  [data-theme*='dark'] .table-cell-border {
    @apply border-slate-600;
  }

  [data-theme*='dark'] .document-card-hover {
    @apply bg-slate-800;
  }
  [data-theme*='dark'] .svg-stroke {
    @apply stroke-slate-950;
  }
  /* Shadows */
  [data-theme*='dark'] .shadow-sm-d {
    box-shadow: var(--shadow-sm-d) var(--shadow-color-secondary);
  }
  [data-theme*='dark'] .shadow-d {
    box-shadow: var(--shadow-d) var(--shadow-color-base),
      var(--shadow-d-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-md-d {
    box-shadow: var(--shadow-md-d) var(--shadow-color-base),
      var(--shadow-md-d-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-lg-d {
    box-shadow: var(--shadow-lg-d) var(--shadow-color-base),
      var(--shadow-lg-d-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-xl-d {
    box-shadow: var(--shadow-xl-d) var(--shadow-color-base),
      var(--shadow-xl-d-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-2xl-d {
    box-shadow: var(--shadow-2xl-d) rgba(0, 0, 0, 0.25);
  }
  [data-theme*='dark'] .shadow-sm-u {
    box-shadow: var(--shadow-sm-u) var(--shadow-color-secondary);
  }
  [data-theme*='dark'] .shadow-u {
    box-shadow: var(--shadow-u) var(--shadow-color-base),
      var(--shadow-u-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-md-u {
    box-shadow: var(--shadow-md-u) var(--shadow-color-base),
      var(--shadow-md-u-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-lg-u {
    box-shadow: var(--shadow-lg-u) var(--shadow-color-base),
      var(--shadow-lg-u-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-xl-u {
    box-shadow: var(--shadow-xl-u) var(--shadow-color-base),
      var(--shadow-xl-u-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-2xl-u {
    box-shadow: var(--shadow-2xl-u) rgba(0, 0, 0, 0.25);
  }
  [data-theme*='dark'] .shadow-l {
    box-shadow: var(--shadow-l) var(--shadow-color-base),
      var(--shadow-l-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-md-l {
    box-shadow: var(--shadow-md-l) var(--shadow-color-base),
      var(--shadow-md-l-2) var(--shadow-color-base);
  }
  [data-theme*='dark'] .shadow-solid-d {
    box-shadow: var(--shadow-solid-d) #020617; /*slate-950*/
  }
  [data-theme*='dark'] .shadow-solid-md-d {
    box-shadow: var(--shadow-solid-md-d) #020617; /*slate-950*/
  }
  /* Used on elements imitating focus ring - tabs nav on mobile */
  [data-theme*='dark'] .focus-border {
    @apply border-green-500;
  }
  [data-theme*='dark'] .text-disabled {
    @apply text-slate-700;
  }
  [data-theme*='dark'] .app-bg-tertiary {
    @apply bg-slate-700;
  }
  [data-theme*='dark'] .break-line {
    @apply bg-slate-900 opacity-25;
  }
  [data-theme*='dark'] .break-line {
    @apply bg-slate-900 opacity-25;
  }
  [data-theme*='dark'] .bg-gradient {
    @apply bg-gradient-to-t from-slate-900;
  }
  [data-theme*='dark'] .panel-bg {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .drawer-bg {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .bottom-sheet-top-nav {
    @apply bg-slate-800 border-b border-slate-700;
  }

  /* Separate classes needed for the select-dropdown due to focus style beng set dynamically via internal props */
  [data-theme*='dark'] .select-ring {
    @apply ring-tailo-green-500;
  }
  [data-theme*='dark'] .select-option-focus {
    @apply bg-slate-800;
  }
  [data-theme*='dark'] .focus-outline {
    @apply outline outline-[3px] outline-tailo-green-500;
  }

  [data-theme*='dark'] .slider-text {
    @apply text-sm font-semibold text-slate-50;
  }

  [data-theme*='dark'] .slider-value {
    @apply mt-1 flex justify-between text-xs text-slate-50;
  }
  [data-theme*='dark'] .slider-dot {
    @apply bg-slate-400 opacity-50;
  }
  [data-theme*='dark'] .slider-dot-active {
    @apply border border-slate-50 bg-slate-50;
  }
  [data-theme*='dark'] .slider-handle {
    @apply border-slate-50 bg-slate-50 hover:bg-slate-50;
  }
  [data-theme*='dark'] .slider-handle-dragging {
    @apply border-none bg-slate-50 ring-4 ring-slate-50/25;
  }
  [data-theme*='dark'] .slider-handle-focus {
    @apply bg-slate-50 ring-4 ring-slate-50/25;
  }
  [data-theme*='dark'] .slider-rail {
    @apply bg-slate-600;
  }
  [data-theme*='dark'] .slider-track {
    @apply bg-slate-50;
  }
  [data-theme*='dark'] .tool-tip {
    @apply bg-slate-50 text-slate-900;
  }
  [data-theme*='dark'] .controls-container {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .text-highlight-word-definition {
    @apply bg-blue-700 text-slate-50;
  }
  [data-theme*='dark'] .active-tts-paragraph {
    @apply bg-slate-50 text-slate-900 px-2 pt-2 rounded-xl;
  }
  [data-theme*='dark'] .tts-progress-bar {
    @apply w-full [&::-webkit-progress-bar]:bg-slate-500
      [&::-webkit-progress-value]:bg-slate-200;
  }
  [data-theme*='dark'] .overview-gradient-border {
    @apply bg-gradient-to-br from-lime-400 via-pink-400 to-blue-400 rounded-2xl p-[2px];
  }
  [data-theme*='dark'] .ai-gradient-border {
    @apply bg-gradient-to-br from-lime-400 via-pink-400 to-blue-400 rounded-md p-[1px];
  }
  [data-theme*='dark'] .ai-gradient-border.btn-secondary {
    /* Support button states whilst maintaining the rainbow border */
    @apply border-0 p-[2px];
  }
  [data-theme*='dark'] .quick-summary-container {
    @apply h-full w-full bg-slate-950 rounded-2xl px-6 py-5;
  }
  [data-theme*='dark'] .ai-notice-container {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .ai-underline {
    @apply underline decoration-slate-400;
  }
  [data-theme*='dark'] .reading-time {
    @apply bg-slate-700;
  }
  [data-theme*='dark'] .welcome-banner button {
    @apply border-slate-950 bg-slate-950 
      hover:bg-slate-800 
      focus-visible:outline-tailo-green-500
      active:bg-slate-900;
  }
  [data-theme*='dark'] .welcome-banner ul svg {
    @apply text-tailo-green-500;
  }
  [data-theme*='dark'] .document-wrapper {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .document-viewer-background {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .closed-right-drawer {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .summary-body {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .reading-environment-wrapper {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .toc-items-text {
    @apply text-slate-400;
  }
  [data-theme*='dark'] .toc-parent-line {
    @apply w-8 h-0.5 bg-slate-400;
  }
  [data-theme*='dark'] .toc-child-line {
    @apply w-6 h-0.5 bg-slate-400;
  }
  [data-theme*='dark'] .toc-grandcchild-line {
    @apply w-4 h-0.5 bg-slate-400;
  }
  [data-theme*='dark'] .toc-active-element {
    @apply bg-slate-50;
  }
  [data-theme*='dark'] .nav-border {
    @apply border-b-2 border-slate-800;
  }
  [data-theme*='dark'] .nav-bg {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .nav-underline {
    @apply bg-slate-50;
  }
  [data-theme*='dark'] .nav-content-styles {
    @apply bg-slate-900 border-t-2 border-slate-800 p-2 lg:bg-slate-950 lg:relative lg:border-none;
  }
  [data-theme*='dark'] .toc-background {
    @apply bg-slate-900/70 backdrop-blur-sm z-50 rounded-tr-lg rounded-br-lg;
  }
  [data-theme*='dark'] .dropdown-nav {
    @apply bg-slate-950 border-slate-600;
  }
  [data-theme*='dark'] .user-svg-icon {
    fill: #f8fafc;
  }
  [data-theme*='dark'] .dashboard-bg {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .documents-panel {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .mobile-dashboard-bg {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .mobile-welcome-panel {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .mobile-document-body {
    @apply bg-slate-900;
  }
  [data-theme*='dark'] .mobile-extraction-message {
    @apply bg-slate-950;
  }
  [data-theme*='dark'] .small-tailo-logo {
    @apply hover:text-slate-300  
    focus:border-slate-100 focus:ring-2 focus:ring-tailo-green-500
    focus:outline-2 focus:outline-tailo-green-500
    active:border-slate-100 active:ring-2 active:ring-tailo-green-500
    active:outline-2 active:outline-tailo-green-500 active:text-slate-400;
  }

  /* The left border for the summary page expand/collapse button, to separate the text link from the button visually */
  [data-theme*='dark'] .summary-expand-btn {
    @apply border-l-slate-800;
  }

  /* The left border for the summary page expand/collapse button, to separate the text link from the button visually */
  [data-theme*='dark'] .summary-expand-btn {
    @apply border-l-slate-800;
  }

  [data-theme*='dark'] .active-toc-parent-text {
    @apply 
    text-slate-50
    hover:bg-slate-800 hover:text-slate-50
    focus:border-green-500 focus:bg-slate-50 focus:text-slate-900
    active:bg-slate-700 active:text-slate-50;
  }
  [data-theme*='dark'] .inactive-toc-parent-text {
    @apply 
    decoration-slate-50
    hover:bg-slate-800 hover:text-slate-50
    focus:border-tailo-green-500 focus:bg-slate-50 focus:text-slate-900 focus:decoration-slate-900
    active:bg-slate-700 active:text-slate-50 
    active:decoration-slate-50;
  }
  [data-theme*='dark'] .inactive-toc-parent-text + ul a {
    @apply 
    text-slate-50;
  }
  [data-theme*='dark'] .active-toc-parent-text + ul a {
    @apply text-slate-50;
  }


  [data-theme*='dark'] .tailo-icon-button-active {
    @apply bg-slate-700;
  }
  [data-theme*='dark'] .search-highlight {
    @apply bg-yellow-400 text-slate-900;
  }
  [data-theme*='dark'] .themed-spinner {
    @apply text-slate-500;
  }
  [data-theme*='dark'] .result-item-card {
    @apply bg-slate-800 border-2 border-transparent hover:bg-slate-600
      active:bg-slate-950 focus-visible:border-2 focus-visible:border-tailo-green-500;
  }
  [data-theme*='dark'] .selected-result-item-card {
    @apply bg-slate-950 border-2 border-transparent border-slate-50;
  }
  [data-theme*='dark'] .explain-this-dropdown {
    @apply bg-slate-800;
  }
  [data-theme*='dark'] .loading-response {
    @apply bg-slate-800;
  }
  [data-theme*='dark'] .divider {
    @apply bg-slate-700;
  }
  [data-theme*='dark'] .result-bg {
    @apply bg-slate-700;
  }
  [data-theme*='dark'] .result-text {
    @apply text-slate-300;
  }
  [data-theme*='dark'] .feedback-faces {
    @apply stroke-white;
  }
  [data-theme*='dark'] .feedback-face-card {
    @apply border-2 border-slate-500 hover:border-slate-300;
  }
  [data-theme*='dark'] .feedback-face-card-active {
    @apply ring-2 ring-tailo-green-500 border-2 border-slate-50
    bg-slate-800;
  }
}
