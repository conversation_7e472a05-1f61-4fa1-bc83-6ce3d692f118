/* [data-theme*='pale-blue'] .custom-class { }*/

:root {
  --scrollbar-track: transparent;
  --scrollbar-thumb: @apply blue-300;
}

@layer base {
}
@layer components {
  [data-theme*='pale-blue'] .btn {
    @apply focus:border-blue-100 focus:ring-rose-600 focus:hover:border-blue-100;
  }

  [data-theme*='pale-blue'] .btn-primary {
    @apply bg-blue-950 text-blue-50 
    hover:border-transparent hover:bg-blue-800 
    active:bg-blue-700 
    disabled:bg-blue-300 disabled:text-blue-800 
    data-[disabled=true]:bg-blue-300 data-[disabled=true]:text-blue-800;
  }

  [data-theme*='pale-blue'] .btn-secondary {
    @apply border-2 border-blue-950 text-blue-950 
    hover:border-blue-700 
    hover:active:border-blue-500
    active:border-blue-500 
    disabled:border-blue-300 disabled:text-blue-700 
    data-[disabled=true]:border-blue-300 data-[disabled=true]:text-blue-700
    disabled:active:border-blue-300;
  }

  [data-theme*='pale-blue'] .btn-tertiary {
    @apply text-blue-950 
    hover:bg-blue-300 
    hover:disabled:bg-transparent
    active:bg-blue-400
    disabled:text-blue-700 
    data-[disabled=true]:text-blue-700;
  }

  [data-theme*='pale-blue'] .btn-positive {
    @apply bg-green-600 text-white 
    hover:bg-green-700 
    active:bg-green-500 
    disabled:bg-green-100 disabled:text-green-700 
    data-[disabled=true]:bg-green-100 data-[disabled=true]:text-green-700;
  }

  [data-theme*='pale-blue'] .btn-destructive {
    @apply bg-red-600 text-white 
    hover:bg-red-700 
    active:bg-red-500 
    disabled:bg-red-100 disabled:text-red-700 
    data-[disabled=true]:bg-red-100 data-[disabled=true]:text-red-700;
  }

  [data-theme*='pale-blue'] .onboarding-page {
    @apply bg-blue-50;
  }
  [data-theme*='pale-blue'] .pill-positive {
    @apply bg-green-400 text-green-950;
  }

  [data-theme*='pale-blue'] .pill-negative {
    @apply bg-rose-300 text-rose-950;
  }

  [data-theme*='pale-blue'] .pill-info {
    @apply bg-sky-400 text-sky-950;
  }

  [data-theme*='pale-blue'] .pill-neutral {
    @apply bg-blue-950 text-blue-50;
  }
  [data-theme*='pale-blue'] .input {
    @apply border-blue-400 text-blue-950 
    placeholder:text-blue-700
    hover:border-blue-500
    hover:active:border-blue-900 
    active:border-blue-900
    disabled:text-blue-800 disabled:border-blue-400 disabled:bg-blue-300
    disabled:active:border-blue-400    
    checked:border-blue-950 checked:bg-blue-950
    focus:border-blue-950 focus:ring-rose-600;
  }
  /* Native HTML <select> component options list bg */
  [data-theme*='pale-blue'] .input optgroup,
  [data-theme*='pale-blue'] .input option {
    @apply bg-blue-100;
  }
  /* Custom <select> component options list bg */
  [data-theme*='pale-blue'] .form-dropdown-select {
    @apply border-blue-100 bg-blue-100 text-blue-950;
  }
  [data-theme*='pale-blue'] .input:is(:-webkit-autofill, :autofill) {
    @apply ring-slate-950;
  }
  [data-theme*='pale-blue'] .form-input-success {
    @apply border-green-600;
  }

  [data-theme*='pale-blue'] .form-error {
    @apply bg-red-700;
  }

  [data-theme*='pale-blue'] .form-error-border {
    @apply border-red-700;
  }

  [data-theme*='pale-blue'] .context-menu {
    @apply bg-blue-200 border-blue-300;
  }
  [data-theme*='pale-blue'] .context-menu-options {
    @apply divide-blue-300;
  }

  /* Checkbox Toggle Control */
  [data-theme*='pale-blue'] input[type="checkbox"] ~ .toggle-control {
    @apply bg-blue-100 border-blue-400 after:bg-blue-950 after:border-blue-100;
  }
  [data-theme*='pale-blue'] input[type="checkbox"]:checked ~ .toggle-control {
    @apply bg-blue-950 after:bg-blue-100;
  }  

  [data-theme*='pale-blue'] .app-navbar-bg,
  [data-theme*='pale-blue'] .highlighted-block {
    @apply bg-blue-200;
  }

  [data-theme*='pale-blue'] .doc-bg {
    @apply bg-blue-200;
  }

  [data-theme*='pale-blue'] .separator {
    @apply border-blue-300;
  }
  [data-theme*='pale-blue'] .panel {
    @apply rounded-lg bg-blue-100;
  }
  [data-theme*='pale-blue'] .dropdown-panel {
    @apply bg-blue-100 border-blue-300;
  }
  [data-theme*='pale-blue'] .nav-item {
    @apply bg-blue-950 text-blue-200;
  }
  [data-theme*='pale-blue'] .account-btn {
    @apply bg-blue-950 text-blue-200;
  }
  [data-theme*='pale-blue'] .modal-bg {
    @apply bg-blue-50;
  }
  [data-theme*='pale-blue'] .bottom-sheet-scrollbar {
    @apply mr-1.5 scrollbar scrollbar-thumb-blue-300 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='pale-blue'] .doc-scrollbar {
    @apply scrollbar scrollbar-thumb-blue-300 scrollbar-thumb-rounded-full scrollbar-w-1.5;
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  [data-theme*='pale-blue'] .upload-area {
    @apply border-dashed border-blue-950 outline-none hover:border-blue-800 focus-visible:border-none focus-visible:ring-4 focus-visible:ring-rose-600;
  }
  [data-theme*='pale-blue'] .upload-area-drag {
    @apply border-rose-800;
  }
  [data-theme*='pale-blue'] .default-progress-bar {
    @apply border-blue-700 bg-blue-50;
  }
  [data-theme*='pale-blue'] .default-progress-bar-upload {
    @apply border-blue-700 bg-blue-300;
  }
  [data-theme*='pale-blue'] .default-progress-bar-icon {
    @apply text-blue-950;
  }
  [data-theme*='pale-blue'] .message-bg-positive {
    @apply bg-green-400 text-green-950;
  }
  [data-theme*='pale-blue'] .message-bg-negative {
    @apply bg-red-300 text-red-950;
  }
  [data-theme*='pale-blue'] .message-bg-info {
    @apply bg-sky-300 text-sky-950;
  }
  [data-theme*='pale-blue'] .card {
    @apply bg-blue-50 border-blue-500
    hover:bg-blue-100
    active:bg-blue-300 active:top-[3px]
    transition-all duration-100;
  }
  [data-theme*='pale-blue'] .card .card-link {
    @apply before:focus-visible:ring-rose-600 before:focus-visible:border-blue-950;
  }
  [data-theme*='pale-blue'] .card-content {
    @apply text-blue-950;
  }
  [data-theme*='pale-blue'] .card-content-subdued {
    @apply text-blue-800;
  }
  [data-theme*='pale-blue'] .card-focus {
    @apply focus-visible:after:ring-rose-600;
  }
  [data-theme*='pale-blue'] .card-icon-container {
    @apply bg-blue-950;
  }
  [data-theme*='pale-blue'] .card-icon-body {
    @apply text-blue-50;
  }
  /* Document list item disabled style, inc. children styling */
  [data-theme*='pale-blue'] .card.card-disabled {
    @apply border-blue-400 bg-blue-200 hover:border-blue-400 hover:bg-blue-200 active:top-0;
  }
  [data-theme*='pale-blue'] .card-disabled .card-icon-container {
    @apply bg-blue-800;
  }
  [data-theme*='pale-blue'] .card-disabled .card-icon-body {
    @apply text-blue-200;
  }
  [data-theme*='pale-blue'] .card-disabled .card-content,
  [data-theme*='pale-blue'] .card-disabled .card-content-subdued {
    @apply text-blue-800;
  }
  [data-theme*='pale-blue'] .mobile-step-indicator {
    @apply bg-blue-950 text-blue-50;
  }
  [data-theme*='pale-blue'] .mobile-step-indicator-inactive {
    @apply bg-blue-600 text-blue-50;
  }
  /* ONBOARDING DESKTOP STEP: default/current */
  [data-theme*='pale-blue'] .desktop-onboarding-step-link .inner-outline {
    @apply fill-blue-950 stroke-blue-950;
  }
  [data-theme*='pale-blue']
    .desktop-onboarding-step-link:focus-visible
    .outer-outline {
    @apply stroke-rose-600;
  }
  /* ONBOARDING DESKTOP STEP: disabled  */
  [data-theme*='pale-blue']
    .desktop-onboarding-step-link.disabled
    .inner-outline {
    @apply fill-none stroke-blue-300;
  }
  /* ONBOARDING DESKTOP STEP: completed  */
  [data-theme*='pale-blue']
    .desktop-onboarding-step-link.completed
    .inner-outline {
    @apply fill-none stroke-blue-950;
  }
  [data-theme*='pale-blue']
    .desktop-onboarding-step-link.completed:hover
    .inner-outline {
    @apply stroke-blue-700;
  }
  [data-theme*='pale-blue']
    .desktop-onboarding-step-link.completed:active
    .inner-outline {
    @apply stroke-blue-500;
  }
  [data-theme*='pale-blue'] .nav-from-gradient {
    @apply from-blue-50;
  }
  [data-theme*='pale-blue'] .verify-spinner {
    @apply text-blue-950;
  }
  [data-theme*='pale-blue'] .toggle {
    @apply border-blue-400 bg-blue-950 focus:outline-2 focus:outline-rose-600;
    --tglbg: #dbeafe;
  }
  [data-theme*='pale-blue'] .toggle:checked {
    @apply border-blue-400 bg-blue-100;
    --tglbg: #172554;
  }
  [data-theme*='pale-blue'] .focus-outer {
    @apply outline-none focus-visible:border-2 focus-visible:border-rose-600;
  }
  [data-theme*='pale-blue'] .definition-part-of-speech {
    @apply bg-blue-100 text-blue-950;
  }
  [data-theme*='pale-blue'] .definition-example {
    @apply bg-blue-200 text-blue-800;
  }
  [data-theme*='pale-blue'] .progress-bar-rail {
    @apply bg-blue-400;
  }
  [data-theme*='pale-blue'] .progress-bar-rail::-webkit-progress-value {
    @apply bg-blue-950;
  }
  [data-theme*='pale-blue'] .accordion:hover .accordion-title {
    @apply bg-blue-300;
  }
  [data-theme*='pale-blue'] .accordion-title-active {
    @apply bg-blue-400;
  }
}
@layer utilities {
  [data-theme*='pale-blue'] .app-bg {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .logo-fill-bg {
    @apply bg-blue-100 fill-blue-100;
  }
  [data-theme*='pale-blue'] .app-bg-subdued {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .theme-border {
    @apply border border-blue-300;
  }
  [data-theme*='pale-blue'] .theme-border-subdued {
    @apply border border-blue-200;
  }
  [data-theme*='pale-blue'] .theme-border-t {
    @apply border-t-blue-300;
  }
  [data-theme*='pale-blue'] .theme-border-l {
    @apply border-l border-l-blue-300;
  }
  [data-theme*='pale-blue'] .theme-warning {
    @apply text-red-500;
  }
  [data-theme*='pale-blue'] .theme-delete {
    @apply text-red-700;
  }

  [data-theme*='pale-blue'] .opposite-bg {
    @apply bg-blue-950;
  }
  [data-theme*='pale-blue'] .opposite-border,
  [data-theme*='pale-blue'] .reading-marker-border {
    @apply border-blue-950;
  }
  [data-theme*='pale-blue'] .onboarding-document-preview-container,
  [data-theme*='pale-blue'] .expand-preview-btn-container > button {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .onboarding-document-preview {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .onboarding-doc-pop-out {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .selected-btn {
    @apply border-4 !border-blue-950;
  }
  [data-theme*='pale-blue'] .svg-stroke {
    @apply stroke-blue-100;
  }
  [data-theme*='pale-blue'] .doc-text-default {
    @apply text-blue-950;
  }
  [data-theme*='pale-blue'] .doc-text-inactive {
    @apply text-blue-700;
  }

  [data-theme*='pale-blue'] .doc-text-subdued {
    @apply text-blue-800;
  }
  [data-theme*='pale-blue'] .doc-text-inverted {
    @apply text-blue-50;
  }
  [data-theme*='pale-blue'] .theme-selected {
    @apply ring-rose-600;
  }
  [data-theme*='pale-blue'] .selected {
    @apply ring-blue-950 ring-offset-2 ring-offset-blue-100;
  }
  [data-theme*='pale-blue'] .bg-default {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .svg-default {
    @apply fill-blue-950;
  }
  [data-theme*='pale-blue'] .doc-link-default {
    @apply bg-blue-200 hover:bg-blue-300 focus-visible:bg-blue-950 active:bg-blue-950 visited:bg-blue-100 text-blue-950 focus-visible:text-blue-50 active:text-blue-50 visited:text-blue-700 rounded-sm border-b border-blue-600 hover:border-blue-950 focus-visible:border-blue-950 active:border-blue-950 visited:border-blue-950 font-semibold outline-none focus-visible:ring ring-rose-600;
  }

  /* Shadows */
  [data-theme*='pale-blue'] .shadow-sm-d {
    box-shadow: var(--shadow-sm-d) rgba(23, 37, 84, 0.05);
  }
  [data-theme*='pale-blue'] .shadow-d {
    box-shadow: var(--shadow-d) var(--shadow-color-base-pale-blue),
      var(--shadow-d-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-md-d {
    box-shadow: var(--shadow-md-d) var(--shadow-color-base-pale-blue),
      var(--shadow-md-d-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-lg-d {
    box-shadow: var(--shadow-lg-d) var(--shadow-color-base-pale-blue),
      var(--shadow-lg-d-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-xl-d {
    box-shadow: var(--shadow-xl-d) var(--shadow-color-base-pale-blue),
      var(--shadow-xl-d-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-2xl-d {
    box-shadow: var(--shadow-2xl-d) rgba(23, 37, 84, 0.25);
  }
  [data-theme*='pale-blue'] .shadow-sm-u {
    box-shadow: var(--shadow-sm-u) rgba(23, 37, 84, 0.05);
  }
  [data-theme*='pale-blue'] .shadow-u {
    box-shadow: var(--shadow-u) var(--shadow-color-base-pale-blue),
      var(--shadow-u-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-md-u {
    box-shadow: var(--shadow-md-u) var(--shadow-color-base-pale-blue),
      var(--shadow-md-u-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-lg-u {
    box-shadow: var(--shadow-lg-u) var(--shadow-color-base-pale-blue),
      var(--shadow-lg-u-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-xl-u {
    box-shadow: var(--shadow-xl-u) var(--shadow-color-base-pale-blue),
      var(--shadow-xl-u-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-2xl-u {
    box-shadow: var(--shadow-2xl-u) rgba(23, 37, 84, 0.25);
  }
  [data-theme*='pale-blue'] .shadow-l {
    box-shadow: var(--shadow-l) var(--shadow-color-base-pale-blue),
      var(--shadow-l-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-md-l {
    box-shadow: var(--shadow-md-l) var(--shadow-color-base-pale-blue),
      var(--shadow-md-l-2) var(--shadow-color-base-pale-blue);
  }
  [data-theme*='pale-blue'] .shadow-solid-d {
    box-shadow: var(--shadow-solid-d) #2563eb; /*blue-600*/
  }
  [data-theme*='pale-blue'] .shadow-solid-md-d {
    box-shadow: var(--shadow-solid-md-d) #2563eb; /*blue-600*/
  }

  /* Table styling */

  [data-theme*='pale-blue'] .table-header-bg {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .table-column-bg {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .table-cell-border {
    @apply border-blue-300;
  }
  /* Used on elements imitating focus ring - tabs nav on mobile */
  [data-theme*='pale-blue'] .focus-border {
    @apply border-rose-600;
  }
  [data-theme*='pale-blue'] .text-disabled {
    @apply text-blue-800;
  }
  [data-theme*='pale-blue'] .app-bg-tertiary {
    @apply bg-sky-300;
  }
  [data-theme*='pale-blue'] .break-line {
    @apply bg-sky-300 opacity-50;
  }
  [data-theme*='pale-blue'] .bg-gradient {
    @apply bg-gradient-to-t from-blue-200;
  }
  [data-theme*='pale-blue'] .panel-bg {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .drawer-bg {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .bottom-sheet-top-nav {
    @apply bg-blue-200 border-b border-blue-300;
  }
  /* Separate classes needed for the select-dropdown due to focus style beng set dynamically via internal props */
  [data-theme*='pale-blue'] .select-ring {
    @apply ring-rose-600;
  }
  [data-theme*='pale-blue'] .select-option-focus {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .focus-outline {
    @apply outline outline-[3px] outline-rose-600;
  }
  [data-theme*='pale-blue'] .slider-text {
    @apply text-sm font-semibold text-blue-950;
  }

  [data-theme*='pale-blue'] .slider-value {
    @apply mt-1 flex justify-between text-xs text-blue-950;
  }
  [data-theme*='pale-blue'] .slider-dot {
    @apply bg-blue-800;
  }
  [data-theme*='pale-blue'] .slider-dot-active {
    @apply border border-blue-950 bg-blue-950;
  }
  [data-theme*='pale-blue'] .slider-handle {
    @apply border-blue-950 bg-blue-950 hover:bg-blue-950;
  }
  [data-theme*='pale-blue'] .slider-handle-dragging {
    @apply border-none bg-blue-950 ring-4 ring-blue-950/25;
  }
  [data-theme*='pale-blue'] .slider-handle-focus {
    @apply bg-blue-950 ring-4 ring-blue-950/25;
  }
  [data-theme*='pale-blue'] .slider-rail {
    @apply bg-blue-400;
  }
  [data-theme*='pale-blue'] .slider-track {
    @apply bg-blue-950;
  }
  [data-theme*='pale-blue'] .tool-tip {
    @apply bg-blue-950 text-blue-50;
  }
  [data-theme*='pale-blue'] .controls-container {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .text-highlight-word-definition {
    @apply bg-rose-600 text-blue-50;
  }
  [data-theme*='pale-blue'] .active-tts-paragraph {
    @apply bg-blue-950 text-blue-50 px-2 pt-2 rounded-xl;
  }
  [data-theme*='pale-blue'] .tts-progress-bar {
    @apply w-full [&::-webkit-progress-bar]:bg-blue-400 h-1
    [&::-webkit-progress-value]:bg-blue-950 h-1;
  }
  [data-theme*='pale-blue'] .overview-gradient-border {
    @apply border border-blue-600 rounded-2xl p-[2px];
  }
  [data-theme*='pale-blue'] .ai-gradient-border {
    @apply rounded-md border-2 border-blue-400;
  }
  [data-theme*='pale-blue'] .quick-summary-container {
    @apply h-full w-full bg-blue-100 rounded-2xl px-6 py-5;
  }
  [data-theme*='pale-blue'] .ai-notice-container {
    @apply bg-transparent;
  }
  [data-theme*='pale-blue'] .ai-underline {
    @apply underline decoration-blue-600;
  }
  [data-theme*='pale-blue'] .reading-time {
    @apply bg-blue-300;
  }
  [data-theme*='pale-blue'] .welcome-banner button {
    @apply border-blue-100 bg-blue-100
      hover:bg-blue-50 
      focus-visible:outline-rose-600 focus-visible:border-blue-200
      active:bg-blue-300;
  }
  [data-theme*='pale-blue'] .document-wrapper {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .document-viewer-background {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .closed-right-drawer {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .summary-body {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .reading-environment-wrapper {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .toc-parent-text {
    @apply text-blue-950 font-bold;
  }
  [data-theme*='pale-blue'] .toc-child-text {
    @apply text-blue-950;
  }
  [data-theme*='pale-blue'] .toc-items-text {
    @apply text-blue-800;
  }
  [data-theme*='pale-blue'] .toc-parent-line {
    @apply w-8 h-0.5 bg-blue-600;
  }
  [data-theme*='pale-blue'] .toc-child-line {
    @apply w-6 h-0.5 bg-blue-600;
  }
  [data-theme*='pale-blue'] .toc-grandcchild-line {
    @apply w-4 h-0.5 bg-blue-600;
  }
  [data-theme*='pale-blue'] .toc-active-element {
    @apply bg-blue-950;
  }
  [data-theme*='pale-blue'] .nav-border {
    @apply border-b-2 border-blue-300;
  }
  [data-theme*='pale-blue'] .nav-bg {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .nav-underline {
    @apply bg-blue-500;
  }
  [data-theme*='pale-blue'] .nav-content-styles {
    @apply bg-blue-200 border-t-2 border-blue-300 p-2 lg:bg-blue-100 lg:relative lg:border-none;
  }
  [data-theme*='pale-blue'] .toc-background {
    @apply bg-blue-200/50 backdrop-blur-sm z-50 rounded-tr-lg rounded-br-lg;
  }
  [data-theme*='pale-blue'] .dropdown-nav {
    @apply bg-blue-100 border-blue-300;
  }
  [data-theme*='pale-blue'] .user-svg-icon {
    fill: #172554;
  }
  [data-theme*='pale-blue'] .dashboard-bg {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .documents-panel {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .mobile-dashboard-bg {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .mobile-welcome-panel {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .mobile-document-body {
    @apply bg-blue-200;
  }
  [data-theme*='pale-blue'] .mobile-extraction-message {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .small-tailo-logo {
    @apply hover:text-slate-800 focus:ring-rose-600 
    focus:outline-2 focus:ring-2 focus:outline-rose-600 focus:border-blue-900
    active:border-blue-900 active:ring-2 active:ring-rose-600
    active:outline-2 active:outline-rose-600 active:text-blue-700;
  }

  /* The left border for the summary page expand/collapse button, to separate the text link from the button visually */
  [data-theme*='pale-blue'] .summary-expand-btn {
    @apply border-l-blue-300;
  }

  [data-theme*='pale-blue'] .active-toc-parent-text {
    @apply text-blue-950
    hover:bg-blue-300 hover:text-blue-950
    focus:border-rose-600 focus:bg-blue-950 focus:text-blue-50
    active:bg-blue-400 active:text-blue-950;
  }
  [data-theme*='pale-blue'] .inactive-toc-parent-text {
    @apply text-slate-950 decoration-blue-950
    hover:bg-blue-300 hover:text-blue-950
    focus:border-rose-600 focus:bg-blue-950 focus:text-blue-50 focus:decoration-blue-300
    active:bg-blue-400 active:text-blue-950 active:decoration-blue-950;
  }
  [data-theme*='pale-blue'] .inactive-toc-parent-text + ul a {
    @apply text-lime-950;
  }
  [data-theme*='pale-blue'] .active-toc-parent-text + ul a {
    @apply text-lime-950;
  }
  [data-theme*='pale-blue'] .tailo-icon-button-active {
    @apply bg-blue-400;
  }
  [data-theme*='pale-blue'] .search-highlight {
    @apply bg-yellow-400 text-blue-950;
  }
  [data-theme*='pale-blue'] .themed-spinner {
    @apply text-blue-950;
  }
  [data-theme*='pale-blue'] .result-item-card {
    @apply bg-slate-50 lg:bg-blue-50 border-2 border-transparent hover:bg-blue-300
    active:bg-blue-400 focus-visible:border-rose-600;
  }
  [data-theme*='pale-blue'] .explain-this-dropdown {
    @apply bg-blue-300;
  }
  [data-theme*='pale-blue'] .loading-response {
    @apply bg-blue-100;
  }
  [data-theme*='pale-blue'] .divider {
    @apply bg-blue-300;
  }
  [data-theme*='pale-blue'] .result-bg {
    @apply bg-blue-300;
  }
  [data-theme*='pale-blue'] .result-text {
    @apply text-blue-800;
  }
  [data-theme*='pale-blue'] .feedback-faces {
    @apply stroke-blue-950;
  }
  [data-theme*='pale-blue'] .feedback-face-card {
    @apply border-2 border-blue-400 hover:border-blue-600;
  }
  [data-theme*='pale-blue'] .feedback-face-card-active {
    @apply ring-2 ring-rose-500 border-2 border-blue-950;
  }
}
