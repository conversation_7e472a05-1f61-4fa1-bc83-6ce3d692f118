/* There are two separate classes:
1. customisation-reactive-wrapper that is responsible for applying user settings to the children elements
2. document-viewer-container only to be used in document Viewer component which adjusts the size of the parent to accommodate for different font sizes without shifting the text in the line  */
@layer base {
}
@layer components {
  /* Text alignment */
  [data-theme*='left'] .customisation-reactive-wrapper {
    @apply text-left;
  }
  [data-theme*='justified'] .customisation-reactive-wrapper {
    @apply text-justify;
  }

  /* small/tight styles below medium/optimised to prevent default setting overwriting */

  /* Line spacing */
  [data-theme*='optimised'] .customisation-reactive-wrapper h1,
  [data-theme*='optimised'] .customisation-reactive-wrapper h2,
  [data-theme*='optimised'] .customisation-reactive-wrapper h3,
  [data-theme*='optimised'] .customisation-reactive-wrapper h4,
  [data-theme*='optimised'] .customisation-reactive-wrapper h5,
  [data-theme*='optimised'] .customisation-reactive-wrapper h6,
  [data-theme*='optimised'] .customisation-reactive-wrapper p,
  [data-theme*='optimised'] .customisation-reactive-wrapper ul,
  [data-theme*='optimised'] .customisation-reactive-wrapper li {
    @apply leading-normal !important;
  }
  [data-theme*='tight'] .customisation-reactive-wrapper h1,
  [data-theme*='tight'] .customisation-reactive-wrapper h2,
  [data-theme*='tight'] .customisation-reactive-wrapper h3,
  [data-theme*='tight'] .customisation-reactive-wrapper h4,
  [data-theme*='tight'] .customisation-reactive-wrapper h5,
  [data-theme*='tight'] .customisation-reactive-wrapper h6,
  [data-theme*='tight'] .customisation-reactive-wrapper p,
  [data-theme*='tight'] .customisation-reactive-wrapper ul,
  [data-theme*='tight'] .customisation-reactive-wrapper li {
    /* !important overrides specificity rule for tags h1-h6 and p */
    @apply leading-tight !important;
  }
  [data-theme*='relaxed'] .customisation-reactive-wrapper h1,
  [data-theme*='relaxed'] .customisation-reactive-wrapper h2,
  [data-theme*='relaxed'] .customisation-reactive-wrapper h3,
  [data-theme*='relaxed'] .customisation-reactive-wrapper h4,
  [data-theme*='relaxed'] .customisation-reactive-wrapper h5,
  [data-theme*='relaxed'] .customisation-reactive-wrapper h6,
  [data-theme*='relaxed'] .customisation-reactive-wrapper p,
  [data-theme*='relaxed'] .customisation-reactive-wrapper ul,
  [data-theme*='relaxed'] .customisation-reactive-wrapper li {
    @apply leading-relaxed !important;
  }

  /* Font size */
  /**
   * the width of the document viewer should adjust to the users font size (on desktop)
   */

  [data-theme*='medium'] .document-viewer-container {
    @apply w-[80%] md:w-[70%] lg:w-[90%];
  }
  [data-theme*='medium'] .customisation-reactive-wrapper {
    @apply text-base lg:text-xl;
  }

  [data-theme*='medium'] .customisation-reactive-wrapper .outer-list,
  [data-theme*='medium'] .customisation-reactive-wrapper p {
    @apply pb-6;
  }
  [data-theme*='medium'] .customisation-reactive-wrapper h1 {
    @apply text-[28px] lg:text-[40px] pb-10;
  }
  [data-theme*='medium'] .customisation-reactive-wrapper h2 {
    @apply text-xl lg:text-[32px] pb-6 pt-4;
  }
  [data-theme*='medium'] .customisation-reactive-wrapper h3 {
    @apply text-lg lg:text-2xl pb-6 pt-4;
  }
  [data-theme*='medium'] .customisation-reactive-wrapper h4 {
    @apply text-base lg:text-xl pb-6 pt-4;
  }
  [data-theme*='medium'] .customisation-reactive-wrapper h5 {
    @apply text-sm lg:text-lg pb-6 pt-4;
  }
  [data-theme*='medium'] .customisation-reactive-wrapper h6 {
    @apply text-xs lg:text-sm pb-6 pt-4;
  }

  [data-theme*='small'] .document-viewer-container {
    @apply w-[90%] sm:w-[75%] md:w-[80%];
  }
  [data-theme*='small'] .customisation-reactive-wrapper {
    @apply text-sm lg:text-base;
  }
  [data-theme*='small'] .customisation-reactive-wrapper .outer-list,
  [data-theme*='small'] .customisation-reactive-wrapper p {
    @apply pb-4;
  }
  /* h1 for document title only has padding on bottom. Other headers have some on top too. */
  [data-theme*='small'] .customisation-reactive-wrapper h1 {
    @apply text-2xl lg:text-4xl pb-8;
  }
  [data-theme*='small'] .customisation-reactive-wrapper h2 {
    @apply text-base lg:text-[28px] pb-4 pt-2;
  }
  [data-theme*='small'] .customisation-reactive-wrapper h3 {
    @apply text-sm lg:text-xl pb-4 pt-2;
  }
  [data-theme*='small'] .customisation-reactive-wrapper h4 {
    @apply text-sm lg:text-base pb-4 pt-2;
  }
  [data-theme*='small'] .customisation-reactive-wrapper h5 {
    @apply text-xs lg:text-sm pb-4 pt-2;
  }
  [data-theme*='small'] .customisation-reactive-wrapper h6 {
    @apply text-xs pb-4 pt-2;
  }

  [data-theme*='large'] .document-viewer-container {
    @apply w-[90%];
  }
  [data-theme*='large'] .customisation-reactive-wrapper {
    @apply text-xl lg:text-2xl;
  }
  [data-theme*='large'] .customisation-reactive-wrapper .outer-list,
  [data-theme*='large'] .customisation-reactive-wrapper p {
    @apply text-2xl pb-8;
  }
  [data-theme*='large'] .customisation-reactive-wrapper h1 {
    @apply text-[32px] lg:text-[44px] pb-12;
  }
  [data-theme*='large'] .customisation-reactive-wrapper h2 {
    @apply text-2xl lg:text-4xl pb-8 pt-6;
  }
  [data-theme*='large'] .customisation-reactive-wrapper h3 {
    @apply text-[22px] lg:text-[28px] pb-8 pt-6;
  }
  [data-theme*='large'] .customisation-reactive-wrapper h4 {
    @apply text-xl lg:text-2xl pb-8 pt-6;
  }
  [data-theme*='large'] .customisation-reactive-wrapper h5 {
    @apply text-lg lg:text-[22px] pb-8 pt-6;
  }
  [data-theme*='large'] .customisation-reactive-wrapper h6 {
    @apply text-base lg:text-lg pb-8 pt-6;
  }
  /* LISTS */
  .customisation-reactive-wrapper ul,
  .customisation-reactive-wrapper ol {
    @apply pl-5;
  }

  .customisation-reactive-wrapper ul {
    @apply list-disc;
  }

  .customisation-reactive-wrapper ol {
    @apply list-decimal;
  }

  .customisation-reactive-wrapper ol ol {
    list-style-type: upper-alpha;
  }
  .customisation-reactive-wrapper ol ol ol {
    list-style-type: upper-roman;
  }

  .customisation-reactive-wrapper ol ol ol ol {
    list-style-type: lower-greek;
  }
  /* Table of content - interactive items are responsive to all user settings */

  .customisation-responsive-toc-li {
    overflow-wrap: anywhere;
  }
  /* The Viewer grid for content */
  [data-theme*='small'] .content-viewer-grid {
    @apply col-start-4 col-span-6;
  }
  [data-theme*='medium'] .content-viewer-grid {
    @apply col-start-4 col-span-6;
  }
  [data-theme*='large'] .content-viewer-grid {
    @apply col-start-3 col-span-8;
  }
}

@layer utilities {
}
