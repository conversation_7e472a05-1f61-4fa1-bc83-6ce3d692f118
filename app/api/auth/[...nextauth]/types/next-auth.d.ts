import {IPermission} from '@libs/services/user/types';
import {ILicenseMinima, ISettings} from '@libs/store/settings/types';
import NextAuth from 'next-auth';

declare module 'next-auth' {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface User {
    permissions: IPermission[];
    accessToken: string;
    refreshToken: string;
    accessExpiry: number;
    error?: 'RefreshAccessTokenError';
    firstName?: string;
    lastName?: string;
    email?: string; // Guest users don't sign up with an email
    settings?: ISettings;
    gdprAcceptance: Date | null;
    license: ILicenseMinima;
    trackingId: string;
  }
  interface Session {
    user: User;
  }
}
declare module 'next-auth/jwt' {
  interface JWT extends DefaultJWT {
    id: string;
    permissions: IPermission[];
    accessToken: string;
    exp: number;
    refreshToken: string;
    error?: 'RefreshAccessTokenError';
  }
}
