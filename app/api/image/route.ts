import { NextRequest, NextResponse } from 'next/server';
// import ImageManipulator from '@components/image-viewer/services/ImageManipulator';

export async function POST(request: NextRequest) {
  const body = await request.json();

  const { src, transform, options } = body;

  // const manipulator = new ImageManipulator(new Uint8ClampedArray(src.data));

  let data = null;

  switch (transform) {
    default:
      // data = await manipulator.rotate(options);
  }

  return NextResponse.json({
    _links: {
      self: {
        href: '/image'
      }
    },
    data,
  });
};
