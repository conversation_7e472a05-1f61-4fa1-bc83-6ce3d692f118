import useSWR from 'swr';
import {IServerDocumentExtractedData, IUseProcessedDocument} from './types';
import {convertServerDocumentExtractedDataToCLientFormat} from '@libs/utils/document/converter';
import {useParams, usePathname} from 'next/navigation';

export const DOCUMENTS_DATA_KEY = '/documents';

export default function useProcessedDocument(
  url: string = '', //Use when rendering pages via specific routes (e.g. onboarding example document)
): IUseProcessedDocument {
  const params = useParams();
  const id = params.id as string;
  const pathname = usePathname();

  let key = url || pathname;
  if (id) key = `/documents/${id}`;

  const {data, isLoading, error} = useSWR<IServerDocumentExtractedData>(key);

  const document = convertServerDocumentExtractedDataToCLientFormat(data);
  return {
    document,
    isLoading,
    error,
    id,
  };
}
