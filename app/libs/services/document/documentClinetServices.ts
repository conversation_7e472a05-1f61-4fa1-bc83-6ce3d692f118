import {
  IDocumentClientServices,
  IPresignedUrlData,
  IServerDocument,
  IServerPresignedUrlData,
} from './types';
import {IServerResponse, ServerCode} from '../types';
import axios, {
  AxiosError,
  AxiosProgressEvent,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios';
import {
  mutateDocumentsOnDocumentDelete,
  mutateDocumentsOnDocumentUpdate,
  mutateDocumentsOnDocumentUpload,
  revalidateDocuments,
} from './useDocuments';
import requestHandler from '../requestHandler';

const documentClientServices: IDocumentClientServices = {
  getDocumentUploadData: async (file: File) => {
    return await requestHandler({
      method: 'post',
      url: '/documents',
      options: {
        body: {
          filename: file.name,
          extension: '.' + file.name.split('.').pop()?.toLowerCase(),
          mime: file.type,
        },
      },
      errorMsg: "Failed to get document's upload url",
      callback: (
        res: AxiosResponse<IServerResponse<IServerPresignedUrlData>>,
      ) => {
        const data = res.data.data;
        if (data) {
          mutateDocumentsOnDocumentUpload(data.document);
          return {
            document: data.document,
            url: data.upload_url.url,
            fields: data.upload_url.fields,
          };
        } else {
          const {message, code} = res.data;
          const err = `Failed to get document's upload url: ${message}`;
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
      },
    });
  },

  storeUploadedDocument: async ({
    uploadData,
    file,
    onProgress,
    onError,
  }: {
    uploadData: IPresignedUrlData;
    file: File;
    onProgress: (progress: number) => void;
    onError: (hasError: boolean) => void;
  }) => {
    const config: AxiosRequestConfig<FormData> = {
      onUploadProgress: function (progressEvent: AxiosProgressEvent) {
        if (progressEvent.total) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total,
          );
          onProgress(percentCompleted);
        }
      },
    };
    if (uploadData) {
      const {url, fields} = uploadData;
      const formData = new FormData();
      for (let key in fields) {
        formData.append(key, fields[key]);
      }
      // The file field needs to be the very last item in the Form Data for the s3 bucket to work
      formData.append('file', file);

      // This goes straight to S3, so we can't use the requestHandler and don't need to pass sentry trace id through
      axios.post(url, formData, config).catch((err: Error | AxiosError) => {
        onError(!!err);
        console.log('Failed to store uploaded document', err);
        throw new Error('Failed to store uploaded document');
      });
    }
    return null;
  },

  updateDocumentName: async ({id, newName}: {id: string; newName: string}) => {
    if (!id) {
      throw new Error('Document id is mandatory.');
    }
    if (!newName) {
      throw new Error('New name is mandatory.');
    }
    // Mutate current data to optimistically update the UI
    // Remember that you are updating the server response: IServerDocument
    await mutateDocumentsOnDocumentUpdate(id, {name: newName});

    return await requestHandler({
      method: 'patch',
      url: `/documents/${id}`,
      options: {
        body: {
          new_name: newName,
        },
      },
      errorMsg: "Failed to update document's name",
      callback: async (
        res: AxiosResponse<IServerResponse<IServerDocument>>,
      ) => {
        const data = res.data.data;
        if (data) {
          return data;
        } else {
          const {message, code} = res.data;
          const err = `Failed to update document's name: ${message}`;
          await revalidateDocuments();
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
      },
    });
  },

  deleteDocument: async (id: string) => {
    if (!id) {
      throw new Error('Document id is mandatory.');
    }

    // Mutate current data to optimistically update the UI
    await mutateDocumentsOnDocumentDelete(id);

    return await requestHandler({
      method: 'delete',
      url: `/documents/${id}`,
      errorMsg: 'Failed to delete document',
      callback: async (res: AxiosResponse<IServerResponse>) => {
        const {code, message} = res.data;
        if (code !== ServerCode.OK) {
          const err = `Failed to delete the document: ${message}`;
          await revalidateDocuments();
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
        return null;
      },
    });
  },

  searchDocuments: async (id: string, query: string) => {
    return await requestHandler({
      method: 'get',
      url: `documents/${id}/search?keywords=${query}`,
      errorMsg: 'Failed to find any documents',
      callback: async (
        res: AxiosResponse<IServerResponse<IServerDocument>>,
      ) => {
        const data = res.data;
        if (data) {
          return data.data;
        } else {
          const {message, code} = res.data;
          const err = `Failed to find any documents with the search term: ${message}`;
          await revalidateDocuments();
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
      },
    });
  },

  downloadDocument: async (id: string) => {
    return await requestHandler({
      method: 'get',
      url: `/documents/download/${id}`,
      errorMsg: 'Failed to download document',
      callback: async (res: AxiosResponse) => {
        return res.data;
      },
    });
  },
};

export default documentClientServices;
