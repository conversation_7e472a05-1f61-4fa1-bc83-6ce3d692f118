import {ISearchResult} from '@components/document/search/types';
import {Footnote} from '@components/footnotes/types';
import {
  IContentHeadingNode,
  IContentImageNode,
  IContentTableNode,
  IContentTextNode,
  IDocument,
  IDocumentExtractedData,
  IHeadingData,
  IContentListNode,
  ITableOfContentItem,
} from '@libs/store/document/types';
import {IStatus, IErrorDetails} from '@libs/store/status/types';

// DOCUMENTS LIST
export interface IServerDocument {
  id: string;
  name: string;
  type: string;
  status: IStatus;
  date_created: string;
  last_modified: string;
  last_read_at: string;
  is_owner: boolean;
  filesize: number;
  error_details?: IErrorDetails;
}
export interface IServerDocumentsData {
  count: number;
  values: IServerDocument[];
}

export interface IDocumentSearchData {}

// DOCUMENT EXTRACTED DATA (different to client types)
export interface IServerContentTextNode extends IContentTextNode {}
export interface IServerContentHeadingNode extends IContentHeadingNode {}
export interface IServerContentTableNode extends IContentTableNode {}
export interface IServerContentListNode extends IContentListNode {}
export interface IServerContentImageNode
  extends Omit<IContentImageNode, 'originalHeight' | 'originalWidth'> {
  original_height: number;
  original_width: number;
}
export interface IServerSectionData extends IHeadingData {
  word_count: number;
  child_word_count: number;
  content: (
    | IServerContentTextNode
    | IServerContentImageNode
    | IServerContentHeadingNode
    | IServerContentTableNode
    | IServerContentListNode
  )[];
}

export interface IServerSectionsData {
  [key: string]: IServerSectionData;
}

export interface IServerDocumentExtractedData {
  details: {
    page_count: number;
    base_font_size: number;
    toc: ITableOfContentItem[];
    summary: string[];
    word_count?: number;
    child_word_count?: number;
    summary_tts: {
      status: string;
      audio_file: string;
    };
    footnotes?: {
      [key: string]: Footnote[];
    };
  };
  sections: IServerSectionsData;
}

//DOCUMENT UPLOAD PRESIGNED URL DATA

export interface IServerPresignedUrlData {
  document: IServerDocument;
  upload_url: {
    url: string;
    fields: {[key: string]: string};
  };
}
export interface IPresignedUrlData {
  document: IServerDocument;
  url: string;
  fields: {[key: string]: string};
}

// HOOKS
export interface IDocumentClientServices {
  getDocumentUploadData: (file: File) => Promise<IPresignedUrlData>;
  storeUploadedDocument: ({
    uploadData,
    file,
    onProgress,
  }: {
    uploadData: IPresignedUrlData;
    file: File;
    onProgress: (progress: number) => void;
    onError: (hasError: boolean) => void;
  }) => Promise<null>;
  updateDocumentName: ({
    id,
    newName,
  }: {
    id: string;
    newName: string;
  }) => Promise<IServerDocument | null>;
  deleteDocument: (id: string) => Promise<null>;
  searchDocuments: (id: string, query: string) => Promise<any>;
  downloadDocument: (id: string) => Promise<any>;
}

export interface IUseDocuments {
  documents: IDocument[];
  isLoading: boolean;
  error: Error;
  getDocumentById: (id: string) => IDocument | undefined;
}

export interface IUseProcessedDocument {
  document: IDocumentExtractedData | null;
  isLoading: boolean;
  error: Error;
  id: string;
}
