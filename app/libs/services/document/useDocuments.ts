import useSWR, {mutate} from 'swr';
import {IServerDocument, IServerDocumentsData, IUseDocuments} from './types';
import {convertServerDocListToClientFormat} from '@libs/utils/document/converter';
import {getNowInUTC} from '@libs/utils/dates/dateFormats';

export const DOCUMENTS_DATA_KEY = '/documents';

export enum DocumentMimeType {
  PDF = 'application/pdf',
  DOCX = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
}

//Fetching documents
export default function useDocuments(): IUseDocuments {
  const {data, isLoading, error} =
    useSWR<IServerDocumentsData>(DOCUMENTS_DATA_KEY);

  const documents = convertServerDocListToClientFormat(data?.values || []);

  const getDocumentById = (id: string) => documents.find(d => d.id === id);
  return {
    documents,
    isLoading,
    error,
    getDocumentById,
  };
}

export async function revalidateDocuments() {
  await mutate(DOCUMENTS_DATA_KEY);
}

//Adding a new document
export async function mutateDocumentsOnDocumentUpload(
  document: IServerDocument,
) {
  try {
    await mutate<IServerDocumentsData>(
      DOCUMENTS_DATA_KEY,
      data => {
        if (data) {
          return {
            count: data.count + 1,
            values: [document, ...data.values],
          };
        }
      },
      {revalidate: false},
    );
  } catch (error) {
    console.log('Error on mutating documents:', error);
  }
}

//Updating the document
//Note: updates are of IServerDocument type
export async function mutateDocumentsOnDocumentUpdate(
  documentId: string,
  updates: Partial<IServerDocument>,
) {
  try {
    await mutate<IServerDocumentsData>(
      DOCUMENTS_DATA_KEY,
      data => {
        if (data) {
          const values = data.values.map(doc => {
            if (doc.id === documentId) {
              return {...doc, ...updates, last_modified: getNowInUTC()};
            }
            return doc;
          });

          return {
            ...data,
            values,
          };
        }
      },
      {revalidate: false},
    );
  } catch (error) {
    console.log('Error on mutating documents:', error);
  }
}

//Deleting the document
export async function mutateDocumentsOnDocumentDelete(documentId: string) {
  try {
    await mutate<IServerDocumentsData>(
      DOCUMENTS_DATA_KEY,
      data => {
        if (data) {
          const values = data.values.filter(doc => doc.id !== documentId);
          return {
            ...data,
            values,
          };
        }
      },
      {revalidate: false},
    );
  } catch (error) {
    console.log('Error on mutating documents:', error);
  }
}
