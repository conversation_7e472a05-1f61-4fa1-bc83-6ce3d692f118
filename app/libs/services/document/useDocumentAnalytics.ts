import {IDocument, IDocumentExtractedData} from '@libs/store/document/types';
import {useAnalytics} from 'use-analytics';
import useDocuments, {DocumentMimeType} from './useDocuments';
import {useCallback, useMemo} from 'react';
import {getTimeTaken} from '@libs/utils/dates/dateFormats';

export const useDocumentAnalytics = () => {
  const {track, user, identify} = useAnalytics();
  const {documents, getDocumentById} = useDocuments();

  const userDocumentTypes = useMemo(() => {
    const pdfCount = documents.filter(
      doc => doc.type === DocumentMimeType.PDF,
    ).length;
    const docxCount = documents.filter(
      doc => doc.type === DocumentMimeType.DOCX,
    ).length;

    return {
      PDFs: pdfCount,
      DocX: docxCount,
    };
  }, [documents]);

  const trackDashboardPageView = useCallback(() => {
    track('page: DocumentDashboard', {
      NumberOfDocuments: documents.length,
      NumberOfPDFs: userDocumentTypes.PDFs,
      NumberOfDocX: userDocumentTypes.DocX,
    });
  }, [documents.length, userDocumentTypes.PDFs, userDocumentTypes.DocX]);

  const trackDocumentTabUsage = (
    document: IDocumentExtractedData,
    tabId: string,
    id: string,
  ) => {
    const documentType = getDocumentById(id)?.type || '';

    const summary = document.details.summary || [];
    track(`page: ${tabId}`, {
      DocumentType: documentType,
      SummaryBullets: summary.length,
      SummaryCharacters: summary.join('').length,
      NumberofPages: document.details.pageCount,
      WordCount: document.details.wordCount,
    });
  };

  const trackContinueReading = (
    source: string,
    viewMode: string,
    wordCount?: number,
  ) => {
    track(`${source}`, {
      WordCount: wordCount,
      ViewMode: viewMode,
    });
  };

  const trackTableOfContentsLength = (entries: number) => {
    track('TableOfContents', {
      NumberOfEntries: entries,
    });
  };

  const trackSectionChange = (direction: string) => {
    track(`${direction} section`);
  };

  const trackUploadDocument = (
    fileSize: number,
    documentType: string,
    uploadSource?: string,
  ) => {
    track('DocumentUploaded', {
      Source: uploadSource,
      FileSize: `${(fileSize / 1000 / 1024).toFixed(2)} Mb`,
      DocumentType: documentType,
    });
  };

  const trackDeleteDocument = (document: IDocument) => {
    track('DocumentDeleted', {
      DocumentType: document.type,
    });
  };

  const trackOpenDocument = (document: IDocumentExtractedData, id: string) => {
    const file = getDocumentById(id);

    if (!file) return;
    const createdDate = new Date(file.createdAt);
    const timeSinceUploadedMinutes =
      (Date.now() - createdDate.getTime()) / 60000;
    const timeSinceUploaded = getTimeTaken(timeSinceUploadedMinutes);

    const lastOpenedDate = new Date(file.lastOpenDate);
    const timeSinceLastAccessedMinutes =
      (Date.now() - lastOpenedDate.getTime()) / 60000;
    const timeSinceLastAccessed = getTimeTaken(timeSinceLastAccessedMinutes);

    track('DocumentOpened', {
      DocumentType: file.type,
      FileSize: `${(file.fileSize / 1024 / 1000).toFixed(1)} Mb`,
      WordCount: document.details.wordCount,
      PageCount: document.details.pageCount,
      TimeSinceUpload: timeSinceUploaded,
      TimeSinceLastAccessed: timeSinceLastAccessed,
      FirstTimeOpened: file.lastOpenDate ? 'No' : 'Yes',
    });
  };

  const trackDefineWordInText = (word: string) => {
    track('DefineWordInText', {
      Word: word,
    });
  };

  const trackDefineWordInPanel = (word: string) => {
    track('DefineWordInPanel', {
      Word: word,
    });
  };

  const trackDefineProcess = (
    timeTaken: number,
    success: boolean,
    wordDefined: string,
  ) => {
    track('DefineWordProcess', {
      TimeTaken: `${timeTaken} seconds`,
      Success: success,
      WordDefined: wordDefined,
    });
  };

  const trackTableOfContentsPress = (numberofEntries: number) => {
    track('TableOfContentsPress', {
      NumberOfEntries: numberofEntries,
    });
  };

  const trackTableOfContentsPin = (pinned: boolean) => {
    if (pinned) {
      track('TableOfContentsPin', {
        Pinned: true,
      });
    } else {
      track('TableOfContentsUnpin', {
        Pinned: false,
      });
    }
  };

  const trackDocumentDownload = (
    isPdf: boolean,
    successfullyDownloaded: boolean,
    error?: string,
  ) => {
    track('DocumentDownloaded', {
      DocumentType: isPdf ? 'PDF' : 'DOCX',
      Success: successfullyDownloaded,
      Error: error,
    });
  };

  return {
    trackDashboardPageView,
    trackDocumentTabUsage,
    trackContinueReading,
    trackTableOfContentsLength,
    trackSectionChange,
    trackUploadDocument,
    trackDeleteDocument,
    trackOpenDocument,
    trackDefineWordInText,
    trackDefineWordInPanel,
    trackDefineProcess,
    trackTableOfContentsPress,
    trackTableOfContentsPin,
    trackDocumentDownload,
  };
};
