import {AxiosResponse} from 'axios';
import {IServerResponse} from '../types';
import {
  ISendTextToSpeech,
  ITextToSpeechService,
  IGetReadyAudioFiles,
  IGetReadyAudioFile,
} from './types';
import requestHandler from '../requestHandler';

const textToSpeechService: ITextToSpeechService = {
  sendTextToSpeech: async (
    id: string,
    sectionId: string,
    paragraphId: string,
    text: string,
    settings: any,
  ) => {
    return await requestHandler({
      method: 'post',
      url: `/documents/${id}/tts`,
      options: {
        body: {
          sectionId,
          paragraphId,
          text,
          settings,
        },
      },
      errorMsg: 'Failed to send text to speech',
      callback: (res: AxiosResponse<IServerResponse<ISendTextToSpeech>>) => {
        const {code} = res.data;
        if (code === 0) {
          return res.data;
        } else {
          const {message, code} = res.data;
          console.log(
            `Failed to send text to speech: ${message}, code: ${code}`,
          );
          throw new Error(`Failed to send text to speech: ${message}`);
        }
      },
    });
  },
  getReadyAudioFiles: async (
    documentId: string,
    sectionId: string,
  ): Promise<IGetReadyAudioFile> => {
    return await requestHandler({
      method: 'get',
      url: `/documents/${documentId}/${sectionId}/tts`,
      errorMsg: 'Failed to get ready audio files',
      callback: (res: AxiosResponse<IServerResponse<IGetReadyAudioFiles>>) => {
        const data = res.data.data && res.data.data.audio;
        if (data) {
          return data;
        } else {
          const {message, code} = res.data;
          const err = `Failed: ${message}`;
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
      },
    });
  },
};

export default textToSpeechService;
