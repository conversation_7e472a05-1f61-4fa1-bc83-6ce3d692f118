export interface ISendTextToSpeech {
  data: string;
}

export interface IGetReadyAudioFiles {
  audio: IGetReadyAudioFile;
}

export interface IGetReadyAudioFile {
  audio_file: string;
  document_id: string;
  section_id: string;
  paragraph_id: string;
}

export interface ITextToSpeechService {
  sendTextToSpeech: (
    id: string,
    sectionId: string,
    paragraphId: string,
    text: string,
    settings: any,
  ) => Promise<ISendTextToSpeech>;
  getReadyAudioFiles: (
    documentId: string,
    sectionId: string,
  ) => Promise<IGetReadyAudioFile>;
}
