import {useAnalytics} from 'use-analytics';
import useSpeakAloud from '@libs/store/speakAloud';

export const useTextToSpeechAnalytics = () => {
  const {track} = useAnalytics();
  const {audioPlayerSettings} = useSpeakAloud();

  const trackReadAloudDownload = (source: string, chosenVoice?: string) => {
    track('ReadAloudDownload', {
      Page: source,
      Voice: chosenVoice ?? 'Brian',
    });
  };

  const trackReadAloudProcessed = (
    source: string,
    processingTime: number,
    quotaUsed: number,
    quotaLeft: number,
    chosenVoice?: string,
    errors?: string,
  ) => {
    track('ReadAloudProcessed', {
      Page: source,
      Voice: chosenVoice ?? 'Brian',
      TimeToProcess: `${processingTime} seconds`,
      TimeToProcessSeconds: processingTime,
      QuotaUsed: quotaUsed,
      // QuotaMinutesSection: quotaUsed, // TODO: This needs implemented in the BE.
      QuotaMinutesLeft: `${quotaLeft} Minutes`,
      QuotaMinutesRemaining: quotaLeft,
      Error: errors,
    });
  };

  const trackReadAloudPlay = (
    source: string,
    // quotaMinutesSection: number,
    quotaMinutesLeft: number,
    chosenVoice?: string,
    playLocation?: string,
  ) => {
    track('ReadAloudPlay', {
      Page: source,
      Location: playLocation ? 'TTS Panel' : 'Section',
      // QuotaMinutesSection: quotaMinutesSection // TODO: This needs implemented in the BE.
      QuotaMinutesLeft: quotaMinutesLeft,
      Voice: chosenVoice ?? 'Brian',
      Speed: audioPlayerSettings.initialRate,
    });
  };

  return {
    trackReadAloudDownload,
    trackReadAloudProcessed,
    trackReadAloudPlay,
  };
};
