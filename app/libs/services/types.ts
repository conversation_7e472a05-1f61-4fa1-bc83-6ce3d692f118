import {WordDefinitionResponse} from '@libs/store/document/wordDefinition/types';

export enum ServerCode {
  UNKNOWN = -1,
  OK = 0,
  MISSING_PARAM = 1,
  DENIED = 2,
  ROLE_MISSING = 3,
  MISSING_TOKEN = 4,
  INVALID_VALUE = 5,
  EXPIRED = 6,
  MISSING_USER = 7,
  INVALID_DOCUMENT = 8,
}
export interface IServerResponse<T = void> extends IServerErrorResponse {
  data?: T;
}
export interface IServerErrorResponse {
  code: ServerCode;
  message: string;
}
export interface IDictionaryServerResponse<T = void>
  extends IDictionaryErrorResponse {
  data?: T;
}

export interface IDictionaryErrorResponse {
  code: ServerCode;
  message: WordDefinitionResponse[] | string;
}

export interface requestOptions {
  headers?: object;
  body?: object;
}