import betaEmailsClientServices from './betaEmailsClientServies';
import {mutate} from 'swr';
import {BETA_EMAILS_DATA_KEY} from './useBetaEmails';
import {IUseBetaEmailsServices} from './types';

const useBetaEmailsServices = (): IUseBetaEmailsServices => {
  const revalidateBetaEmails = async () => {
    await mutate(BETA_EMAILS_DATA_KEY);
  };
  const addEmails = async (emails: string[]) => {
    const serverResponse = await betaEmailsClientServices.addEmails(emails);
    await revalidateBetaEmails();
    if (serverResponse.data) {
      // Server returns an array of duplicated and/or invalid emails along added emails if at least one email is added
      const invalidEmails = serverResponse.data.invalid_emails;
      const duplicateEmails = serverResponse.data.duplicate_emails;
      let error = '';
      if (invalidEmails && invalidEmails.length > 0) {
        error = `Invalid emails submitted: ${invalidEmails.join(', ')}. `;
      }
      if (duplicateEmails && duplicateEmails.length > 0) {
        error =
          error +
          `Following emails already exist: ${duplicateEmails.join(', ')}.`;
      }
      if (error) {
        throw new Error(error);
      }
    } else {
      // Server returns an error message if no email is added and there are invalid or duplicated emails
      throw new Error(serverResponse.message);
    }
  };
  const deleteEmails = async (emails: string[]) => {
    const serverResponse = await betaEmailsClientServices.deleteEmails(emails);
    await revalidateBetaEmails();
    if (serverResponse.data) {
      // Server returns an array of not found and/or already deleted emails along added emails if at least one email is added
      const notFoundEmails = serverResponse.data.not_found;
      const alreadyDeletedEmails = serverResponse.data.already_deleted;
      let error = '';
      if (notFoundEmails && notFoundEmails.length > 0) {
        error = `Couldn't find the following emails: ${notFoundEmails.join(
          ', ',
        )}. `;
      }
      if (alreadyDeletedEmails && alreadyDeletedEmails.length > 0) {
        error =
          error +
          `Following emails already deleted: ${alreadyDeletedEmails.join(
            ', ',
          )}.`;
      }
      if (error) {
        throw new Error(error);
      }
    } else {
      // Server returns an error message if no email is deleted and there are not found or already deleted emails in the request
      throw new Error(serverResponse.message);
    }
  };

  return {
    addEmails,
    deleteEmails,
  };
};

export default useBetaEmailsServices;
