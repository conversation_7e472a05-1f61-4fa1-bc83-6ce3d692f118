import useS<PERSON> from 'swr';
import {IBetaEmailsServerData, IUseBetaEmails} from './types';
import {convertServerBetaEmailsToClientFormat} from '@libs/utils/betaEmails/converter';
import {sortByLastCreated} from '@libs/utils/betaEmails/formatter';

export const BETA_EMAILS_DATA_KEY = '/admin/beta-emails';

//Fetching beta emails
export default function useBetaEmails(): IUseBetaEmails {
  const {data, isLoading, error} =
    useSWR<IBetaEmailsServerData>(BETA_EMAILS_DATA_KEY);

  const betaEmails = convertServerBetaEmailsToClientFormat(
    data?.beta_emails || [],
  ).sort(sortByLastCreated);

  return {
    isLoading,
    error,
    betaEmails,
  };
}
