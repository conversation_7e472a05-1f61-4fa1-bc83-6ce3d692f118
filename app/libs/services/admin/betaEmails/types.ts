import {IServerResponse} from '@libs/services/types';
import {IBetaEmail} from '@libs/store/admin/betaEmails/types';

export interface IUseBetaEmails {
  isLoading: boolean;
  error: Error;
  betaEmails: IBetaEmail[];
}

export interface IBetaEmailServerData {
  email: string;
  added_by: string; //string: full name or email when the name is missing
  deleted_by: string | null; //string: full name or email when the name is missing
  claimed_by: string | null; //string: full name or email when the name is missing
  date_created: string;
  date_deleted: string | null;
}
export interface IBetaEmailsServerData {
  beta_emails: IBetaEmailServerData[];
}

export interface IAddEmailsServerData {
  added_emails: string[];
  invalid_emails?: string[];
  duplicate_emails?: string[];
}
export interface IDeleteEmailsServerData {
  deleted_emails: string[];
  not_found?: string[];
  already_deleted?: string[];
}

export interface IBetaEmailClientServices {
  addEmails: (
    emails: string[],
  ) => Promise<IServerResponse<IAddEmailsServerData>>;
  deleteEmails: (
    emails: string[],
  ) => Promise<IServerResponse<IDeleteEmailsServerData>>;
}

export interface IUseBetaEmailsServices {
  addEmails: (emails: string[]) => Promise<void>;
  deleteEmails: (emails: string[]) => Promise<void>;
}
