import {AxiosError, AxiosResponse} from 'axios';
import {
  IAddEmailsServerData,
  IBetaEmailClientServices,
  IDeleteEmailsServerData,
} from './types';
import {BETA_EMAILS_DATA_KEY} from './useBetaEmails';
import {IServerResponse} from '@libs/services/types';
import requestHandler from '@libs/services/requestHandler';

const betaEmailsClientServices: IBetaEmailClientServices = {
  addEmails: async (emails: string[]) => {
    return await requestHandler({
      method: 'post',
      url: BETA_EMAILS_DATA_KEY,
      options: {
        body: {emails},
      },
      errorMsg: 'Failed to add beta users emails',
      callback: (res: AxiosResponse<IServerResponse<IAddEmailsServerData>>) => {
        if (res.data.data) {
          return res.data;
        } else {
          //TODO Handle server error response with toast: IServerErrorResponse
          const {message, code} = res.data;
          throw new Error(message, {
            cause: code,
          });
        }
      },
    });
  },
  deleteEmails: async (emails: string[]) => {
    return await requestHandler({
      method: 'delete',
      url: BETA_EMAILS_DATA_KEY,
      options: {
        body: {emails},
      },
      errorMsg: 'Failed to delete beta users emails',
      callback: (
        res: AxiosResponse<IServerResponse<IDeleteEmailsServerData>>,
      ) => {
        if (res.data.data) {
          return res.data;
        } else {
          //TODO Handle server error response with toast: IServerErrorResponse
          const {message, code} = res.data;
          throw new Error(message, {
            cause: code,
          });
        }
      },
    });
  },
};

export default betaEmailsClientServices;
