import {Session} from 'next-auth';
import {getSession} from 'next-auth/react';
import {getServerSession} from 'next-auth/next';
import axios, {InternalAxiosRequestConfig} from 'axios';

function authorizationInterceptor(
  internalAxiosRequestConfig: InternalAxiosRequestConfig,
  accessToken: string,
) {
  internalAxiosRequestConfig.headers.set(
    'Authorization',
    `Bearer ${accessToken}`,
  );
  return internalAxiosRequestConfig;
}

const createAxiosClientInstance = () => {
  // Create a new Axios instance for client.
  const axiosClientInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
  });

  // Create the interceptor for client requests.
  axiosClientInstance.interceptors.request.use(
    async (config: InternalAxiosRequestConfig) => {
      const session = await getSession();

      if (session) {
        authorizationInterceptor(config, session.user.accessToken);
      }

      return config;
    },
  );

  return axiosClientInstance;
};

const createAxiosServerInstance = (session: Session) => {
  // Create a new Axios instance for server.
  const axiosServerInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
  });

  // Create the interceptor for server requests.
  axiosServerInstance.interceptors.request.use(
    async (config: InternalAxiosRequestConfig) => {
      if (session) {
        authorizationInterceptor(config, session.user.accessToken);
      }

      return config;
    },
  );

  return axiosServerInstance;
};

export const authenticatedClientApi = createAxiosClientInstance;
export const authenticatedServerApi = createAxiosServerInstance;
