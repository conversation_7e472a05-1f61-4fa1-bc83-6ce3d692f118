import {AxiosError, AxiosResponse} from 'axios';
import {requestOptions} from './types';
import {authenticatedClientApi, authenticatedServerApi} from './axiosInstance';
import {Session} from 'next-auth';
import * as Sentry from '@sentry/nextjs';

const requestHandler = async ({
  server = false,
  session = null,
  method,
  url,
  options = {},
  errorMsg = 'Failed to make request',
  throwError = true,
  callback,
}: {
  server?: boolean;
  session?: Session | null;
  method: string;
  url: string;
  options?: requestOptions;
  errorMsg?: string;
  throwError?: boolean;
  callback?: (res: AxiosResponse) => any;
}) => {
  let api;
  if (server && session) {
    api = authenticatedServerApi(session);
  } else api = authenticatedClientApi();

  const traceId = Sentry.getCurrentScope().getPropagationContext()?.traceId;

  if (traceId) {
    options.headers = {
      ...options.headers,
      'sentry-trace': traceId,
    };
  }

  try {
    let response: AxiosResponse;
    switch (method.toLowerCase()) {
      case 'get':
        response = await api.get(url, options);
        break;
      case 'post':
        response = await api.post(url, options.body, options);
        break;
      case 'put':
        response = await api.put(url, options.body, options);
        break;
      case 'patch':
        response = await api.patch(url, options.body, options);
        break;
      case 'delete':
        response = await api.delete(url, options);
        break;
      default:
        throw new Error(`Unsupported method: ${method}`);
    }
    if (callback) {
      return callback(response);
    }
    return response.data;
  } catch (e) {
    if (e instanceof Error) {
      console.log(`${errorMsg}: ${e.message}`);
    } else if (e instanceof AxiosError) {
      console.log(`${errorMsg}: ${e.message}`, e.response?.data);
    }
    if (throwError) throw e;
    return null;
  }
};

export default requestHandler;
