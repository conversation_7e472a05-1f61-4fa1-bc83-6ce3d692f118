import {convertSummariesDataFromServer} from '@libs/utils/summaries/converter';
import useSWR, {mutate} from 'swr';
import {IServerSummariesData, IServerSummary, IUseSummaries} from './types';
import {sortByCreatedAt} from '@libs/utils/summaries/formatter';
import {ISummary} from '@libs/store/summarys/types';

export const getSummaryKeyPerDocument = (documentId: string) =>
  `/documents/${documentId}/summarisations`;

export default function useSummaries(documentId: string): IUseSummaries {
  const key = getSummaryKeyPerDocument(documentId);
  const {data, isLoading} = useSWR<IServerSummariesData>(key);

  let summaries: Omit<ISummary, 'summary'>[] = [];
  let latestSummary: Omit<ISummary, 'summary'> | undefined;

  if (data) {
    summaries = convertSummariesDataFromServer(data.values || []).sort(
      sortByCreatedAt,
    );

    latestSummary = summaries[0];
  }

  const summaryById = (id: string) =>
    summaries.find(summary => summary.id === id);

  return {
    summaries,
    latestSummary,
    isLoading,
    summaryById,
  };
}

// Adding a new summary
export async function mutateSummariesOnSummaryCreation(
  documentId: string,
  summary: IServerSummary,
) {
  const key = getSummaryKeyPerDocument(documentId);
  try {
    await mutate<IServerSummariesData>(
      key,
      data => {
        if (data) {
          return {
            len: data.len + 1,
            values: [...data.values, summary],
          };
        }
      },
      {revalidate: false},
    );
  } catch (error) {
    console.log('Error on mutating summaries:', error);
  }
}

//Updating the summary
//Note: updates are of IServerSummary type
export async function mutateSummariesOnSummaryUpdate(
  documentId: string,
  summaryId: string,
  updates: Partial<IServerSummary>,
) {
  const key = getSummaryKeyPerDocument(documentId);
  try {
    await mutate<IServerSummariesData>(
      key,
      data => {
        if (data) {
          const values = data.values.map(summary => {
            if (summary.id === summaryId) {
              return {...summary, ...updates};
            }
            return summary;
          });

          return {
            ...data,
            values,
          };
        }
      },
      {revalidate: false},
    );
  } catch (error) {
    console.log('Error on mutating summaries:', error);
  }
}
