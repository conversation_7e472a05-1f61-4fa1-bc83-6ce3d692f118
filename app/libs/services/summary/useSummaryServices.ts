import {IPromptFormData} from '@libs/store/summarys/types';
import {IUseSummaryServices} from './types';
import summaryClientServices from './summaryClientServices';

const useSummaryServices = (): IUseSummaryServices => {
  const updateNewPrompt = async ({
    newPromptData,
  }: {
    newPromptData: IPromptFormData;
  }) => {
    const updatedPrompt = await summaryClientServices.updateNewPrompt({
      newPromptData,
    });
    return updatedPrompt;
  };

  const generateFixedPromptSummary = async ({
    documentId,
    prompt,
  }: {
    documentId: string;
    prompt: IPromptFormData;
  }) => {
    const submittedSummary =
      await summaryClientServices.generateFixedPromptSummary({
        documentId,
        prompt,
      });
    return submittedSummary;
  };

  const generateCustomPromptSummary = async ({
    documentId,
    prompt,
  }: {
    documentId: string;
    prompt: string;
  }) => {
    const submittedSummary =
      await summaryClientServices.generateCustomPromptSummary({
        documentId,
        prompt,
      });
    return submittedSummary;
  };

  return {
    updateNewPrompt,
    generateFixedPromptSummary,
    generateCustomPromptSummary,
  };
};

export default useSummaryServices;
