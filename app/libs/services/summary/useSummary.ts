import useSWR from 'swr';
import {IServerFullSummary, IUseSummary} from './types';
import {convertSummaryFullDataFromServer} from '@libs/utils/summaries/converter';
import {ISummary} from '@libs/store/summarys/types';

export default function useSummary(summaryId: string): IUseSummary {
  let {data, isLoading, error, mutate} = useSWR<IServerFullSummary>(
    `/summarisations/${summaryId}`,
  );

  let summaryDetails: ISummary | undefined;

  if (data) {
    summaryDetails = convertSummaryFullDataFromServer(data) as ISummary;
  }

  return {summaryDetails, error, isLoading, mutate};
}
