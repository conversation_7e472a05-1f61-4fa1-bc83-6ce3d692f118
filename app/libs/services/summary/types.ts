import {IPrompt, IPromptFormData} from '@libs/store/summarys/types';
import {IServerResponse} from '../types';
import {ISummary, PromptBuilderMethod} from '@libs/store/summarys/types';
import {KeyedMutator} from 'swr';
import {IErrorDetails, IStatus} from '@libs/store/status/types';

export interface ISummaryClientServices {
  updateNewPrompt: ({
    newPromptData,
  }: {
    newPromptData: IPromptFormData;
  }) => Promise<IServerResponse<IPrompt>>;
  generateFixedPromptSummary: ({
    documentId,
    prompt,
  }: {
    documentId: string;
    prompt: IPromptFormData;
  }) => Promise<IServerResponse<IServerSummary>>;
  generateCustomPromptSummary: ({
    documentId,
    prompt,
  }: {
    documentId: string;
    prompt: string;
  }) => Promise<IServerResponse<IServerSummary>>;
}

export interface IUseSummaryServices {
  updateNewPrompt: ({
    newPromptData,
  }: {
    newPromptData: IPromptFormData;
  }) => Promise<IServerResponse<IPrompt>>;
  generateFixedPromptSummary: ({
    documentId,
    prompt,
  }: {
    documentId: string;
    prompt: IPromptFormData;
  }) => Promise<IServerResponse<IServerSummary>>;
  generateCustomPromptSummary: ({
    documentId,
    prompt,
  }: {
    documentId: string;
    prompt: string;
  }) => Promise<IServerResponse<IServerSummary>>;
}

export interface IUseSummaries {
  summaries: Omit<ISummary, 'summary'>[];
  latestSummary: Omit<ISummary, 'summary'> | undefined;
  isLoading: boolean;
  summaryById: (id: string) => Omit<ISummary, 'summary'> | undefined;
}
export interface IUseSummary {
  summaryDetails: ISummary | undefined;
  error: Error | undefined;
  isLoading: boolean;
  mutate: KeyedMutator<IServerFullSummary>;
}

export interface IServerSummary {
  id: string;
  prompt: string;
  builder_method: PromptBuilderMethod;
  status: IStatus;
  date_created: string;
  error_details?: IErrorDetails;
}

export interface IServerFullSummary extends IServerSummary {
  summary: string;
}

export interface IServerSummariesData {
  len: number;
  values: IServerSummary[];
}

//document_id is added in the request
export interface IPromptServerData {
  trying_to_do: string;
  for_me: boolean | undefined;
  who_for: string;
  student: boolean | undefined;
  field: string;
  learning_differences: string;
  bullet_points: boolean | undefined;
  focusing_on: string;
  specific_summary: string;
  tone: string;
  word_limit: number;
}
