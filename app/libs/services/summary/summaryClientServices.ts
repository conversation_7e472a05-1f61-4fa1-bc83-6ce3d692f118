import {IPrompt, IPromptFormData} from '@libs/store/summarys/types';
import {IServerResponse} from '../types';
import {IServerSummary, ISummaryClientServices} from './types';
import {convertPromptDataToServer} from '@libs/utils/summaries/converter';
import {authenticatedClientApi} from '../axiosInstance';
import {AxiosError, AxiosResponse} from 'axios';

const summaryClientServices: ISummaryClientServices = {
  updateNewPrompt: async ({
    newPromptData,
  }: {
    newPromptData: IPromptFormData;
  }) => {
    return authenticatedClientApi()
      .post(
        '/summarisations/view-prompt',
        convertPromptDataToServer(newPromptData),
      )
      .then((res: AxiosResponse<IServerResponse<IPrompt>>) => {
        if (res.data.data) {
          return res.data;
        } else {
          //TODO Handle server error response with toast: IServerErrorResponse
          const {message, code} = res.data;
          const err = `Failed to get document's built prompt: ${message}`;
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
      })
      .catch((err: Error | AxiosError) => {
        console.log("Failed to get document's built prompt:", err);
        throw new Error("Failed to get document's built prompt");
      });
  },

  generateFixedPromptSummary: async ({
    documentId,
    prompt,
  }: {
    documentId: string;
    prompt: IPromptFormData;
  }) => {
    return authenticatedClientApi()
      .post('/summarisations', {
        document_id: documentId,
        ...convertPromptDataToServer(prompt),
      })
      .then((res: AxiosResponse<IServerResponse<IServerSummary>>) => {
        if (res.data.data) {
          return res.data;
        } else {
          //TODO Handle server error response with toast: IServerErrorResponse
          const {message, code} = res.data;
          const err = `Failed to get document's summary from built prompt: ${message}`;
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
      })
      .catch((err: Error | AxiosError) => {
        console.log("Failed to get document's summary from built prompt:", err);
        throw new Error("Failed to get document's summary from built prompt");
      });
  },

  generateCustomPromptSummary: async ({
    documentId,
    prompt,
  }: {
    documentId: string;
    prompt: string;
  }) => {
    return authenticatedClientApi()
      .post('/summarisations/custom', {
        document_id: documentId,
        freeTextPrompt: prompt,
      })
      .then((res: AxiosResponse<IServerResponse<IServerSummary>>) => {
        if (res.data.data) {
          return res.data;
        } else {
          //TODO Handle server error response with toast: IServerErrorResponse
          const {message, code} = res.data;
          const err = `Failed to get document's summary from custom prompt: ${message}`;
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
      })
      .catch((err: Error | AxiosError) => {
        console.log(
          "Failed to get document's summary from custom prompt:",
          err,
        );
        throw new Error("Failed to get document's summary from custom prompt");
      });
  },
};

export default summaryClientServices;
