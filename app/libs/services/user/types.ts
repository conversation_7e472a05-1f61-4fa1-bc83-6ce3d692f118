import {User} from 'next-auth';
import {IServerResponse} from '../types';
import {ILicenseMinima, ISettings} from '@libs/store/settings/types';
import {IOccupation} from '@components/onboarding/register/types';
import {UserType} from '@libs/utils/user/useUserDataFromSession';

export interface IUserServices {
  sendUserEmailForMagicLink: (
    email: string,
    tos_accepted: boolean,
    registration: boolean,
  ) => Promise<IServerResponse<IUserSignInData> | undefined>;
  sendNewUserForMagicLink: (
    email: string,
    tos_accepted: boolean,
    registration: boolean,
    first_name: string,
    last_name: string,
    settings: ISettings,
    occupation: IOccupation,
    occupation_title: string,
    gdpr_acceptance: boolean,
  ) => Promise<IServerResponse<IUserSignInData> | undefined>;
  verifyUserAccountWithMagicLink: (ticket: string) => Promise<User | null>;
  deleteUser: () => Promise<boolean>;
  fetchUserSettings: () => Promise<IServerResponse>;
  updateUserSettings: (settings: ISettings) => Promise<IServerResponse>;
  sendUserFeedback: (
    user: string,
    topic: string,
    feedback: string,
    contact: boolean,
  ) => Promise<IServerResponse>;
}

export enum ROLE_NAME {
  ACCESS_ONLY = 'access-only',
  GUEST_USER = 'guest-user',
  GENERAL_USER = 'general-user',
  ADMIN_USER = 'admin-user',
}

export interface IPermission {
  action: string; // TODO type actions and subjects
  subject: string[];
}

export interface IUserData {
  auth: {
    refresh_token: string;
    access_token: string;
    access_expiry: number;
  };
  details: {
    id: string;
    permissions: IPermission[];
    trackingId: string;
    email?: string;
    first_name?: string;
    last_name?: string;
    settings?: ISettings;
    gdpr_acceptance: Date | null;
    license: ILicenseMinima;
  };
}

export interface IUserSignInData {
  magic_link_url: string;
  user_type: UserType;
}

export interface IUserRegistrationData {
  email: string;
}
