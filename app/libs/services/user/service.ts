import {Session} from 'next-auth';
import {IServerResponse} from '../types';
import {IUserData, IUserRegistrationData, IUserSignInData} from './types';
import {getUser} from '@libs/utils/user/coverter';
import {ISettings} from '@libs/store/settings/types';
import {IOccupation} from '@components/onboarding/register/types';
import {AxiosResponse} from 'axios';
import requestHandler from '../requestHandler';
class UserService {
  public server: boolean = false;
  public session: Session | null = null;

  constructor(server: boolean = false, session: Session | null = null) {
    this.server = server;
    this.session = session;
  }

  public async sendUserEmailForMagicLink(
    email: string,
    tos_accepted: boolean,
    registration: boolean,
  ) {
    return await requestHandler({
      server: this.server,
      session: this.session,
      method: 'post',
      url: process.env.NEXT_PUBLIC_API_URL + '/auth/magic-link/email',
      options: {
        body: {
          email,
          tos_accepted,
          registration,
        },
      },
      errorMsg: 'Failed to gain access',
      callback: (res: AxiosResponse<IServerResponse<IUserSignInData>>) => {
        return res.data;
      },
    });
  }

  public async getRegistrationTicketDetails(ticket: string) {
    return await requestHandler({
      server: this.server,
      session: this.session,
      method: 'get',
      url: process.env.NEXT_PUBLIC_API_URL + '/auth/register/details/' + ticket,
      errorMsg: 'Failed to get ticket',
      throwError: false,
      callback: (
        res: AxiosResponse<IServerResponse<IUserRegistrationData>>,
      ) => {
        return res.data;
      },
    });
  }

  public async sendNewUserForMagicLink(
    email: string,
    tos_accepted: boolean,
    registration: boolean,
    first_name: string,
    last_name: string,
    settings: ISettings,
    occupation: IOccupation,
    occupation_title: string,
    gdpr_acceptance: boolean,
    ticket?: string,
  ) {
    return await requestHandler({
      server: this.server,
      session: this.session,
      method: 'post',
      url: process.env.NEXT_PUBLIC_API_URL + '/auth/magic-link/email',
      options: {
        body: {
          email,
          registration,
          tos_accepted,
          first_name,
          last_name,
          settings,
          occupation,
          occupation_title,
          gdpr_acceptance,
          ticket,
        },
      },
      errorMsg: 'Failed to gain access',
      callback: (res: AxiosResponse<IServerResponse<IUserSignInData>>) => {
        return res.data;
      },
    });
  }

  public async verifyUserAccountWithMagicLink(ticket: string) {
    return await requestHandler({
      server: this.server,
      session: this.session,
      method: 'post',
      url: process.env.NEXT_PUBLIC_API_URL + '/auth/magic-link/auth',
      options: {
        body: {
          ticket,
        },
      },
      errorMsg: 'Failed to gain access',
      callback: (res: AxiosResponse<IServerResponse<IUserData>>) => {
        return getUser(res.data.data);
      },
    });
  }

  public async getUserAccountFromCredentials(data: IUserData) {
    return getUser(data);
  }

  public async deleteUser() {
    return await requestHandler({
      server: this.server,
      session: this.session,
      method: 'delete',
      url: '/users',
      errorMsg: 'Failed to delete user',
      callback: (res: AxiosResponse<IServerResponse>) => {
        const {code} = res.data;
        if (code === 0) {
          return true;
        } else {
          const {message, code} = res.data;
          console.log(
            `Failed to update users's name: ${message}, code: ${code}`,
          );
          return false;
        }
      },
    });
  }

  public async updateUserSettings(settings: ISettings) {
    return await requestHandler({
      server: this.server,
      session: this.session,
      method: 'put',
      url: '/users/settings',
      options: {
        body: {
          settings: settings,
        },
      },
      errorMsg: `Failed to update user's settings`,
      callback: (res: AxiosResponse<IServerResponse>) => {
        const {code} = res.data;
        if (code === 0) {
          return res.data;
        } else {
          const {message, code} = res.data;
          console.log(
            `Failed to update users's settings: ${message}, code: ${code}`,
          );
          throw new Error(`Failed to update user's settings: ${message}`);
        }
      },
    });
  }

  public async fetchUserSettings() {
    return await requestHandler({
      server: this.server,
      session: this.session,
      method: 'get',
      url: '/users/settings',
      errorMsg: "Failed to fetch user's settings",
      callback: (
        res: AxiosResponse<{message: string; code: number; data: any}>,
      ) => {
        const {code, data} = res.data;
        if (code === 0) {
          return data;
        } else {
          const {message, code} = res.data;

          console.log(
            `Failed to fetch users's settings: ${message}, code: ${code}`,
          );

          throw new Error(`Failed to fetch user's settings: ${message}`);
        }
      },
    });
  }

  public async sendUserFeedback(
    user: string,
    user_uuid: string,
    subject: string,
    location_in_app: string,
    feedback: string,
    reply_consent: boolean,
  ) {
    return await requestHandler({
      server: this.server,
      session: this.session,
      method: 'post',
      url: '/users/feedback',
      options: {
        body: {
          user,
          user_uuid,
          subject,
          location_in_app,
          feedback,
          reply_consent,
        },
      },
      errorMsg: `Failed to send feedback`,
      callback: (res: AxiosResponse<IServerResponse>) => {
        const {code} = res.data;
        if (code === 0) {
          return res.data;
        } else {
          const {message, code} = res.data;
          console.log(`Failed to send feedback: ${message}, code: ${code}`);
          throw new Error(`Failed to send feedback: ${message}`);
        }
      },
    });
  }

  public sendUserSatifactionRatingFeedback(
    user: string,
    user_uuid: string,
    subject: string,
    page: string | null,
    documentType: string,
    rating: string,
    feedbackText: string,
  ) {
    return requestHandler({
      server: this.server,
      session: this.session,
      method: 'post',
      url: '/users/feedback',
      options: {
        body: {
          user,
          user_uuid,
          subject,
          location_in_app: page,
          document_type: documentType,
          user_rating: rating,
          feedback: feedbackText,
        },
      },
      errorMsg: `Failed to send feedback`,
      callback: (res: AxiosResponse<IServerResponse>) => {
        const {code} = res.data;
        if (code === 0) {
          return res.data;
        } else {
          const {message, code} = res.data;
          console.log(`Failed to send feedback: ${message}, code: ${code}`);
          throw new Error(`Failed to send feedback: ${message}`);
        }
      },
    });
  }
}

export default UserService;
