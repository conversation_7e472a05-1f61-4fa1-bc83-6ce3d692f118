import {IStatus, IErrorDetails} from '@libs/store/status/types';
import {SendMessage} from 'react-use-websocket';

export interface ICommsData {
  url: string;
}

export enum IMessageUpdateType {
  documentStatus = 'document status',
  documentAudioStatus = 'document audio status',
  summaryStatus = 'summary status',
}
export enum IMessageTarget {
  document = 'document',
  documentAudio = 'document audio',
  summary = 'summary',
}

//Use union type to extend the values if needed
export type IMessageValue = IStatus | null;

export interface IWebSocketMessage {
  updateType: IMessageUpdateType;
  documentId: string;
  target: IMessageTarget;
  targetId: string;
  value: IMessageValue;
  message?: IErrorDetails;
}

export interface IUseReactWebsocket {
  sendMessage: SendMessage;
  lastMessage: IWebSocketMessage | null;
}

export interface IServerWebSocketMessage {
  update_type: IMessageUpdateType;
  document_id: string;
  target: IMessageTarget;
  target_id: string;
  value: IMessageValue;
  message?: IErrorDetails;
}
