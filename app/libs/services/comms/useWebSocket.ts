'use client';

import {
  IServerWebSocketMessage,
  IUseReactWebsocket,
  IWebSocketMessage,
} from './types';
import useGetWebSocketUrl from './useGetWebSocketUrl';
import useReactWebsocket from 'react-use-websocket';
import {convertWebSocketMessageFromServer} from '@libs/utils/comms/converter';
import React from 'react';
import useTabHasFocus from '@libs/utils/tab/useTabHasFocus';
import {useIsMounted} from 'usehooks-ts';

/** Websocket reconnects on the browser tab's focus if previous connection gets closed thanks to a new url being generated each time on the tab focus in useGetWebSocketUrl; We use only use the new url when the current one is cancelled.
 * When the tab is out of focus we cancel any attempts for reconnect as the new url will be issued upon regaining the focus and all endpoint are revalidated
 *
 */

export default function useWebSocket(): IUseReactWebsocket {
  const url = useGetWebSocketUrl();
  const hasFocus = useTabHasFocus();
  const isMounted = useIsMounted();
  const wsUrl = React.useRef<null | string>(null);

  React.useEffect(() => {
    if (!wsUrl.current && url) {
      wsUrl.current = url;
    }
  }, [url]);

  const onOpen = () => {
    // Leave for debugging
    console.log('Opening ws connection...');
    const ticket = wsUrl.current?.split('?')[1];
    console.log(url, wsUrl.current, ticket);
  };

  const onClose = () => {
    wsUrl.current = null;
    console.log('Ws connection closed for url:', wsUrl.current);
  };

  //Will attempt to reconnect on all close events, such as server shutting down
  // Will stop automatic reconnection on component unmounting ( when user is log-out due to expired token)
  const shouldReconnect = () => isMounted() && hasFocus;

  // Passing new url closes the existing connection and opens a new one each time hence use of wsUrl ref to prevent that happening
  const {sendMessage, lastMessage} = useReactWebsocket(wsUrl.current, {
    onOpen,
    onClose,
    shouldReconnect,
    reconnectAttempts: hasFocus ? 10 : 0,
    retryOnError: hasFocus,
  });

  let message: IWebSocketMessage | null = null;
  if (lastMessage && lastMessage.data) {
    const parsedMessage: IServerWebSocketMessage = JSON.parse(lastMessage.data);
    message = convertWebSocketMessageFromServer(parsedMessage);
  }

  return {sendMessage, lastMessage: message};
}
