import {mutateDocumentsOnDocumentUpdate} from '../document/useDocuments';
import {mutateSummariesOnSummaryUpdate} from '../summary/useSummaries';
import {IMessageUpdateType} from './types';
import useWebSocket from './useWebSocket';

export default function useHandleWebSocketsMessages() {
  const {lastMessage} = useWebSocket();

  if (lastMessage) {
    const {value, updateType, documentId, message, targetId} = lastMessage;

    switch (updateType) {
      case IMessageUpdateType.documentStatus:
        if (value) {
          mutateDocumentsOnDocumentUpdate(documentId, {
            status: value,
            error_details: message,
          });
        }
        break;
      case IMessageUpdateType.summaryStatus:
        if (value) {
          mutateSummariesOnSummaryUpdate(documentId, targetId, {
            status: value,
            error_details: message,
          });
        }
        break;
      case IMessageUpdateType.documentAudioStatus:
        console.log(lastMessage);
        break;
    }
  }
}
