'use client';

import useS<PERSON> from 'swr';
import {ICommsData} from './types';
import {fetcher} from '../swrParams';

export default function useGetWebSocketUrl(): string | null {
  const {data, error} = useSWR<ICommsData>('/socket-url', fetcher, {
    revalidateIfStale: true,
    revalidateOnFocus: true,
    revalidateOnReconnect: true,
  });

  if (error) {
    //TODO handle error via notifications/snackbar?
    console.log(error);
  }
  if (data) {
    const {url} = data;
    return url;
  }
  return null;
}
