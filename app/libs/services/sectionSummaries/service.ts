import {AxiosResponse} from 'axios';
import requestHandler from '../requestHandler';
import {IServerResponse} from '../types';
import {ISectionSummaryService} from './types';

export interface IServerSectionSummaryData {
  code: number;
  message: string;
  data: string[] | undefined;
}

const SectionSummaryService: ISectionSummaryService = {
  sendSectionSummaryConfig: async (id: string, section_id: string) => {
    return await requestHandler({
      method: 'post',
      url: `/documents/${id}/summary/${section_id}`,
      errorMsg: 'Failed to send section summary prompt config',
      callback: async (
        res: AxiosResponse<IServerResponse<IServerSectionSummaryData>>,
      ) => {
        const data = res.data;
        if (data) {
          return data;
        } else {
          const {message, code} = res.data;
          const err = `Failed to send section summary prompt config: ${message}`;
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
      },
    });
  },
};

export default SectionSummaryService;
