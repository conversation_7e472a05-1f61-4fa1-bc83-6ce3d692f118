import {useSearchParams} from 'next/navigation';
import {useAnalytics} from 'use-analytics';

export const useSearchAnalytics = () => {
  const {track} = useAnalytics();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  const trackOpenSearch = (page: string) => {
    track('search: OpenSearch', {
      Page: tabParam,
    });
  };

  const trackCloseSearch = () => {
    track('search: CloseSearch');
  };

  const trackGenerateSearch = (
    lengthOfQuery: number,
    numberOfResults: number,
    page: string,
  ) => {
    track('search: GenerateSearch', {
      'Length Of Query': lengthOfQuery,
      'Number Of Results': numberOfResults,
      Page: tabParam,
    });
  };

  const trackClearRecentSearches = () => {
    track('search: ClearRecentSearches');
  };

  const trackClearSelectedWord = () => {
    track('search: ClearSelectedWord');
  };

  return {
    trackOpenSearch,
    trackCloseSearch,
    trackGenerateSearch,
    trackClearRecentSearches,
    trackClearSelectedWord,
  };
};
