import {useSearchParams} from 'next/navigation';
import {useAnalytics} from 'use-analytics';

export const useSectionSummariesAnalytics = () => {
  const {track} = useAnalytics();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  const trackGenerateSectionSummary = (
    inputLength: number,
    outputLength: number,
  ) => {
    track('Summarise', {
      LengthOfInput: inputLength,
      LengthOfOutput: outputLength,
      location: tabParam,
    });
  };

  const trackCollapseSectionSummary = () => {
    track('Collapse');
  };

  const trackExpandSectionSummary = () => {
    track('Expand');
  };

  const trackLearnMoreSummary = () => {
    track('AIDisclaimerLink');
  };

  return {
    trackGenerateSectionSummary,
    trackCollapseSectionSummary,
    trackExpandSectionSummary,
    trackLearnMoreSummary,
  };
};
