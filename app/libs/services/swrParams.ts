import {AxiosResponse} from 'axios';
import requestHandler from './requestHandler';
import {IServerResponse} from './types';

export const fetcher = async (url: string) => {
  return await requestHandler({
    method: 'get',
    url: url,
    errorMsg: 'Failed to fetch data',
    callback: (res: AxiosResponse<IServerResponse>) => {
      if (res.data.data) {
        // Server successful response
        return res.data.data;
      } else {
        // Server error response, that gets caught by SWR error
        const {message, code} = res.data;
        throw new Error(message, {cause: code});
      }
    },
  });
};

//This options reduce the number of calls made to server by disabling all kinds of automatic revalidation for the time being except of on focus; will need updating when we bring other features or based on users permissions.
export const defaultOptions = {
  revalidateIfStale: false,
  revalidateOnFocus: false,
  revalidateOnReconnect: false,
};
