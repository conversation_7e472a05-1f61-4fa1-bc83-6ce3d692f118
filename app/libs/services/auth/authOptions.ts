import {
  Account,
  CallbacksOptions,
  NextAuthOptions,
  PagesOptions,
  Profile,
  SessionOptions,
} from 'next-auth';

import {Provider} from 'next-auth/providers';
import UserService from '@libs/services/user/service';
import {isTokenExpired, refreshAccessToken} from './token';
import {defaultSettings} from '@libs/store/settings/values';
import CredentialsProvider from 'next-auth/providers/credentials';

const pages: Partial<PagesOptions> = {signIn: '/verify-account'};

const session: Partial<SessionOptions> = {
  strategy: 'jwt',
};

const userService = new UserService();

const providers: Provider[] = [
  CredentialsProvider({
    id: 'magic-link',
    name: 'magic-link',
    credentials: {
      ticket: {label: 'Ticket', type: 'text'},
    },
    async authorize(_, req) {
      try {
        return await userService.verifyUserAccountWithMagicLink(
          req.body?.ticket,
        );
      } catch (e) {
        console.log('Failed to authorize magic link.', e);
        return null;
      }
    },
  }),
  CredentialsProvider({
    id: 'authenticated-jwt',
    name: 'autheticated-jwt',
    credentials: {
      authData: {label: 'AutheticationData', type: 'json'},
    },
    async authorize(_, req) {
      try {
        return await userService.getUserAccountFromCredentials(
          JSON.parse(req.body?.authData),
        );
      } catch (e) {
        console.log('FAILED', e);
        return null;
      }
    },
  }),
];
const callbacks: Partial<CallbacksOptions<Profile, Account>> = {};

callbacks.signIn = async function signIn({user}) {
  if (user) return true;

  return false;
};

//The jwt() callback is invoked before the session() callback, so anything you add to the JSON Web Token will be immediately available in the session callback
callbacks.jwt = async function jwt({token, user, trigger, session}) {
  if (trigger && trigger === 'signIn') {
    //TODO: Send off the sign-in with account and license
  }

  // Only passed the first time this callback is called on a new session
  if (user) {
    // Save the access token and refresh token in the JWT on the initial login
    return {
      ...token,
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      settings: user.settings || defaultSettings,
      permissions: user.permissions,
      accessToken: user.accessToken,
      refreshToken: user.refreshToken,
      accessExpiry: user.accessExpiry,
      trackingId: user.trackingId,
      gdprAcceptance: user.gdprAcceptance,
      license: user.license,
    };
  }

  if (isTokenExpired(token)) {
    // If the access token has expired, try to refresh it
    const tokenWithError = {
      ...token,
      error: 'RefreshAccessTokenError' as const,
    };

    try {
      const newToken = await refreshAccessToken(token.id, token.refreshToken);

      if (newToken === null) {
        // The error property will be used client-side to handle the refresh token error when needed to sign users out
        return tokenWithError;
      }
      const {accessToken, refreshToken, accessExpiry} = newToken;
      return {
        ...token, //TODO this is actually missing the new exp
        id: newToken.id,
        email: newToken.email,
        firstName: newToken.firstName,
        lastName: newToken.lastName,
        settings: newToken.settings || defaultSettings,
        permissions: newToken.permissions,
        license: newToken.license,
        trackingId: newToken.trackingId,
        accessToken,
        refreshToken,
        accessExpiry,
      };
    } catch (e) {
      console.error('Error refreshing access token', e);
      return tokenWithError;
    }
  } else {
    // The access token has not expired yet -> return it
    // Updating the session if triggered on client side
    if (trigger === 'update') {
      token.accessToken = session.accessToken;
      token.permissions = session.permissions;
      token.accessExpiry = session.accessExpiry;
      token.firstName = session.firstName;
      token.lastName = session.lastName;
      token.settings = session.settings;
      token.gdprAcceptance = session.gdprAcceptance;
      token.license = session.license;
      token.trackingId = session.trackingId;
    }
    return token;
  }
};

callbacks.session = async function session({session, token}) {
  return {
    ...session,
    user: {
      ...session.user,
      id: token.id,
      email: token.email,
      firstName: token.firstName,
      lastName: token.lastName,
      settings: token.settings,
      permissions: token.permissions,
      license: token.license,
      accessToken: token.accessToken,
      refreshToken: token.refreshToken,
      accessExpiry: token.accessExpiry,
      error: token.error,
      gdprAcceptance: token.gdprAcceptance,
      trackingId: token.trackingId,
    },
  };
};

export const authOptions: NextAuthOptions = {
  session,
  providers,
  callbacks,
  pages,
  secret: process.env.NEXTAUTH_SECRET,
};
