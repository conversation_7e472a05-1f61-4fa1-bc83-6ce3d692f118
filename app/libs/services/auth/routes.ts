import {getServerSession} from 'next-auth/next';
import {authOptions} from '@libs/services/auth/authOptions';
import {redirect} from 'next/navigation';
import {defaultSteps} from '@libs/store/onboarding/steps';

export const enum AppPublicRoutes {
  SignIn = '/',
  VerifyAccount = '/verify-account',
  VerifyEmail = '/verify-email',
  CheckEmail = '/check-email',
  OnboardingStart = '/onboarding',
  OnboardingEnd = '/onboarding/create-account',
}

export const enum AppPrivateRoutes {
  Dashboard = '/dashboard',
  Admin = '/admin',
  Account = '/account',
  SignOut = '/logout',
}
//Dynamic private routes
export const AppDynamicRoutes = {
  document: (docId: string) => `/document/${docId}`,
  summary: (docId: string, summaryId: string) =>
    `/document/${docId}/summary/${summaryId}`,
  onboarding: (step: string, ticket?: string) =>
    `/onboarding/${step}${ticket ? `?ticket=${ticket}` : ''}`,
};

export const enum AppSharedRoutes {
  TermsOfService = '/terms-of-service',
  PrivacyStatement = '/privacy-statement',
  DocumentExample = '/documents/example',
}

// NOTE: using process.env.NEXTAUTH_URL in production doesn't work as expected for dynamic routes
// Prevents non-authenticated users from accessing private route same as middleware; ensures that no html code is being sent from server as middleware isn't fast enough;
export const protectPrivateRoutes = async () => {
  const session = await getServerSession(authOptions);

  //TODO: extend the check of permissions when we get rid of the guest login page
  // session && session.user.permissions.length
  if (session === null) {
    return redirect(AppPublicRoutes.SignIn);
  }
};

// Redirects logged-in users to dashboard while trying to access the public routes
export const disablePublicRoutesForAuthUser = async () => {
  const session = await getServerSession(authOptions);

  if (session !== null) {
    if (session.user.error === 'RefreshAccessTokenError') {
      //log user out
      return redirect(AppPrivateRoutes.SignOut);
    } else {
      return redirect(AppPrivateRoutes.Dashboard);
    }
  }
};

// Protects private routes such as admin based on user permissions;
//Invokes on server;
export const protectExplicitRoute = async (route: AppPrivateRoutes) => {
  const session = await getServerSession(authOptions);

  if (session && session.user.permissions) {
    const readPermissions = session.user.permissions.find(
      permission => permission.action === 'read',
    );

    const subject = route.slice(1);

    if (
      readPermissions === undefined ||
      !readPermissions?.subject.includes(subject)
    ) {
      return redirect(AppPrivateRoutes.Dashboard);
    }
  }
};

export const isValidOnboardingStep = (step: string) =>
  defaultSteps[step] !== undefined || redirect(AppPublicRoutes.OnboardingStart);
