import {User} from 'next-auth';
import {JWT} from 'next-auth/jwt';
import axios, {AxiosError, AxiosResponse} from 'axios';
import {IServerResponse} from '../types';
import {IUserData} from '../user/types';
import {getUser} from '@libs/utils/user/coverter';
import {JwtPayload} from 'jsonwebtoken';

export function isTokenInvalid(token: JWT | JwtPayload): boolean {
  // Does the token have a valid issuer and contains an expiry time
  if (!token.iss || token.iss !== 'EstendioLicensing' || !token.exp) {
    return true;
  }
  return false;
}

export function isTokenExpired(token: JWT): boolean {
  // 15 mins margin accounts for potential delay in processing the request
  const MSEC = 1000;
  const MIN = 60 * MSEC;
  const SERVER_LATENCY = 15 * MIN;
  // Expiry gets returned from server in seconds; needs converting to milliseconds;
  const tokenExpiry = token.exp;

  return Date.now() > tokenExpiry - SERVER_LATENCY;
}

export async function refreshAccessToken(
  userId: string,
  refreshToken: string,
): Promise<User | null> {
  return axios({
    method: 'post',
    url: process.env.NEXT_PUBLIC_API_URL + '/auth/refresh',
    data: {
      user_id: userId,
      refresh_token: refreshToken,
    },
  })
    .then((res: AxiosResponse<IServerResponse<IUserData>>) => {
      return getUser(res.data.data);
    })
    .catch((e: Error | AxiosError) => {
      console.log('Failed to refresh access token:', e);
      throw new Error('Error refreshing access token');
    });
}
