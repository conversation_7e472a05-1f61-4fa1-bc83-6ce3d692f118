import {AxiosResponse} from 'axios';
import requestHandler from '../requestHandler';
import {IServerResponse} from '../types';
import {
  IExplainPromptConfig,
  IExplainService,
  IServerPromptData,
} from './types';

const explainService: IExplainService = {
  sendExplainPromptConfig: async (id: string, req: IExplainPromptConfig) => {
    return await requestHandler({
      method: 'post',
      url: `/documents/${id}/explain`,
      options: {
        body: req,
      },
      errorMsg: 'Failed to send explain prompt config',
      callback: async (
        res: AxiosResponse<IServerResponse<IServerPromptData>>,
      ) => {
        const data = res.data.data;
        if (data) {
          return data;
        } else {
          const {message, code} = res.data;
          const err = `Failed to send explain prompt config: ${message}`;
          console.log(err);
          throw new Error(err, {
            cause: code,
          });
        }
      },
    });
  },
};

export default explainService;
