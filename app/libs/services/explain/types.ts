import {AxiosResponse} from 'axios';
import {IServerResponse} from '../types';

export interface IExplainPromptConfig {
  input_text: string;
  level_of_detail: ExplainPromptConfigLevelOfDetail;
  language_complexity: ExplainPromptConfigLanguageComplexity;
  level_of_knowledge: ExplainPromptConfigLevelOfKnowledge;
}

export interface IServerPromptData {
  role: string;
  content: string;
  refusal: string | null;
}

export enum ExplainPromptConfigLevelOfDetail {
  BRIEF = 'brief',
  IN_DEPTH = 'in_depth',
}

export enum ExplainPromptConfigLanguageComplexity {
  SIMPLE = 'simple',
  MATCH_SOURCE = 'match_source',
}

export enum ExplainPromptConfigLevelOfKnowledge {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  EXPERT = 'expert',
}

export interface IExplainService {
  sendExplainPromptConfig: (
    id: string,
    req: IExplainPromptConfig,
  ) => Promise<AxiosResponse<IServerResponse<IServerPromptData>>>;
}
