import {useAnalytics} from 'use-analytics';

export const useExplainThisAnalytics = () => {
  const {track} = useAnalytics();

  const trackOpenExplainThis = (location: string, queryLength?: number) => {
    track('OpenExplain', {
      LengthOfQuery: queryLength,
      location: location,
    });
  };

  const trackCloseExplainThis = () => {
    track('CloseExplain');
  };

  const generateExplainThis = (queryLength: number) => {
    track('GenerateExplain', {
      LengthOfQuery: queryLength,
    });
  };

  const resetExplainThis = () => {
    track('ResetExplain');
  };

  const learnMoreExplainThis = () => {
    track('LearnMoreExplain');
  };

  const openReadingPreferencesExplainThis = () => {
    track('OpenReadingPreferences');
  };

  const closeReadingPreferencesExplainThis = () => {
    track('CloseReadingPreferences');
  };

  const readingPreferencesLevelOfDetail = (levelOfDetail: string) => {
    track('ReadingPreferencesLevelOfDetail', {
      LevelOfDetail: levelOfDetail,
    });
  };

  const readingPreferencesLanguageComplexity = (languageComplexity: string) => {
    track('ReadingPreferencesLanguageComplexity', {
      LanguageComplexity: languageComplexity,
    });
  };

  const readingPreferencesLevelOfKnowledge = (levelOfKnowledge: string) => {
    track('ReadingPreferencesLevelOfKnowledge', {
      LevelOfKnowledge: levelOfKnowledge,
    });
  };

  const savedChangesExplainThis = () => {
    track('SavedChangesExplain');
  };

  return {
    trackOpenExplainThis,
    trackCloseExplainThis,
    generateExplainThis,
    resetExplainThis,
    learnMoreExplainThis,
    openReadingPreferencesExplainThis,
    closeReadingPreferencesExplainThis,
    readingPreferencesLevelOfDetail,
    readingPreferencesLanguageComplexity,
    readingPreferencesLevelOfKnowledge,
    savedChangesExplainThis,
  };
};
