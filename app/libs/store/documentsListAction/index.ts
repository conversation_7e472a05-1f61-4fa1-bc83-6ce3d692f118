import {atom, useAtom} from 'jotai';
import {
  IDocumentsListAction,
  IDocumentsListActionType,
  IUseDocumentsListAction,
} from './types';

const defaultDocumentsListAction: IDocumentsListAction = {
  itemId: '',
  actionType: null,
};
const documentsListActionAtom = atom<IDocumentsListAction>(
  defaultDocumentsListAction,
);

const useDocumentsListAction = (): IUseDocumentsListAction => {
  const [documentsListAction, setDocumentsListAction] = useAtom(
    documentsListActionAtom,
  );

  const updateDocumentsListAction = (
    updates: Partial<IDocumentsListAction>,
  ) => {
    setDocumentsListAction(prev => ({...prev, ...updates}));
  };
  const clearAction = () => {
    setDocumentsListAction({
      itemId: '',
      actionType: null,
    });
  };

  return {
    ...documentsListAction,
    updateDocumentsListAction,
    clearAction,
  };
};

export default useDocumentsListAction;
