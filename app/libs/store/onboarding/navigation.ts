import {useAtom} from 'jotai';
import {atomWithStorage, createJSONStorage} from 'jotai/utils';
import {IUseNavigation, IStepDetails, ISteps, Step} from './types';
import {defaultSteps} from './steps';

export const ONBOARDING_STEPS_STORAGE_KEY = 'onboarding-steps';
const stepsAtom = atomWithStorage<ISteps>(
  ONBOARDING_STEPS_STORAGE_KEY,
  defaultSteps,
  createJSONStorage(() => sessionStorage),
);

const useNavigation = (): IUseNavigation => {
  const [steps, setSteps] = useAtom(stepsAtom);
  const updateStep = (stepName: Step, update: Partial<IStepDetails>) => {
    setSteps(prevSteps => {
      return {
        ...prevSteps,
        [stepName]: {
          ...prevSteps[stepName],
          ...update,
        },
      };
    });
  };

  return {
    steps,
    updateStep,
  };
};

export default useNavigation;
