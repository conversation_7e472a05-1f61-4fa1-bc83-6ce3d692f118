// Step enum using kebab case format here as it is used to check against route params
// They must mirror the settings enum to allow for fetching the correct settings
//The enum values are used to navigate between the steps  and corresponds to the page's params
export enum Step {
  Theme = 'theme',
  Font = 'font',
  FontSize = 'font-size',
  LineSpacing = 'line-spacing',
  Alignment = 'text-alignment',
}

export enum StepQuestion {
  Theme = 'Which colour makes the text below easier to read?',
  Font = 'Choose the font that helps you read comfortably',
  FontSize = 'Choose the text size that suits you best',
  LineSpacing = 'Choose the line spacing that helps you read more clearly',
  TextAlignment = 'Do you like text aligned to the left or justified?',
}

export interface IStepDetails {
  completed: boolean;
}

export interface ISteps {
  [key: string]: IStepDetails;
}

export interface IUseNavigation {
  steps: ISteps;
  updateStep: (stepName: Step, update: Partial<IStepDetails>) => void;
}
