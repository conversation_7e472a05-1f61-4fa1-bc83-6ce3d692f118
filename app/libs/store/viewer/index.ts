import {useAtom, atom} from 'jotai';
import {IUseViewer} from './types';

const contentsAtom = atom('');

function useViewerState(): IUseViewer {
  const [currentContentItem, setCurrentContentItem] = useAtom(contentsAtom);

  function updateCurrentContentItem(id: string) {
    setCurrentContentItem(id);
  }

  return {
    updateCurrentContentItem,
    currentContentItem,
  };
}

export default useViewerState;
