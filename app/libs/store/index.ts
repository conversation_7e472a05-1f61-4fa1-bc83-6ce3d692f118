import {useAnalytics} from 'use-analytics';
import useSettings from './settings';
import {IUseStore} from './types';
import {useSWRConfig} from 'swr';

export default function useStore(): IUseStore {
  const {resetSettings} = useSettings();
  const {mutate} = useSWRConfig();
  const {reset} = useAnalytics();

  const clearStore = () => {
    resetSettings();
    //Clears all cached keys from SWR
    mutate(/* match all keys */ () => true, undefined, false);
    reset(); //resets Analytics
  };
  return {clearStore};
}
