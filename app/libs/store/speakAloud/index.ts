import {useAtom, atom} from 'jotai';

import {
  IParagraphSkeleton,
  IUseSpeakAloud,
  IDocumentSkeleton,
  ISectionSkeleton,
} from './types';

import {AudioLoadOptions} from 'react-use-audio-player';

import {
  IContentTextNode,
  IDocumentExtractedData,
  ITextToSpeech,
} from '../document/types';

export enum SpeakAloudState {
  PLAY = 'play',
  PAUSE = 'pause',
  OFF = 'off',
}

export enum SectionStatus {
  START = 'start',
  MIDDLE = 'middle',
  END = 'end',
}

const speakAloudAtom = atom(SpeakAloudState.OFF);
const currentParagraphAtom = atom<IParagraphSkeleton | undefined>(undefined);
const documentAtom = atom<IDocumentSkeleton | undefined>(undefined);
const SectionStatusAtom = atom(SectionStatus.START);
const audioPlayerSettingsAtom = atom<AudioLoadOptions>({
  initialVolume: 1,
  initialRate: 1,
  autoplay: false,
  html5: true,
});

function useSpeakAloud(): IUseSpeakAloud {
  const [speakAloudState, setSpeakAloudState] = useAtom(speakAloudAtom);
  const [documentSkeleton, updateDocumentSkeleton] = useAtom(documentAtom);
  const [currentParagraph, setCurrentParagraph] = useAtom(currentParagraphAtom);
  const [sectionStatus, setSectionStatus] = useAtom(SectionStatusAtom);
  const [audioPlayerSettings, setAudioPlayerSettings] = useAtom(
    audioPlayerSettingsAtom,
  );

  function convertDocumentToSkeleton(
    id: string,
    document: IDocumentExtractedData,
  ): IDocumentSkeleton {
    const sections = Object.values(document.sections);

    return {
      id,
      summary: {
        tts: document?.details?.summary_tts as ITextToSpeech,
      },
      sections: sections.map((section: any) => {
        // Strip out image paragraphs.
        const nonImageParagraphs = section.content.filter(
          (paragraph: any) => paragraph.type !== 'img',
        );

        return {
          id: section.id,
          paragraphs: nonImageParagraphs.map((content: IContentTextNode) => {
            return {
              id: content.id,
              tts: content.tts,
            };
          }),
        };
      }),
    };
  }

  function setInitialSkeletonState(state: IDocumentSkeleton) {
    if (documentSkeleton && documentSkeleton.id === state.id) return;

    updateDocumentSkeleton(state);
  }

  function updateSpeakAloudState(state: SpeakAloudState) {
    setSpeakAloudState(state);
  }

  function clearCurrentParagraph() {
    setCurrentParagraph(undefined);
  }

  function updateCurrentParagraph(paragraph: IParagraphSkeleton) {
    setCurrentParagraph((prev: IParagraphSkeleton | undefined) => {
      return {...prev, ...paragraph};
    });
  }

  function updateSectionStatus(status: SectionStatus) {
    setSectionStatus(status);
  }

  function updateAudioPlayerSettings(settings: AudioLoadOptions) {
    setAudioPlayerSettings((prev: AudioLoadOptions) => {
      return {...prev, ...settings};
    });
  }

  function determineCurrentSection(): ISectionSkeleton | null {
    if (!currentParagraph) return null;

    const section = documentSkeleton?.sections.find(section => {
      const found = section.paragraphs.find(
        paragraph => paragraph.id === currentParagraph.id,
      );

      if (found) return section;

      return null;
    });

    if (!section) return null;

    return section;
  }

  function determineNextParagraph(): IParagraphSkeleton | null {
    if (!currentParagraph) return null;

    const section = determineCurrentSection();

    let currentIndex = section?.paragraphs.findIndex(
      paragraph => paragraph.id === currentParagraph.id,
    );

    if (!currentIndex) {
      currentIndex = 0;
    }

    const nextParagraph = section?.paragraphs[currentIndex + 1];

    if (!nextParagraph || !nextParagraph.tts) {
      return null;
    }

    return nextParagraph;
  }

  function determinePreviousParagraph(): IParagraphSkeleton | null {
    if (!currentParagraph) return null;

    const section = determineCurrentSection();

    let currentIndex = section?.paragraphs.findIndex(
      paragraph => paragraph.id === currentParagraph.id,
    );

    if (!currentIndex) {
      currentIndex = 0;
    }

    const previousParagraph = section?.paragraphs[currentIndex - 1];

    if (!previousParagraph) {
      return null;
    }

    return previousParagraph;
  }

  return {
    audioPlayerSettings,
    updateAudioPlayerSettings,
    determineNextParagraph,
    determinePreviousParagraph,
    setInitialSkeletonState,
    documentSkeleton,
    updateDocumentSkeleton,
    updateSpeakAloudState,
    updateCurrentParagraph,
    speakAloudState,
    currentParagraph,
    sectionStatus,
    updateSectionStatus,
    convertDocumentToSkeleton,
    clearCurrentParagraph,
  };
}

export default useSpeakAloud;
