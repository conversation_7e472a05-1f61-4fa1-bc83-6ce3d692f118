import {AudioLoadOptions} from 'react-use-audio-player';
import {SpeakAloudState, SectionStatus} from '.';
import {IDocumentExtractedData, ITextToSpeech} from '../document/types';

export interface IUpdateAudioFile {
  audio_file: string;
  document_id: string;
  section_id: string;
  paragraph_id: string;
  status: string;
  order: number;
}

export interface IUpdateAudioFiles extends Array<IUpdateAudioFile> {}

export interface ISectionSkeleton {
  id: string;
  paragraphs: Array<IParagraphSkeleton>;
}

export interface IParagraphSkeleton {
  id: string;
  tts: ITextToSpeech;
}

export interface ISectionSkeleton {
  id: string;
  paragraphs: Array<IParagraphSkeleton>;
}

export interface IDocumentSkeleton {
  id: string;
  summary: {
    tts: ITextToSpeech;
  };
  sections: Array<ISectionSkeleton>;
}

export interface IUseSpeakAloud {
  audioPlayerSettings: AudioLoadOptions;
  updateAudioPlayerSettings: (settings: AudioLoadOptions) => void;
  determineNextParagraph: () => IParagraphSkeleton | null;
  determinePreviousParagraph: () => IParagraphSkeleton | null;
  setInitialSkeletonState: (data: any) => void;
  documentSkeleton: IDocumentSkeleton | undefined;
  updateDocumentSkeleton: (data: any) => void;
  updateSpeakAloudState: (val: SpeakAloudState) => void;
  updateCurrentParagraph: (file: IParagraphSkeleton) => void;
  speakAloudState: SpeakAloudState;
  currentParagraph: IParagraphSkeleton | undefined;
  sectionStatus: SectionStatus;
  updateSectionStatus: (status: SectionStatus) => void;
  convertDocumentToSkeleton: (
    id: string,
    document: IDocumentExtractedData,
  ) => IDocumentSkeleton;
  clearCurrentParagraph: () => void;
}
