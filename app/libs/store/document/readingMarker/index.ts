import {useAtom} from 'jotai';
import {IUseStoredIds, IStoredIdsPerDocument} from './types';
import {atomWithStorage, createJSONStorage} from 'jotai/utils';

const storedIdAtom = atomWithStorage<IStoredIdsPerDocument>(
  'storedReadingMarkerIds',
  {},
  createJSONStorage(() => localStorage),
);

export const useStoredIds = (docId: string): IUseStoredIds => {
  const [storedIds, setStoredIds] = useAtom(storedIdAtom);

  const getStoredIdsPerDocument = () => {
    return storedIds[docId] || '';
  };
  const updateStoredIdsPerDocument = (
    currentId: string,
    parentSectionId: string,
  ) => {
    return setStoredIds(prev => ({
      ...prev,
      [docId]: {currentId, parentSectionId},
    }));
  };

  return {
    getStoredIdsPerDocument,
    updateStoredIdsPerDocument,
  };
};
