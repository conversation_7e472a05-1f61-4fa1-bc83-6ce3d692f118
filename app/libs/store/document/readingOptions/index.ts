import {useAtom} from 'jotai';
import {atomWithStorage, createJSONStorage} from 'jotai/utils';
import {ISectionViewConfigPerDocument, IUseSectionViewConfig} from './types';

const sectionViewConfigAtom = atomWithStorage<ISectionViewConfigPerDocument>(
  'sectionViewConfig',
  {},
  createJSONStorage(() => localStorage),
);

export const useSectionViewConfig = (docId: string): IUseSectionViewConfig => {
  const [sectionViewConfig, setSectionViewConfig] = useAtom(
    sectionViewConfigAtom,
  );

  const getSectionViewConfig = () => {
    return sectionViewConfig[docId] || '';
  };

  const updateSectionViewConfig = (usingSectionView: boolean) => {
    return setSectionViewConfig(prev => ({
      ...prev,
      [docId]: {usingSectionView},
    }));
  };

  return {
    getSectionViewConfig,
    updateSectionViewConfig,
  };
};
