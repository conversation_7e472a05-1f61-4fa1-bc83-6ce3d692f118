import {useAtom} from 'jotai';
import {IActiveSectionIdPerDocument, IUseActiveSectionId} from './types';
import {atomWithStorage, createJSONStorage} from 'jotai/utils';

const allActiveSectionIdsAtom = atomWithStorage<IActiveSectionIdPerDocument>(
  'activeSectionIds',
  {},
  createJSONStorage(() => localStorage),
);

export const useActiveSectionId = (docId: string): IUseActiveSectionId => {
  const [allActiveSectionIds, setAllActiveSectionIds] = useAtom(
    allActiveSectionIdsAtom,
  );

  const getActiveSectionIdPerDocument = () => {
    return allActiveSectionIds[docId] || '';
  };
  const updateActiveSectionIdPerDocument = (
    parentSectionId: string,
    activeSectionId: string,
    allNestedIds: string[],
  ) => {
    return setAllActiveSectionIds(prev => ({
      ...prev,
      [docId]: {parentSectionId, activeSectionId, allNestedIds},
    }));
  };

  return {
    getActiveSectionIdPerDocument,
    updateActiveSectionIdPerDocument,
  };
};
