export interface IUseActiveSectionId {
  getActiveSectionIdPerDocument: () => {
    parentSectionId: string;
    activeSectionId: string;
    allNestedIds: string[];
  };
  updateActiveSectionIdPerDocument: (
    parentSectionId: string,
    activeSectionId: string,
    allNestedIds: string[],
  ) => void;
}

export interface IActiveSectionIdPerDocument {
  [key: string]: {
    parentSectionId: string;
    activeSectionId: string;
    allNestedIds: string[];
  };
}
