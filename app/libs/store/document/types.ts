import {
  Footnote,
  FootnoteRelationship,
  FootnotesDetails,
} from '@components/footnotes/types';
import {IStatusDetails} from '../status/types';

// DOCUMENTS LIST
export interface IDocument extends IStatusDetails {
  id: string;
  name: string;
  type: string;
  isOwner: boolean;
  createdAt: Date;
  lastModified: Date;
  lastOpenDate: Date;
  fileSize: number;
}
export interface IStoreDocuments {
  [key: string]: IDocument;
}
export interface IStoreDocumentsData {
  [key: string]: IDocumentExtractedData;
}

// DOCUMENT EXTRACTED DATA
// 'none' case is returned when there is a block of text without a heading
export type ITag = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'none';
export enum ITextFormatType {
  None = 'none',
  Bold = 'bold',
  Italic = 'italic',
  Underline = 'underline',
  Link = 'link',
  Super = 'super',
  Sub = 'sub',
}

export interface IHeadingData {
  title: string;
  id: string;
  tag: ITag;
}

export interface IFormattedText {
  type: ITextFormatType;
  value: string;
  url?: string;
  relationships?: FootnoteRelationship[];
}

export interface ITextToSpeech {
  audio_file: string;
  status: string;
}

export interface IContentTextNode {
  id: string;
  type: 'paragraph';
  text: string;
  formats: IFormattedText[];
  parentSectionId: string;
  tts: ITextToSpeech;
}
export interface IFormattedListItem {
  value: string;
  items: IFormattedListItem[];
}

export type ContentListNodeFormat = 'ordered' | 'unordered';
export interface IContentListNode {
  id: string;
  type: 'list';
  format: ContentListNodeFormat;
  items: IFormattedListItem[];
  parentSectionId: string;
}
export interface IContentImageNode {
  id: string;
  type: 'img';
  height: number;
  width: number;
  originalHeight: number;
  originalWidth: number;
  transform: [number, number, number, number, number, number];
  url: string;
  parentSectionId: string;
}

export interface ITableHeader {
  type: 'text';
  text: string;
}
export interface ITableRow extends ITableHeader {}
export interface ITableContent {
  headers: ITableHeader[];
  rows: ITableRow[][];
}
export interface IContentTableNode {
  id: string;
  type: 'table';
  content: ITableContent;
  parentSectionId: string;
}
export interface IContentHeadingNode extends IHeadingData {
  id: string;
  type: 'heading';
}

export interface IContentSectionNode {
  id: string;
  parentSectionId: string;
  title: string;
  type: 'section';
}

export interface ITableOfContentItem extends IHeadingData {
  sections: ITableOfContentItem[];
}
export interface ISectionData extends IHeadingData {
  wordCount: number;
  childWordCount: number;
  content: (
    | IContentTextNode
    | IContentImageNode
    | IContentHeadingNode
    | IContentTableNode
    | IContentListNode
  )[];
}

export type IContentNode =
  | IContentTextNode
  | IContentImageNode
  | IContentHeadingNode
  | IContentTableNode
  | IContentListNode
  | IContentSectionNode;

export interface ISectionsData {
  [key: string]: ISectionData;
}

export interface IDocumentDetails {
  pageCount: number;
  baseFontSize: number;
  toc: ITableOfContentItem[];
  wordCount: number;
  childWordCount: number;
  title?: string;
  summary?: string[];
  summary_tts?: {
    status: string;
    audio_file: string;
  };
  footnotes?: FootnotesDetails;
}

export interface IDocumentExtractedData {
  details: IDocumentDetails;
  sections: ISectionsData;
}
