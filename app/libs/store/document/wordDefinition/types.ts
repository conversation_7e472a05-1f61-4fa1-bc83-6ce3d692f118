export interface IUseWordDefinition {
  currentlySearchedWord: string;
  updateCurrentlySearchedWord: (word: string) => void;
  clearCurrentlySearchedWord: () => void;
}

export interface WordDefinitionPhonetics {
  text: string;
  audio?: string;
}

export interface WordDefinitionMeanings {
  part_of_speech: string;
  definitions: WordDefinitionDefinitions[];
  origin?: string;
}

export interface WordDefinitionDefinitions {
  definition: string;
  example: string[];
  synonyms?: string[];
  antonyms?: string[];
}
export interface WordDefinitionResponse {
  word?: string;
  phonetics?: WordDefinitionPhonetics[];
  meanings?: WordDefinitionMeanings[];
  origin?: string;
}

export const dummyWordDefinitionData: WordDefinitionResponse = {
  word: 'hello',
  phonetics: [
    {
      text: 'həˈləʊ',
      audio:
        '//ssl.gstatic.com/dictionary/static/sounds/20200429/hello--_gb_1.mp3',
    },
    {
      text: 'hɛˈləʊ',
    },
  ],
  meanings: [
    {
      part_of_speech: 'exclamation',
      definitions: [
        {
          definition: 'used as a greeting or to begin a phone conversation.',
          example: ['hello there, Katie!'],
          synonyms: [],
          antonyms: [],
        },
      ],
    },
    {
      part_of_speech: 'noun',
      definitions: [
        {
          definition: 'an utterance of ‘hello’; a greeting.',
          example: ['she was getting polite nods and hellos from people'],
          synonyms: [
            'greeting',
            'welcome',
            'salutation',
            'saluting',
            'hailing',
          ],
          antonyms: [],
        },
      ],
    },
    {
      part_of_speech: 'verb',
      definitions: [
        {
          definition: 'say or shout ‘hello’.',
          example: ['I pressed the phone button and helloed'],
          synonyms: [],
          antonyms: [],
        },
      ],
    },
  ],
  origin: 'early 19th century: variant of earlier hollo ; related to holla.',
};
