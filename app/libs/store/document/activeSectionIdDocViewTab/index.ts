import {atom, useAtom} from 'jotai';
import {
  IActiveSectionIdPerDocument,
  IUseActiveSectionIdDocViewTab,
} from './types';
// This component is not used anywhere currently, was previously used to have different active sections between doc and section view tabs
const allActiveSectionIdsAtom = atom<IActiveSectionIdPerDocument>({});

export const useActiveSectionIdDocViewTab = (
  docId: string,
): IUseActiveSectionIdDocViewTab => {
  const [allActiveSectionIds, setAllActiveSectionIds] = useAtom(
    allActiveSectionIdsAtom,
  );

  const getActiveSectionIdPerDocument = () => {
    return allActiveSectionIds[docId] || '';
  };
  const updateActiveSectionIdPerDocument = (activeSectionId: string) => {
    return setAllActiveSectionIds(prev => ({
      ...prev,
      [docId]: activeSectionId,
    }));
  };

  return {
    getActiveSectionIdPerDocument,
    updateActiveSectionIdPerDocument,
  };
};
