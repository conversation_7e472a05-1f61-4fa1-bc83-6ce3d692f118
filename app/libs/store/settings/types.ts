import {FontSizedElement} from './fontSize';

// keep as readable strings - they are read by screen readers when choosing the theme
export enum ThemeColour {
  Dark = 'dark',
  Light = 'light',
  Yellow = 'yellow',
  PaleGreen = 'pale-green',
  PaleBlue = 'pale-blue',
  Peach = 'peach',
  PalePink = 'pale-pink',
}

export enum Font {
  Poppins = 'Poppins',
  Arial = 'Arial',
  RobotoMono = 'Roboto Mono',
  Merriweather = 'Merriweather',
  OpenDyslexic = 'OpenDyslexic',
}

export enum Voice {
  Brian = '<PERSON>',
  <PERSON> = '<PERSON>',
  <PERSON> = '<PERSON>',
  <PERSON> = '<PERSON>',
  <PERSON> = '<PERSON>',
  <PERSON><PERSON> = '<PERSON><PERSON>',
}

export enum FontSize {
  Small = 'small',
  Medium = 'medium',
  Large = 'large',
}

export enum LineSpacing {
  Tight = 'tight',
  Optimised = 'optimised',
  Relaxed = 'relaxed',
}

export enum TextAlignment {
  Left = 'left',
  Justified = 'justified',
}

export interface ISettings {
  Theme: ThemeColour;
  Font: Font;
  FontSize: FontSize;
  LineSpacing: LineSpacing;
  Alignment: TextAlignment;
  Voice: Voice;
  HideSmallImages: boolean;
}

export interface IServerSettings {
  theme: ThemeColour;
  font: Font;
  font_size: FontSize;
  line_spacing: LineSpacing;
  alignment: TextAlignment;
}

export interface IUseSettings extends ISettings {
  updateSettings: (update: Partial<ISettings>) => void;
  resetSettings: () => void;
  settingsAttrString: string;
  fontClassName: string;
  settings: ISettings;
}

export interface ILicenseMinima {
  id: string;
  category: string;
  accountName: string;
  accountId: string;
}
