import {FontSize} from './types';

// The elements we want to adjust to user fontSize selection
export type FontSizedElement =
  | 'p'
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'h6'
  | 'li'
  | 'a';

// We can add other things here like 'buttonText' etc
export const fontSizeConfig = {
  [FontSize.Small]: {
    base: 'text-base', // base will be used for elements like 'p' | 'a' | 'li' etc
    h1: 'text-3xl',
    h2: 'text-xl',
    h3: 'text-lg',
    h4: 'text-base',
    h5: 'text-sm',
    h6: 'text-xs',
  },
  [FontSize.Medium]: {
    base: 'text-xl',
    h1: 'text-5xl',
    h2: 'text-3xl',
    h3: 'text-2xl',
    h4: 'text-xl',
    h5: 'text-lg',
    h6: 'text-sm',
  },
  [FontSize.Large]: {
    base: 'text-2xl',
    h1: 'text-6xl',
    h2: 'text-5xl',
    h3: 'text-3xl',
    h4: 'text-2xl',
    h5: 'text-xl',
    h6: 'text-base',
  },
};

export const getFontSizeForElement = (
  element: FontSizedElement,
  userFontSize: FontSize,
) => {
  let style = '';
  switch (element) {
    case 'a':
    case 'p':
    case 'li':
      style = fontSizeConfig[userFontSize].base;
      break;
    case 'h1':
      style = fontSizeConfig[userFontSize].h1;
      break;
    case 'h2':
      style = fontSizeConfig[userFontSize].h2;
      break;
    case 'h3':
      style = fontSizeConfig[userFontSize].h3;
      break;
    case 'h4':
      style = fontSizeConfig[userFontSize].h4;
      break;
    case 'h5':
      style = fontSizeConfig[userFontSize].h5;
      break;
    case 'h6':
      style = fontSizeConfig[userFontSize].h6;
      break;
  }
  return style;
};
