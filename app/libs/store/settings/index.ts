import {useAtom} from 'jotai';
import {atomWithStorage, createJSONStorage} from 'jotai/utils';
import {ISettings, IUseSettings} from './types';
import {defaultSettings} from './values';
import {getFontClassName} from './fonts';
import {useAnalytics} from 'use-analytics';
import {usePathname} from 'next/navigation';
import {useSession} from 'next-auth/react';
import {useEffect} from 'react';
import useUserDataFromSession from '@libs/utils/user/useUserDataFromSession';

const settingsSessionStorageAtom = atomWithStorage<ISettings>(
  'tailo-settings',
  defaultSettings,
  createJSONStorage(() => sessionStorage),
);

function useSettings(): IUseSettings {
  const [settings, setSettings] = useAtom(settingsSessionStorageAtom);
  const pathname = usePathname();
  const {trackingId} = useUserDataFromSession();

  const {track, identify, user} = useAnalytics();

  const trackSettingChange = (updates: Partial<ISettings>) => {
    identify(trackingId ?? '', {VisualSettingsChanged: true, ...settings}); //update the analytics user properties
    track('VisualSettingsChanged', {...updates, PageSource: pathname}); // track the settings event
  };

  function updateSettings(updates: Partial<ISettings>) {
    trackSettingChange(updates);
    setSettings({...settings, ...updates});
  }

  function resetSettings() {
    setSettings(defaultSettings);
  }

  const fontClassName = getFontClassName(settings.Font);

  return {
    ...settings,
    updateSettings,
    resetSettings,
    settingsAttrString: Object.values(settings).join(' '),
    fontClassName,
    settings,
  };
}

export default useSettings;
