import {Poppins} from 'next/font/google';
import {<PERSON><PERSON>_Mono} from 'next/font/google';
import {Merriweather} from 'next/font/google';
import localFont from 'next/font/local';
import {Font} from './types';

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
});

export const defaultFontClassName = poppins.className;

const openDyslexic = localFont({
  src: [
    {
      path: '../../../../public/fonts/OpenDyslexic-Regular.otf',
      weight: '400',
      style: 'normal',
    },
  ],
  variable: '--font-opendyslexic',
});

const roboto = Roboto_Mono({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
});

const merriweather = Merriweather({
  subsets: ['latin'],
  weight: ['400', '700', '900'],
});

export const getFontClassName = (font: Font): string => {
  const userFont = font;
  let fontStyle = '';
  switch (userFont) {
    case Font.RobotoMono:
      fontStyle = roboto.className;
      break;
    case Font.Poppins:
      fontStyle = poppins.className;
      break;
    case Font.Arial:
      fontStyle = 'font-arial'; // arial is a browser font, so we use the TW style we added in the twconfig
      break;
    case Font.Merriweather:
      fontStyle = merriweather.className;
      break;
    case Font.OpenDyslexic:
      fontStyle = openDyslexic.className; // Reference the Tailwind class
      break;
  }
  return fontStyle;
};
