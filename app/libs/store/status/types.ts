export enum IStatus {
  Queued = 'queued',
  Uploading = 'uploading',
  Processing = 'processing',
  Extracting = 'extracting',
  Ready = 'ready',
  Complete = 'complete',
  Error = 'error',
}

export interface IErrorDetails {
  code: number; //TODO This will need be updated when error are scoped
  message: string;
}

export interface IStatusDetails {
  errorDetails?: IErrorDetails;
  status: IStatus;
}
