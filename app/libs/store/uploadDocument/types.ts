import {DocUploadView, UploadSource} from '@components/modals/uploadFile/types';

export interface IUseUploadDocument {
  uploadProgress: number;
  updateUploadProgress: (progress: number) => void;
  uploadError: boolean;
  updateUploadError: (error: boolean) => void;
  file: File | null;
  updateFile: (file: File | null) => void;
  activeView: DocUploadView;
  updateActiveView: (view: DocUploadView) => void;
  isOpen: boolean;
  updateIsOpen: (open: boolean) => void;
  fileUploadSource: UploadSource | undefined;
  updateFileUploadSource: (source: UploadSource) => void;
}
