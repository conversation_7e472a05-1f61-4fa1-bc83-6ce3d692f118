import {DocUploadView, UploadSource} from '@components/modals/uploadFile/types';
import {atom, useAtom} from 'jotai';
import {IUseUploadDocument} from './types';
import {useEffect, useState} from 'react';

const uploadProgressAtom = atom(0);
const uploadErrorAtom = atom(false);
const fileAtom = atom<File | null>(null);
const activeViewAtom = atom<DocUploadView>(DocUploadView.SELECT_DOC);
const isOpenAtom = atom(false);
const uploadSourceAtom = atom<UploadSource | undefined>(undefined);

const useUploadDocument = (): IUseUploadDocument => {
  const [uploadProgress, setUploadProgress] = useAtom(uploadProgressAtom);
  const [uploadError, setUploadError] = useAtom(uploadErrorAtom);
  const [file, setFile] = useAtom(fileAtom);
  const [activeView, setActiveView] = useAtom(activeViewAtom);
  const [isOpen, setIsOpen] = useAtom(isOpenAtom);
  const [fileUploadSource, setFileUploadSource] = useAtom(uploadSourceAtom);

  const updateUploadProgress = (progress: number) =>
    setUploadProgress(progress);
  const updateIsOpen = (open: boolean) => setIsOpen(open);
  const updateUploadError = (error: boolean) => setUploadError(error);
  const updateFile = (file: File | null) => setFile(file);
  const updateActiveView = (view: DocUploadView) => setActiveView(view);
  const updateFileUploadSource = (source: UploadSource) =>
    setFileUploadSource(source);

  return {
    uploadProgress,
    updateUploadProgress,
    uploadError,
    updateUploadError,
    file,
    updateFile,
    activeView,
    updateActiveView,
    isOpen,
    updateIsOpen,
    fileUploadSource,
    updateFileUploadSource,
  };
};

export default useUploadDocument;
