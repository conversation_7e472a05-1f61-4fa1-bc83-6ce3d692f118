import {useAtom, atom} from 'jotai';
import {
  DRAWER_ID,
  DrawerTab,
  IDocToolsDrawerContent,
  ID<PERSON>erState,
  IUseDrawer,
  drawPosition,
} from './types';
import {produce} from 'immer';

const defaultDrawerState: IDrawerState = {
  [DRAWER_ID.CUSTOMISATION]: 'closed',
  [DRAWER_ID.ACCOUNT]: 'closed',
  [DRAWER_ID.DOCUMENT_TOOLS]: 'closed',
  [DRAWER_ID.DOCUMENT_MENU]: 'closed',
  [DRAWER_ID.DOCUMENT_CONTENTS_LIST]: 'closed',
  [DRAWER_ID.SUMMARIES_HISTORY]: 'closed',
  [DRAWER_ID.DOCUMENT_TOOLS_MENU]: 'closed',
  [DRAWER_ID.TOC_SECTION_VIEW]: 'closed',
  [DRAWER_ID.FEEDBACK]: 'closed',
  [DRAWER_ID.FOOTNOTES]: 'closed',
};

const defaultDocToolsDrawerContent: IDocToolsDrawerContent = {
  content: DrawerTab.WORD_DEFINITION,
};

const drawerAtom = atom(defaultDrawerState);
const docToolsdrawerContentAtom = atom(defaultDocToolsDrawerContent);

function useDrawer(): IUseDrawer {
  const [drawerState, setDrawState] = useAtom(drawerAtom);
  const [docToolsDrawerContent, setDocToolsDrawerContent] = useAtom(
    docToolsdrawerContentAtom,
  );

  function updateDrawer(drawerId: DRAWER_ID, state: drawPosition) {
    setDrawState(produce(draft => void (draft[drawerId] = state)));
  }

  function openDrawer(drawerId: DRAWER_ID) {
    getOpenDrawerId() && closeDrawer(getOpenDrawerId());
    setDrawState(produce(draft => void (draft[drawerId] = 'open')));
  }

  function closeDrawer(drawerId: DRAWER_ID) {
    if (drawerId === DRAWER_ID.DOCUMENT_TOOLS) {
      setDocToolsDrawerContent(docToolsDrawerContent);
    }
    setDrawState(produce(draft => void (draft[drawerId] = 'closed')));
  }

  const updateDocToolsDrawerContent = (content: DrawerTab) => {
    setDocToolsDrawerContent(prev => {
      return {...prev, content};
    });
  };

  const drawStateById = (id: DRAWER_ID) => {
    return drawerState[id];
  };

  const getOpenDrawerId = () => {
    return Object.keys(drawerState).find(
      key => drawerState[key as DRAWER_ID] === 'open',
    ) as DRAWER_ID;
  };

  const toggleFeedbackDrawer = () => {
    if (drawStateById(DRAWER_ID.FEEDBACK) === 'open') {
      closeDrawer(DRAWER_ID.FEEDBACK);
      return;
    } else {
      drawStateById(DRAWER_ID.FEEDBACK) === 'closed';
      openDrawer(DRAWER_ID.FEEDBACK);
    }
  };
  const isFeedbackDrawerActive = drawStateById(DRAWER_ID.FEEDBACK);

  const toggleCustomisationDrawer = () => {
    if (drawStateById(DRAWER_ID.CUSTOMISATION) === 'open') {
      closeDrawer(DRAWER_ID.CUSTOMISATION);
      return;
    } else {
      openDrawer(DRAWER_ID.CUSTOMISATION);
    }
  };
  const isCustomisationDrawerActive = drawStateById(DRAWER_ID.CUSTOMISATION);

  const drawerOpen = (drawerId: DRAWER_ID) =>
    drawStateById(drawerId) === 'open';
  const drawerOpenWithContent = (drawerId: DRAWER_ID, content: DrawerTab) => {
    return drawerOpen(drawerId) && docToolsDrawerContent.content === content;
  };

  const setDrawerContentToDefine = (drawerId: DrawerTab) => {
    updateDocToolsDrawerContent(drawerId);
  };

  const handleDrawerTab = (drawerTab: DrawerTab) => {
    setDrawerContentToDefine(drawerTab);
  };
  const isWordDefinitionDrawerActive = drawerOpenWithContent(
    DRAWER_ID.DOCUMENT_TOOLS,
    DrawerTab.WORD_DEFINITION,
  );
  const isSpeakAloudDrawerActive = drawerOpenWithContent(
    DRAWER_ID.DOCUMENT_TOOLS,
    DrawerTab.SPEAK_ALOUD,
  );
  const isSearchDrawerActive = drawerOpenWithContent(
    DRAWER_ID.DOCUMENT_TOOLS,
    DrawerTab.DOC_SEARCH,
  );

  const isExplainThisDrawerActive = drawerOpenWithContent(
    DRAWER_ID.DOCUMENT_TOOLS,
    DrawerTab.EXPLAIN_THIS,
  );

  const isTailoFeedbackDrawerActive = drawerOpenWithContent(
    DRAWER_ID.DOCUMENT_TOOLS,
    DrawerTab.TAILO_FEEDBACK,
  );

  return {
    drawStateById,
    updateDrawer,
    openDrawer,
    closeDrawer,
    getOpenDrawerId,
    docToolsDrawerContent,
    updateDocToolsDrawerContent,
    toggleFeedbackDrawer,
    isFeedbackDrawerActive,
    toggleCustomisationDrawer,
    isCustomisationDrawerActive,
    handleDrawerTab,
    isWordDefinitionDrawerActive,
    isSpeakAloudDrawerActive,
    isSearchDrawerActive,
    isExplainThisDrawerActive,
    isTailoFeedbackDrawerActive,
  };
}

export default useDrawer;
