export enum DRAWER_ID {
  CUSTOMISATION = 'customisation_drawer',
  ACCOUNT = 'account_drawer',
  DOCUMENT_TOOLS_MENU = 'document_tools_menu_drawer',
  DOCUMENT_TOOLS = 'document_tools_drawer',
  DOCUMENT_MENU = 'document_menu_drawer',
  DOCUMENT_CONTENTS_LIST = 'document_contents_list_drawer',
  SUMMARIES_HISTORY = 'summaries_history_drawer',
  TOC_SECTION_VIEW = 'toc_section_view',
  FEEDBACK = 'feedback_drawer',
  FOOTNOTES = 'footnotes_drawer',
}

export type drawPosition = 'open' | 'closed';

export type IDrawerState = {
  [key in DRAWER_ID]: drawPosition;
};

export enum DrawerTab {
  WORD_DEFINITION = 'Define', // Word Definition Lookup, set it to Define as that is the title of the drawer content at the moment
  SPEAK_ALOUD = 'Read aloud settings',
  FEEDBACK = 'Feedback',
  CUSTOMISATION = 'Customisation',
  DOC_SEARCH = 'Search',
  EXPLAIN_THIS = 'Explain',
  TAILO_FEEDBACK = 'Tailo Feedback',
}

export interface IDocToolsDrawerContent {
  content: DrawerTab;
}

export interface IUseDrawer {
  updateDrawer: (drawer: DRAWER_ID, state: drawPosition) => void;
  openDrawer: (drawer: DRAWER_ID) => void;
  closeDrawer: (drawer: DRAWER_ID) => void;
  drawStateById: (id: DRAWER_ID) => drawPosition;
  getOpenDrawerId: () => DRAWER_ID | null;
  docToolsDrawerContent: IDocToolsDrawerContent;
  updateDocToolsDrawerContent: (content: DrawerTab) => void;
  toggleFeedbackDrawer: () => void;
  toggleCustomisationDrawer: () => void;
  isFeedbackDrawerActive: drawPosition;
  isCustomisationDrawerActive: drawPosition;
  handleDrawerTab: (drawerTab: DrawerTab) => void;
  isWordDefinitionDrawerActive: boolean;
  isSpeakAloudDrawerActive: boolean;
  isSearchDrawerActive: boolean;
  isExplainThisDrawerActive: boolean;
  isTailoFeedbackDrawerActive: boolean;
}
