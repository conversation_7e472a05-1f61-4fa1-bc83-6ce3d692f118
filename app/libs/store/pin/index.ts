import {useAtom, atom} from 'jotai';
import {IUsePin} from './types';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';

const pinAtom = atom(false);

function usePinAtom(): IUsePin {
  const [pinned, setPinned] = useAtom(pinAtom);
  const {trackTableOfContentsPin} = useDocumentAnalytics();

  const togglePin = () => {
    trackTableOfContentsPin(!pinned);
    setPinned(!pinned);
  };

  return {
    pinned,
    togglePin,
    setPinned,
  };
}

export default usePinAtom;
