import {atomWithReset} from 'jotai/utils';
import {IStoreSummary, IPromptFormData, IPromptSegment, IPrompt} from './types';
import {atom} from 'jotai';

export const defaultFormData: IPromptFormData = {
  tryingToDo: '',
  forMe: undefined,
  whoFor: '',
  student: undefined,
  field: '',
  learningDifferences: '',
  bulletPoints: undefined,
  focusingOn: '',
  specificSummary: '', // TODO get options
  tone: '',
  wordLimit: 0,
};
export const defaultPrompt: IPrompt = {
  start: [{type: 'text', text: 'Summarise my document'}],
};

export const defaultSummaries: IStoreSummary = {};

export const summariesAtom = atomWithReset(defaultSummaries);

export const summaryFormAtom = atom(defaultFormData);

export const summaryPromptAtom = atom(defaultPrompt);

export const summaryCustomPromptAtom = atom('');
