import {useAtom} from 'jotai';
import {
  defaultFormData,
  defaultPrompt,
  summaryCustomPromptAtom,
  summaryFormAtom,
  summaryPromptAtom,
} from '.';
import {IUseStoreSummaryForm, IPromptFormData, IPrompt} from './types';
import useSummaryServices from '@libs/services/summary/useSummaryServices';
import {useParams, useRouter} from 'next/navigation';
import {mutateSummariesOnSummaryCreation} from '@libs/services/summary/useSummaries';
import {IServerResponse} from '@libs/services/types';
import {IServerSummary} from '@libs/services/summary/types';
import {AppDynamicRoutes} from '@libs/services/auth/routes';

export function useStoreSummaryForm(): IUseStoreSummaryForm {
  const [formData, setFormData] = useAtom(summaryFormAtom);
  const [prompt, setPrompt] = useAtom(summaryPromptAtom);
  const [customPrompt, setCustomPrompt] = useAtom(summaryCustomPromptAtom);
  const {
    updateNewPrompt,
    generateFixedPromptSummary,
    generateCustomPromptSummary,
  } = useSummaryServices();
  const params = useParams();
  const documentId = params.id as string;
  const router = useRouter();

  const updateForm = (data: Partial<IPromptFormData>) => {
    setFormData({...formData, ...data});
  };

  // The prompt will be coming back from the API when we send off the formData
  const updatePrompt = (updated: IPrompt) => {
    setPrompt(updated);
  };

  const updateCustomPrompt = (prompt: string) => {
    setCustomPrompt(prompt);
  };

  const resetPromptData = () => {
    updateForm(defaultFormData);
    updatePrompt(defaultPrompt);
    updateCustomPrompt('');
  };

  const submitNewFormPrompt = () => {
    updateNewPrompt({newPromptData: formData}).then(prompt => {
      if (prompt.data) {
        updatePrompt(prompt.data);
      }
    });
  };

  const navigateToSummaryPage = (id: string) => {
    if (id) {
      router.push(AppDynamicRoutes.summary(documentId, id));
    }
  };

  const handleSummariesMutationOnPromptSubmit = async (
    summary: IServerResponse<IServerSummary>,
  ) => {
    if (summary.data) {
      await mutateSummariesOnSummaryCreation(documentId, summary.data);
      resetPromptData();
      navigateToSummaryPage(summary.data.id);
    }
  };

  const submitBuilderForm = async () => {
    try {
      const summary = await generateFixedPromptSummary({
        documentId,
        prompt: formData,
      });
      await handleSummariesMutationOnPromptSubmit(summary);
    } catch (err) {
      console.log('Error submitting builder prompt:', err);
    }
  };

  const submitCustomForm = async () => {
    try {
      const summary = await generateCustomPromptSummary({
        documentId,
        prompt: customPrompt,
      });
      await handleSummariesMutationOnPromptSubmit(summary);
    } catch (err) {
      console.log('Error submitting custom prompt:', err);
    }
  };

  return {
    formData,
    prompt,
    customPrompt,
    updateForm,
    updatePrompt,
    updateCustomPrompt,
    submitNewFormPrompt,
    submitBuilderForm,
    submitCustomForm,
  };
}
