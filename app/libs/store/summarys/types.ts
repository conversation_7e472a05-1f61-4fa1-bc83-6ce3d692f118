import {IStatusDetails} from '../status/types';

export type PromptBuilderMethod = 'custom' | 'builder';

export interface IPromptFormData {
  tryingToDo: string;
  forMe: boolean | undefined;
  whoFor: string;
  student: boolean | undefined;
  field: string;
  learningDifferences: string;
  bulletPoints: boolean | undefined;
  focusingOn: string;
  specificSummary: string;
  tone: string;
  wordLimit: number;
}

export interface IPromptSegment {
  type: 'text' | 'argument';
  text: string;
}

export interface IPrompt {
  [section: string]: IPromptSegment[];
}

export interface ISummary extends IStatusDetails {
  id: string;
  prompt: string; // agreed on text value for now
  builderMethod: PromptBuilderMethod;
  createdAt: Date;
  summary: string; //TODO i guess this will be some kind of DocumentData
}

export interface IStoreSummary {
  [documentId: string]: ISummary[];
}

// HOOKS
export interface IUseStoreSummaries {
  documentSummaries: ISummary[];
  clearSummaries: () => void;
  addSummary: (summary: ISummary) => void;
}

export interface IUseStoreSummaryForm {
  formData: IPromptFormData;
  prompt: IPrompt;
  customPrompt: string;
  updateForm: (formData: Partial<IPromptFormData>) => void;
  updatePrompt: (prompt: IPrompt) => void;
  updateCustomPrompt: (prompt: string) => void;
  submitNewFormPrompt: () => void;
  submitBuilderForm: () => Promise<void>;
  submitCustomForm: () => Promise<void>;
}
