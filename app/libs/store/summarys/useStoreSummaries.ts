import {useAtom, useAtomValue} from 'jotai';
import {ISummary, IUseStoreSummaries} from './types';
import {RESET, selectAtom} from 'jotai/utils';
import {summariesAtom} from '.';
import React from 'react';
import isEqual from 'lodash/isEqual';
import {sortByCreatedAt} from '@libs/utils/summaries/formatter';

export function useStoreSummaries(documentId: string): IUseStoreSummaries {
  const [summaries, setSummaries] = useAtom(summariesAtom);

  const documentSummariesAtom = selectAtom(
    summariesAtom,
    sums => sums[documentId],
    isEqual,
  );

  const documentSummaries = useAtomValue(
    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useMemo(() => selectAtom(documentSummariesAtom, v => v), []),
  );

  const sortedSummaries = documentSummaries.sort(sortByCreatedAt);
  const clearSummaries = () => setSummaries(RESET);

  const addSummary = (summary: ISummary) =>
    setSummaries({...summaries, [documentId]: [...documentSummaries, summary]});

  return {
    documentSummaries: sortedSummaries,
    clearSummaries,
    addSummary,
  };
}
