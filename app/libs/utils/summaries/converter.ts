import {
  IPromptServerData,
  IServerFullSummary,
  IServerSummary,
} from '@libs/services/summary/types';
import {ISummary, IPromptFormData} from '@libs/store/summarys/types';

export function convertSummaryDataFromServer(
  summary: IServerSummary,
): Omit<ISummary, 'summary'> {
  return {
    id: summary.id,
    prompt: summary.prompt,
    status: summary.status,
    builderMethod: summary.builder_method,
    createdAt: summary.date_created as unknown as Date,
    errorDetails: summary.error_details,
  };
}
export function convertSummaryFullDataFromServer(
  summary: IServerFullSummary,
): ISummary {
  return {
    id: summary.id,
    prompt: summary.prompt,
    status: summary.status,
    builderMethod: summary.builder_method,
    createdAt: summary.date_created as unknown as Date,
    errorDetails: summary.error_details,
    summary: summary.summary,
  };
}

export function convertSummariesDataFromServer(
  summaries: IServerSummary[],
): Omit<ISummary, 'summary'>[] {
  return summaries.map(summary => convertSummaryDataFromServer(summary));
}

export function convertPromptDataToServer(
  promptData: IPromptFormData,
): IPromptServerData {
  return {
    trying_to_do: promptData.tryingToDo,
    for_me: promptData.forMe,
    who_for: promptData.whoFor,
    student: promptData.student,
    field: promptData.field,
    learning_differences: promptData.learningDifferences,
    bullet_points: promptData.bulletPoints,
    focusing_on: promptData.focusingOn,
    specific_summary: promptData.specificSummary,
    tone: promptData.tone,
    word_limit: promptData.wordLimit,
  };
}
