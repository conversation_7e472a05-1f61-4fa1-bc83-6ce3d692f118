import {WordDefinitionPhonetics} from '@libs/store/document/wordDefinition/types';

export const getValidAudioFile = (phonetics: WordDefinitionPhonetics[]) => {
  return phonetics.map(phonetic => phonetic.audio).find(audio => audio !== '');
};

export const getValidPhoneticWordValue = (
  phonetics: WordDefinitionPhonetics[],
) => {
  return phonetics.map(phonetic => phonetic.text).find(text => text !== '');
};

export const getElapsedSeconds = (startTime: number) => {
  const now = Date.now(); // Get the current time
  const elapsedMilliseconds = now - startTime; // Calculate the elapsed time in milliseconds
  return Math.floor(elapsedMilliseconds / 1000); // Convert to seconds and return
};

export const startTimer = () => {
  const thisNow = Date.now(); // Record the start time
  return () => getElapsedSeconds(thisNow);
};
