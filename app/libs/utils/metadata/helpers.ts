import {Step} from '@libs/store/onboarding/types';
import {IOnboardingMetadata} from './types';

const getPageTitle = (stepName: string) => {
  let words = stepName.split('-');
  words = words.map(word => word.charAt(0).toUpperCase() + word.slice(1));
  return `Tailo | ${words.join(' ')}`;
};

function getOnboardingDynamicPageMetadata() {
  const defaultSteps: IOnboardingMetadata = {};
  (Object.values(Step) as unknown as Array<keyof typeof Step>).forEach(
    stepName => {
      defaultSteps[stepName] = {
        title: getPageTitle(stepName),
      };
    },
  );
  return defaultSteps;
}

export const defaultSteps = getOnboardingDynamicPageMetadata();
