import {ITableOfContentItem} from '@libs/store/document/types';

// ToC should support only 2 levels of nesting due to designs constraints
function flattenNestedToc(
  nestedToc: ITableOfContentItem[],
  flattenedSections: ITableOfContentItem[],
) {
  nestedToc.forEach(nestedItem => {
    if (nestedItem.sections.length > 0) {
      flattenedSections.push({...nestedItem, sections: []});
      flattenNestedToc(nestedItem.sections, flattenedSections);
    } else {
      flattenedSections.push(nestedItem);
    }
  });
}
export function flattenToc(toc: ITableOfContentItem[]): ITableOfContentItem[] {
  toc.forEach(itemLevel1 => {
    const level1Sections = itemLevel1.sections;
    if (level1Sections.length > 0) {
      const flattenedSections: ITableOfContentItem[] = [];
      flattenNestedToc(level1Sections, flattenedSections);
      itemLevel1.sections = flattenedSections;
    }
  });
  return toc;
}
