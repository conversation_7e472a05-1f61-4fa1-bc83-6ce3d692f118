import {ITableOfContentItem} from '@libs/store/document/types';
import {flattenToc} from './flattenToc';

const emptyTocItem = (id: string): ITableOfContentItem => ({
  id,
  title: 'test',
  tag: 'h1',
  sections: [],
});
const tocItemWithNestedSection = (
  id: string,
  nestedToc: ITableOfContentItem[],
): ITableOfContentItem => ({
  id,
  title: 'test',
  tag: 'h1',
  sections: nestedToc,
});

describe('Test flattening of table of content', () => {
  test("Shouldn't flatten level 1 and level 2 only TOC", () => {
    const toc: ITableOfContentItem[] = [
      emptyTocItem('1'),
      tocItemWithNestedSection('2', [emptyTocItem('3')]),
    ];
    const flattenedToc = flattenToc(toc);

    expect(flattenedToc[0].sections).toHaveLength(0);
    expect(flattenedToc[1].sections).toHaveLength(1);
    expect(flattenedToc[1].sections[0].sections).toHaveLength(0);
  });

  test('Should flatten level 3 toc', () => {
    const toc: ITableOfContentItem[] = [
      tocItemWithNestedSection('1', [
        tocItemWithNestedSection('2', [emptyTocItem('3'), emptyTocItem('4')]),
      ]),
    ];
    const flattenedToc = flattenToc(toc);

    expect(flattenedToc[0].sections).toHaveLength(3);
    expect(flattenedToc[0].sections.map(i => i.id)).toEqual(['2', '3', '4']);
  });

  test('Should flatten level 4 toc', () => {
    const toc: ITableOfContentItem[] = [
      tocItemWithNestedSection('1', [
        tocItemWithNestedSection('2', [
          emptyTocItem('3'),
          emptyTocItem('4'),
          tocItemWithNestedSection('5', [emptyTocItem('6'), emptyTocItem('7')]),
        ]),
      ]),
    ];
    const flattenedToc = flattenToc(toc);
    expect(flattenedToc[0].sections).toHaveLength(6);
    expect(flattenedToc[0].sections.map(i => i.id)).toEqual([
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
    ]);
  });

  test('Should flatten level 5 toc', () => {
    const toc: ITableOfContentItem[] = [
      tocItemWithNestedSection('1', [
        tocItemWithNestedSection('2', [
          emptyTocItem('3'),
          emptyTocItem('4'),
          tocItemWithNestedSection('5', [
            tocItemWithNestedSection('6', [
              emptyTocItem('7'),
              emptyTocItem('8'),
            ]),
            emptyTocItem('9'),
          ]),
        ]),
      ]),
    ];
    const flattenedToc = flattenToc(toc);

    expect(flattenedToc[0].sections).toHaveLength(8);
    expect(flattenedToc[0].sections.map(i => i.id)).toEqual([
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
    ]);
  });
  test('Should flatten level 6 toc', () => {
    const toc: ITableOfContentItem[] = [
      tocItemWithNestedSection('1', [
        tocItemWithNestedSection('2', [
          emptyTocItem('3'),
          emptyTocItem('4'),
          tocItemWithNestedSection('5', [
            emptyTocItem('12'),
            tocItemWithNestedSection('6', [
              emptyTocItem('7'),
              tocItemWithNestedSection('8', [
                emptyTocItem('9'),
                emptyTocItem('10'),
              ]),
            ]),
            emptyTocItem('11'),
          ]),
        ]),
      ]),
    ];
    const flattenedToc = flattenToc(toc);

    expect(flattenedToc[0].sections).toHaveLength(11);
    expect(flattenedToc[0].sections.map(i => i.id)).toEqual([
      '2',
      '3',
      '4',
      '5',
      '12',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
    ]);
  });
});
