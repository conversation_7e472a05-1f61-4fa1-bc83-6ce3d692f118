'use client';

import React from 'react';

export default function useTabHasFocus() {
  const [focus, setFocus] = React.useState(false);

  React.useEffect(() => {
    if (document.hasFocus()) {
      setFocus(true);
    }

    const onFocus = () => setFocus(true);
    const onBlur = () => setFocus(false);

    window.addEventListener('focus', onFocus);
    window.addEventListener('blur', onBlur);

    return () => {
      window.removeEventListener('focus', onFocus);
      window.removeEventListener('blur', onBlur);
    };
  }, []);

  return focus;
}
