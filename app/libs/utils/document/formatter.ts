import {IDocument} from '@libs/store/document/types';

export function sortByLastModified(a: IDocument, b: IDocument) {
  return +new Date(b.lastModified) - +new Date(a.lastModified);
}

export function getAllIds(documents: IDocument[]) {
  return documents.sort(sortByLastModified).map(doc => doc.id);
}
export function formatDocumentsListIntoStoreData(documents: IDocument[]) {
  return documents.reduce((all, doc) => ({...all, [doc.id]: doc}), {});
}
