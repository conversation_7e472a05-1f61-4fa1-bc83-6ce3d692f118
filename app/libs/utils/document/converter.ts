import {
  IServerContentImageNode,
  IServerDocument,
  IServerDocumentExtractedData,
  IServerSectionsData,
} from '@libs/services/document/types';
import {
  IContentImageNode,
  IDocument,
  IDocumentExtractedData,
  ISectionsData,
} from '@libs/store/document/types';
import {flattenToc} from '../toc/flattenToc';

export function convertServerDocListToClientFormat(
  documents: IServerDocument[],
): IDocument[] {
  return documents.map(document =>
    convertServerDocInTheListToClientFormat(document),
  );
}

export function convertServerDocInTheListToClientFormat(
  document: IServerDocument,
): IDocument {
  return {
    id: document.id,
    name: document.name,
    type: document.type,
    isOwner: document.is_owner,
    createdAt: document.date_created as unknown as Date,
    lastModified: document.last_modified as unknown as Date,
    lastOpenDate: document.last_read_at as unknown as Date,
    status: document.status,
    fileSize: document.filesize,
    errorDetails: document.error_details,
  };
}

function convertServerImageDataToClientFormat(
  img: IServerContentImageNode,
): IContentImageNode {
  return {
    id: img.id,
    type: img.type,
    height: img.height,
    width: img.width,
    transform: img.transform,
    url: img.url,
    originalWidth: img.original_width,
    originalHeight: img.original_height,
    parentSectionId: img.parentSectionId,
  };
}

function convertServerSectionDataToClientFormat(
  sections: IServerSectionsData,
): ISectionsData {
  const formattedSections: ISectionsData = {};
  for (let key in sections) {
    if (sections.hasOwnProperty(key)) {
      const content = sections[key].content.map(c => {
        if (c.type === 'img') {
          return convertServerImageDataToClientFormat(c);
        }
        return c;
      });
      const {
        id,
        title,
        tag,
        word_count: wordCount,
        child_word_count: childWordCount,
      } = sections[key];
      formattedSections[key] = {
        id,
        title,
        tag,
        content,
        wordCount,
        childWordCount,
      };
    }
  }
  return formattedSections;
}

export function convertServerDocumentExtractedDataToCLientFormat(
  data: IServerDocumentExtractedData | undefined,
): IDocumentExtractedData | null {
  if (!data) return null;

  return {
    sections: convertServerSectionDataToClientFormat(data.sections),
    details: {
      toc: flattenToc(data.details.toc),
      pageCount: data.details.page_count,
      baseFontSize: data.details.base_font_size,
      summary: data.details.summary,
      wordCount: data.details.word_count || 0,
      childWordCount: data.details.child_word_count || 0,
      summary_tts: data.details.summary_tts,
      footnotes: data.details.footnotes,
    },
  };
}
