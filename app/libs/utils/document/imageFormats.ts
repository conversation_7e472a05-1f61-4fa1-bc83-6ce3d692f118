export function shouldDisplayAsBlock(
  viewerWidth: number,
  pageWidth: number,
  originalWidth: number,
  width: number,
) {
  //Only display across viewer the images that take more than half of the document width and have a good resolution
  // Otherwise display as a block image but without stretching across the viewer
  return {
    block: width > pageWidth / 2,
    fullWidth: originalWidth >= viewerWidth,
  };
}

export function getImageSize(
  viewerWidth: number,
  pageWidth: number,
  originalSize: number,
  sizeInDocument: number,
) {
  // Scales image up to 2 times based on viewer to document width ratio if the resolution allows for that otherwise keep the size from document
  // For viewer size smaller than the document keeps the image's document sizing
  let size = sizeInDocument;
  let ratio = viewerWidth / pageWidth;
  const MAX_RATIO = 1.5;

  if (ratio >= 1) {
    if (ratio > MAX_RATIO) ratio = MAX_RATIO;
    const newSize = sizeInDocument * ratio;
    if (newSize < originalSize) {
      size = sizeInDocument * ratio;
    }
  }

  return size;
}
