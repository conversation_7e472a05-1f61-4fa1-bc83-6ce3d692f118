const dayjs = require('dayjs');

const FMT_FILE_DATE = 'MMM D, YYYY';
const FMT_FILE_TIME = 'HH:mm';

export const formatDateWithAt = (date: Date | string) => {
  const dateBrowser = dayjs(date, 'YYYY/MM/DD HH:mm:ss');
  return dateBrowser.format(`${FMT_FILE_DATE} [AT] ${FMT_FILE_TIME}`);
};

export const getNowInUTC = () => dayjs(new Date());

export const getTimeTaken = (timeInMins: number) => {
  if (timeInMins < 60) {
    // If it's been less than an hour, show the time in minutes
    return `${Math.floor(timeInMins)} minutes ago`;
  } else if (timeInMins < 1440) {
    // If it's been less than a day, show the time in hours
    const hours = Math.floor(timeInMins / 60);
    return `${hours} hours ago`;
  } else {
    // If it's been a day or more, show the time in days
    const days = Math.floor(timeInMins / 1440);
    return `${days} days ago`;
  }
};
