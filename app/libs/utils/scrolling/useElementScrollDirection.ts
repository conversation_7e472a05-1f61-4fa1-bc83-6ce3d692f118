import React from 'react';

export default function useElementScrollDirection(
  scrollableRef: React.RefObject<HTMLElement>,
) {
  const [scrollingDown, setScrollingDown] = React.useState(false);
  const [scrollTop, setScrollTop] = React.useState(0);

  React.useEffect(() => {
    if (scrollableRef.current === null) return;
    let lastScrollTop = 0;

    const handleScroll = (ev: Event) => {
      const target = ev.currentTarget as HTMLElement;
      const scrollingDown = target.scrollTop > lastScrollTop && target.scrollTop > 0;
      setScrollingDown(scrollingDown);
      setScrollTop(target.scrollTop);
      lastScrollTop = target.scrollTop;
    };

    scrollableRef.current.addEventListener('scroll', handleScroll);

    return () => {
      if (scrollableRef.current)
        scrollableRef.current.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return {scrollingDown, scrollingUp: !scrollingDown, scrollTop};
}
