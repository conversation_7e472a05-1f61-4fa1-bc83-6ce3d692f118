'use client';
import {ILicenseMinima} from '@libs/store/settings/types';
import {useSession} from 'next-auth/react';

export const DEFAULT_INITIALS = 'G';
const ADMIN_DOMAIN = 'presentpal.co.uk';

export enum UserType {
  Admin = 'Admin',
  AlphaUser = 'Alpha User',
  BetaUser = 'BETA User',
  Guest = 'Guest User',
}

export default function useUserDataFromSession() {
  const {data: session} = useSession();
  let userInitials = DEFAULT_INITIALS;
  let user = null;
  let userType: UserType = UserType.BetaUser;
  let userEmail: string | undefined = '';
  let userFirstName: string | null = '';
  let userLastName: string | null = '';
  let gdprAcceptance: Date | null = null;
  let license: ILicenseMinima | undefined = undefined;
  let trackingId: string | undefined = undefined;

  if (session) {
    user = session.user;
    trackingId = session.user.trackingId;
    const {firstName, lastName, email} = session.user;
    userEmail = email || '';
    if (firstName && lastName) {
      userInitials = firstName[0] + lastName[0];
      userFirstName = firstName;
      userLastName = lastName;
    }

    license = session.user.license;

    // Compute user type
    if (!email) {
      userType = UserType.Guest;
    }
    if (email && email.includes(ADMIN_DOMAIN)) {
      userType = UserType.Admin;
    } else {
      userType = UserType.AlphaUser; // TODO: This is horrendously hacky and will probably need moved to being handled in the back end, but for now it works.
    }
    if (user?.gdprAcceptance) {
      gdprAcceptance = user.gdprAcceptance;
    }
  }

  userInitials = userInitials.toUpperCase();

  return {
    userInitials,
    user,
    userType,
    userFirstName,
    userLastName,
    userEmail,
    gdprAcceptance,
    license,
    trackingId,
  };
}
