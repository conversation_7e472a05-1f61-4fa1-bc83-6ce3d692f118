import {IUserData} from '@libs/services/user/types';
import {User} from 'next-auth';

export function getUser(data: IUserData | undefined): null | User {
  if (data) {
    const {auth, details} = data;

    return {
      ...details,
      firstName: details.first_name,
      lastName: details.last_name,
      accessToken: auth.access_token,
      refreshToken: auth.refresh_token,
      license: details.license,
      accessExpiry: auth.access_expiry,
      gdprAcceptance: details.gdpr_acceptance
        ? new Date(details.gdpr_acceptance)
        : null,
      trackingId: details.trackingId,
    };
  }
  return null;
}
