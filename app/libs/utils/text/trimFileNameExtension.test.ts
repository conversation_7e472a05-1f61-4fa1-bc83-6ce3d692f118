import trimFileNameExtension from './trimFileNameExtension';

describe('Test trimming file name extension', () => {
  test('Should trim regular file correctly', () => {
    const fileName = 'test.pdf';
    const fileNameWithoutExtension = trimFileNameExtension(fileName);
    expect(fileNameWithoutExtension).toBe('test');
  });

  test('Should trim file with additional comma correctly', () => {
    const fileName = 'test.test.pdf';
    const fileNameWithoutExtension = trimFileNameExtension(fileName);
    expect(fileNameWithoutExtension).toBe('test.test');
  });
});
