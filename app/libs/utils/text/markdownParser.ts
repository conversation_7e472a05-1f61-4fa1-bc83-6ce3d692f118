export const transformMarkdownToHTML = (text: string) => {
  const markdownPatterns = [
    {pattern: /###### (.*$)/gim, replacement: '<h6>$1</h6>'},
    {pattern: /##### (.*$)/gim, replacement: '<h5>$1</h5>'},
    {pattern: /#### (.*$)/gim, replacement: '<h4>$1</h4>'},
    {pattern: /### (.*$)/gim, replacement: '<h3>$1</h3>'},
    {pattern: /## (.*$)/gim, replacement: '<h2>$1</h2>'},
    {pattern: /# (.*$)/gim, replacement: '<h1>$1</h1>'},
    {pattern: /\*\*(.*?)\*\*/gim, replacement: '<strong>$1</strong>'},
    {pattern: /\*(.*?)\*/gim, replacement: '<em>$1</em>'},
    {pattern: /\[(.*?)\]\((.*?)\)/gim, replacement: '<a href="$2">$1</a>'},
    {
      pattern: /!\[(.*?)\]\((.*?)\)/gim,
      replacement: '<img alt="$1" src="$2" />',
    },
    {pattern: /\n\*(.*)/gim, replacement: '<ul><li>$1</li></ul>'},
    {pattern: /-\s+(.*)/gim, replacement: '<ul><li>$1</li></ul>'},
    {pattern: /\n[0-9]+\.(.*)/gim, replacement: '<ol><li>$1</li></ol>'},
    {pattern: /\n\n/gim, replacement: '<br />'},
  ];

  // ensures that segments of text that are wrapped in backticks or double quotes are not transformed
  const segments = text.split(/(`[^`]*`|"[^"]*")/g);

  const transformedSegments = segments.map(segment => {
    if (
      (segment.startsWith('`') && segment.endsWith('`')) ||
      (segment.startsWith('"') && segment.endsWith('"'))
    ) {
      return escapeHTML(segment);
    }
    // Apply markdown patterns to the segment
    let transformedSegment = segment;
    markdownPatterns.forEach(({pattern, replacement}) => {
      transformedSegment = transformedSegment.replace(pattern, replacement);
    });

    return transformedSegment;
  });

  let transformed = transformedSegments.join('');

  // Removes the markdown code block syntax if it exists
  if (transformed.startsWith('```') && transformed.endsWith('```')) {
    transformed = transformed
      .replace(/```markdown/gim, '')
      .replace(/```/gim, '');
  }

  // Remove any extra <ul> or <ol> tags
  transformed = transformed.replace(/<\/ul>\s*<ul>/gim, '');
  transformed = transformed.replace(/<\/ol>\s*<ol>/gim, '');

  return transformed.trim();
};

const escapeHTML = (str: string) => {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};
