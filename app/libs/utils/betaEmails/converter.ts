import {IBetaEmailServerData} from '@libs/services/admin/betaEmails/types';
import {IBetaEmail} from '@libs/store/admin/betaEmails/types';

function convertServerBetaEmailToClientFormat(
  betaEmail: IBetaEmailServerData,
): IBetaEmail {
  return {
    email: betaEmail.email,
    addedBy: betaEmail.added_by,
    deletedBy: betaEmail.deleted_by,
    claimedBy: betaEmail.claimed_by,
    dateCreated: betaEmail.date_created as unknown as Date,
    dateDeleted: betaEmail.date_deleted
      ? (betaEmail.date_deleted as unknown as Date)
      : null,
  };
}

export function convertServerBetaEmailsToClientFormat(
  betaEmails: IBetaEmailServerData[],
): IBetaEmail[] {
  return betaEmails.map(betaEmail =>
    convertServerBetaEmailToClientFormat(betaEmail),
  );
}
