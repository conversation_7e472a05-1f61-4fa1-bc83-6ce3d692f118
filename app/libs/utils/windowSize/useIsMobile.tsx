'use client';
import {useWindowSize} from 'usehooks-ts';

export const SMALL_BREAKPOINT = 640; // 640 is the tailwind breakpoint
export const MEDIUM_BREAKPOINT = 768; // 768 is the tailwind breakpoint
export const LARGE_BREAKPOINT = 1024; // 1024 is the tailwind breakpoint

/**
 * Helper hook to determine if window size is mobile
 * Notes:
 *  - Only to be used in client components
 *  - Where possible avoid using this hook. Tailwind media queries should be used instead
 *
 */
export default function useIsMobile() {
  const {width} = useWindowSize();
  const isMobile = width < SMALL_BREAKPOINT;
  const isTablet = width < MEDIUM_BREAKPOINT;
  const isLargeTablet = width < LARGE_BREAKPOINT;
  return {isMobile, isTablet, isLargeTablet};
}
