import {
  IServerWebSocketMessage,
  IWebSocketMessage,
} from '@libs/services/comms/types';

export function convertWebSocketMessageFromServer(
  message: IServerWebSocketMessage,
): IWebSocketMessage {
  return {
    updateType: message.update_type,
    documentId: message.document_id,
    target: message.target,
    targetId: message.target_id,
    value: message.value,
    message: message.message,
  };
}
