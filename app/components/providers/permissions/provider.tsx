'use client';
import React, {createContext} from 'react';
import {createContextualCan} from '@casl/react';
import {AnyAbility, createMongoAbility} from '@casl/ability';
import {UserPermissions} from './types';

export const PermissionsContext = createContext<AnyAbility>({} as any);

export default function PermissionsProvider({
  children,
  userPermissions,
}: {
  children: React.ReactNode;
  userPermissions: UserPermissions;
}) {
  const userAbilities = createMongoAbility(userPermissions);

  return (
    <PermissionsContext.Provider value={userAbilities}>
      {children}
    </PermissionsContext.Provider>
  );
}

export const Can = createContextualCan(PermissionsContext.Consumer);
