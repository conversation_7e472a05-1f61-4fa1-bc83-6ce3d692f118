'use client';

import {SWRConfig} from 'swr';
import * as Sentry from '@sentry/browser';
import StateProvider from './state/provider';
import { SessionProvider } from 'next-auth/react';
import {fetcher, defaultOptions} from '@libs/services/swrParams';
import {AnalyticsWrapper} from '@components/wrappers/analyticsWrapper';

type Props = {
  children?: React.ReactNode;
};

export const Providers = ({children}: Props) => {
  return (
    <AnalyticsWrapper>
      <SWRConfig
        value={{
          fetcher,
          ...defaultOptions,
          onError: (error, key) => {
            if (error.status !== 403 && error.status !== 404) {
              // Send the error to Sentry
              Sentry.captureException(error);
              //TODO: In the future we can use the key to show a notification UI.
            }
          },
        }}>

        <SessionProvider>
          <StateProvider>{children}</StateProvider>
        </SessionProvider>
      </SWRConfig>
    </AnalyticsWrapper>
  );
};
