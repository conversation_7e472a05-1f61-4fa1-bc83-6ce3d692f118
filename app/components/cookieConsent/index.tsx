'use client';
import useSettings from '@libs/store/settings';
import Script from 'next/script';
import React, {useCallback, useEffect, useRef} from 'react';

export default function CookieConsent() {
  const {settingsAttrString} = useSettings();
  const theme = settingsAttrString;
  const cookieBanner = useRef<HTMLElement | null>(null);

  // Apply theme to cookie banner. Currently theme being applied client side and doesn't apply styles properly, but works for toggles.
  const applyTheme = useCallback(() => {
    const setThemeAttribute = (element: HTMLElement) => {
      element.setAttribute('data-theme', theme);
    };

    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node instanceof HTMLElement && node.id === 'cookiebanner') {
            setThemeAttribute(node);
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    if (cookieBanner.current) setThemeAttribute(cookieBanner.current);

    return () => observer.disconnect();
  }, [theme]);

  useEffect(() => {
    if (document)
      cookieBanner.current = document.querySelector('#cookiebanner');
    applyTheme();
  }, [applyTheme]);

  return (
    <Script
      id="Cookiebot"
      src="https://consent.cookiebot.com/uc.js"
      strategy="afterInteractive"
      data-cbid="c679a94d-10f0-4e44-b839-1ec300595ca3"
      data-blockingmode="auto"
      type="text/javascript"
    />
  );
}
