import Button from '@components/buttons/button';
import CheckBox from '@components/forms/checkbox/checkbox';
import DropdownSelect from '@components/forms/dropdown/dropdownSelect';
import React from 'react';
import {useFeedbackForm} from './useFeedbackForm';
import TextArea from '@components/forms/textArea/textArea';
import MessagesPanel from '@components/messagesPanel/messagesPanel';

export const feedbackTopics = [
  'Uploading documents',
  'How a document is displayed',
  'General bugs',
  'Feature requests',
  'General feedback',
  'Other',
];

const FeedbackForm: React.FC = () => {
  const {
    checkboxChecked,
    feedbackContent,
    submissionError,
    selectedTopic,
    loading,
    updateSelectedTopic,
    toggleCheckbox,
    setFeedback,
    submitFeedbackForm,
  } = useFeedbackForm();

  return (
    <>
      <div className="pt-4">
        <DropdownSelect
          inputLabel="Topic (required)"
          required
          requiredStyling={false}
          onSelect={updateSelectedTopic}
          defaultOption={selectedTopic}
          options={feedbackTopics}
        />
      </div>
      <div className="pt-4">
        <TextArea
          label="Feedback (required)"
          value={feedbackContent}
          required
          onChange={setFeedback}
          hintLabel="Please provide as much detail as you can."
        />
      </div>
      <div className="pt-4">
        <CheckBox
          label="I'm happy to be contacted about my feedback"
          ariaLabel="feedback contact checkbox"
          id="feedback-checkbox"
          checked={checkboxChecked}
          onPress={toggleCheckbox}
        />
      </div>
      <Button
        variant={'primary'}
        size="sm"
        className="btn-full mt-4"
        loading={loading}
        disabled={!feedbackContent || !selectedTopic || loading}
        onClick={submitFeedbackForm}>
        Send feedback
      </Button>
      {submissionError && (
        <MessagesPanel
          type="danger"
          messageTitle="Error sending feedback"
          message={submissionError}
          hideCloseButton
        />
      )}
    </>
  );
};

export default FeedbackForm;
