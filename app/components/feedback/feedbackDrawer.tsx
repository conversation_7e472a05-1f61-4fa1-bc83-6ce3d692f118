import {FC} from 'react';
import Drawer from '@components/modals/drawer/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import {FeedbackContentView} from './feedbackContentView';

const DocToolsDrawer: FC = () => {
  return (
    <Drawer
      drawerTitle="Give feedback"
      drawerID={DRAWER_ID.FEEDBACK}
      drawerContent={<FeedbackContentView />}
      direction="right"
      right={0}
      zIndex={9998} // z-max -1 to ensure the drawer is below the nav
      drawerBgColour="app-navbar-bg border-l theme-border"
    />
  );
};

export default DocToolsDrawer;
