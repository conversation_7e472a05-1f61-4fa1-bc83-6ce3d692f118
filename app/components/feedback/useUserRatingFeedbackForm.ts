import {useState} from 'react';
import UserService from '@libs/services/user/service';
import useUserDataFromSession from '@libs/utils/user/useUserDataFromSession';
import {useSession} from 'next-auth/react';

export const useUserRatingFeedbackForm = () => {
  const userService = new UserService();
  const {userEmail} = useUserDataFromSession();
  const {data: session} = useSession();
  const [loading, setLoading] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  const submitUserRatingFeedbackForm = async (
    page: string | null,
    documentType: string,
    userRating: string,
    text: string,
  ) => {
    setLoading(true);
    const locationInApp = window.location.pathname;
    const userUuid = session?.user.id;
    await userService
      .sendUserSatifactionRatingFeedback(
        userEmail,
        userUuid ?? '',
        'user_rating',
        page,
        documentType,
        userRating,
        text,
      )
      .then(() => {
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
        setSubmissionError('An error occurred. Please try again.');
      });
  };
  return {
    submitUserRatingFeedbackForm,
    loading,
  };
};
