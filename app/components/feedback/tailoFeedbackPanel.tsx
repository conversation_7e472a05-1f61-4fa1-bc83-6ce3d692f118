import TailoIconButton from '@components/buttons/tailoIconButton';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID, DrawerTab} from '@libs/store/drawer/types';
import {CloseCircle} from 'iconsax-react';
import FeedbackFaceCard from './feedbackFaceCard';
import Button from '@components/buttons/button';
import {MessageText1} from 'iconsax-react';
import Frown from '@components/customIcons/frown';
import SlightFrown from '@components/customIcons/slightFrown';
import Meh from '@components/customIcons/meh';
import SlightSmile from '@components/customIcons/slightSmile';
import Smile from '@components/customIcons/smile';
import {useEffect, useState} from 'react';
import TextArea from '@components/forms/textArea/textArea';
import FeedbackEndScreen from './feedbackEndScreen';
import {useSearchParams, usePathname} from 'next/navigation';
import useDocuments from '@libs/services/document/useDocuments';
import {useFeedbackFormAnalysis} from './useFeedbackFormAnalysis';
import {useUserRatingFeedbackForm} from './useUserRatingFeedbackForm';
import Loading from '@components/loaders/loading';

export interface TailoFeedbackPanelProps {
  panelTitle?: string;
}

export default function TailoFeedbackPanel({
  panelTitle,
}: TailoFeedbackPanelProps) {
  const {submitUserRatingFeedbackForm, loading} = useUserRatingFeedbackForm();
  const [showTextArea, setShowTextArea] = useState(false);
  const [text, setText] = useState('');
  const [ratingPressed, setRatingPressed] = useState(false);
  const [userRating, setUserRating] = useState('');
  const [hasCommented, setHasCommented] = useState(false);
  const [showFeedbackEndScreen, setShowFeedbackEndScreen] = useState(false);
  const {
    drawStateById,
    openDrawer,
    docToolsDrawerContent,
    updateDocToolsDrawerContent,
    updateDrawer,
  } = useDrawer();
  const {getDocumentById} = useDocuments();
  const {trackCloseFeedbackForm, trackSubmitFeedbackForm} =
    useFeedbackFormAnalysis();
  const params = useSearchParams();
  const pathname = usePathname();

  const id = pathname.split('/')[2];
  const page = params.get('tab');
  const documentType = getDocumentById(id)?.type || '';

  const drawerOpen = (drawerId: DRAWER_ID) =>
    drawStateById(drawerId) === 'open';

  const setDrawerContentToDefine = (drawerId: DrawerTab) => {
    updateDocToolsDrawerContent(drawerId);
  };

  const handleDrawerTab = (drawerTab: DrawerTab) => {
    if (!drawerOpen(DRAWER_ID.DOCUMENT_TOOLS)) {
      openDrawer(DRAWER_ID.DOCUMENT_TOOLS);
    }
    if (
      drawerOpen(DRAWER_ID.DOCUMENT_TOOLS) &&
      drawerTab === docToolsDrawerContent.content
    ) {
      updateDrawer(DRAWER_ID.DOCUMENT_TOOLS, 'closed');
    }
    setDrawerContentToDefine(drawerTab);
  };

  const toggleRatingPressed = () => {
    setRatingPressed(true);
  };

  useEffect(() => {
    if (text.length === 0) return;
    setHasCommented(true);
  }, [text]);

  const handleCloseFeedbackDrawer = () => {
    handleDrawerTab(DrawerTab.TAILO_FEEDBACK);
    setShowFeedbackEndScreen(false);
    trackCloseFeedbackForm(page, documentType);
    setShowTextArea(false);
  };

  const handleSubmitUserRatingFeedbackForm = () => {
    if (!loading) setShowFeedbackEndScreen(true);
    trackSubmitFeedbackForm(page, documentType, userRating, hasCommented);
    submitUserRatingFeedbackForm(page, documentType, userRating, text);
  };

  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-2xl font-bold hidden lg:block">{panelTitle}</h3>
        <TailoIconButton
          className="hidden lg:block"
          label="Close"
          handler={handleCloseFeedbackDrawer}
          size="small">
          <CloseCircle className="h-8 w-8" />
        </TailoIconButton>
      </div>
      {!showFeedbackEndScreen ? (
        <>
          <p>How satisfied are you with the reading experience in Tailo?</p>
          <div className="space-y-2 my-4">
            <FeedbackFaceCard
              title="Very Satisfied"
              emoji={<Smile className="feedback-faces" />}
              handler={() => {
                toggleRatingPressed();
                setUserRating('Very Satisfied');
              }}
            />
            <FeedbackFaceCard
              title="Satisfied"
              emoji={<SlightSmile className="feedback-faces" />}
              handler={() => {
                toggleRatingPressed();
                setUserRating('Satisfied');
              }}
            />
            <FeedbackFaceCard
              title="Indifferent"
              emoji={<Meh className="feedback-faces" />}
              handler={() => {
                toggleRatingPressed();
                setUserRating('Indifferent');
              }}
            />
            <FeedbackFaceCard
              title="Dissatisfied"
              emoji={<SlightFrown className="feedback-faces" />}
              handler={() => {
                toggleRatingPressed();
                setUserRating('Dissatisfied');
              }}
            />
            <FeedbackFaceCard
              title="Very Dissatisfied"
              emoji={<Frown className="feedback-faces" />}
              handler={() => {
                toggleRatingPressed();
                setUserRating('Very Dissatisfied');
              }}
            />
          </div>

          {!showTextArea ? (
            <Button
              variant="secondary"
              size="sm"
              className="btn-full mb-6"
              onClick={() => setShowTextArea(true)}>
              <MessageText1 variant="Bold" className="w-6 h-6" />
              Add comment
            </Button>
          ) : (
            <TextArea
              label="Your feedback (optional)"
              placeholder="Tell us about your experience..."
              value={text}
              onChange={e => {
                setText(e.target.value);
              }}
            />
          )}

          <Button
            variant="primary"
            size="sm"
            className="btn-full"
            disabled={!ratingPressed}
            onClick={handleSubmitUserRatingFeedbackForm}>
            Submit feedback
          </Button>
        </>
      ) : (
        <>
          {loading ? (
            <div className="w-full text-center">
              <Loading />
            </div>
          ) : (
            <FeedbackEndScreen page={page} documentType={documentType} />
          )}
        </>
      )}
    </>
  );
}
