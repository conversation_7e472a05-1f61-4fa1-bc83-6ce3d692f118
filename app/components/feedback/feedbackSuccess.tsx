import Button from '@components/buttons/button';
import {TickCircle} from 'iconsax-react';
import React, {useEffect} from 'react';
import {useFeedbackForm} from './useFeedbackForm';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';

const FeedbackSuccess: React.FC = () => {
  const {returnToFeedbackForm} = useFeedbackForm();
  const {drawStateById} = useDrawer();

  useEffect(() => {
    if (drawStateById(DRAWER_ID.FEEDBACK) === 'closed') returnToFeedbackForm();
  }, [drawStateById, returnToFeedbackForm]);

  return (
    <div className="flex flex-col items-center pt-4">
      <TickCircle className="text-green-500 h-16 w-16" variant="Bold" />
      <h4 className="text-xl font-bold leading-snug doc-text-default mt-4">
        Feedback sent! 🙌
      </h4>
      <Button
        variant="primary"
        className="btn-full mt-4"
        onClick={returnToFeedbackForm}>
        Give more feedback
      </Button>
    </div>
  );
};

export default FeedbackSuccess;
