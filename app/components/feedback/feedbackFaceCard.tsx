'use client';
import {useState} from 'react';

export interface FeedbackFaceCardProps {
  title: string;
  emoji: JSX.Element;
  handler: () => void;
}

export default function FeedbackFaceCard({
  title,
  emoji,
  handler,
}: FeedbackFaceCardProps) {
  return (
    <label
      className="w-full h-12 flex items-center gap-[10px]
     hover:cursor-pointer feedback-face-card border-2 p-2 rounded-lg 
     font-semibold checkbox-label"
      onClick={handler}>
      <input type="radio" name="option" className="hidden peer" />
      <div className="">{emoji}</div>
      <span className="">{title}</span>
    </label>
  );
}
