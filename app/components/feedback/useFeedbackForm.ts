import {atom, useAtom} from 'jotai';
import {ChangeEvent, useState} from 'react';
import {feedbackTopics} from './feedbackForm';
import UserService from '@libs/services/user/service';
import useUserDataFromSession from '@libs/utils/user/useUserDataFromSession';
import {useSession} from 'next-auth/react';

export enum FeedbackView {
  FORM = 'FORM',
  SUCCESS = 'SUCCESS',
}

const activeViewAtom = atom<FeedbackView>(FeedbackView.FORM);

export const useFeedbackForm = () => {
  const userService = new UserService();
  const {userEmail} = useUserDataFromSession();
  const {data: session} = useSession();
  const [selectedTopic, setSelectedTopic] = useState(feedbackTopics[0]);
  const [feedbackContent, setFeedbackContent] = useState('');
  const [checkboxChecked, setCheckboxChecked] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const [activeView, setActiveView] = useAtom(activeViewAtom);

  const resetFormFields = () => {
    setSelectedTopic('');
    setFeedbackContent('');
    setCheckboxChecked(false);
    setSubmissionError(null);
  };

  const resetSubmissionError = () => {
    setSubmissionError(null);
  };

  const updateSelectedTopic = (topic: string) => {
    setSelectedTopic(topic);
  };

  const setFeedback = (event: ChangeEvent<HTMLTextAreaElement>) => {
    setFeedbackContent(event.target.value);
  };

  const toggleCheckbox = () => {
    setCheckboxChecked(!checkboxChecked);
  };

  const updateCurrentFeedbackView = (view: FeedbackView) => {
    setActiveView(view);
  };

  const submitFeedbackForm = async () => {
    // Clear any error message
    setSubmissionError(null);
    setLoading(true);
    const locationInApp = window.location.pathname;
    const userUuid = session?.user.id;
    // Submit feedback form
    await userService
      .sendUserFeedback(
        userEmail,
        userUuid ?? '',
        selectedTopic,
        locationInApp,
        feedbackContent,
        checkboxChecked,
      )
      .then(() => {
        // If it is successful, update the view
        updateCurrentFeedbackView(FeedbackView.SUCCESS);
      })
      .catch(error => {
        setSubmissionError(
          'There has been an error sending your feedback. Please try again.',
        );
        console.error('Error sending feedback:', error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const returnToFeedbackForm = () => {
    // Reset form fields
    resetFormFields();
    updateCurrentFeedbackView(FeedbackView.FORM);
  };

  return {
    activeView,
    selectedTopic,
    feedbackContent,
    checkboxChecked,
    submissionError,
    loading,
    setFeedback,
    toggleCheckbox,
    updateSelectedTopic,
    updateCurrentFeedbackView,
    resetFormFields,
    resetSubmissionError,
    submitFeedbackForm,
    returnToFeedbackForm,
  };
};
