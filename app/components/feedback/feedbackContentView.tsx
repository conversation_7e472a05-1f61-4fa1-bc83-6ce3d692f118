import React from 'react';
import FeedbackForm from './feedbackForm';
import FeedbackSuccess from './feedbackSuccess';
import {FeedbackView, useFeedbackForm} from './useFeedbackForm';

export const FeedbackContentView: React.FC = () => {
  const {activeView} = useFeedbackForm();

  switch (activeView) {
    case FeedbackView.SUCCESS:
      return <FeedbackSuccess />;
    case FeedbackView.FORM:
    default:
      return <FeedbackForm />;
  }
};
