import Button from '@components/buttons/button';
import {TickCircle} from 'iconsax-react';
import {useFeedbackFormAnalysis} from './useFeedbackFormAnalysis';

interface FeedbackEndScreenProps {
  page: string | null;
  documentType: string;
}

export default function FeedbackEndScreen({
  page,
  documentType,
}: FeedbackEndScreenProps) {
  const {trackFeedbackSignup} = useFeedbackFormAnalysis();
  return (
    <div className="space-y-2 ">
      <TickCircle className="text-green-500 w-8 h-8 " variant="Bold" />
      <h3 className="font-semibold text-xl">We&apos;ve got your feedback!</h3>
      <p>
        Thank you for helping us improve <PERSON>lo. Your feedback is very important
        to us.
      </p>
      <br />
      <p>
        If you&apos;d like to share more feedback, you can sign up to our user
        research pool. You may be invited to try out new features, or take part
        in compensated interviews.
      </p>
      <a href="https://itdpqts15l9.typeform.com/to/SG6Qz8TW" target="_blank">
        <Button
          variant="primary"
          className="btn-sm w-full mt-6"
          onClick={() => {
            trackFeedbackSignup(page, documentType);
          }}>
          Sign up now
        </Button>
      </a>
    </div>
  );
}
