import {useAnalytics} from 'use-analytics';

export const useFeedbackFormAnalysis = () => {
  const {track} = useAnalytics();

  const trackOpenFeedbackForm = (page: string | null, documentType: string) => {
    track('OpenFeedbackFormDrawer', {
      page: page,
      documentType: documentType,
    });
  };
  const trackCloseFeedbackForm = (
    page: string | null,
    documentType: string,
  ) => {
    track('CloseFeedbackFormDrawer', {
      page: page,
      documentType: documentType,
    });
  };
  const trackSubmitFeedbackForm = (
    page: string | null,
    documentType: string,
    rating: string,
    hasCommented: boolean,
  ) => {
    track('SubmitFeedbackForm', {
      page: page,
      documentType: documentType,
      rating: rating,
      hasCommented: hasCommented,
    });
  };
  const trackFeedbackSignup = (page: string | null, documentType: string) => {
    track('FeedbackSignup', {
      page: page,
      documentType: documentType,
    });
  };
  return {
    trackOpenFeedbackForm,
    trackCloseFeedbackForm,
    trackSubmitFeedbackForm,
    trackFeedbackSignup,
  };
};
