import Link from 'next/link';
import React, {useState} from 'react';

import {
  ArrowUp2,
  ArrowDown2,
  Book1,
  <PERSON>rushSquare,
  DocumentFilter,
  Headphone,
} from 'iconsax-react';

export default function WelcomeBanner(props: {defaultVisibility: boolean}) {
  const {defaultVisibility} = props;

  // Set the default state right away.
  let state = defaultVisibility;

  // Check and see if we have any stored state.
  const storedState = localStorage.getItem('welcome-banner.visibility');

  // Reset the main state if we have stored state.
  if (storedState) {
    state = storedState === 'true';
  }

  const [open, setOpen] = useState<boolean>(state);

  const toggleBanner = () => {
    setOpen(!open);

    localStorage.setItem('welcome-banner.visibility', `${!open}`);
  };

  return (
    <div className="welcome-banner w-full" id="welcome-accordion">
      <header className="w-full">
        <button
          id="welcome-accordion-btn"
          className="toggle-visibility text-right flex w-full justify-between border p-4 lg:p-6 rounded-md focus-visible:outline focus-visible:outline-4 hover:transition-all hover:duration-150"
          onClick={() => toggleBanner()}
          aria-expanded={open ? true : false}
          aria-controls="welcome-content"
          >
            <span className="text-xl font-semibold">Get started with Tailo! 🚀</span>

            {!open && <ArrowDown2 className="inline" />}
            {open && <ArrowUp2 className="inline" />}
        </button>
      </header>

      {open && (
        <section id="welcome-content" role="region" aria-labelledby="welcome-accordion-btn" className="p-6">
          <p className="mb-1">
            When you upload a document, Tailo will process it and transform your
            reading experience.
          </p>

          <p className="mb-8">In just a few minutes, you will be able to:</p>

          <ul className="mx-auto grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-4 mb-8">
            <li>
              <BrushSquare className="mb-2 mr-2 lg:mr-0 inline lg:block" />
              <strong>Read</strong> in a visual environment that suits you
            </li>
            <li>
              <DocumentFilter className="mb-2 mr-2 lg:mr-0 inline lg:block" />
              <strong>Summarise</strong> your document into more digestible
              points
            </li>
            <li>
              <Book1 className="mb-2 mr-2 lg:mr-0 inline lg:block" />
              <strong>Define</strong> words and phrases you don&apos;t know yet
            </li>
            <li>
              <Headphone className="mb-2 mr-2 lg:mr-0 inline lg:block" />
              <strong>Listen</strong> to your document being read aloud
            </li>
          </ul>

          <p>
            Need help? You can contact support at{' '}
            <a className="doc-link-default" href="mailto:<EMAIL>">
              <EMAIL>
            </a>
          </p>
        </section>
      )}
    </div>
  );
}
