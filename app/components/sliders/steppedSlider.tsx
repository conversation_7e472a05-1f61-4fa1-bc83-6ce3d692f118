import Slider from 'rc-slider';
import 'rc-slider/assets/index.css';
import Tooltip from '@components/tooltip/tooltip';
import {useToolTip} from '@components/tooltip/useToolTip';

interface ISteppedSliderProps {
  defaultValue: number;
  stepValues: number[];
  label: string;
  accessibilityLabel: string;
  onStepChange: (step: number | number[]) => void;
}

export default function SteppedSlider(props: ISteppedSliderProps) {
  const {defaultValue, stepValues, label, accessibilityLabel, onStepChange} =
    props;

  const {active, newStep, coords, onChange, onChangeComplete} = useToolTip();

  const handleChange = (step: number | number[]) => {
    onStepChange(step);
    onChangeComplete();
  };

  return (
    <>
      <div className="w-64 flex-col items-start relative">
        <h1 className="slider-text">{label}</h1>
        <Slider
          aria-label={accessibilityLabel}
          onChange={onChange}
          onChangeComplete={handleChange}
          defaultValue={defaultValue}
          min={stepValues[0]}
          max={stepValues[stepValues.length - 1]}
          step={stepValues[1] - stepValues[0]}
          dots={true}
          dotStyle={{
            height: 4,
            width: 4,
          }}
          styles={{
            track: {
              height: 6,
              marginTop: 1,
              marginInline: -2,
            },
            rail: {
              height: 6,
              marginTop: 1,
              marginInline: 2,
            },
            handle: {
              height: 14,
              width: 14,
              marginTop: -3,
              opacity: 'inherit',
              marginInline: 1,
            },
          }}
        />
        <div className="slider-value font-semibold">
          {stepValues.map((val, index) => (
            <div key={index}>{val}x</div>
          ))}
        </div>
        {active && <Tooltip value={newStep} coords={coords} />}
      </div>
    </>
  );
}
