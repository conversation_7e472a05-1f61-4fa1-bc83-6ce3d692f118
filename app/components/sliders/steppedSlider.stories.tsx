import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import SteppedSlider from './steppedSlider';

const meta: Meta<typeof SteppedSlider> = {
  title: 'Inputs/SteppedSlider',
  component: SteppedSlider,
};

export default meta;
type Story = StoryObj<typeof SteppedSlider>;

export const SteppedSliderAllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <SteppedSlider
            defaultValue={0}
            onStepChange={() => {}}
            stepValues={[1, 2, 3, 4, 5]}
            label="Zoom"
            accessibilityLabel="Zoom slider"
          />
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <SteppedSlider
            defaultValue={0}
            onStepChange={() => {}}
            stepValues={[0.5, 1, 1.5, 2, 2.5, 3.0, 3.5]}
            label="Volume"
            accessibilityLabel="Volume slider"
          />
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <SteppedSlider
            defaultValue={0}
            onStepChange={() => {}}
            stepValues={[1, 2, 3, 4, 5]}
            label="Zoom"
            accessibilityLabel="Zoom slider"
          />
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <SteppedSlider
            defaultValue={0}
            onStepChange={() => {}}
            stepValues={[0.5, 1, 1.5, 2, 2.5, 3.0, 3.5]}
            label="Volume"
            accessibilityLabel="Volume slider"
          />
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <SteppedSlider
            defaultValue={0}
            onStepChange={() => {}}
            stepValues={[1, 2, 3, 4, 5]}
            label="Zoom"
            accessibilityLabel="Zoom slider"
          />
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <SteppedSlider
            defaultValue={0}
            onStepChange={() => {}}
            stepValues={[0.5, 1, 1.5, 2, 2.5, 3.0, 3.5]}
            label="Volume"
            accessibilityLabel="Volume slider"
          />
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <SteppedSlider
            defaultValue={0}
            onStepChange={() => {}}
            stepValues={[1, 2, 3, 4, 5]}
            label="Zoom"
            accessibilityLabel="Zoom slider"
          />
        </div>
      </div>
    </>
  ),
};
