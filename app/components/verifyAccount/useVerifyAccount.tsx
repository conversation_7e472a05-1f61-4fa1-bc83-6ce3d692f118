import React from 'react';
import {AppPrivateRoutes, AppPublicRoutes} from '@libs/services/auth/routes';
import {useSearchParams, useRouter} from 'next/navigation';
import {signIn} from 'next-auth/react';
import {useOnboardingAnalytics} from '@components/onboarding/useOnboardingAnalytics';

export default function useVerifyAccount() {
  const searchParams = useSearchParams();
  const {trackRefreshVerifyManually, trackVerifyPageView} =
    useOnboardingAnalytics();
  const ticket = searchParams.get('ticket');
  const registration = searchParams.get('registration');

  const [error, setError] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState<string | JSX.Element>(
    '',
  );
  const router = useRouter();
  const reloadPage = () => {
    window.location.reload();
    trackRefreshVerifyManually();
  };

  const redirectMessage = (
    <p>
      {
        'Sorry, that magic link has expired and we weren’t able to securely log you in. Please try again.'
      }
    </p>
  );

  React.useEffect(() => {
    trackVerifyPageView(
      'VerifyingAccount',
      registration ? 'registration' : 'signIn',
    );
    signIn('magic-link', {
      redirect: false, //Allows to handle errors on the same page
      ticket,
    })
      .then(async res => {
        if (res?.error) {
          // Catching Next auth credentials error
          if (res.status === 401) {
            setErrorMessage(redirectMessage);
          }
          setError(true);
        } else {
          let callbackUrl = AppPrivateRoutes.Dashboard;
          return router.push(callbackUrl);
        }
      })
      .catch(() => {
        setError(true);
        setErrorMessage('Failed to authorise user');
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {error, errorMessage, registration, reloadPage};
}
