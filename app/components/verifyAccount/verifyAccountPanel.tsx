'use client';

import SvgGradientLoader from '@components/loaders/svgGradientLoader';
import useVerifyAccount from './useVerifyAccount';
import MessagesPanel from '@components/messagesPanel/messagesPanel';
import React from 'react';
import {AppPublicRoutes} from '@libs/services/auth/routes';
import {useRouter} from 'next/navigation';

export default function VerifyAccountPanel() {
  const {error, errorMessage, registration, reloadPage} = useVerifyAccount();

  const heading = registration ? ' Creating account... ' : 'Signing in...';
  const router = useRouter();

  return (
    <>
      <div className="w-full p-4 sm:w-96 sm:p-6 login-panel border flex-col gap-4 inline-flex  justify-center text-center">
        {!error && (
          <div className="inline-block m-auto">
            <SvgGradientLoader />
          </div>
        )}
        <h1 className="text-xl font-bold sm:text-2xl ">{heading}</h1>
        <p className="whitespace-pre-wrap">
          This page will automatically reload.
        </p>
        {error && (
          <MessagesPanel
            type="danger"
            messageTitle="Login Unsuccessful"
            message={errorMessage}
            hideCloseButton
          />
        )}
      </div>
      <div className="w-72 sm:w-96 pl-4 pr-2 sm:px-6 space-y-2">
        {errorMessage ? (
          <span
            role="button"
            onClick={() => router.push(AppPublicRoutes.SignIn)}
            className="link">
            Click here to go back and request a new magic link.
          </span>
        ) : (
          <p>
            {'If the page doesn’t automatically reload after 5 seconds, '}
            <span role="button" onClick={reloadPage} className="link">
              click here to manually refresh the page.
            </span>
          </p>
        )}
      </div>
    </>
  );
}
