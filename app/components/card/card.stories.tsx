import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import Card from './card';

const meta: Meta<typeof Card> = {
  title: 'Containers/Card',
  component: Card,
};

export default meta;
type Story = StoryObj<typeof Card>;

export const CardStory: Story = {
  render: args => (
    <>
      <div data-theme="dark" className="pb-4 space-y-4">
        <Card {...args}>
          <p className="p-4 doc-text-default">Test Card</p>
        </Card>
      </div>
      <div data-theme="light" className="pb-4">
        <Card {...args}>
          <p className="p-4 doc-text-default">Test Card</p>
        </Card>
      </div>
      <div data-theme="yellow" className="pb-4">
        <Card {...args}>
          <p className="p-4 doc-text-default">Test Card</p>
        </Card>
      </div>
      <div data-theme="pale-green" className="pb-4">
        <Card {...args}>
          <p className="p-4 doc-text-default">Test Card</p>
        </Card>
      </div>
      <div data-theme="pale-pink" className="pb-4">
        <Card {...args}>
          <p className="p-4 doc-text-default">Test Card</p>
        </Card>
      </div>
      <div data-theme="pale-blue" className="pb-4">
        <Card {...args}>
          <p className="p-4 doc-text-default">Test Card</p>
        </Card>
      </div>
      <div data-theme="peach">
        <Card {...args}>
          <p className="p-4 doc-text-default">Test Card</p>
        </Card>
      </div>
    </>
  ),
};

CardStory.args = {
  disabled: false,
};
