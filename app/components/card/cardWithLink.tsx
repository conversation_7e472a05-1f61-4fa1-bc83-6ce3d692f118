import React from 'react';
import Card, {ICardProps} from './card';
import CardLink from './cardLink';

export interface ICardWithLinkProps extends ICardProps {
  href: string;
  linkText: string;
  disabled?: boolean;
}
export default function CardWithLink({
  href,
  linkText,
  children,
  className,
  disabled,
}: ICardWithLinkProps) {
  return (
    <Card className={className} disabled={disabled}>
      <CardLink href={href} text={linkText} disabled={disabled} />
      {children}
    </Card>
  );
}
