import Link from 'next/link';
import React from 'react';

export default function CardLink({
  text,
  href,
  disabled,
}: {
  text: string;
  href: string;
  disabled?: boolean;
}) {
  if (disabled) {
    return (
      <a role="link" aria-disabled="true" className="card-link grow truncate order-2">
        {text}
      </a>
    );
  }
  return (
    <Link href={href} className="card-link grow truncate order-2">
      {text}
    </Link>
  );
}
