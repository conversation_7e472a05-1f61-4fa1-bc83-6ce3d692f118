import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import Card<PERSON>ithLink from './cardWithLink';

const meta: Meta<typeof CardWithLink> = {
  title: 'Containers/CardWithLink',
  component: CardWithLink,
};

export default meta;
type Story = StoryObj<typeof CardWithLink>;

export const CardWithLinkStory: Story = {
  render: args => (
    <>
      <div data-theme="dark" className="pb-4 space-y-4">
        <CardWithLink {...args} href="" linkText="test">
          <p className="p-4 doc-text-default">Test CardWithLink</p>
        </CardWithLink>
      </div>
      <div data-theme="light" className="pb-4">
        <CardWithLink {...args} href="" linkText="test">
          <p className="p-4 doc-text-default">Test CardWithLink</p>
        </CardWithLink>
      </div>
      <div data-theme="yellow" className="pb-4">
        <CardWithLink {...args} href="" linkText="test">
          <p className="p-4 doc-text-default">Test CardWithLink</p>
        </CardWithLink>
      </div>
      <div data-theme="pale-green" className="pb-4">
        <CardWithLink {...args} href="" linkText="test">
          <p className="p-4 doc-text-default">Test CardWithLink</p>
        </CardWithLink>
      </div>
      <div data-theme="pale-pink" className="pb-4">
        <CardWithLink {...args} href="" linkText="test">
          <p className="p-4 doc-text-default">Test CardWithLink</p>
        </CardWithLink>
      </div>
      <div data-theme="pale-blue" className="pb-4">
        <CardWithLink {...args} href="" linkText="test">
          <p className="p-4 doc-text-default">Test CardWithLink</p>
        </CardWithLink>
      </div>
      <div data-theme="peach">
        <CardWithLink {...args} href="" linkText="test">
          <p className="p-4 doc-text-default">Test CardWithLink</p>
        </CardWithLink>
      </div>
    </>
  ),
};

CardWithLinkStory.args = {
  disabled: false,
};
