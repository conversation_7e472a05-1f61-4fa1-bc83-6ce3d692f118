import React, {ReactNode} from 'react';

export interface ICardProps {
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}
export default function Card({children, className, disabled}: ICardProps) {
  const disabledStyle = disabled
    ? 'card-disabled'
    : 'shadow-solid-md-d active:shadow-none';
  return (
    <div
      className={`flex flex-col lg:flex-row lg:items-center relative w-full rounded-lg p-4 lg:pl-6 lg:pr-[88px] py-5 border card ${disabledStyle} ${className}`}>
      {children}
    </div>
  );
}
