interface BulletsProps {
  className?: string;
}

export default function Bullets(props: BulletsProps) {
  const {className = ''} = props;
  return (
    <svg
      width="32"
      height="32"
      className={className}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 29.3333H12C5.33333 29.3333 2.66667 26.6667 2.66667 20V12C2.66667 5.33333 5.33333 2.66666 12 2.66666H20C26.6667 2.66666 29.3333 5.33333 29.3333 12V20C29.3333 26.6667 26.6667 29.3333 20 29.3333ZM7.49349 10.5065C7.49349 9.95422 7.94121 9.50651 8.49349 9.50651H10.1602C10.7124 9.50651 11.1602 9.95422 11.1602 10.5065C11.1602 11.0588 10.7124 11.5065 10.1602 11.5065H8.49349C7.94121 11.5065 7.49349 11.0588 7.49349 10.5065ZM12.8268 10.5065C12.8268 9.95422 13.2745 9.50651 13.8268 9.50651H23.4935C24.0458 9.50651 24.4935 9.95422 24.4935 10.5065C24.4935 11.0588 24.0458 11.5065 23.4935 11.5065H13.8268C13.2745 11.5065 12.8268 11.0588 12.8268 10.5065ZM13.8268 15C13.2745 15 12.8268 15.4477 12.8268 16C12.8268 16.5523 13.2745 17 13.8268 17H23.4935C24.0458 17 24.4935 16.5523 24.4935 16C24.4935 15.4477 24.0458 15 23.4935 15H13.8268ZM7.49349 16C7.49349 15.4477 7.94121 15 8.49349 15H10.1602C10.7124 15 11.1602 15.4477 11.1602 16C11.1602 16.5523 10.7124 17 10.1602 17H8.49349C7.94121 17 7.49349 16.5523 7.49349 16ZM8.49349 20.3333C7.94121 20.3333 7.49349 20.781 7.49349 21.3333C7.49349 21.8856 7.94121 22.3333 8.49349 22.3333H10.1602C10.7124 22.3333 11.1602 21.8856 11.1602 21.3333C11.1602 20.781 10.7124 20.3333 10.1602 20.3333H8.49349ZM12.8268 21.3333C12.8268 20.781 13.2745 20.3333 13.8268 20.3333H23.4935C24.0458 20.3333 24.4935 20.781 24.4935 21.3333C24.4935 21.8856 24.0458 22.3333 23.4935 22.3333H13.8268C13.2745 22.3333 12.8268 21.8856 12.8268 21.3333Z"
        fill="currentColor"
      />
    </svg>
  );
}
