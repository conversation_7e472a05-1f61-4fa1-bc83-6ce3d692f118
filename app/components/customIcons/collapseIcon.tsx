interface Props {
  className?: string;
}

export default function CollapseIcon({className}: Props) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      className={className}
      xmlns="http://www.w3.org/2000/svg">
      <path d="M13.4054 29.5496C13.4054 30.4951 12.6213 31.2793 11.6757 31.2793C10.7301 31.2793 9.94595 30.4951 9.94595 29.5496V24.4987L3.66702 30.7777C2.99194 31.4527 1.89742 31.4527 1.22234 30.7777C0.547261 30.1026 0.54726 29.0081 1.22234 28.333L7.50126 22.0541H2.45045C1.50486 22.0541 0.720718 21.2699 0.720718 20.3243C0.720718 19.3787 1.50486 18.5946 2.45045 18.5946H11.6757C11.9063 18.5946 12.1139 18.6407 12.3445 18.733C12.7596 18.9175 13.1056 19.2404 13.2901 19.6786C13.3593 19.8861 13.4054 20.0937 13.4054 20.3243V29.5496Z" />
      <path d="M30.7777 1.22234C31.4527 1.89742 31.4527 2.99195 30.7777 3.66703L24.4987 9.94594H29.5495C30.4951 9.94594 31.2793 10.7301 31.2793 11.6757C31.2793 12.6213 30.4951 13.4054 29.5495 13.4054H20.3243C20.0937 13.4054 19.8631 13.3593 19.6555 13.267C19.2404 13.0825 18.8944 12.7596 18.7099 12.3214C18.6407 12.1139 18.5946 11.9063 18.5946 11.6757V2.45045C18.5946 1.50486 19.3787 0.720719 20.3243 0.720719C21.2699 0.720719 22.0541 1.50486 22.0541 2.45045V7.50126L28.333 1.22234C29.0081 0.54726 30.1026 0.547261 30.7777 1.22234Z" />
    </svg>
  );
}
