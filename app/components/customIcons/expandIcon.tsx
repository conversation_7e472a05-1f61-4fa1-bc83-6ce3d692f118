interface Props {
  className?: string;
}

export default function ExpandIcon({className}: Props) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg">
      <path d="M32 11.3778C32 12.3599 31.1856 13.1743 30.2035 13.1743C29.2214 13.1743 28.407 12.3599 28.407 11.3778V6.13202L21.8857 12.6533C21.1846 13.3544 20.0478 13.3544 19.3467 12.6533C18.6456 11.9522 18.6456 10.8154 19.3467 10.1143L25.868 3.59299H20.6222C19.6401 3.59299 18.8257 2.77857 18.8257 1.79649C18.8257 0.814408 19.6401 0 20.6222 0H30.2035C30.443 0 30.6586 0.0479062 30.8982 0.143719C31.3293 0.335345 31.6886 0.670691 31.8802 1.1258C31.9521 1.34138 32 1.55696 32 1.79649V11.3778Z" />
      <path d="M12.6533 19.3467C13.3544 20.0478 13.3544 21.1846 12.6533 21.8858L6.13202 28.407H11.3778C12.3599 28.407 13.1743 29.2214 13.1743 30.2035C13.1743 31.1856 12.3599 32 11.3778 32H1.7965C1.55696 32 1.31743 31.9521 1.10185 31.8563C0.670693 31.6647 0.311393 31.3293 0.119768 30.8742C0.0479083 30.6586 0 30.443 0 30.2035V20.6222C0 19.6401 0.814413 18.8257 1.7965 18.8257C2.77858 18.8257 3.59299 19.6401 3.59299 20.6222V25.868L10.1142 19.3467C10.8154 18.6456 11.9522 18.6456 12.6533 19.3467Z" />
    </svg>
  );
}
