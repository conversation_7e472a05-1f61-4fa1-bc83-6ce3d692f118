interface PinProps {
  className?: string;
}

export default function Pin(props: PinProps) {
  const {className = ''} = props;
  return (
    <svg
      width="32"
      height="25"
      viewBox="0 0 32 32"
      className={className}
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.1025 5.81798C10.8497 5.00927 11.974 4.5568 13.2451 4.5568L18.9585 4.55681C21.3811 4.55681 23.4226 6.56163 22.9006 8.94012L20.8281 19.8833C20.7566 20.261 20.4265 20.5345 20.0421 20.5345L11.8598 20.5344C11.4673 20.5344 11.1328 20.2497 11.0701 19.8623L9.30091 8.93148C9.0372 7.74767 9.3514 6.63087 10.1025 5.81798Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.1017 19.0693C16.5436 19.0693 16.9017 19.4274 16.9017 19.8693L16.9017 27.0011C16.9017 27.4429 16.5436 27.8011 16.1017 27.8011C15.6599 27.8011 15.3017 27.4429 15.3017 27.0011L15.3017 19.8693C15.3017 19.4274 15.6599 19.0693 16.1017 19.0693Z"
      />
    </svg>
  );
}
