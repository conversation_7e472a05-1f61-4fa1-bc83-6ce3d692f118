import {c} from 'vitest/dist/reporters-5f784f42';

interface UserCircleProps {
  className?: string;
}

export default function UserCircle(props: UserCircleProps) {
  const {className = ''} = props;
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      className="user-svg-icon"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M22.0015 12C22.0015 6.49 17.5115 2 12.0015 2C6.49146 2 2.00146 6.49 2.00146 12C2.00146 14.9 3.25146 17.51 5.23146 19.34C5.23146 19.35 5.23146 19.35 5.22146 19.36C5.32146 19.46 5.44146 19.54 5.54146 19.63C5.60146 19.68 5.65146 19.73 5.71146 19.77C5.89146 19.92 6.09146 20.06 6.28147 20.2C6.35147 20.25 6.41146 20.29 6.48146 20.34C6.67146 20.47 6.87146 20.59 7.08146 20.7C7.15146 20.74 7.23146 20.79 7.30147 20.83C7.50146 20.94 7.71146 21.04 7.93146 21.13C8.01146 21.17 8.09146 21.21 8.17146 21.24C8.39146 21.33 8.61146 21.41 8.83146 21.48C8.91146 21.51 8.99146 21.54 9.07146 21.56C9.31146 21.63 9.55147 21.69 9.79146 21.75C9.86146 21.77 9.93147 21.79 10.0115 21.8C10.2915 21.86 10.5715 21.9 10.8615 21.93C10.9015 21.93 10.9415 21.94 10.9815 21.95C11.3215 21.98 11.6615 22 12.0015 22C12.3415 22 12.6815 21.98 13.0115 21.95C13.0515 21.95 13.0915 21.94 13.1315 21.93C13.4215 21.9 13.7015 21.86 13.9815 21.8C14.0515 21.79 14.1215 21.76 14.2015 21.75C14.4415 21.69 14.6915 21.64 14.9215 21.56C15.0015 21.53 15.0815 21.5 15.1615 21.48C15.3815 21.4 15.6115 21.33 15.8215 21.24C15.9015 21.21 15.9815 21.17 16.0615 21.13C16.2715 21.04 16.4815 20.94 16.6915 20.83C16.7715 20.79 16.8415 20.74 16.9115 20.7C17.1115 20.58 17.3115 20.47 17.5115 20.34C17.5815 20.3 17.6415 20.25 17.7115 20.2C17.9115 20.06 18.1015 19.92 18.2815 19.77C18.3415 19.72 18.3915 19.67 18.4515 19.63C18.5615 19.54 18.6715 19.45 18.7715 19.36C18.7715 19.35 18.7715 19.35 18.7615 19.34C20.7515 17.51 22.0015 14.9 22.0015 12ZM16.9415 16.97C14.2315 15.15 9.79146 15.15 7.06146 16.97C6.62146 17.26 6.26147 17.6 5.96146 17.97C4.44146 16.43 3.50146 14.32 3.50146 12C3.50146 7.31 7.31146 3.5 12.0015 3.5C16.6915 3.5 20.5015 7.31 20.5015 12C20.5015 14.32 19.5615 16.43 18.0415 17.97C17.7515 17.6 17.3815 17.26 16.9415 16.97Z"
        fill="user-svg-icon"
      />
      <path
        d="M12.0015 6.92993C9.93146 6.92993 8.25146 8.60993 8.25146 10.6799C8.25146 12.7099 9.84146 14.3599 11.9515 14.4199C11.9815 14.4199 12.0215 14.4199 12.0415 14.4199C12.0615 14.4199 12.0915 14.4199 12.1115 14.4199C12.1215 14.4199 12.1315 14.4199 12.1315 14.4199C14.1515 14.3499 15.7415 12.7099 15.7515 10.6799C15.7515 8.60993 14.0715 6.92993 12.0015 6.92993Z"
        fill="user-svg-icon"
      />
    </svg>
  );
}
