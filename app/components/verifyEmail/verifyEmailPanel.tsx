'use client';

import React, {useEffect} from 'react';
import {AppPublicRoutes} from '@libs/services/auth/routes';
import {SmsTracking} from 'iconsax-react';
import {useSearchParams} from 'next/navigation';
import {usePathname} from 'next/navigation';
import Link from 'next/link';
import {useOnboardingAnalytics} from '@components/onboarding/useOnboardingAnalytics';

export default function VerifyEmailPanel() {
  const {trackVerifyPageView, trackNoEmailVerify} = useOnboardingAnalytics();
  const searchParams = useSearchParams();
  const email = searchParams.get('email');
  const firstName = searchParams.get('first-name');
  const lastName = searchParams.get('last-name');
  const registration = searchParams.get('registration');
  const path = usePathname();
  const checkEmail = path === '/check-email';

  const encodedEmail = encodeURIComponent(email || '');
  const encodedFirstName = encodeURIComponent(firstName || '');
  const encodedLastName = encodeURIComponent(lastName || '');

  useEffect(() => {
    trackVerifyPageView(
      'VerifyEmail',
      registration ? 'registration' : 'signIn',
    );
  }, [trackVerifyPageView]);

  return (
    <>
      <div className="w-full p-4 sm:w-96 sm:p-6 panel border flex-col gap-4 inline-flex  justify-center panel-bg theme-border">
        <div className="text-center">
          <SmsTracking
            className="m-auto h-16 w-16"
            variant="Bold"></SmsTracking>
        </div>
        <h1 className="text-xl font-bold sm:text-2xl">
          {!checkEmail ? 'Verify your account' : 'Check your email inbox'}
        </h1>
        {!!email && (
          <p className="whitespace-pre-wrap">
            {/* //Don't remove {} needed for spacing and formatting */}
            {!checkEmail
              ? `Check your inbox and click the magic link sent to:${' '}`
              : `Click the magic link sent to:${' '}`}
            <span className="font-bold">{email}</span>
          </p>
        )}
      </div>
      <div className="w-72 sm:w-96 pl-4 pr-2 sm:px-6 space-y-2">
        <h2 className="text-xl font-bold">Didn&#39;t get an email?</h2>
        {!checkEmail ? (
          <>
            <p>
              Double check your email address above and check it isn&apos;t in
              your spam folder.
            </p>
            <p className="pt-6">
              {/* //Don't remove {} needed for spacing and formatting */}
              Still no joy?{' '}
              <Link
                className="font-semibold underline leading-normal"
                onClick={trackNoEmailVerify}
                href={
                  registration
                    ? `${AppPublicRoutes.OnboardingEnd}?email=${encodedEmail}&first-name=${encodedFirstName}&last-name=${encodedLastName}`
                    : AppPublicRoutes.SignIn + '?email=' + encodedEmail
                }>
                Go back
              </Link>{' '}
              and enter your email again.
            </p>
          </>
        ) : (
          <p>
            Check your spam folder or{' '}
            <Link
              className="font-semibold underline leading-normal"
              href={
                registration
                  ? `${AppPublicRoutes.OnboardingEnd}?email=${encodedEmail}&first-name=${encodedFirstName}&last-name=${encodedLastName}`
                  : AppPublicRoutes.SignIn + '?email=' + encodedEmail
              }>
              review your email and try again
            </Link>{' '}
          </p>
        )}
      </div>
    </>
  );
}
