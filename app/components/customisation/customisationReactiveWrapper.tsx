'use client';
import React, {useRef, ElementType, HTMLAttributes} from 'react';
import {useCustomisation} from './useCustomisation';
import {getFontClassName} from '@libs/store/settings/fonts';
import {Font} from '@libs/store/settings/types';
import ContextMenu from '@components/contextMenu';
import MenuItem from '@components/contextMenu/menuItem';

/**
 * Use this wrapper to apply the customisation settings to the section of the page.
 * This includes all settings but theme, which is applied to the whole page.
 * Keep the styling out of this component as it should only care for the settings related classes!
 */

interface ICustomisationReactiveWrapper
  extends HTMLAttributes<HTMLOrSVGElement> {
  children: React.ReactNode;
  useAsViewer?: boolean;
  enableContextMenu?: boolean;
  as?: ElementType;
  className?: string;
}

export default function CustomisationReactiveWrapper({
  children,
  useAsViewer = false,
  enableContextMenu = false,
  as: Tag = 'div',
  className = '',
  ...rest
}: ICustomisationReactiveWrapper) {
  const {selectedCategory: currentFont} = useCustomisation('Font');
  const fontClassName = getFontClassName(currentFont as Font);
  const reactiveRef = useRef(null);
  return (
    <>
      <Tag
        {...rest}
        className={`customisation-reactive-wrapper ${
          useAsViewer ? 'document-viewer-container' : ' '
        } ${fontClassName} ${className}

        `}
        ref={reactiveRef}>
        {children}
      </Tag>
      {enableContextMenu && (
        <ContextMenu contextRef={reactiveRef}>
          <MenuItem itemType="word-definition" />
          <MenuItem itemType="explain-this" />
        </ContextMenu>
      )}
    </>
  );
}
