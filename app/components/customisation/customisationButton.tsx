import {FC, ReactNode} from 'react';

export interface CustomisationButtonProps {
  children: ReactNode;
  label?: string;
  onClick: () => void;
  selected: boolean;
  className?: string;
}

const CustomisationButton: FC<CustomisationButtonProps> = ({
  children,
  label,
  onClick,
  selected,
  className = '',
}) => {
  return (
    <div className="flex w-full flex-col">
      <button
        onClick={onClick}
        className={`btn btn-secondary btn-md btn-full form-input mb-2 h-16 ${
          selected ? 'selected-btn' : ''
        } ${className}`}>
        <span className="sr-only">{label}</span>
        {children}
      </button>
      {label && (
        <p className="doc-text-default text-center font-medium tracking-widest uppercase">
          {label}
        </p>
      )}
    </div>
  );
};

export default CustomisationButton;
