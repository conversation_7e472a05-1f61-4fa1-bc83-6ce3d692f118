import {FC} from 'react';
import Customisation from './customisation';
import Drawer from '@components/modals/drawer/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';

const CustomisationDrawer: FC = () => {
  return (
    <Drawer
      drawerTitle="Appearance"
      drawerID={DRAWER_ID.CUSTOMISATION}
      drawerContent={<Customisation />}
      direction="right"
      right={0}
      zIndex={9998} // z-max -1 to ensure the drawer is below the nav
    />
  );
};

export default CustomisationDrawer;
