import {useState} from 'react';
import {useSession} from 'next-auth/react';
import useSettings from '@libs/store/settings';
import {ISettings} from '@libs/store/settings/types';
import UserService from '@libs/services/user/service';

export const useCustomisation = (settingCategory: keyof ISettings) => {
  const userService = new UserService();

  const {settings, updateSettings} = useSettings();
  const {data: session, update} = useSession();
  const [errorMessage, setErrorMessage] = useState<string | undefined>(); // Store something here if the update to the DB fails?

  const handleSelect = (categoryValue: string) => {
    // Update the correct state: session for authenticated users and settings for onboarding
    const updates = {[settingCategory]: categoryValue};

    if (session && session.user.settings) {
      // Take a Cut of the current settings
      const previousSettings = session.user.settings;

      updateSettings({
        ...settings,
        ...updates,
      });

      // Update the settings on the server
      userService.updateUserSettings({...settings, ...updates}).catch(() => {
        // TODO: This will need looked at again I guess for proper Error handling
        setErrorMessage(
          'Failed to update settings, changes have been reverted.',
        );
        // Revert what we tried to change
        updateSettings({
          ...settings,
          ...previousSettings,
        });
      });
    } else {
      updateSettings(updates);
    }
  };

  const selectedCategory = settings[settingCategory];

  return {
    handleSelect,
    selectedCategory,
    errorMessage,
  };
};
