import type {<PERSON>a, StoryObj} from '@storybook/react';
import {Font} from '@libs/store/settings/types';
import CustomisationButton, {
  CustomisationButtonProps,
} from './customisationButton';
import JustifiedIcon from './textAlignPicker/justifiedIcon';
import LeftAlignIcon from './textAlignPicker/leftAlignIcon';
import {getFontClassName} from '@libs/store/settings/fonts';

const meta: Meta<typeof CustomisationButton> = {
  title: 'Customisation/Button',
  component: CustomisationButton,
  parameters: {
    controls: {expanded: true},
  },
};

export default meta;
type Story = StoryObj<typeof CustomisationButton>;

const getAllButtons = (args: CustomisationButtonProps) => (
  <ul className="app-bg flex gap-4 p-4">
    <li className={getFontClassName(Font.RobotoMono)}>
      <CustomisationButton
        {...args}
        label="Selected"
        selected={true}
        onClick={() => console.log('clicked')}>
        Roboto Mono
      </CustomisationButton>
    </li>
    <li>
      <CustomisationButton {...args} onClick={() => console.log('clicked')}>
        <LeftAlignIcon />
      </CustomisationButton>
    </li>
    <li>
      <CustomisationButton {...args} onClick={() => console.log('clicked')}>
        <JustifiedIcon />
      </CustomisationButton>
    </li>
    <li>
      <CustomisationButton {...args} onClick={() => console.log('clicked')}>
        <span className="font-xl" aria-hidden={true}>
          A
        </span>
      </CustomisationButton>
    </li>
  </ul>
);

export const CustomisationButtonStory: Story = {
  render: args => (
    <>
      <div data-theme="dark" className="pb-4 space-y-4">
        {getAllButtons(args)}
      </div>
      <div data-theme="light" className="pb-4 space-y-4">
        {getAllButtons(args)}
      </div>
      <div data-theme="yellow" className="pb-4 space-y-4">
        {getAllButtons(args)}
      </div>
      <div data-theme="peach" className="pb-4 space-y-4">
        {getAllButtons(args)}
      </div>
      <div data-theme="pale-green" className="pb-4 space-y-4">
        {getAllButtons(args)}
      </div>
      <div data-theme="pale-blue" className="pb-4 space-y-4">
        {getAllButtons(args)}
      </div>
      <div data-theme="pale-pink" className="pb-4 space-y-4">
        {getAllButtons(args)}
      </div>
    </>
  ),
};

CustomisationButtonStory.args = {};
