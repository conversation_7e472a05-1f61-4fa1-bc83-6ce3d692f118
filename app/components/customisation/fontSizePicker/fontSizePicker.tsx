'use client';
import {FontSize} from '@libs/store/settings/types';
import {useFontSizePicker} from './useFontSizePicker';
import {useCustomisation} from '../useCustomisation';
import CustomisationButton from '../customisationButton';

export interface FontSizePickerProps {
  className?: string;
  buttonStyles?: string;
}

const FontSizePicker = (props: FontSizePickerProps) => {
  const {className = '', buttonStyles = ''} = props;
  const {fontSizeForLabel} = useFontSizePicker();

  const fontSizes = Object.values(FontSize);

  const {handleSelect, selectedCategory: selectedFontSize} =
    useCustomisation('FontSize');

  const handleOnClick = (size: FontSize) => {
    handleSelect(size);
  };

  return (
    <ul
      aria-label="Font size"
      className={`ml-0 pl-0 flex flex-row gap-4 ${className}`}>
      {fontSizes.map((size, index) => {
        const labelStyle = fontSizeForLabel(size);
        return (
          <li
            className="flex flex-1 w-full content-center"
            key={index.toString()}>
            <CustomisationButton
              className={`${buttonStyles}`}
              selected={selectedFontSize === size}
              onClick={() => handleOnClick(size)}
              label={size}>
              <span className={labelStyle} aria-hidden={true}>
                A
              </span>
            </CustomisationButton>
          </li>
        );
      })}
    </ul>
  );
};

export default FontSizePicker;
