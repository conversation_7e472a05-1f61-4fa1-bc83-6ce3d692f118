import {FontSize} from '@libs/store/settings/types';

export const useFontSizePicker = () => {
  const fontSizeForLabel = (size: FontSize) => {
    let style = '';
    switch (size) {
      case FontSize.Small:
        style = 'text-base';
        break;
      case FontSize.Medium:
        style = 'text-xl';
        break;
      case FontSize.Large:
        style = 'text-2xl';
        break;
    }
    return style;
  };

  return {
    fontSizeForLabel,
  };
};
