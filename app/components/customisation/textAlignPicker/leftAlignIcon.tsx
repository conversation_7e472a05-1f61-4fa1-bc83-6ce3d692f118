const LeftAlignIcon = () => {
  const containerStyle = 'flex-col gap-1 justify-start items-start inline-flex';
  const lineStyle = 'w-7 h-0.5 opposite-bg rounded-sm';

  return (
    <div className={containerStyle}>
      <div className={lineStyle} />
      <div className={`${lineStyle} !w-5`} />
      <div className={`${lineStyle} !w-3.5`} />
      <div className={`${lineStyle} !w-1.5`} />
    </div>
  );
};

export default LeftAlignIcon;
