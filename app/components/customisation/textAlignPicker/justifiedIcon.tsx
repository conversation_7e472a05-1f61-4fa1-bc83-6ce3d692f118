const JustifiedIcon = () => {
  const containerStyle = 'flex-col gap-1 justify-start items-start inline-flex';
  const lineStyle = 'w-12 h-0.5 opposite-bg rounded-sm';

  return (
    <div className={containerStyle}>
      <div className={lineStyle} />
      <div className={lineStyle} />
      <div className={lineStyle} />
      <div className={lineStyle} />
    </div>
  );
};

export default JustifiedIcon;
