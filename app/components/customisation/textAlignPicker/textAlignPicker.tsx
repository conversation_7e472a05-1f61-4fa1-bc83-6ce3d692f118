'use client';
import {TextAlignment} from '@libs/store/settings/types';
import LeftAlignIcon from './leftAlignIcon';
import JustifiedIcon from './justifiedIcon';
import {useCustomisation} from '../useCustomisation';
import CustomisationButton from '../customisationButton';

export interface TextAlignPickerProps {
  className?: string;
  buttonStyles?: string;
}

const TextAlignPicker = (props: TextAlignPickerProps) => {
  const {className = '', buttonStyles = ''} = props;

  const textAlignOptions = Object.values(TextAlignment);

  const {handleSelect, selectedCategory: selectedAlignment} =
    useCustomisation('Alignment');

  const handleOnClick = (size: TextAlignment) => {
    handleSelect(size);
  };

  return (
    <ul
      aria-label="Text alignment menu"
      className={`ml-0 pl-0 flex flex-row gap-4 ${className}`}>
      {textAlignOptions.map((size, index) => {
        return (
          <li
            className="flex flex-1 w-full content-center"
            key={index.toString()}>
            <CustomisationButton
              className={buttonStyles}
              selected={selectedAlignment === size}
              onClick={() => handleOnClick(size)}
              label={size}>
              {size === TextAlignment.Justified ? (
                <JustifiedIcon />
              ) : (
                <LeftAlignIcon />
              )}
            </CustomisationButton>
          </li>
        );
      })}
    </ul>
  );
};

export default TextAlignPicker;
