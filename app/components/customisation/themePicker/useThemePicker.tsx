import {ThemeColour} from '@libs/store/settings/types';

const themePickerButtonColors = {
  [ThemeColour.Dark]: 'bg-slate-950 text-slate-50',
  [ThemeColour.Light]: 'bg-white text-slate-950',
  [ThemeColour.Yellow]: 'bg-yellow-400 text-black',
  [ThemeColour.PaleGreen]: 'bg-lime-100 text-lime-950',
  [ThemeColour.PaleBlue]: 'bg-blue-100 text-blue-950',
  [ThemeColour.Peach]: 'bg-tailo-peach-400 text-tailo-peach-950',
  [ThemeColour.PalePink]: 'bg-rose-100 text-rose-950',
};

export const currentThemes = [
  ThemeColour.Dark,
  ThemeColour.Light,
  ThemeColour.Yellow,
  ThemeColour.PaleGreen,
  ThemeColour.PaleBlue,
  ThemeColour.Peach,
  ThemeColour.PalePink,
];

export function useThemePicker() {
  const getStylesForThemePicker = (theme: ThemeColour) => {
    return themePickerButtonColors[theme];
  };

  return {getStylesForThemePicker};
}
