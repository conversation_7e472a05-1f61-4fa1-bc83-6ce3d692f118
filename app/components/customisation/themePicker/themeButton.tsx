'use client';
import {FC, useState} from 'react';
import {useThemePicker} from './useThemePicker';
import {ThemeColour} from '@libs/store/settings/types';
import {Bucket, TickCircle} from 'iconsax-react';
import {useCustomisation} from '../useCustomisation';

interface ThemeButtonProps {
  theme: ThemeColour;
  size?: string;
}

const ThemeButton: FC<ThemeButtonProps> = ({theme, size = 'w-12 h-12'}) => {
  const [showHoverIcon, setShowHoverIcon] = useState(false);
  const {getStylesForThemePicker} = useThemePicker();
  const {handleSelect, selectedCategory: selectedTheme} =
    useCustomisation('Theme');

  const handleOnClick = () => handleSelect(theme);

  const colors = getStylesForThemePicker(theme);
  const baseStyles = `outline-none ring-2 ring-transparent focus:theme-selected ${size} rounded-full border-2 border-transparent justify-center items-center text-3xl font-semibold leading-tightcus:ring-4 ${size} rounded-full border-2 border-transparent justify-center items-center text-3xl font-semibold leading-tight`;
  const isSelected = selectedTheme === theme;
  const selectedStyles = isSelected ? `selected` : '';

  const label = isSelected
    ? `"${theme}" theme selected`
    : `Select "${theme}" theme`;

  return (
    <button
      onMouseEnter={() => setShowHoverIcon(true)}
      onMouseLeave={() => setShowHoverIcon(false)}
      onClick={handleOnClick}
      aria-current={selectedTheme === theme}
      className={`${colors} ${baseStyles} ${selectedStyles}`}
      aria-label={label}
      title={label}>
      {selectedTheme === theme ? (
        <TickCircle className="w-full h-full p-2.5" variant="Bold" />
      ) : showHoverIcon ? (
        <Bucket className="w-full h-full p-3" variant="Bold" />
      ) : (
        <span className="w-full h-full" aria-hidden={true}>
          A
        </span>
      )}
    </button>
  );
};

export default ThemeButton;
