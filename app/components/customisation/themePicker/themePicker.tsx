import {ThemeColour} from '@libs/store/settings/types';
import React from 'react';
import ThemeButton from './themeButton';
import {currentThemes} from './useThemePicker';

export interface ThemePickerProps {
  className?: string;
  gap?: string;
  buttonSize?: string;
}

const ThemePicker = (props: ThemePickerProps) => {
  //The ThemeColour determines the order of the buttons
  const themeKeys = Object.values(ThemeColour);

  const visibleThemes = themeKeys.filter(element =>
    currentThemes.includes(element),
  );
  const {className = '', gap = 'gap-x-5 gap-y-3', buttonSize} = props;
  return (
    <div className={`flex flex-wrap ${gap} ${className}`}>
      {visibleThemes.map(theme => {
        return <ThemeButton key={theme} theme={theme} size={buttonSize} />;
      })}
    </div>
  );
};

export default ThemePicker;
