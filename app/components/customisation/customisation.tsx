'use client';

import {FC} from 'react';
import ThemePicker from './themePicker/themePicker';
import FontPicker from './fontPicker/fontPicker';
import FontSizePicker from './fontSizePicker/fontSizePicker';
import LineSpacePicker from './lineSpacePicker/lineSpacePicker';
import TextAlignPicker from './textAlignPicker/textAlignPicker';

interface CustomisationProps {}

const Customisation: FC<CustomisationProps> = () => {
  const headerStyle = 'text-base font-semibold leading-tight pb-2 pt-6';
  const pickerStyle = 'text-xs !gap-3';
  const buttonStyle = 'mb-1';

  return (
    <div>
      <h3 className={headerStyle}>Theme colour</h3>
      <ThemePicker />
      <h3 className={`${headerStyle}`}>Font</h3>
      <FontPicker />
      <h3 className={headerStyle}>Font size</h3>
      <FontSizePicker className={pickerStyle} buttonStyles={buttonStyle} />
      <h3 className={headerStyle}>Line spacing</h3>
      <LineSpacePicker className={pickerStyle} buttonStyles={buttonStyle} />
      <h3 className={headerStyle}>Text alignment</h3>
      <TextAlignPicker className={pickerStyle} buttonStyles={buttonStyle} />
    </div>
  );
};

export default Customisation;
