'use client';
import {LineSpacing} from '@libs/store/settings/types';
import LineSpaceIcon from './lineSpaceIcon';
import {useCustomisation} from '../useCustomisation';
import CustomisationButton from '../customisationButton';

export interface LineSpacePickerProps {
  className?: string;
  buttonStyles?: string;
}

const LineSpacePicker = (props: LineSpacePickerProps) => {
  const {className = '', buttonStyles = ''} = props;
  const lineSpaces = Object.values(LineSpacing);

  const {handleSelect, selectedCategory: selectedLineSpacing} =
    useCustomisation('LineSpacing');

  const handleOnClick = (size: LineSpacing) => {
    handleSelect(size);
  };

  return (
    <ul
      aria-label="Line spacing menu"
      className={`ml-0 pl-0 md:flex md:flex-row gap-4 ${className}`}>
      {lineSpaces.map((size, index) => {
        let label = '';
        
        switch (size) {
          case LineSpacing.Tight:
            label = 'compact';
            break
          case LineSpacing.Optimised:
            label = 'comfortable';
            break;
          case LineSpacing.Relaxed:
            label = 'spacious'
            break;
        }
        
        return (
          <li
            className="flex flex-1 w-full content-center mb-3 md:mb-0"
            key={index.toString()}>
            <CustomisationButton
              className={buttonStyles}
              selected={selectedLineSpacing === size}
              onClick={() => handleOnClick(size)}
              label={label}>
              <LineSpaceIcon size={size} />
            </CustomisationButton>
          </li>
        );
      })}
    </ul>
  );
};

export default LineSpacePicker;
