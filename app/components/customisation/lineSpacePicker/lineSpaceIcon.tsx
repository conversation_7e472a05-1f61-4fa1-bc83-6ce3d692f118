import {LineSpacing} from '@libs/store/settings/types';
import React, {FC} from 'react';

interface LineSpaceIconProps {
  size: LineSpacing;
}

const LineSpaceIcon: FC<LineSpaceIconProps> = ({size}) => {
  const getSizeStyle = () => {
    let style = '';

    switch (size) {
      case LineSpacing.Tight:
        style = 'gap-0.5';
        break;
      case LineSpacing.Optimised:
        style = 'gap-1';
        break;
      case LineSpacing.Relaxed:
        style = 'gap-1.5';
        break;
    }
    return style;
  };

  const gapStyle = getSizeStyle();
  const lineStyle = 'w-10 h-0.5 opposite-bg rounded-sm';

  return (
    <div
      className={`flex-col justify-start items-start inline-flex ${gapStyle}`}>
      <div className={lineStyle} />
      <div className={lineStyle} />
      <div className={lineStyle} />
      <div className={lineStyle} />
    </div>
  );
};

export default LineSpaceIcon;
