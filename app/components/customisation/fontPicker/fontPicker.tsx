'use client';

import {Font} from '@libs/store/settings/types';
import {getFontClassName} from '@libs/store/settings/fonts';
import DropdownSelect from '@components/forms/dropdown/dropdownSelect';
import {useCustomisation} from '../useCustomisation';
import CustomisationButton from '../customisationButton';

export interface FontPickerProps {
  className?: string;
  dropdown?: boolean;
  buttonStyles?: string;
  inputLabel?: string;
}

const FontPicker = (props: FontPickerProps) => {
  const {className = '', dropdown = true, buttonStyles = ''} = props;
  const fonts = Object.values(Font);

  const {handleSelect, selectedCategory: selectedFont} =
    useCustomisation('Font');

  const handleOnClick = (option: string) => {
    option = option.replace(/\s/g, ''); // removes spaces to match the enum keys
    const selected: Font = Font[option as keyof typeof Font];
    handleSelect(selected);
  };

  return (
    <>
      {dropdown ? (
        <DropdownSelect
          onSelect={handleSelect}
          defaultOption={selectedFont as Font}
          options={fonts}
          inputLabel={props.inputLabel}
          fontPicker
        />
      ) : (
        fonts.map((font, index) => (
          <CustomisationButton
            className={buttonStyles}
            selected={font === selectedFont}
            key={index}
            onClick={() => handleOnClick(font)}>
            <span className={getFontClassName(font)}>{font}</span>
          </CustomisationButton>
        ))
      )}
    </>
  );
};

export default FontPicker;
