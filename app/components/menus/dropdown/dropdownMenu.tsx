import React, {FC, ReactNode} from 'react';
export interface DropdownMenuProps {
  children: ReactNode;
  button: ReactNode; // the button to trigger the dropdown
  listAccessibilityLabel: string; // accessibility label applied to the UL component
}

const DropdownMenu: FC<DropdownMenuProps> = ({
  children,
  button,
  listAccessibilityLabel,
}) => {
  return (
    <div className="dropdown dropdown-end">
      <label>{button}</label>
      <ul
        aria-label={listAccessibilityLabel}
        tabIndex={1}
        className="dropdown-panel z-20">
        {children}
      </ul>
    </div>
  );
};

export default DropdownMenu;
