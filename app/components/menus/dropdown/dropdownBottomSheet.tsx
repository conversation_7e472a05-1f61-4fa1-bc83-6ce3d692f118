'use client';
import {FC, ReactNode, useState} from 'react';
import {DropdownMenuProps} from './dropdownMenu';
import DropDownNavButton from '@components/nav/applicationNavigation/dropdownNavButton';
import {FiMoreVertical} from 'react-icons/fi';
import ItemMenuContent from '@components/dashboard/documentsList/itemMenu/itemMenuContent';
import OutsideClickHandler from './dropdownItems/outsideClickHandler';
import useDocumentsListAction from '@libs/store/documentsListAction';
import TailoIconButton from '@components/buttons/tailoIconButton';

interface DropdownBottomSheetProps
  extends Omit<DropdownMenuProps, 'children' | 'listAccessibilityLabel'> {
  dropdownMenuContent: ReactNode;
  id: string;
}

/**
 * Dropdown menu at mobile breakpoint
 * Bottom sheet menu on mobile
 */
export default function DropdownBottomSheet({
  button,
  dropdownMenuContent,
  id,
}: DropdownBottomSheetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const {updateDocumentsListAction} = useDocumentsListAction();

  const handleOutsideClick = () => {
    setIsOpen(false);
  };
  const openMenu = () => {
    setIsOpen(!isOpen);
    updateDocumentsListAction({itemId: id});
  };

  return (
    <>
      <div className="hidden lg:block" onClick={openMenu}>
        {/* Button for dropdown (not mobile) is in the dropdown menu  */}

        <TailoIconButton
          label="Toggle dropdown menu"
          isActive={isOpen}
          size="small"
          isCardIconButton={true}>
          <FiMoreVertical className="h-6 w-6 relative z-10" />
        </TailoIconButton>
        {isOpen && (
          <OutsideClickHandler onOutsideClick={handleOutsideClick}>
            <div
              className="dropdown-panel z-20">
              <ItemMenuContent id={id} />
            </div>
          </OutsideClickHandler>
        )}
      </div>
      {/* Button on mobile to trigger the bottomSheet; the BottomSheet sits in documentsListModals component to keep only one instance of bottom sheet for all items in the list */}
      <div className="lg:hidden">{button}</div>
    </>
  );
}
