import {ReactNode} from 'react';

export const getDropdownButtonStyles = (
  forBottomDrawer: boolean,
  stylesForBottomDrawer?: string,
  stylesForDropdown?: string,
  sharedStyles?: string,
): string => {
  return `${
    forBottomDrawer
      ? 'btn-sm ' + stylesForBottomDrawer
      : 'btn-xs' + stylesForDropdown
  } ${sharedStyles}`;
};

export interface DropdownItemProps {
  children: ReactNode;
  sharedStyles?: string; // applied to items in dropdown and bottomdrawer
  stylesForBottomDrawer?: string; // to pass specific styles to the item when it is in the bottom drawer
  stylesForDropdown?: string; // to pass specific styles to the item when it is in the dropdown
  forBottomDrawer?: boolean; // if used in bottom drawer, some larger styles will be applied
}
