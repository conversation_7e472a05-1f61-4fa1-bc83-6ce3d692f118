'use client';
import React, {FC} from 'react';
import {DropdownItemProps, getDropdownButtonStyles} from './shared';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
import Link from 'next/link';

interface DropdownItemLinkProps extends DropdownItemProps {
  href: string;
  onClick?: () => void;
}

const DropdownItemLink: FC<DropdownItemLinkProps> = ({
  href,
  onClick,
  stylesForBottomDrawer,
  stylesForDropdown,
  sharedStyles,
  children,
}) => {
  const {isLargeTablet} = useIsMobile();

  const styles = getDropdownButtonStyles(
    isLargeTablet,
    stylesForBottomDrawer,
    stylesForDropdown,
    sharedStyles,
  );

  return (
    <li onClick={onClick} className="flex dropdown-nav-links">
      <Link className={styles} href={href}>
        {children}
      </Link>
    </li>
  );
};

export default DropdownItemLink;
