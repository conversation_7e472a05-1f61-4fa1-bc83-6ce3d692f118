'use client';
import React, {FC} from 'react';
import {DropdownItemProps, getDropdownButtonStyles} from './shared';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';

interface DropdownItemButtonProps extends DropdownItemProps {
  onClick: () => void;
}

const DropdownItemButton: FC<DropdownItemButtonProps> = ({
  onClick,
  stylesForBottomDrawer,
  stylesForDropdown,
  sharedStyles,
  children,
}) => {
  const {isLargeTablet} = useIsMobile();

  const styles = getDropdownButtonStyles(
    isLargeTablet,
    stylesForBottomDrawer,
    stylesForDropdown,
    sharedStyles,
  );

  return (
    <li className="flex">
      <button className={styles} onClick={onClick}>
        {children}
      </button>
    </li>
  );
};

export default DropdownItemButton;
