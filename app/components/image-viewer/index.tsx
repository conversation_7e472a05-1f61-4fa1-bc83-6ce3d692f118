/* eslint-disable react-hooks/exhaustive-deps */

import axios from 'axios';
import Image from 'next/image';
import { useEffect, useMemo, useRef, useState } from 'react';
import updateStorage from './utilities/update-storage';
import createStorage from './utilities/create-storage';
import TailoIconButton from '../buttons/tailoIconButton';
import Panzoom, { PanzoomObject } from '@panzoom/panzoom';
import fetchFromStorage from './utilities/fetch-from-storage';

import {
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  AddSquare,
  ArrowRight,
  MinusSquare,
  CloseCircle,
  LayoutMaximize,
  // ArrowRotateRight,
  Gallery as ImageIcon,
} from 'iconsax-react';

import './styles/base.css';

type ViewerState = {
  rendered: boolean;
  render: boolean;
  panzoom: PanzoomObject | null;
  buffer: Buffer | null;
  hidden: boolean;
  expanded: boolean;
  loading: boolean;
  scale: number;
  viewerHeight: string;
  pan: {
    x: number;
    y: number;
  };
};

type Props = {
  id: string;
  src: string;
  width: number;
  height: number;
};

const PAN_STEP = 10;
const ZOOM_STEP = 0.1;

export default function Component(props: Props) {
  const { id, src, width, height } = props;

  // Create storage if required.
  createStorage();

  // Fetch the key from storage.
  const stored = fetchFromStorage(id);

  const imageRef = useRef<HTMLImageElement>(null);
  const imageViewerRef = useRef<HTMLDivElement>(null);
  const lightboxElementRef = useRef<HTMLImageElement>(null);

  // Define our initial state values.
  const [state, setState] = useState<ViewerState>({
    rendered: stored?.settings.hidden ?? false,
    render: false,
    panzoom: null,
    buffer: null,
    hidden: stored?.settings.hidden ?? false,
    expanded: false,
    loading: false,
    viewerHeight: '65px',
    scale: stored?.settings.scale ?? 1,
    pan: stored?.settings.pan ?? {
      x: 0,
      y: 0,
    },
  });

  /**
   * Move the viewport upwards.
   */
  const panUp = () => {
    setState((prev) => ({
      ...prev,
      pan: {
        x: prev.pan.x,
        y: prev.pan.y + PAN_STEP
      }
    }));
  };

  /**
   * Move the viewport downwards.
   */
  const panDown = () => {
    setState((prev) => ({
      ...prev,
      pan: {
        x: prev.pan.x,
        y: prev.pan.y - PAN_STEP
      }
    }));
  };

  /**
   * Move the viewport to the left.
   */
  const panLeft = () => {
    setState((prev) => ({
      ...prev,
      pan: {
        x: prev.pan.x + PAN_STEP,
        y: prev.pan.y
      }
    }));
  };

  /**
   * Move the viewport to the right.
   */
  const panRight = () => {
    setState((prev) => ({
      ...prev,
      pan: {
        x: prev.pan.x - PAN_STEP,
        y: prev.pan.y
      }
    }));
  };

  /**
   * Zoom in (rescale the image).
   */
  const zoomIn = () => {
    setState((prev) => ({ ...prev, scale: prev.scale + ZOOM_STEP }));
  };

  /**
   * Zoom out (rescale the image).
   */
  const zoomOut = () => {
    setState((prev) => ({ ...prev, scale: prev.scale - ZOOM_STEP }));
  };

  /**
   * Fetch the buffer data of the image source.
   *
   * @returns Buffer
   */
  const fetchBuffer = async () => {
    const response = await axios.get(src, {
      responseType: 'arraybuffer',
      headers: { "Cache-Control": "no-cache" }
    });

    return Buffer.from(response.data, "utf-8");
  };

  /**
   * Call the server-side image manipulation.
   *
   * @param body Configuration options.
   * @returns void
   */
  const call = async (body: any) => {
    if (!src) return;

    const response = await fetch(`/api/image`, {
      method: 'POST',
      body: JSON.stringify({
        ...body,
        src: state.buffer,
      })
    });

    if (!response.ok) {
      return;
    }

    const { data } = await response.json();

    setState((prev) => ({ ...prev, loading: false }));

    setState((prev) => ({
      ...prev,
      buffer: Buffer.from(
        data.data,
        'binary'
      )
    }));
  };

  // If you want to completely manipulate the image (rotation).
  const manipulate = (transform: string, options: any) => {
    setState((prev) => ({ ...prev, loading: true }));

    call({ transform, options });
  };

  // Transform the image's scale and panning functionality.
  const transform = () => {
    if (!lightboxElementRef.current || !state.panzoom) return;

    const lightboxRect = lightboxElementRef.current.getBoundingClientRect();

    const lightboxPoint = {
      clientX: lightboxRect.left + lightboxRect.width / 2,
      clientY: lightboxRect.top + lightboxRect.height / 2
    };

    // Fully transform the lightbox panzoom.
    state.panzoom.zoomToPoint(state.scale, lightboxPoint, {
      animate: true,
      disablePan: true,
      setTransform: () => {
        if (!state.panzoom) return;

        state.panzoom.setStyle(
          'transform',
          `scale(${state.scale}) translate(${state.pan.x}px, ${state.pan.y}px)`
        )
      }
    });
  };

  // On change of zoom and rotation, transform the image.
  useEffect(() => {
    transform();

    // Update storage on change.
    updateStorage(id, {
      hidden: state.hidden,
      scale: state.scale,
      pan: state.pan,
    });
  }, [state.pan, state.scale, state.hidden]);

  // Fetch the initial image buffer data.
  useEffect(() => {
    if (state.buffer) return;

    fetchBuffer()
      .then((data) => {
        setState((prev) => ({
          ...prev,
          buffer: Buffer.from(data),
        }))
      });
  }, []);

  // Initialise and define our lightbox panzoom.
  useEffect(() => {
    if (state.panzoom) return;

    if (!state.buffer || !lightboxElementRef.current) return;
  
    const lightboxPanzoom = Panzoom(lightboxElementRef.current, {
      disablePan: true,
      setTransform: () => {
        lightboxPanzoom.setStyle(
          'transform',
          `scale(${state.scale}) translate(${state.pan.x}px, ${state.pan.y}px)`
        )
      }
    });

    setState((prev) => ({
      ...prev,
      panzoom: lightboxPanzoom,
    }));
  }, [
    state.buffer, 
    state.panzoom, 
    state.expanded, 
    state.render, 
    lightboxElementRef.current,
  ]);

  // Clean up panzoom when not rendering.
  useEffect(() => {
    if (state.render) return;

    setState((prev) => ({
      ...prev,
      panzoom: null,
    }));
  }, [state.render]);

  useEffect(() => {
    if (!imageViewerRef.current) return;

    const observer = new IntersectionObserver(([entry]) => {
      if (!state.rendered) {
        // If it's the first render, wait a little (removes flicker).
        setTimeout(() => {
          setState((prev) => ({ ...prev, render: entry.isIntersecting }));
        }, 300);

        return;
      }

      setState((prev) => ({ ...prev, render: entry.isIntersecting }));
    }, {
      rootMargin: '800px',
      threshold: 0.1,
    });

    observer.observe(imageViewerRef.current);
  }, []);

  useEffect(() => {
    if (state.render && !state.rendered) {
      setState((prev) => ({ ...prev, rendered: true }));
    }
  }, [state.render]);

  useEffect(() => {
    if (state.hidden) return;

    setState((prev) => ({ ...prev, render: true }));
  }, [state.hidden]);

  // Find out what the viewer height should be when un-rendered.
  useEffect(() => {
    if (!state.rendered || !state.render) return;

    if (state.hidden) {
      imageRef.current?.classList.remove('fade-in');
  
      setTimeout(() => {
        setState((prev) => ({ ...prev, viewerHeight: '65px' }));

        return;
      }, 200);
    }
  
    setState((prev) => ({ ...prev, viewerHeight: `calc(${imageRef?.current?.clientHeight}px + 65px)` }));

    if (!state.hidden) {
      setTimeout(() => {
        imageRef.current?.classList.add('fade-in');
      }, 300);
    }
  }, [
    state.hidden,
    state.rendered,
    state.render,
    state.buffer,
    imageRef?.current?.clientHeight,
  ]);

  return (
    <div className={`image-viewer ${state.render ? 'fade-in' : 'fade-out'}`} ref={imageViewerRef} style={{ height: `${state.viewerHeight}` }}>
      {state.render && (
        <>
          <div className={`image-container inline ${state.expanded ? 'expanded' : null}`}>
            <header>
              <div>
                <TailoIconButton
                  label="Toggle image visibility"
                  handler={() => setState((prev) => ({ ...prev, hidden: !state.hidden }))}
                  size="small"
                  isActive={false}>
                  <ImageIcon variant="Bold" />
                </TailoIconButton>
              </div>

              <ul className="action-group flex">
                {state.loading ? (
                  <li>
                    Loading...
                  </li>
                ) : null}
                {/* <li>
              <TailoIconButton
                label="Rotate image"
                handler={() => manipulate('rotate', { angle: 90 })}
                size="small"
                isActive={false}>
                <ArrowRotateRight />
              </TailoIconButton>
            </li> */}

                <li>
                  <TailoIconButton
                    label="Expand image"
                    handler={() => setState((prev) => ({ ...prev, expanded: true }))}
                    size="small"
                    isActive={false}>
                    <LayoutMaximize variant="Bold" />
                  </TailoIconButton>
                </li>

                <li>
                  <TailoIconButton
                    label="Toggle image visibility"
                    handler={() => setState((prev) => ({ ...prev, hidden: !state.hidden }))}
                    size="small"
                    isActive={false}>
                    {state.hidden && (
                      <ArrowDown variant="Bold" />
                    )}

                    {!state.hidden && (
                      <ArrowUp variant="Bold" />
                    )}
                  </TailoIconButton>
                </li>
              </ul>
            </header>

            {!state.hidden && (
              <section style={{ height: imageRef.current?.clientHeight ?? height, maxHeight: '400px' }}>
                {state.buffer ? (
                  <Image
                    alt=""
                    ref={imageRef}
                    src={`data:image/png;base64,${state.buffer.toString('base64')}`}
                    fill={true}
                    objectFit="contain"
                  />
                ) : null}
              </section>
            )}
          </div>

          <div
            onClick={() => setState((prev) => ({ ...prev, expanded: false }))}
            className={`lightbox-backdrop ${state.expanded ? 'expanded' : null}`}
          ></div>

          {state.expanded && (
            <div className="close" tabIndex={0}>
              <TailoIconButton
                label="Close lightbox"
                handler={() => setState((prev) => ({ ...prev, expanded: false }))}
                size="small"
                isActive={false}>
                <CloseCircle />
              </TailoIconButton>
            </div>
          )}

          <div className={`lightbox ${state.expanded ? 'expanded' : null}`}>
            <header>
              <ul className="action-group">
                {/* <li>
              <TailoIconButton
                label="Rotate image"
                handler={() => manipulate('rotate', { angle: 90 })}
                size="small"
                isActive={false}>
                <ArrowRotateRight />
              </TailoIconButton>
            </li> */}

                <li>
                  <TailoIconButton
                    label="Zoom in image"
                    disabled={state.scale >= 1.0}
                    handler={() => zoomIn()}
                    size="small"
                    isActive={false}>
                    <AddSquare variant="Bold" />
                  </TailoIconButton>
                </li>

                <li>
                  <TailoIconButton
                    label="Zoom out image"
                    disabled={state.scale <= 0.2}
                    handler={() => zoomOut()}
                    size="small"
                    isActive={false}>
                    <MinusSquare variant="Bold" />
                  </TailoIconButton>
                </li>

                <li>
                  <TailoIconButton
                    label="Pan up"
                    handler={() => panUp()}
                    size="small"
                    isActive={false}>
                    <ArrowUp variant="Bold" />
                  </TailoIconButton>
                </li>

                <li>
                  <TailoIconButton
                    label="Pan down"
                    handler={() => panDown()}
                    size="small"
                    isActive={false}>
                    <ArrowDown variant="Bold" />
                  </TailoIconButton>
                </li>

                <li>
                  <TailoIconButton
                    label="Pan left"
                    handler={() => panLeft()}
                    size="small"
                    isActive={false}>
                    <ArrowLeft variant="Bold" />
                  </TailoIconButton>
                </li>

                <li>
                  <TailoIconButton
                    label="Pan right"
                    handler={() => panRight()}
                    size="small"
                    isActive={false}>
                    <ArrowRight variant="Bold" />
                  </TailoIconButton>
                </li>
              </ul>
            </header>

            <section>
              {state.buffer ? (
                <Image
                  alt=""
                  ref={lightboxElementRef}
                  src={`data:image/png;base64,${state.buffer.toString('base64')}`}
                  width={width}
                  height={height}
                />
              ) : null}
            </section>
          </div>
        </>
      )}
    </div>
  );
};
