@import 'themes/dark';
@import 'themes/light';
@import 'themes/pale-blue';
@import 'themes/pale-green';
@import 'themes/pale-pink';
@import 'themes/peach';
@import 'themes/yellow';

.image-viewer {
  position: relative;
  margin: 1rem 0 2rem;
  transition: all linear 0.1s;
}

.image-viewer.fade-in {
  opacity: 1;
}

.image-viewer.fade-out {
  opacity: 0;
}

.image-viewer .inline {
  border: 1px solid var(--image-viewer-border-color);
  border-radius: 0.5rem;
  display: block;
  position: relative;
  margin: 0;
}

.image-viewer .inline header {
  display: grid;
  grid-template-columns: auto auto;
  border-bottom: 1px solid var(--image-viewer-border-color);
  padding: 0.75rem 1rem;
}

.image-viewer .inline header ul {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  margin: 0;
  padding: 0;
}

.image-viewer .inline header ul li {
  list-style-type: none;
}

.image-viewer .inline header {
  color: var(--image-viewer-button-color);
}

.image-viewer .inline section {
  border-radius: 0 0 0.375rem 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image-viewer .inline section img {
  border-radius: 0 0 0.375rem 0.375rem;
  cursor: auto !important;
  transition: all linear 0.1s;
  opacity: 0;
}

.image-viewer .inline section img.fade-in {
  opacity: 1;
}

.image-viewer .lightbox {
  background: var(--image-viewer-lightbox-background-color);
  border-radius: 0.375rem;
  display: none;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  height: 80vh;
  z-index: 9992;
}

.image-viewer .close {
  color: #ffffff;
  position: fixed;
  top: 1rem;
  right: 1.5rem;
  z-index: 9993;
}

@media screen and (max-width: 1200px) {
  .image-viewer .lightbox .close {
    top: -45px;
    right: 0;
  }  
}

.image-viewer .lightbox header {
  background: var(--image-viewer-lightbox-header-background-color);
  border: 2px solid var(--image-viewer-lightbox-header-border-color);
  border-radius: 0.5rem;
  box-shadow: 0px 2px 4px -2px #000000;
  color: var(--image-viewer-lightbox-header-button-color);
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  z-index: 2;
}

.image-viewer .lightbox .action-group {
  margin: 0;
  padding: 0;
  text-align: center;
}

.image-viewer .lightbox .action-group li {
  list-style-type: none;
}

.image-viewer .lightbox .action-group li button {
  color: var(--image-viewer-lightbox-action-group-button-color);
}

.image-viewer .lightbox .action-group li button:hover svg {
  background-color: var(--image-viewer-lightbox-header-button-hover-color);
}

.image-viewer .lightbox section {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  z-index: 1;
}

.image-viewer .lightbox-backdrop {
  background: rgba(0, 0, 0, 0.8);
  display: none;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9991;
}

.image-viewer .lightbox img,
.image-viewer .lightbox section
 {
  border-radius: 0.375rem;
}

.image-viewer .lightbox.expanded,
.image-viewer .lightbox-backdrop.expanded {
  display: block;
}
