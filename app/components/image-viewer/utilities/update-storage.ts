import { Settings, Storage } from '../types/storage';

const key = 'image-viewer';

export default function UpdateStorage(id: string, settings: Settings) {
  const jsonFromStorage = localStorage.getItem(key);

  if (!jsonFromStorage) return null;

  const store = JSON.parse(jsonFromStorage) as Storage;

  const exists = store.images.find((image) => image.id === id);

  if (!exists) {
    // Store the new image settings in storage.
    localStorage.setItem(key, JSON.stringify({
      images: [
        ...store.images,
        {
          id,
          settings,
        }
      ],
    }));

    return;
  }

  // Find and update the image in the array.
  const images = store.images.map((image) => {
    if (image.id === id) {
      return {
        id: image.id,
        settings,
      };
    }

    return image;
  });

  // Store the new image settings in storage.
  localStorage.setItem(key, JSON.stringify({
    images,
  }));
};
