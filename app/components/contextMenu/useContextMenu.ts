import {useWordDefinitionContext} from '@contexts/wordDefinitionContext';
import {useState, useEffect, useRef, useCallback} from 'react';

interface ContextMenuProps {
  multipleWordsAllowed?: boolean;
  context?: React.RefObject<HTMLElement>;
}

export const useContextMenu = (props: ContextMenuProps) => {
  const {multipleWordsAllowed, context} = props;
  const {setCurrentlySearchedWord} = useWordDefinitionContext();
  const [selection, setSelection] = useState('');
  // Set coords to a negative value, to remove them from the viewport completely, otherwise it will show underneath the navbar
  const [coords, setCoords] = useState({left: 0, top: -200});
  const [multipleHighlightedWords, setMultipleHighlightedWords] =
    useState(false);
  const [rect, setRect] = useState<DOMRect | null>(null);
  const [scroll, setScroll] = useState(!selection);
  const containerRef = useRef(null);
  const showBeneathWord = rect && rect.bottom < window.innerHeight / 4;

  const handleSelection = useCallback(
    (text: string) => {
      setScroll(false);
      setSelection(text);
      setCurrentlySearchedWord(text);
    },
    [setCurrentlySearchedWord],
  );

  const handleMouseUp = useCallback(() => {
    const text = window.getSelection()?.toString();
    if (text) {
      text.split(' ').length > 1
        ? setMultipleHighlightedWords(true)
        : setMultipleHighlightedWords(false);

      if (!multipleWordsAllowed && text.split(' ').length > 1) return;
      handleSelection(text);
      setRect(
        window.getSelection()?.getRangeAt(0).getBoundingClientRect() || null,
      );
    } else {
      setCoords({left: 0, top: -200});
    }
  }, [handleSelection, multipleWordsAllowed]);

  const setContainerPosition = useCallback(() => {
    if (selection && rect && containerRef.current) {
      const containerWidth = containerRef.current
        ? (containerRef.current as HTMLElement).offsetWidth
        : 0;
      const containerHeight = containerRef.current
        ? (containerRef.current as HTMLElement).offsetHeight
        : 0;

      const leftPos = rect.left + rect.width / 2 - containerWidth / 2;
      const topPos = showBeneathWord
        ? Math.max(0, rect.bottom + 10)
        : Math.max(0, rect.top - (containerHeight + 10));

      const contextRect = context?.current?.getBoundingClientRect();

      if (contextRect) {
        if (
          leftPos < contextRect.left ||
          topPos < contextRect.top ||
          rect.right > contextRect.right
        ) {
          setCoords({left: 0, top: -200});
          return;
        }
      }
      setCoords({
        left: leftPos,
        top: topPos,
      });
    }
  }, [selection, rect, showBeneathWord]);

  const handleScroll = useCallback(() => {
    setRect(null);
    setScroll(true);
  }, []);

  useEffect(() => {
    window.addEventListener('mouseup', handleMouseUp);
    selection && window.addEventListener('scroll', handleScroll, true); // true to use capture instead of using bubbling/propagation for event
    return () => {
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [handleMouseUp, handleScroll, selection]);

  useEffect(() => {
    setContainerPosition();
    const style = document.createElement('style');
    style.innerHTML = `
      ::selection {
        background: ${selection}; 
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [selection, setContainerPosition]);

  return {
    selection,
    handleSelection,
    multipleHighlightedWords,
    coords,
    containerRef,
    scroll,
  };
};
