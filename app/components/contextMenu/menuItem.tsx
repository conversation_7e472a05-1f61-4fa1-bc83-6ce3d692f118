import React from 'react';
import {Book1, MessageQuestion} from 'iconsax-react';
import {DRAWER_ID, DrawerTab} from '@libs/store/drawer/types';
import DrawerTrigger from '@components/modals/drawer/drawerTrigger';
import useDocumentToolsDrawer from '@components/nav/documentOverview/documentToolsNavigation/documentTools/useDocumentToolsDrawer';
import useDrawer from '@libs/store/drawer';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';
import {useWordDefinitionContext} from '@contexts/wordDefinitionContext';
import {useExplainThisContext} from '@contexts/explainThisContext';
import {useExplainThisAnalytics} from '@libs/services/explain/useExplainThisAnalytics';

interface MenuItemProps {
  children?: React.ReactNode;
  itemType?: string; //TODO add enum with more types
}

export default function MenuItem(props: MenuItemProps) {
  const {children, itemType} = props;
  const {updateDocToolsDrawerContent, isExplainThisDrawerActive} = useDrawer();
  const {handleDocumentToolsDrawer} = useDocumentToolsDrawer();
  const {
    currentlySearchedWord,
    fetchSelectedWordDefinition,
    setCurrentlySearchedWord,
  } = useWordDefinitionContext();
  const {trackDefineWordInText} = useDocumentAnalytics();
  const {dispatch} = useExplainThisContext();
  const {trackOpenExplainThis} = useExplainThisAnalytics();

  const setDrawerContent = (drawerTab: DrawerTab) => {
    if (drawerTab === DrawerTab.WORD_DEFINITION && currentlySearchedWord) {
      setCurrentlySearchedWord(currentlySearchedWord); // Ensure word is updated before fetch
      // fetchSelectedWordDefinition();
      trackDefineWordInText(currentlySearchedWord);
    }
    if (drawerTab === DrawerTab.EXPLAIN_THIS) {
      dispatch({type: 'setText', text: currentlySearchedWord});
    }
    updateDocToolsDrawerContent(drawerTab);
  };

  const handleTabs = (drawerTab: DrawerTab) => {
    //TODO: Handle all tab functionality within this function
    handleDocumentToolsDrawer();
    setDrawerContent(drawerTab);
  };

  const handleExplainThisDrawer = () => {
    if (isExplainThisDrawerActive) {
      dispatch({type: 'setText', text: currentlySearchedWord});
    } else {
      handleTabs(DrawerTab.EXPLAIN_THIS);
      trackOpenExplainThis('flyout', currentlySearchedWord.length);
    }
  };

  if (children) return <li>{children}</li>;
  else {
    switch (itemType) {
      case 'explain-this':
        return (
          <li>
            <DrawerTrigger
              onClick={handleExplainThisDrawer}
              drawerId={DRAWER_ID.DOCUMENT_TOOLS}
              ariaLabel="Look up the word definition in the document tool menu"
              className="btn btn-sm btn-tertiary">
              <MessageQuestion variant="Bold" className="h-4 w-4" />
              <p className="font-semibold">Explain</p>
            </DrawerTrigger>
          </li>
        );
      case 'word-definition':
      default:
        return (
          <li>
            <DrawerTrigger
              onClick={() => handleTabs(DrawerTab.WORD_DEFINITION)}
              drawerId={DRAWER_ID.DOCUMENT_TOOLS}
              ariaLabel="Look up the word definition in the document tool menu"
              className="btn btn-sm btn-tertiary">
              <Book1 variant="Bold" className="h-4 w-4" />
              <p className="font-semibold">Define</p>
            </DrawerTrigger>
          </li>
        );
    }
  }
}
