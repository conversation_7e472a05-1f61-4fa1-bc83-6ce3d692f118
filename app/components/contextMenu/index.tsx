import React from 'react';
import {useContextMenu} from './useContextMenu';

interface ContextMenuProps {
  children: React.ReactNode;
  contextRef?: React.RefObject<HTMLElement>;
}

export default function ContextMenu(props: ContextMenuProps) {
  const {children, contextRef} = props;
  const {scroll, selection, coords, containerRef, multipleHighlightedWords} =
    useContextMenu({context: contextRef, multipleWordsAllowed: true});

  if (selection)
    return (
      <div
        ref={containerRef}
        className={`${
          scroll ? 'opacity-0' : 'opacity-100'
        } fixed context-menu overflow-clip shadow-xl transition-opacity duration-200`}
        style={{left: coords.left, top: coords.top}}>
        {!multipleHighlightedWords ? (
          <ul className="context-menu-options">{children}</ul>
        ) : (
          <ul className="context-menu-options">
            {React.Children.map(children, child => {
              if (
                React.isValidElement(child) &&
                child.props.itemType !== 'word-definition'
              ) {
                return child;
              }
            })}
          </ul>
        )}
      </div>
    );
}
