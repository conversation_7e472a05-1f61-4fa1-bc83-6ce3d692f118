'use client';

import {Can, PermissionsContext} from './providers/permissions/provider';
import {useAbility} from '@casl/react';

const TestPermission = () => {
  const ability = useAbility(PermissionsContext);

  return (
    <div>
      Testing Permissions
      {ability.can('read', 'dashboard') && (
        <p className="text-blue-500">I CAN with context!!</p>
      )}
      <Can I="read" a="dashboard">
        <p className="text-blue-500">I CAN with Can!</p>
      </Can>
    </div>
  );
};

export default TestPermission;
