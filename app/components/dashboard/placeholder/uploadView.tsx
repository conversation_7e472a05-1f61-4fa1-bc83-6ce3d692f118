import FileSelected from '@components/modals/uploadFile/fileSelected/fileSelected';
import FileUploaded from '@components/modals/uploadFile/fileUploaded/fileUploaded';
import SelectDocument from '@components/modals/uploadFile/selectDocument/selectDocument';
import { DocUploadView } from '@components/modals/uploadFile/types';
import useDocumentUpload from '@components/modals/uploadFile/useDocumentUpload';
import React from 'react'

const UploaderView = ({placeholder}: {placeholder: boolean}) => {
  const {activeView} = useDocumentUpload();

  switch (activeView) {
    case DocUploadView.SELECT_DOC:
    default:
      return <SelectDocument placeholder={placeholder} />;
    case DocUploadView.FILE_SELECTED:
      return <FileSelected placeholder={placeholder} />;
    case DocUploadView.FILE_UPLOAD:
      return <FileUploaded />;
  }
}

export default UploaderView