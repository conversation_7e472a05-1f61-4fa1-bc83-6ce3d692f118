'use client';

import React from 'react';
import DeleteModal from '@components/modals/deleteDocument';
import useDocumentsListAction from '@libs/store/documentsListAction';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import BottomSheet from '@components/modals/bottomSheet/bottomSheet';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
import ItemMenuContent from '../itemMenu/itemMenuContent';

export default function DocumentsListModals() {
  const {isLargeTablet} = useIsMobile();
  const {closeDrawer, drawStateById} = useDrawer();
  const {itemId, actionType, clearAction} = useDocumentsListAction();

  const closeMenu = () => {
    const isOpen = drawStateById(DRAWER_ID.DOCUMENT_MENU) === 'open';
    if (isOpen) closeDrawer(DRAWER_ID.DOCUMENT_MENU);
  };

  const closeModals = () => {
    clearAction(); //closes the modal
    closeMenu(); // closes the bottom drawer
  };

  // If user resizes screen while bottom sheet is open, we want to close it so that scrolling is enabled
  React.useEffect(() => {
    closeModals();
  }, [isLargeTablet]);

  return (
    <>
      <DeleteModal
        id={itemId}
        isOpen={actionType === 'delete'}
        closeModal={closeModals}
      />
      <div className="lg:hidden">
        <BottomSheet onClose={closeModals} drawerId={DRAWER_ID.DOCUMENT_MENU}>
          <ul aria-label="Document menu">
            <ItemMenuContent id={itemId} />
          </ul>
        </BottomSheet>
      </div>
    </>
  );
}
