'use client';
import React from 'react';
import ItemMenuContent from './itemMenuContent';
import DropdownBottomSheet from '@components/menus/dropdown/dropdownBottomSheet';
import ItemMenuButton from './itemMenuButton';
import {DRAWER_ID} from '@libs/store/drawer/types';
import useDrawer from '@libs/store/drawer';
import useDocumentsListAction from '@libs/store/documentsListAction';

export default function ItemMenu({id}: {id: string}) {
  const {openDrawer} = useDrawer();
  const {updateDocumentsListAction} = useDocumentsListAction();

  const openMenu = () => {
    openDrawer(DRAWER_ID.DOCUMENT_MENU);
    updateDocumentsListAction({itemId: id});
  };

  return (
    <DropdownBottomSheet
      id={id}
      button={<ItemMenuButton handleMenuOpen={openMenu} />}
      dropdownMenuContent={<ItemMenuContent id={id} />}
    />
  );
}
