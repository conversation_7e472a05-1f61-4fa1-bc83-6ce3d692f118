'use client';

import {Trash} from 'iconsax-react';
import React, {FC} from 'react';
import DropdownItemLink from '@components/menus/dropdown/dropdownItems/dropdownItemLink';
import {AppPrivateRoutes} from '@libs/services/auth/routes';
import useDocumentsListAction from '@libs/store/documentsListAction';

interface ItemMenuContentProps {
  id: string;
}

const ItemMenuContent: FC<ItemMenuContentProps> = ({
  id,
}: ItemMenuContentProps) => {
  const {updateDocumentsListAction} = useDocumentsListAction();

  const handleDeleteClick = () => {
    updateDocumentsListAction({actionType: 'delete'});
  };
  return (
    <>
      <DropdownItemLink
        sharedStyles="btn btn-sm btn-tertiary w-full"
        onClick={handleDeleteClick}
        href={AppPrivateRoutes.Dashboard}
        stylesForBottomDrawer="!text-red-700">
        <Trash variant="Bold" className="theme-delete" />
        <span className="theme-delete">Delete</span>
      </DropdownItemLink>
    </>
  );
};

export default ItemMenuContent;
