import React, {FC} from 'react';
import {FiMoreVertical} from 'react-icons/fi';
import TailoIconButton from '@components/buttons/tailoIconButton';

interface ItemMenuButtonProps {
  handleMenuOpen: () => void;
  disabled?: boolean;
}

const ItemMenuButton: FC<ItemMenuButtonProps> = ({
  handleMenuOpen,
  disabled,
}) => {
  return (
    <TailoIconButton
      handler={handleMenuOpen}
      label="Open menu"
      isActive={false}
      disabled={disabled}
      size="small"
      className="relative z-10"
      isCardIconButton={true}>
      <FiMoreVertical className="h-6 w-6 relative z-10" />
    </TailoIconButton>
  );
};

export default ItemMenuButton;
