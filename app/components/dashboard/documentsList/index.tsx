'use client';
import React from 'react';
import ListItem from './listItem';
import useDocuments from '@libs/services/document/useDocuments';
import ItemPlaceholder from './itemPlaceholder';
import Error from '@components/errorBoundary/error';
import {preload} from 'swr';
import {fetcher} from '@libs/services/swrParams';
import {IStatus} from '@libs/store/status/types';
import {getSummaryKeyPerDocument} from '@libs/services/summary/useSummaries';
import DocumentsListModals from './documentsListModals';

export default function DocumentsList() {
  const {documents, isLoading, error} = useDocuments();

  if (isLoading) {
    return <ItemPlaceholder />;
  }

  if (error) return <Error error={error} />;

  return (
    <div className="w-full">
      <ul className="documents-list space-y-2 w-full" data-clarity-mask="True">
        {documents.map(document => {
          // Turned off preloading for now as the pre-signed URL generated for that image expires within an hour
          // if (document.status === IStatus.Ready && document.status === IStatus.Complete) {
          //   preload(`/documents/${document.id}`, fetcher);
          //   const summaryKey = getSummaryKeyPerDocument(document.id);
          //   preload(summaryKey, fetcher);
          // }
          return <ListItem key={document.id} item={document} />;
        })}
      </ul>
      <DocumentsListModals />
    </div>
  );
}
