import React from 'react';
import DocumentIcon from './documentIcon';
import DateWithAt from '../../../dates/dateWithAt';
import CardWithLink from '@components/card/cardWithLink';
import {IDocument} from '@libs/store/document/types';
import {IStatus} from '@libs/store/status/types';
import BasePill from '@components/pills/basePill';
import {AppDynamicRoutes} from '@libs/services/auth/routes';
import {DocOverviewNavTabs} from '@components/nav/documentOverview/documentNavigation/documentNavigationContent';
import MenuWrapper from './menuWrapper';

export default function ListItemDesktop({
  item: {id, name, status, lastModified, errorDetails},
}: {
  item: IDocument;
}) {
  const disabled =
    status.toLowerCase() !== IStatus.Complete && status !== IStatus.Ready;
  return (
    <MenuWrapper id={id}>
      <CardWithLink
        disabled={disabled}
        // Ensures that the first tab content is displayed when the user navigates to the document page
        href={`${AppDynamicRoutes.document(id)}?tab=${
          DocOverviewNavTabs.Summary
        }`}
        linkText={name}
        className="relative">
        <div className="flex-none order-1 mr-3">
          <DocumentIcon />
        </div>
        <div className="flex-none order-3 mx-8 max-w-fit">
          <BasePill variant={status} errorMessage={errorDetails?.message} />
        </div>
        <div className="flex-none order-4 w-40">
          <DateWithAt date={lastModified} />
        </div>
      </CardWithLink>
    </MenuWrapper>
  );
}
//
