import React from 'react';
import ItemMenu from '../itemMenu/itemMenu';

/** Ensures that no sudo state's style is applied to the link item when interacting with the menu */
export default function MenuWrapper({
  children,
  id,
  isMobile = false,
}: {
  children: React.ReactNode;
  id: string;
  isMobile?: boolean;
}) {
  return (
    <div className={`relative ${
      isMobile ? 'lg:hidden' : 'hidden lg:block'
    }`}>
      
      {children}
      <div
        className={`absolute right-4 lg:right-6 top-0 h-full py-4 flex ${
          isMobile ? 'items-top' : 'items-center'
        }`}>
        <ItemMenu id={id} />
      </div>

    </div>
  );
}
