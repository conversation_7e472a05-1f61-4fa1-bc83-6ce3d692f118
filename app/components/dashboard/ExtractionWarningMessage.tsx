import Link from 'next/link';
import useDrawer from '../../libs/store/drawer';
import {DRAWER_ID} from '../../libs/store/drawer/types';

export default function ExtractionWarningMessage() {
  const {openDrawer} = useDrawer();

  const onClick = (event: React.MouseEvent<HTMLElement>) => {
    event.preventDefault();

    openDrawer(DRAWER_ID.FEEDBACK);
  };

  return (
    <>
      <div className="px-6 py-8 flex flex-col gap-4">
        <p>
          <PERSON><PERSON> uses brand-new AI extraction technology to transform your
          documents. With all cutting-edge technology, there can be mistakes
          from time to time, so if you do experience any issues or see something
          that doesn&apos;t seem right, then we&apos;d love to hear from you.
        </p>
        <p>
          You can share your feedback with us using the{' '}
          <a
            onClick={onClick}
            href="mailto:<EMAIL>"
            className="doc-link-default">
            Feedback form.
          </a>
        </p>
        <p>
          {' '}
          If you&apos;d like to learn more about our extraction technology,
          check out our{' '}
          <a
            href="https://tailoapp.com/2024/09/03/embracing-the-future-of-reading-with-tailo/"
            target="_blank"
            className="doc-link-default">
            blog post.
          </a>
        </p>
      </div>
    </>
  );
}
