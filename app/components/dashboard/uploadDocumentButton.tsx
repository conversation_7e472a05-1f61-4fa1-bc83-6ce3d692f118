'use client';
import But<PERSON> from '@components/buttons/button';
import Fab from '@components/buttons/fab';
import UploadModal from '@components/modals/uploadFile';
import useDocumentUpload from '@components/modals/uploadFile/useDocumentUpload';
import {DocumentUpload} from 'iconsax-react';
import React from 'react';

export default function UploadDocumentButton() {
  const label = 'Upload new document';
  const {openModal} = useDocumentUpload();

  return (
    <>
      <Button onClick={openModal} variant="primary" size="sm" className="hidden lg:flex">
        <DocumentUpload variant="Bold" /> {label}
      </Button>
      <Fab
        size="large"
        onClick={openModal}
        fab={true}
        accessibilityLabel={label}
        className="lg:hidden">
        <DocumentUpload variant="Bold" />
      </Fab>
      <UploadModal />
    </>
  );
}
