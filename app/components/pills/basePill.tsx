import React from 'react';
import ReadyPill from '@components/pills/readyPill';
import ErrorPill from '@components/pills/errorPill';
import ProcessingPill from '@components/pills/processingPill';
import UploadingPill from '@components/pills/uploadingPill';
import {IStatus} from '@libs/store/status/types';
import {IWordType} from '@libs/store/wordDefinition/types';
import WordTypePill from './wordTypePill';

export default function BasePill({
  variant,
  errorMessage,
}: {
  variant: IStatus | IWordType;
  errorMessage?: string;
}) {
  const wordTypes = Object.values(IWordType);
  if (wordTypes.includes(variant as IWordType)) {
    return <WordTypePill type={variant as IWordType} />;
  }

  switch (variant.toLowerCase()) {
    case IStatus.Ready:
    case IStatus.Complete:
      return <ReadyPill />;
    case IStatus.Error:
      return <ErrorPill errorMessage={errorMessage} />;
    case IStatus.Processing:
    case IStatus.Extracting:
    case IStatus.Queued:
      return <ProcessingPill />;
    case IStatus.Uploading:
      return <UploadingPill />;
    default:
      return null;
  }
}
