import {Meta, StoryObj} from '@storybook/react';
import BasePill from './basePill';
import {IStatus} from '@libs/store/status/types';
import {IWordType} from '@libs/store/wordDefinition/types';

const meta: Meta = {
  title: 'Pills/BasePill',
  component: BasePill,
  parameters: {
    controls: {expanded: true},
  },
};
export default meta;

type Story = StoryObj<typeof BasePill>;

export const BasePillStory: Story = {
  render: args => {
    return (
      <>
        <div data-theme="dark">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <BasePill {...args} />
          </div>
        </div>
        <div data-theme="light">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <BasePill {...args} />
          </div>
        </div>
        <div data-theme="yellow">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <BasePill {...args} />
          </div>
        </div>
        <div data-theme="pale-blue">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <BasePill {...args} />
          </div>
        </div>
        <div data-theme="pale-green">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <BasePill {...args} />
          </div>
        </div>
        <div data-theme="pale-pink">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <BasePill {...args} />
          </div>
        </div>
        <div data-theme="peach">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <BasePill {...args} />
          </div>
        </div>
      </>
    );
  },
};

BasePillStory.args = {
  variant:
    IStatus.Ready ||
    IStatus.Complete ||
    IStatus.Error ||
    IStatus.Processing ||
    IStatus.Extracting ||
    IStatus.Queued ||
    IStatus.Uploading ||
    IWordType.Noun ||
    IWordType.Verb ||
    IWordType.Adjective ||
    IWordType.Adverb ||
    IWordType.Pronoun ||
    IWordType.Preposition ||
    IWordType.Conjunction ||
    IWordType.Interjection,
  errorMessage: 'Error message',
};

BasePillStory.argTypes = {
  variant: {
    options: [
      IStatus.Ready || IStatus.Complete,
      IStatus.Error,
      IStatus.Processing || IStatus.Extracting,
      IStatus.Queued,
      IStatus.Uploading ||
        IWordType.Noun ||
        IWordType.Verb ||
        IWordType.Adjective ||
        IWordType.Adverb ||
        IWordType.Pronoun ||
        IWordType.Preposition ||
        IWordType.Conjunction ||
        IWordType.Interjection,
    ],
  },
};
