import React from 'react';
import {Danger} from 'iconsax-react';

export default function ErrorPill({
  errorMessage,
  label = 'Error',
}: {
  errorMessage?: string;
  label?: string;
}) {
  return (
    <div
      className="pill pill-negative z-10 relative"
      aria-live="polite"
      title={errorMessage}>
      <Danger variant="Bold" />
      <p>{label}</p>
      <p className="sr-only">{errorMessage}</p>
    </div>
  );
}
