'use client';
import useUserDataFromSession from '@libs/utils/user/useUserDataFromSession';
import React from 'react';
import {getIconStyle} from './helper';

interface Initials {
  size: 'small' | 'large';
}

export default function Initials({size}: Initials) {
  const {userInitials} = useUserDataFromSession();
  const iconStyle = getIconStyle(size);
  return (
    <div
      className={`nav-item rounded-full ${iconStyle}  justify-center items-center flex  `}>
      {userInitials}
    </div>
  );
}
