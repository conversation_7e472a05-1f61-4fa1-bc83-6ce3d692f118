'use client';
import SummaryTab from './summaryTab';
import PageViewTab from './pageViewTab';
import React, {useEffect, useRef} from 'react';
import SectionViewTab from './sectionViewTab';
import PageLoader from '@components/loaders/pageLoader';
import {useParams, useSearchParams} from 'next/navigation';
import {DocOverviewNavTabs} from '@components/nav/documentOverview/documentNavigation/documentNavigationContent';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';

export default function TabContentWrapper() {
  const searchParams = useSearchParams();
  const {trackDocumentTabUsage, trackOpenDocument} = useDocumentAnalytics();
  const tabId = searchParams.get('tab');
  const params = useParams();
  const {document: documentData, isLoading, id} = useProcessedDocument();
  const hasTracked = useRef(false);

  /**
   * Ensures that the page scrolls to the correct section when the
   * hash is present on navigation between tabs using the links in
   * the toc.
   */
  const scrollIntoView = () => {
    const hash = window.location.hash;

    if (!hash) return;

    document.getElementById(hash.substring(1))?.scrollIntoView();
  };

  // If the hash ID changes.
  useEffect(() => {
    scrollIntoView();
  }, [params.id]);

  useEffect(() => {
    if (documentData && tabId) {
      trackDocumentTabUsage(documentData, tabId, id);
    }
  }, [documentData, tabId]);

  // Track the document being opened
  useEffect(() => {
    if (documentData && id && !hasTracked.current) {
      trackOpenDocument(documentData, id);
      hasTracked.current = true; // Set to true after tracking
    }
  }, [id, documentData]);

  // On first page load.
  useEffect(() => {
    if (isLoading) return;

    scrollIntoView();
  }, [isLoading]);

  if (isLoading) return <PageLoader />;

  if (!documentData) return null;

  switch (tabId) {
    case DocOverviewNavTabs.SectionView:
      return <SectionViewTab />;
    case DocOverviewNavTabs.PageView:
      return <PageViewTab />;
    default:
    case DocOverviewNavTabs.Summary:
      return <SummaryTab />;
  }
}
