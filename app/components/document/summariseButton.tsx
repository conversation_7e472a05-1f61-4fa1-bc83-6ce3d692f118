'use client';

import Button from '@components/buttons/button';
import TailoIconButton from '@components/buttons/tailoIconButton';
import SummariseDocumentModal from '@components/modals/summariseDoc';
import {DocumentFilter} from 'iconsax-react';
import React, {FC, useState} from 'react';

interface SummariseButtonProps {
  id: string;
}

const SummariseButton: FC<SummariseButtonProps> = ({id}) => {
  const [isOpen, setIsOpen] = useState(false);
  const closeModal = () => setIsOpen(false);
  const openModal = () => setIsOpen(true);

  const label = 'Summarise';

  return (
    <>
      <Button
        variant="primary"
        onClick={openModal}
        className={`btn-sm hidden lg:flex`}>
        <DocumentFilter variant="Bold" />
        {label}
      </Button>
      <TailoIconButton label={label} handler={openModal} size="small">
        <DocumentFilter variant="Bold" className="w-10 h-10 md:w-8 md:h-8" />
      </TailoIconButton>
      <SummariseDocumentModal isOpen={isOpen} onClose={closeModal} />
    </>
  );
};

export default SummariseButton;
