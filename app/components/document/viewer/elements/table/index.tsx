import React from 'react';
import {ITableContent} from '@libs/store/document/types';
import THead from './tHead';
import TBody from './tBody';
import MobilePseudoTable from './mobilePseudoTable';

/**
 * This component now renders 2 types of tables: standard and mobile table. The mobile table with columns display is hidden from assistive technology as it's not accessibility compliant.The AT still can access the standard table on mobiles.
 * Note: Accommodating for table footer has no support in the BE's.
 * @param id needed to render unique keys on mapping the content
 *
 */

export default function ResponsiveTable({
  content,
  id,
}: {
  content: ITableContent;
  id: string;
}) {
  if (!content) return null;
  return (
    <>
      <div className="hidden sm:block" data-clarity-mask="True">
        <table className="responsive-table table-cell-border">
          <THead headers={content.headers} id={id} />
          <TBody content={content} id={id} />
        </table>
      </div>
      <div
        aria-hidden={true}
        className="block sm:hidden"
        data-clarity-mask="True">
        <MobilePseudoTable content={content} id={id} />
      </div>
    </>
  );
}
