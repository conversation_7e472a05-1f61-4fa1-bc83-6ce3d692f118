import {ITableHeader} from '@libs/store/document/types';
import React from 'react';
import TCel from './tCel';

export interface THeadProps {
  headers: ITableHeader[];
  id: string;
}

export default function THead({headers, id}: THeadProps) {
  return (
    <thead>
      <tr className="table-column-bg ">
        {headers.map((th, index) => (
          <th
            key={id + '-tr-' + index}
            className="table-cell-border table-header-bg">
            <TCel>{th.text || ''}</TCel>
          </th>
        ))}
      </tr>
    </thead>
  );
}
