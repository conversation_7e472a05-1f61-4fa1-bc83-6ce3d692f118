import type {<PERSON>a, <PERSON>Obj} from '@storybook/react';

import ResponsiveTable from './index';
import {ITableContent} from '@libs/store/document/types';

const meta: Meta<typeof ResponsiveTable> = {
  title: 'Table/All',
  component: ResponsiveTable,
};

export default meta;
type Story = StoryObj<typeof ResponsiveTable>;

const content: ITableContent = {
  headers: [
    {
      type: 'text',
      text: 'Column 1',
    },
    {
      type: 'text',
      text: 'Column 2',
    },
    {
      type: 'text',
      text: 'Column 3',
    },
  ],
  rows: [
    [
      {
        type: 'text',
        text: 'Row 1, Col 1',
      },
      {
        type: 'text',
        text: 'Row 1, Col 2',
      },
      {
        type: 'text',
        text: 'Row 1, Col 3',
      },
    ],
    [
      {
        type: 'text',
        text: 'Row 2, Col 1',
      },
      {
        type: 'text',
        text: 'Row 2, Col 2',
      },
      {
        type: 'text',
        text: 'Row 2, Col 3',
      },
    ],
    [
      {
        type: 'text',
        text: 'Row 3, Col 1',
      },
      {
        type: 'text',
        text: 'Row 3, Col 2',
      },
      {
        type: 'text',
        text: 'Row 3, Col 3',
      },
    ],
    [
      {
        type: 'text',
        text: 'Row 4, Col 1',
      },
      {
        type: 'text',
        text: 'Row 4, Col 2',
      },
      {
        type: 'text',
        text: 'Row 4, Col 3',
      },
    ],
    [
      {
        type: 'text',
        text: 'Row 5, Col 1',
      },
      {
        type: 'text',
        text: 'Row 5, Col 2',
      },
      {
        type: 'text',
        text: 'Row 5, Col 3',
      },
    ],
  ],
};

export const AllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark Poppins medium optimised lef">
        <div className="app-bg doc-text-default p-8 ">
          <ResponsiveTable id="t-dark" content={content} />
        </div>
      </div>
      <div data-theme="light Poppins medium optimised lef">
        <div className="app-bg doc-text-default p-8 ">
          <ResponsiveTable id="t-dark" content={content} />
        </div>
      </div>
      <div data-theme="yellow Poppins medium optimised lef">
        <div className="app-bg doc-text-default p-8 ">
          <ResponsiveTable id="t-yellow" content={content} />
        </div>
      </div>
      <div data-theme="pale-green Poppins medium optimised lef">
        <div className="app-bg doc-text-default p-8 ">
          <ResponsiveTable id="t-green" content={content} />
        </div>
      </div>
      <div data-theme="pale-pink Poppins medium optimised lef">
        <div className="app-bg doc-text-default p-8 ">
          <ResponsiveTable id="t-pink" content={content} />
        </div>
      </div>
      <div data-theme="pale-blue Poppins medium optimised lef">
        <div className="app-bg doc-text-default p-8 ">
          <ResponsiveTable id="t-blue" content={content} />
        </div>
      </div>
      <div data-theme="peach Poppins medium optimised lef">
        <div className="app-bg doc-text-default p-8 ">
          <ResponsiveTable id="t-peach" content={content} />
        </div>
      </div>
    </>
  ),
};
