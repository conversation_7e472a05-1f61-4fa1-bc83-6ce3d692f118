import React from 'react';
import TCel from './tCel';
import {ITableContent} from '@libs/store/document/types';

export interface TBodyProps {
  content: ITableContent;
  id: string;
}
export default function TBody({content, id}: TBodyProps) {
  const {headers, rows} = content;
  return (
    <tbody>
      {rows.map((tr, index) => (
        <tr key={id + '-row-' + index} className="table-cell-border">
          {tr.map((row, index) => {
            const th = headers[index];
            const dataLabel = th ? th.text : '';
            return (
              <td key={id + 'row-text-' + index} className="table-cell-border">
                <span className="tdBefore th-bg pl-2">
                  <TCel>{dataLabel}</TCel>
                </span>
                <TCel>{row.text || ''}</TCel>
              </td>
            );
          })}
        </tr>
      ))}
    </tbody>
  );
}
