import React from 'react';
import {TBodyProps} from './tBody';
import TCel from './tCel';

/* This component is hidden from assistive technology as it's not accessibility compliant due to columnar layout. The standard table is still accessible by the AT */

export default function MobilePseudoTable({
  id,
  content: {headers, rows},
}: TBodyProps) {
  return (
    <div className="border table-cell-border">
      {rows.map((_, index) => {
        if (!headers[index]) return;
        return (
          <div key={id + '-row-' + index} className="table-cell-border">
            <div
              key={id + '-tr-' + index}
              className="border table-cell-border table-header-bg px-4 py-2">
              <TCel>{headers[index]?.text || ''}</TCel>
            </div>
            {rows.map((row, i) => {
              return (
                <div
                  key={id + 'row-text-' + i}
                  className="table-cell-border border px-4 py-2">
                  <TCel>{row[index]?.text || ''}</TCel>
                </div>
              );
            })}
          </div>
        );
      })}
    </div>
  );
}
