import {useSpeakAloudScroll} from '@components/buttons/custom/speakAloud/useSpeakAloudScroll';
import {
  ContentListNodeFormat,
  IFormattedListItem,
} from '@libs/store/document/types';
import useSpeakAloud, {SpeakAloudState} from '@libs/store/speakAloud';

export default function ViewerList({
  items,
  format,
  nested = false,
  id = '',
}: {
  items: IFormattedListItem[];
  format: ContentListNodeFormat;
  nested?: boolean;
  id?: string;
}) {
  const ListTag = format === 'ordered' ? 'ol' : 'ul';
  const {currentParagraph, speakAloudState} = useSpeakAloud();
  // Adds bottom padding to the outer list
  const outerListStyle = nested ? '' : 'outer-list';
  const refs = useSpeakAloudScroll(id);

  return (
    <ListTag
      className={`
      ${
        currentParagraph?.id === id && speakAloudState !== SpeakAloudState.OFF
          ? 'active-tts-paragraph'
          : outerListStyle
      }
      `}
      id={id}>
      {items.map((item, index) => (
        <li ref={refs as React.RefObject<HTMLLIElement>} key={index}>
          {item.value}
          {item.items && (
            <ViewerList items={item.items} format={format} nested={true} />
          )}
        </li>
      ))}
    </ListTag>
  );
}
