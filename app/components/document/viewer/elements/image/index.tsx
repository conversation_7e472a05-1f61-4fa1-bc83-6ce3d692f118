import React from 'react';
import Image from 'next/image';
import ImageViewer from '@components/image-viewer';
import {IContentImageNode} from '@libs/store/document/types';

import {
  getImageSize,
  shouldDisplayAsBlock,
} from '@libs/utils/document/imageFormats';

import useViewerSize from '../../useViewerSize';
import useSettings from '@libs/store/settings';

export interface IFormattedContentImageNode extends IContentImageNode {
  pageWidth: number;
}
export default function ViewerImage({
  item,
}: {
  item: IFormattedContentImageNode;
}) {
  const {id, url, height, width, originalWidth, originalHeight, pageWidth} = item;

  const { settings } = useSettings();

  const {width: viewerWidth} = useViewerSize(pageWidth);

  const {block, fullWidth} = shouldDisplayAsBlock(
    viewerWidth,
    pageWidth,
    originalWidth,
    width,
  );

  const imageHeight = getImageSize(
    viewerWidth,
    pageWidth,
    originalHeight,
    height,
  );
  const imageWidth = getImageSize(viewerWidth, pageWidth, originalWidth, width);

  const imgH = block ? originalHeight : imageHeight;
  const imgW = block ? originalWidth : imageWidth;

  // If the user wishes small images to be hidden, hide them.
  if (settings.HideSmallImages && imgW < 80 && imgH < 80) {
    return null;
  }

  // Don't use the image viewer if the image is smaller than 600 pixels wide.
  if (imgW <= 600) {
    return <Image src={url} height={imgH} width={imgW} alt="" />;
  }

  // Render the image viewer for larger than container images.
  return <ImageViewer id={id} src={url} width={imgW} height={imgH} />;
}
