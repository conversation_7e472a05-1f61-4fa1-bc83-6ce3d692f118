import {forwardRef} from 'react';
import {HeaderProps} from '../types';
import HighlightWord from '@components/document/search/highlightWord';

const Header = forwardRef(function Header(
  {children, id, variant}: HeaderProps,
  ref: React.Ref<HTMLHeadingElement>,
) {
  const getHeaderForVariant = () => {
    let elem = <></>;

    switch (variant) {
      case 'h1':
        elem = (
          <h1 ref={ref} id={id} className="!text-left font-bold">
            {children}
          </h1>
        );
        break;
      case 'h2':
        elem = (
          <h2 ref={ref} id={id} className="!text-left font-bold">
            {children}
          </h2>
        );
        break;
      case 'h3':
        elem = (
          <h3 ref={ref} id={id} className="!text-left font-bold">
            {children}
          </h3>
        );
        break;
      case 'h4':
        elem = (
          <h4 ref={ref} id={id} className="!text-left font-bold">
            {children}
          </h4>
        );
        break;
      case 'h5':
        elem = (
          <h5 ref={ref} id={id} className="!text-left font-semibold">
            {children}
          </h5>
        );
        break;
      case 'h6':
        elem = (
          <h6 ref={ref} id={id} className="!text-left font-semibold">
            {children}
          </h6>
        );
    }
    return (
      <HighlightWord>
        {elem}
      </HighlightWord>
    );
  };

  return getHeaderForVariant();
});

export default Header;
