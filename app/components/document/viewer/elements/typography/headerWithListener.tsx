'use client';

import {FC} from 'react';
import {HeaderProps} from '../types';

import useViewerState from '@libs/store/viewer';
import {InView} from 'react-intersection-observer';
import Header from './headers';
import {useDocumentViewer} from '../../useDocumentViewer';

interface HeaderWithListenerProps extends HeaderProps {}

/**
 * Header has a listener that triggers when item is in view
 * Stores the id in ViewerState to be used to highlight the contents list as user scrolls through document
 */
const HeaderWithListener: FC<HeaderWithListenerProps> = ({
  children,
  ...props
}) => {
  const {updateCurrentContentItem} = useViewerState();
  const {thresholdForScrollListener} = useDocumentViewer();

  const handleItemInView = (entry: IntersectionObserverEntry) => {
    updateCurrentContentItem(entry.target.id);
  };

  return (
    <InView
      rootMargin={`0px 0px -${thresholdForScrollListener}px 0px`}
      onChange={(inView, entry) => {
        if (inView && entry) {
          handleItemInView(entry);
        }
      }}>
      {({ref}) => (
        <Header ref={ref} {...props}>
          {children}
        </Header>
      )}
    </InView>
  );
};

export default HeaderWithListener;
