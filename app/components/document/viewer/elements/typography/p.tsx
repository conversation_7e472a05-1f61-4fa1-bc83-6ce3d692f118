import {FC} from 'react';
import {ElementProps} from '../types';
import useSpeakAloud, {SpeakAloudState} from '@libs/store/speakAloud';
import {useSpeakAloudScroll} from '@components/buttons/custom/speakAloud/useSpeakAloudScroll';
import HighlightWord from '@components/document/search/highlightWord';

const P: FC<ElementProps> = ({children, className = '', id = ''}) => {
  const {currentParagraph, speakAloudState} = useSpeakAloud();
  const refs = useSpeakAloudScroll(id);

  return (
    <p
      id={id}
      className={`${
        currentParagraph?.id === id && speakAloudState !== SpeakAloudState.OFF
          ? 'active-tts-paragraph'
          : className
      } text-pretty break-words`}
      ref={refs as React.RefObject<HTMLParagraphElement>}>
      <HighlightWord>{children}</HighlightWord>
    </p>
  );
};

export default P;
