'use client';

import {
  IContentHeadingNode,
  IContentListNode,
  IContentTableNode,
  IContentTextNode,
  IDocumentExtractedData,
  IFormattedText,
  ITextFormatType,
} from '@libs/store/document/types';
import React, {useState} from 'react';
import {useCallback} from 'react';
import P from './elements/typography/p';
import {useWindowSize} from 'usehooks-ts';
import {IFormattedContentImageNode} from './elements/image';
import FootnotesDocLink from '@components/footnotes/footnotesDocLink';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import {Footnote, FootnoteRelationship} from '@components/footnotes/types';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';
import {useFootnotes} from '@contexts/footnotesContext';

export const useDocumentViewer = () => {
  const {height} = useWindowSize();

  const threshold = 25;
  const percent = (height / 100) * threshold;
  const thresholdForScrollListener = (height - percent).toFixed(0);
  const {isMobile} = useIsMobile();
  const {openDrawer} = useDrawer();
  const {document} = useProcessedDocument();

  //TODO: Footnotes currently aren't working in onboarding so are commented out currently

  const [isModalOpen, setIsModalOpen] = useState(false);
  // const {setFootnotes} = useFootnotes();

  const handleFootnoteClick = (relationships?: FootnoteRelationship[]) => {
    if (!relationships || !document) return;

    // Replace footnotes to ensure we only show the relevant ones for this event
    // setFootnotes([]);

    const newFootnotes: Footnote[] = [];

    // Loop through backend relationships to fetch relevant footnotes
    relationships.forEach(relationship => {
      const footnotesDetails = document.details.footnotes;

      if (footnotesDetails && footnotesDetails[relationship.id]) {
        newFootnotes.push(...footnotesDetails[relationship.id]);
      }
    });

    // if (newFootnotes.length) {
    //    setFootnotes(newFootnotes);
    //  }

    // If we are on mobile, open the drawer; otherwise, open the modal
    isMobile ? openDrawer(DRAWER_ID.FOOTNOTES) : setIsModalOpen(true);
  };

  const closeModal = () => setIsModalOpen(false);

  const flattenContent = useCallback(
    (
      document: IDocumentExtractedData,
      explicitSectionsIdsToDisplay: string[] = [],
    ) => {
      let content: (
        | IContentTextNode
        | IFormattedContentImageNode
        | IContentHeadingNode
        | IContentTableNode
        | IContentListNode
      )[][] = [];

      const sections = document.sections || [];
      let sectionsArray = Object.values(sections);
      if (explicitSectionsIdsToDisplay.length) {
        sectionsArray = sectionsArray.filter(section =>
          explicitSectionsIdsToDisplay.includes(section.id),
        );
      }

      // TODO: BE to return page width in details; This is a temporary fix, by no means of any value apart from preventing the code from breaking
      const pageWidth = document.details.baseFontSize * 100;

      sectionsArray.map(section => {
        const {id, tag, title} = section;
        if (tag !== 'none' && title !== '') {
          const formattedHeading: IContentHeadingNode = {
            id,
            tag,
            title,
            type: 'heading',
          };
          content.push([formattedHeading]);
        }

        const formattedContent = section.content.map(node => {
          if (node.type === 'img') {
            return {...node, pageWidth};
          }
          return {...node, parentSectionId: id};
        });
        content.push(formattedContent);
      });

      return content.flat();
    },
    [],
  );
  const formattedTextByType = (format: IFormattedText) => {
    let elem = <></>;
    const {value, url, type, relationships} = format;

    switch (type) {
      case ITextFormatType.Bold:
        elem = <strong className="font-bold">{value}</strong>;
        break;
      case ITextFormatType.Italic:
        elem = <span className="italic">{value}</span>;
        break;
      case ITextFormatType.Super:
        // // Currently we only cater for footnotes, so we need to accommodate for references which are also superscript but have no footer relationships
        // if (relationships && relationships.length > 0) {
        //   elem = (
        //     <FootnotesDocLink
        //       value={value}
        //       onPress={relations => handleFootnoteClick(relations)}
        //       relationships={relationships}
        //     />
        //   );
        // } else {
        elem = <sup className="relative -top-2 text-[80%]">{value}</sup>;
        // }
        break;
      case ITextFormatType.Sub:
        elem = <sub className="relative top-2 text-[80%]">{value}</sub>;
        break;
      case ITextFormatType.Link:
        if (url) {
          elem = (
            <a
              href={url}
              className="doc-link-default"
              target="_blank"
              rel="noopener noreferrer">
              {value}
            </a>
          );
        } else {
          elem = <>{value}</>; // do we want to guard against missing url here?
        }
        break;
      case ITextFormatType.None:
        // Some numbered references are slipping through the backend into number only strings - This catches these
        if (value.match(/^\d+(,\d+)*$/) != null) {
          elem = <sup className="relative -top-2 text-[80%]">{value}</sup>;
        } else {
          elem = <>{value}</>;
        }
        break;
      case ITextFormatType.Underline:
        elem = <u className="underline">{value}</u>;
        break;
    }

    return <>{elem}</>;
  };

  const getFormattedText = (
    formats: IFormattedText[] = [],
    id = '',
    masked = false,
  ) => {
    if (formats.length) {
      return (
        <P
          id={id}
          aria-disabled="True"
          data-clarity-mask={masked ? 'True' : 'False'}>
          <>
            {formats.map((format, index) => {
              return (
                <React.Fragment key={index.toString()}>
                  {formattedTextByType(format)}
                </React.Fragment>
              );
            })}
          </>
        </P>
      );
    }
  };

  return {
    flattenContent,
    getFormattedText,
    thresholdForScrollListener,
    isModalOpen,
    closeModal,
  };
};
