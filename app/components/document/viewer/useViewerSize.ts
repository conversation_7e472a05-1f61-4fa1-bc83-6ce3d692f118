import React from 'react';
import {VIEWER_ID} from './viewer';

export default function useViewerSize(defaultWidth = 900) {
  const [width, setWidth] = React.useState(defaultWidth);

  React.useEffect(() => {
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        setWidth(entry.contentRect.width);
      }
    });

    const viewerEl = document.getElementById(VIEWER_ID);
    if (viewerEl) {
      resizeObserver.observe(viewerEl);
    }
  }, []);

  return {
    width,
  };
}
