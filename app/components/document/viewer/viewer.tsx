'use client';

import ViewerList from './elements/list';
import React, {FC, useContext, useMemo, useState} from 'react';
import ViewerImage from './elements/image';
import {useSearchParams, usePathname} from 'next/navigation';
import ReadingMarker from '../readingMarker';
import ResponsiveTable from './elements/table';
import Error from '@components/errorBoundary/error';
import {useDocumentViewer} from './useDocumentViewer';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
import useReadingMarker from '../readingMarker/useReadingMarker';
import ReadingTimeWidget from '@components/document/readingTime';
import ViewerPlaceholder from '../placeholder/viewerPlaceholder';
import ReadingMarkerWrapper from '../readingMarker/readingMarkerWrapper';
import HeaderWithListener from './elements/typography/headerWithListener';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';
import CustomisationReactiveWrapper from '@components/customisation/customisationReactiveWrapper';
import SpeakAloudSectionButton from '@components/buttons/custom/speakAloud/speakAloudSectionButton';
import GeneratedAIPanel from '@components/panel/GeneratedAIPanel';
import FootnotesModal from '@components/footnotes/footnotesModal';
import AIPanel from '@components/panel/AIPanel';
import {ISectionData} from '@libs/store/document/types';
import {SectionSummariesContext} from '@contexts/sectionSummariesContext';
import {useSectionSummariesAnalytics} from '@libs/services/search/useSectionSummariesAnalytics';

interface ViewerProps {
  url?: string;
  enableContextMenu?: boolean;
  wrapperClassName?: string;
  explicitSectionsIdsToDisplay?: string[];
  enableReadingMarker?: boolean;
}

export const VIEWER_ID = 'tailo-document-viewer';

const Viewer: FC<ViewerProps> = ({
  url = '',
  enableContextMenu = false,
  wrapperClassName = '',
  explicitSectionsIdsToDisplay = [],
  enableReadingMarker = false,
}) => {
  const {
    document: documentData,
    isLoading,
    error,
    id: documentID,
  } = useProcessedDocument(url);

  const {fetchSectionSummary, generatedSummaries, loading} = useContext(
    SectionSummariesContext,
  );
  const [loadingState, setLoadingState] = useState<{[key: string]: boolean}>(
    {},
  );
  const {isMobile} = useIsMobile();
  const params = useSearchParams();
  const isUSLocale = navigator.language.startsWith('en-US');
  const tab = params.get('tab');

  const {flattenContent, getFormattedText, isModalOpen, closeModal} =
    useDocumentViewer();

  const disallowedRoutes = [
    'onboarding',
    'terms-of-service',
    'privacy-statement',
  ];

  const currentRoute = usePathname();

  const disallowedRoute = disallowedRoutes.includes(
    currentRoute.substring(1).split('/')[0],
  );

  const content = useMemo(() => {
    if (documentData) {
      return flattenContent(documentData, explicitSectionsIdsToDisplay);
    } else {
      return [];
    }
  }, [flattenContent, documentData, explicitSectionsIdsToDisplay]);

  const {storedCurrentId, prevId, nextId, updateId} = useReadingMarker(
    content,
    documentID,
    explicitSectionsIdsToDisplay.length > 0,
    enableReadingMarker,
  );

  // Fetch all sections that should be displayed.
  let sections = useMemo(
    () =>
      documentData
        ? Object.values(documentData.sections).filter(section =>
            explicitSectionsIdsToDisplay.includes(section.id),
          )
        : [],
    [documentData, explicitSectionsIdsToDisplay],
  );

  // If we should display all, fetch them all.
  if (explicitSectionsIdsToDisplay.length === 0) {
    sections = documentData ? Object.values(documentData.sections) : [];
  }

  // Calculate the total word count across the sections.
  const totalWordCountForParentSection = sections
    .map(value => value.wordCount)
    .reduce((acc, val) => acc + val, 0);

  const elemContent = useMemo(() => {
    const idCountMap: {[key: string]: number} = {};
    const firsts: {[key: string]: string} = {};
    const minimumH1Words = 200;
    const minimumWords = 100;

    if (!documentData) {
      return [];
    }

    const h1Section: ISectionData | undefined = Object.values(sections).find(
      section => {
        return section.tag === 'h1';
      },
    );

    const h1WordCount = h1Section?.wordCount;
    const allowedWordCountForH1 = h1WordCount && h1WordCount > minimumH1Words;

    return content.map((item, index) => {
      let elem = null;

      const section = documentData.sections[item.id];

      const generatedSummary = generatedSummaries.find(
        summary => summary.section_id === item.id,
      );

      const isFirstElem =
        !firsts['elem'] && ['paragraph', 'list', 'heading'].includes(item.type);
      if (isFirstElem) {
        firsts['elem'] = item.id;
      }

      //These are the conditions for displaying the summarise button

      const isHeadingOne = item.type === 'heading' && item.tag === 'h1';
      const isHeadingTwo = item.type === 'heading' && item.tag === 'h2';

      const firstHeadingOne = isHeadingOne && !firsts['h1'];
      if (firstHeadingOne) {
        firsts['h1'] = item.id;
      }

      const isFirstHeadingTwo = isHeadingTwo && !firsts['h2'];
      if (isFirstHeadingTwo) {
        firsts['h2'] = item.id;
      }

      const dissalowedTitles = ['references', 'keywords'];
      const isDissalowedTitle = dissalowedTitles.some(
        title =>
          'title' in item && item.title.toLocaleLowerCase().includes(title),
      );

      //Check if title h2 has a child h3 that has more than 100 words
      const allowedWordCount =
        section?.childWordCount > minimumWords ||
        section?.wordCount > minimumWords;

      const noChildWords =
        section?.wordCount === 0 ||
        section?.title.split(' ').length === section?.wordCount;

      const toggleSectionSummary = async (itemID: string) => {
        // Set the loading state to true for the specific section panel
        setLoadingState(prevState => ({...prevState, [itemID]: true}));
        const sectionWordCount = section.wordCount;

        try {
          // Call fetchSectionSummary
          await fetchSectionSummary(documentID, itemID, sectionWordCount);
        } catch (error) {
          // It broke (surprise, surprise)
          console.error('Error fetching section summary:', error);
        } finally {
          // Once the fetch is done (either success or failure), set the loading state to false
          setLoadingState(prevState => ({...prevState, [itemID]: false}));
        }
      };

      const generatedSectionId = generatedSummaries.map(
        summary => summary.section_id,
      );
      const currentSummarySection = generatedSectionId.includes(item.id);

      switch (item.type) {
        case 'heading':
          elem = (
            <>
              {isFirstElem && (
                <div className="flex items-center gap-4">
                  <ReadingTimeWidget
                    wordCount={totalWordCountForParentSection}
                  />
                  {allowedWordCount && (
                    <SpeakAloudSectionButton
                      documentId={documentID}
                      section={section}
                      showText={true}
                    />
                  )}
                </div>
              )}
              <div
                id={`${item.id}-title`}
                className={`flex ${
                  isFirstHeadingTwo ? 'flex-wrap' : ''
                } justify-between items-center mb-4`}>
                <div>
                  <HeaderWithListener variant={item.tag} id={item.id}>
                    {item.title}
                  </HeaderWithListener>
                  {isHeadingOne && (
                    <div className="flex justify-end gap-4">
                      {allowedWordCountForH1 ? (
                        currentSummarySection ? (
                          ''
                        ) : (
                          <AIPanel
                            id={item.id}
                            label={isUSLocale ? 'Summarize' : 'Summarise'}
                            className="hover:cursor-pointer"
                            showText={true}
                            handler={() => toggleSectionSummary(item.id)}
                          />
                        )
                      ) : null}
                      {!isFirstElem ? (
                        <SpeakAloudSectionButton
                          documentId={documentID}
                          section={section}
                          showText={true}
                        />
                      ) : null}
                    </div>
                  )}
                </div>

                {!disallowedRoute && (
                  <div>
                    {!isFirstElem && item.title && (
                      <>
                        <div className="flex justify-end gap-4 items-center">
                          {!isDissalowedTitle &&
                            allowedWordCount &&
                            isHeadingTwo &&
                            !currentSummarySection && (
                              <AIPanel
                                id={item.id}
                                label={isUSLocale ? 'Summarize' : 'Summarise'}
                                className="hover:cursor-pointer"
                                showText={
                                  (isFirstHeadingTwo &&
                                    !allowedWordCountForH1) ||
                                  isFirstElem
                                }
                                handler={() => toggleSectionSummary(item.id)}
                              />
                            )}
                          {!noChildWords && !isMobile && !isFirstElem && (
                            <SpeakAloudSectionButton
                              documentId={documentID}
                              section={section}
                              showText={
                                isFirstHeadingTwo && !allowedWordCountForH1
                              }
                            />
                          )}
                        </div>
                      </>
                    )}

                    {/* Section View */}
                    {isFirstElem && tab != 'document-view' && (
                      <>
                        {item.title && (
                          <div className="flex gap-2">
                            {!isDissalowedTitle &&
                              allowedWordCount &&
                              isHeadingTwo &&
                              !currentSummarySection && (
                                <AIPanel
                                  id={item.id}
                                  label="Summarise"
                                  className="hover:cursor-pointer"
                                  showText={false}
                                  handler={() => toggleSectionSummary(item.id)}
                                />
                              )}
                            {!isMobile && isHeadingTwo && !isFirstElem && (
                              <SpeakAloudSectionButton
                                documentId={documentID}
                                section={section}
                                showText={false}
                              />
                            )}
                          </div>
                        )}
                      </>
                    )}
                  </div>
                )}
              </div>
              {/* Render generated summary or loading panel */}
              {loadingState[item.id] ? (
                <GeneratedAIPanel id={item.id} isLoading={true} />
              ) : generatedSummary ? (
                <GeneratedAIPanel
                  id={item.id}
                  sectionSummary={generatedSummary.text}
                  isLoading={false}
                />
              ) : null}
            </>
          );
          break;
        case 'img':
          elem = <ViewerImage item={item} />;
          break;
        case 'list':
          elem = (
            <ViewerList items={item.items} format={item.format} id={item.id} />
          );
          break;
        case 'paragraph':
          const textEl = getFormattedText(item.formats, item.id, true);
          if (textEl) elem = textEl;
          break;
        case 'table':
          elem = (
            <ResponsiveTable content={item.content} id={'table-' + index} />
          );
          break;
      }
      if (!elem) return null;

      idCountMap[item.id] = idCountMap[item.id] ? idCountMap[item.id] + 1 : 1;
      const key = `${item.id}-${idCountMap[item.id]}`;
      if (enableReadingMarker)
        return (
          <ReadingMarkerWrapper
            id={item.id}
            currentId={storedCurrentId}
            key={key}>
            {storedCurrentId === item.id && (
              <ReadingMarker
                currentId={storedCurrentId}
                prevId={prevId}
                nextId={nextId}
                index={index}
                lastContentIndex={content.length - 1}
                useSectionView={explicitSectionsIdsToDisplay.length > 0}
              />
            )}

            <div
              onClick={() =>
                item.type !== 'heading'
                  ? updateId(item.id, item.parentSectionId)
                  : updateId(item.id, item.id)
              }>
              {elem}
            </div>
          </ReadingMarkerWrapper>
        );

      return <React.Fragment key={key}>{elem}</React.Fragment>;
    });
  }, [
    content,
    disallowedRoute,
    documentData,
    documentID,
    enableReadingMarker,
    explicitSectionsIdsToDisplay.length,
    fetchSectionSummary,
    generatedSummaries,
    getFormattedText,
    isMobile,
    isUSLocale,
    loadingState,
    nextId,
    prevId,
    sections,
    storedCurrentId,
    tab,
    totalWordCountForParentSection,
    updateId,
  ]);

  if (isLoading) return <ViewerPlaceholder />;

  if (error) return <Error error={error} />;

  if (!documentData) return null;

  return (
    <div
      className="grid grid-cols-1 justify-center overflow-hidden"
      id={VIEWER_ID}>
      <CustomisationReactiveWrapper
        useAsViewer
        enableContextMenu={enableContextMenu}
        className={`${wrapperClassName} mx-auto`}>
        {elemContent}
      </CustomisationReactiveWrapper>

      {isModalOpen && (
        <FootnotesModal isOpen={isModalOpen} closeModal={closeModal} />
      )}
    </div>
  );
};

export default Viewer;
