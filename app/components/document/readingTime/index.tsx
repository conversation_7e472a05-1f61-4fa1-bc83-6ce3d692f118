import {Timer1} from 'iconsax-react';
import {useSearchParams} from 'next/navigation';
const READING_TIME_WPM = 238;

export default function ReadingTime(props: {
  wordCount: number;
  className?: string;
}) {
  const {wordCount, className} = props;
  const searchParams = useSearchParams();
  const isSummaryTab = searchParams.get('tab') === 'summary';

  return (
    <div className="reading-time">
      <Timer1 variant="Bold" className="w-4 h-4" />
      {Math.ceil(wordCount / READING_TIME_WPM)} min read
    </div>
  );
}
