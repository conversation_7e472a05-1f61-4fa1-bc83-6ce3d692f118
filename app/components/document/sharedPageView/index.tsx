'use client';
import React from 'react';
import Viewer from '../viewer/viewer';
import Toc from '../../toc';
import usePinAtom from '@libs/store/pin';

export default function SharedPageView() {
  const {pinned} = usePinAtom();
  const moveRight = 'col-start-4 col-span-8';
  const centerView = 'col-start-3 col-span-8';

  return (
    <div className="flex lg:grid grid-cols-12 justify-center px-2 w-full max-w-desktop">
      <Toc />
      <div
        className={`block 
        ${pinned ? moveRight : centerView}
        pb-28`}>
        <div className="reading-environment-wrapper">
          <Viewer
            enableContextMenu={true}
            wrapperClassName="w-full lg:px-8 py-16"
          />
        </div>
      </div>
    </div>
  );
}
