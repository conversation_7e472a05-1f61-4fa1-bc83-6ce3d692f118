import {FC} from 'react';
import Drawer from '@components/modals/drawer/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import DocTools from './docTools';

interface DocToolsDrawerProps {}

export const docToolsDrawerWidth = 380;

const DocToolsDrawer: FC<DocToolsDrawerProps> = () => {
  return (
    <Drawer
      drawerTitle="Define"
      drawerID={DRAWER_ID.DOCUMENT_TOOLS}
      drawerContent={<DocTools />}
      direction="right"
      hideTitleHeader
      drawerBgColour="app-navbar-bg border-l theme-border"
      width={docToolsDrawerWidth}
    />
  );
};

export default DocToolsDrawer;
