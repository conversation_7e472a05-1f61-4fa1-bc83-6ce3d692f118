'use client';

import {FC} from 'react';
import WordDefinitionPanel from '../wordDefinition/wordDefinitionPanel';
import useDrawer from '@libs/store/drawer';
import {DrawerTab} from '@libs/store/drawer/types';
import DocSearchPanel from '../search/docSearchPanel';
import ExplainThisPanel from '../explainThis/explainThisPanel';
import TailoFeedbackPanel from '@components/feedback/tailoFeedbackPanel';

interface DocToolsProps {}

const DocTools: FC<DocToolsProps> = () => {
  const {docToolsDrawerContent} = useDrawer();

  let panel;
  switch (docToolsDrawerContent.content) {
    case DrawerTab.DOC_SEARCH:
      panel = <DocSearchPanel panelTitle="Search" />;
      break;
    case DrawerTab.TAILO_FEEDBACK:
      panel = <TailoFeedbackPanel panelTitle="Feedback" />;
      break;
    case DrawerTab.WORD_DEFINITION:
      panel = <WordDefinitionPanel panelTitle="Define" />;
      break;
    case DrawerTab.EXPLAIN_THIS:
      panel = <ExplainThisPanel panelTitle="Explain" />;
      break;
    default:
      panel = <WordDefinitionPanel panelTitle="Define" />;
      break;
  }

  return (
    // Had to put padding top here to make sure the content is fully visible with the nav bars present because of where ive attached the drawer
    <div
      className="pt-[16px] px-4 doc-bg theme-border-l overflow-auto"
      style={{height: 'calc(100vh - 60px)'}}>
      {panel}
    </div>
  );
};

export default DocTools;
