import Button from '@components/buttons/button';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID, DrawerTab} from '@libs/store/drawer/types';
import {MessageText1} from 'iconsax-react';
import {useFeedbackFormAnalysis} from '@components/feedback/useFeedbackFormAnalysis';
import {useSearchParams, usePathname} from 'next/navigation';
import useDocuments from '@libs/services/document/useDocuments';

interface Props {
  children: React.ReactNode;
}

export default function DocumentWrapper({children}: Props) {
  const {
    drawStateById,
    openDrawer,
    docToolsDrawerContent,
    updateDocToolsDrawerContent,
    updateDrawer,
  } = useDrawer();
  const {getDocumentById} = useDocuments();
  const {trackOpenFeedbackForm} = useFeedbackFormAnalysis();

  const params = useSearchParams();
  const pathname = usePathname();

  const id = pathname.split('/')[2];
  const page = params.get('tab');
  const documentType = getDocumentById(id)?.type || '';

  const drawerOpen = (drawerId: DRAWER_ID) =>
    drawStateById(drawerId) === 'open';

  const setDrawerContentToDefine = (drawerId: DrawerTab) => {
    updateDocToolsDrawerContent(drawerId);
  };

  const handleDrawerTab = (drawerTab: DrawerTab) => {
    if (!drawerOpen(DRAWER_ID.DOCUMENT_TOOLS)) {
      openDrawer(DRAWER_ID.DOCUMENT_TOOLS);
    }
    if (
      drawerOpen(DRAWER_ID.DOCUMENT_TOOLS) &&
      drawerTab === docToolsDrawerContent.content
    ) {
      updateDrawer(DRAWER_ID.DOCUMENT_TOOLS, 'closed');
    }
    setDrawerContentToDefine(drawerTab);
  };

  return (
    <div id="document-wrapper" className="document-wrapper relative">
      {children}
      <div className="hidden lg:block fixed bottom-[2%] lg:right-14 xl:right-20">
        <Button
          variant="secondary"
          size="sm"
          onClick={() => {
            handleDrawerTab(DrawerTab.TAILO_FEEDBACK);
            trackOpenFeedbackForm(page, documentType);
          }}>
          <MessageText1 variant="Bold" className="w-6 h-6" />
          <p className=" lg:hidden xl:block">Feedback</p>
        </Button>
      </div>
    </div>
  );
}
