import React from 'react';

export default function ViewerPlaceholder() {
  return (
    <div className="py-8 px-6 lg:pr-12 animate-pulse w-full">
      <div className="opposite-bg w-full p-12 rounded-md space-y-3 opacity-40">
        <div className="w-48 h-8 panel-bg rounded-md" />
        <div className="w-3/4 h-8 panel-bg rounded-md" />
        <div className="w-4/5 h-8 panel-bg rounded-md" />
        <div className="flex-1 h-8 panel-bg rounded-md" />
        <div className="flex-1 h-8 panel-bg rounded-md" />
        <div className="flex-1 h-8 panel-bg rounded-md" />
        <div className="w-8 h-8 panel-bg rounded-md" />
        <div className="h-12" />
        <div className="w-1/4 h-8 panel-bg rounded-md" />
        <div className="w-3/4 h-8 panel-bg rounded-md" />
        <div className="w-1/4 h-8 panel-bg rounded-md" />
        <div className="flex-1 h-8 panel-bg rounded-md" />
        <div className="flex-1 h-8 panel-bg rounded-md" />
        <div className="flex-1 h-8 panel-bg rounded-md" />
        <div className="w-1/2 h-8 panel-bg rounded-md" />
      </div>
    </div>
  );
}
