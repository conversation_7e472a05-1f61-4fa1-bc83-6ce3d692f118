import MessagesPanel from '@components/messagesPanel/messagesPanel';
import AIPanel from '@components/panel/AIPanel';
import {useExplainThisContext} from '@contexts/explainThisContext';
import {transformMarkdownToHTML} from '@libs/utils/text/markdownParser';

export default function ExplainThisGeneratedResult() {
  const {state} = useExplainThisContext();

  const transformedText = () => {
    return (
      <p
        dangerouslySetInnerHTML={{
          __html: transformMarkdownToHTML(state.generatedText),
        }}></p>
    );
  };

  return (
    <>
      {state.generatedText.length > 0 && (
        <>
          <p>Result</p>
          <AIPanel label="Powered by AI" className="p-1 w-full mb-2" />
          <div
            className={`${state.generatedText === '' ? 'h-48' : ''}
              w-full result-bg p-4 rounded-lg mb-6 whitespace-break-spaces`}>
            <div className="result-text">
              {state.generatedText.length > 0 ? (
                transformedText()
              ) : (
                <p>Your AI-generated result will appear here...</p>
              )}
            </div>
          </div>
        </>
      )}
      {state.error && (
        <MessagesPanel
          message={state.error || ''}
          type="danger"
          messageTitle={
            'Unable to generate a response. Please refresh the page and try again.'
          }
          hideCloseButton
        />
      )}
    </>
  );
}
