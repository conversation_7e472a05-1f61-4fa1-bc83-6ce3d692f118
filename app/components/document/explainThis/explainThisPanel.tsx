import Button from '@components/buttons/button';
import ExplainThisForm from './explainThisForm';
import TextArea from '@components/forms/textArea/textArea';
import {Refresh, Magicpen} from 'iconsax-react';
import ExplainThisLoading from './explainThisLoading';
import {useExplainThisContext} from '@contexts/explainThisContext';
import ExplainThisGeneratedResult from './explainThisGeneratedResult';
import {useExplainThisAnalytics} from '@libs/services/explain/useExplainThisAnalytics';

interface ExplainThisPanelProps {
  panelTitle?: string;
}

export default function ExplainThisPanel({panelTitle}: ExplainThisPanelProps) {
  const {state, dispatch, fetchExplainThisText, resetText} =
    useExplainThisContext();
  const {learnMoreExplainThis} = useExplainThisAnalytics();

  return (
    <div>
      <h3 className="hidden lg:block text-2xl font-bold mb-3">Explain</h3>
      <ExplainThisForm />
      <TextArea
        label="Your text"
        placeholder="Highlight, copy and paste, or enter text..."
        value={state.text}
        onChange={e => {
          dispatch({type: 'setText', text: e.target.value});
        }}
      />
      <div className="flex flex-row lg:flex-col-reverse xl:flex-row gap-3 justify-between mb-4 w-full ">
        <Button
          variant="secondary"
          size="sm"
          className="flex-none"
          disabled={state.text.length === 0}
          onClick={resetText}>
          <Refresh variant="Bold" className="w-4 h-4" />
          Reset
        </Button>

        <Button
          variant="primary"
          size="sm"
          className="btn-full"
          disabled={state.text.length === 0}
          onClick={fetchExplainThisText}>
          <Magicpen variant="Bold" className="w-4 h-4" />
          Explain
        </Button>
      </div>
      <hr className="h-px my-4 divider border-0" />
      {state.loading ? <ExplainThisLoading /> : <ExplainThisGeneratedResult />}
      <p className="text-sm !pb-0">
        AI features can make mistakes. <br />
        <a
          onClick={learnMoreExplainThis}
          href="https://tailoapp.com/2024/09/03/embracing-the-future-of-reading-with-tailo/"
          target="_blank"
          className="ai-underline">
          Learn More
        </a>
      </p>
    </div>
  );
}
