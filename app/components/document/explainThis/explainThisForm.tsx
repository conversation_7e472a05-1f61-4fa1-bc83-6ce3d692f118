import {useState} from 'react';
import {ArrowSquareDown, ArrowSquareUp} from 'iconsax-react';
import TailoIconButton from '@components/buttons/tailoIconButton';
import DropdownSelect from '@components/forms/dropdown/dropdownSelect';
import Button from '@components/buttons/button';
import {useExplainThisContext} from '@contexts/explainThisContext';
import {
  ExplainPromptConfigLevelOfDetail,
  ExplainPromptConfigLanguageComplexity,
  ExplainPromptConfigLevelOfKnowledge,
} from '@libs/services/explain/types';
import {
  formatLevelOfDetailOptions,
  formatLanguageComplexityOptions,
  formatLevelOfKnowledgeOptions,
} from '@components/document/explainThis/explainThisOutputTransfomer';
import {useExplainThisAnalytics} from '@libs/services/explain/useExplainThisAnalytics';

export default function ExplainThisForm() {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const {dispatch, state} = useExplainThisContext();
  const isUSLocale = navigator.language.startsWith('en-US');
  const {
    openReadingPreferencesExplainThis,
    closeReadingPreferencesExplainThis,
    readingPreferencesLevelOfDetail,
    readingPreferencesLanguageComplexity,
    readingPreferencesLevelOfKnowledge,
    savedChangesExplainThis,
  } = useExplainThisAnalytics();

  const handleSaveChanges = () => {
    setDropdownOpen(!dropdownOpen);
    localStorage.setItem(
      'readingPreferences',
      JSON.stringify({
        levelOfDetail: state.levelOfDetail,
        languageComplexity: state.languageComplexity,
        levelOfKnowledge: state.levelOfKnowledge,
      }),
    );
  };

  return (
    <div className="mt-4 lg:mt-0 dropdown-input-item w-full rounded-lg mb-8">
      <div className="h-14 flex items-center justify-between relative">
        <p className="font-bold">{isUSLocale ? 'Customize' : 'Customise'}</p>
        <TailoIconButton
          label="Toggle Explain Menu"
          handler={() => {
            dropdownOpen
              ? closeReadingPreferencesExplainThis()
              : openReadingPreferencesExplainThis();
            setDropdownOpen(!dropdownOpen);
          }}
          size="small">
          {dropdownOpen ? (
            <ArrowSquareUp className="w-8 h-8" />
          ) : (
            <ArrowSquareDown className=" w-8 h-8" />
          )}
        </TailoIconButton>
      </div>
      {dropdownOpen && (
        <div className="w-full [&>div]:mb-6">
          <div className="">
            <p className="">Level of detail</p>
            <DropdownSelect
              onSelect={value => {
                dispatch({
                  type: 'setLevelOfDetail',
                  levelOfDetail: value as ExplainPromptConfigLevelOfDetail,
                });
                readingPreferencesLevelOfDetail(value as string);
              }}
              disabled={false}
              defaultOption={state.levelOfDetail}
              options={formatLevelOfDetailOptions}
            />
          </div>
          <div className="">
            <p className="">Language complexity</p>
            <DropdownSelect
              onSelect={value => {
                dispatch({
                  type: 'setLanguageComplexity',
                  languageComplexity:
                    value as ExplainPromptConfigLanguageComplexity,
                });
                readingPreferencesLanguageComplexity(value as string);
              }}
              disabled={state.levelOfKnowledge === 'expert'}
              defaultOption={state.languageComplexity}
              options={formatLanguageComplexityOptions}
            />
          </div>
          <div className="">
            <p className="">Level of knowledge</p>
            <DropdownSelect
              onSelect={value => {
                dispatch({
                  type: 'setLevelOfKnowledge',
                  levelOfKnowledge:
                    value as ExplainPromptConfigLevelOfKnowledge,
                });
                readingPreferencesLevelOfKnowledge(value as string);
              }}
              disabled={false}
              defaultOption={state.levelOfKnowledge}
              options={formatLevelOfKnowledgeOptions}
            />
          </div>

          <div className="flex lg:justify-center xl:justify-end">
            <Button
              variant="primary"
              size="sm"
              className="mb-4"
              onClick={() => {
                handleSaveChanges();
                savedChangesExplainThis();
              }}>
              Save changes
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
