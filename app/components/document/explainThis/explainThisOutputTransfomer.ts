import {
  ExplainPromptConfigLevelOfDetail,
  ExplainPromptConfigLanguageComplexity,
  ExplainPromptConfigLevelOfKnowledge,
} from '@libs/services/explain/types';

const explainPromptLevelOfDetailMapping = {
  [ExplainPromptConfigLevelOfDetail.BRIEF]: 'Brief (default)',
  [ExplainPromptConfigLevelOfDetail.IN_DEPTH]: 'In depth',
};

export const formatLevelOfDetailOptions = Object.entries(
  explainPromptLevelOfDetailMapping,
).map(([key, value]) => ({
  value: key,
  label: value,
}));

const explainPromptLanguageComplexityMapping = {
  [ExplainPromptConfigLanguageComplexity.MATCH_SOURCE]:
    'Match source (default)',
  [ExplainPromptConfigLanguageComplexity.SIMPLE]: 'Simple',
};

export const formatLanguageComplexityOptions = Object.entries(
  explainPromptLanguageComplexityMapping,
).map(([key, value]) => ({
  value: key,
  label: value,
}));

const explainPromptLevelOfKnowledgeMapping = {
  [ExplainPromptConfigLevelOfKnowledge.BEGINNER]: 'Beginner',
  [ExplainPromptConfigLevelOfKnowledge.INTERMEDIATE]: 'Intermediate (default)',
  [ExplainPromptConfigLevelOfKnowledge.EXPERT]: 'Expert',
};

export const formatLevelOfKnowledgeOptions = Object.entries(
  explainPromptLevelOfKnowledgeMapping,
).map(([key, value]) => ({
  value: key,
  label: value,
}));
