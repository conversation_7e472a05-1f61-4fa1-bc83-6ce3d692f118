/* eslint-disable react-hooks/exhaustive-deps */

import { useEffect } from 'react';
import {ArrowSquareRight} from 'iconsax-react';
import { useSearchParams } from 'next/navigation';
import useTOCSections from '@components/toc/useTOCSections';

export default function ResultItem(props: {
  sectionId: string;
  sectionTitle: string;
  blockId: string;
  content: string[];
  selected?: boolean;
}) {
  const {sectionId, sectionTitle, blockId, content, selected} = props;

  const searchParams = useSearchParams();

  const { activeSectionId, updateActiveSection } = useTOCSections();

  const handleClick = (e: React.MouseEvent<HTMLElement>) => {
    e.preventDefault();

    const isSectionView = searchParams.get('tab') === 'section-view';

    if (isSectionView) {
      updateActiveSection(sectionId);

      return;
    }

    scrollToBlock();
  };

  const scrollToBlock = (behavior : ScrollBehavior = 'smooth') => {
    let elementToScrollTo = null;

    const element = document.getElementById(blockId);
    const section = document.getElementById(sectionId);

    if (!element) return;

    // Find the first instance of highlight in the block.
    const instances = element.getElementsByClassName('search-highlight');

    elementToScrollTo = instances[0];

    // Find the section, if the block is not highlighted.
    if (!elementToScrollTo && section) {
      elementToScrollTo = section;
    }

    const y = elementToScrollTo.getBoundingClientRect().top + window.scrollY + -100;

    window?.scrollTo({ top: y, behavior });

    elementToScrollTo?.classList.add('in-focus');
  };

  useEffect(() => {
    const isSectionView = searchParams.get('tab') === 'section-view';

    if (!activeSectionId || !isSectionView) return;

    scrollToBlock('auto');
  }, [activeSectionId]);

  return (
    <div
      onClick={(event) => handleClick(event)}
      className={`
        cursor-pointer rounded-lg px-4 pb-2 my-2 -pr-2
        ${selected ? 'selected-result-item-card' : 'result-item-card '} `}>
      <div className="w-full flex items-center justify-between py-2">
        <h3 className="doc-text-default font-medium">{sectionTitle}</h3>
        <ArrowSquareRight variant="Bold" className="min-w-[24px]" />
      </div>
      {content.map((item, index) => (
        <p
          key={index}
          className="text-sm py-1"
          dangerouslySetInnerHTML={{
            __html: item.replace(/<em>/g, '<em class="font-semibold">'),
          }}
        />
      ))}
    </div>
  );
}
