import React, {ReactNode, ReactElement, useContext, useMemo} from 'react';
import {DocumentSearchContext} from '@contexts/documentSearchContext';

export interface HighlightWordProps {
  children: ReactNode;
}

/**
 * Highlights matching words within text content by wrapping them in span elements.
 * @param text The text content to process.
 * @param regex The regular expression to match highlight words.
 * @returns An array of React nodes with highlighted words.
 */
const highlightText = (text: string, regex: RegExp): ReactNode[] =>
  text.split(regex).map((part, index) =>
    regex.test(part) ? (
      <span key={index} className="search-highlight p-[1px] rounded-sm">
        {part}
      </span>
    ) : (
      part
    ),
  );

/**
 * Recursively applies highlighting to text nodes within nested children elements.
 * @param children The children nodes to process.
 * @param regex The regular expression to match highlight words.
 * @returns The processed children with highlighted words.
 */
const processChildren = (children: ReactNode, regex: RegExp): ReactNode => {
  if (typeof children === 'string') {
    return highlightText(children, regex);
  }

  if (React.isValidElement(children)) {
    // Explicitly asserting that `children` is of type `ReactElement` so that `React.cloneElement` works correctly
    return React.cloneElement(children as ReactElement, {
      children: processChildren(children.props.children, regex),
    });
  }

  if (Array.isArray(children)) {
    return children.map((child, index) => (
      <React.Fragment key={index}>
        {processChildren(child, regex)}
      </React.Fragment>
    ));
  }

  return children;
};

export default function HighlightWord({children}: HighlightWordProps) {
  const {words} = useContext(DocumentSearchContext);

  const processedChildren = useMemo(() => {
    if (!words.length) return children;

    const pattern = words
      .sort((a, b) => b.length - a.length)
      .map(word => word.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'))
      .join('|');

    return processChildren(children, new RegExp(`\\b(${pattern})\\b`, 'gm'));
  }, [children, words]);

  return <>{processedChildren}</>;
}
