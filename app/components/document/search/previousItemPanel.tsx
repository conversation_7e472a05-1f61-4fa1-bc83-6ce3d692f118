import {useContext, useMemo} from 'react';
import useDrawer from '@libs/store/drawer';
import PreviousItemCard from './previousItemCard';
import {DocumentSearchContext} from '@contexts/documentSearchContext';

export default function PreviousItemPanel() {
  const {clearRecentSearch, results, searchedWord, recentSearch} = useContext(
    DocumentSearchContext,
  );

  const {toggleFeedbackDrawer} = useDrawer();

  const recent = useMemo(() => {
    if (!recentSearch) return [];

    const items = JSON.parse(localStorage.getItem('recentSearches') || '[]');

    if (!items.length) return [];

    return items;
  }, [recentSearch]);

  return (
    <div>
      {!searchedWord && recent?.length === 0 && (
        <div>
          <p className="pb-3">
            <strong>Search</strong> shows results matching your query and
            related root words.
          </p>

          <p className="text-sm pb-2">Not the results you expected?</p>

          <a
            onClick={() => toggleFeedbackDrawer()}
            className="pt-2 underline cursor-pointer">
            Give Feedback
          </a>
        </div>
      )}

      {!searchedWord && recent.length !== 0 && (
        <>
          <div className="flex justify-between pb-2">
            <h3 className="font-semibold doc-text-default">Recent</h3>
            <span
              onClick={clearRecentSearch}
              className="cursor-pointer underline text-sm">
              Clear
            </span>
          </div>

          <div className="flex flex-col gap-3">
            {recent.map((result: string, index: number) => (
              <PreviousItemCard key={index} title={result} />
            ))}
          </div>
        </>
      )}
    </div>
  );
}
