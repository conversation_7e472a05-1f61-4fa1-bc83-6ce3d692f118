import {ISearchResult} from './types';
import ResultItem from './resultItem';
import React, {useContext} from 'react';
import useDrawer from '@libs/store/drawer';
import SpellingSuggestions from './spellingSuggestions';
import {DocumentSearchContext} from '@contexts/documentSearchContext';

interface SearchResultsProps {
  results?: ISearchResult[];
}

export default function SearchResults({
  results,
}: SearchResultsProps): JSX.Element {
  const {wordSubmitted, suggestions} = useContext(DocumentSearchContext);

  const { toggleFeedbackDrawer } = useDrawer();
  
  return (
    <>
      {results && results.length > 0 && (
        <div className="pt-2">
          <p className="text-sm font-semibold pb-2">Showing {results.length} results</p>
          <ul>
            {results.map((result, index) => (
              <li key={index}>
                <ResultItem
                  sectionId={result.section_id}
                  sectionTitle={result.section_title}
                  blockId={result.block_id}
                  content={result.content}
                />
              </li>
            ))}
          </ul>
        </div>
      )}

      {wordSubmitted && results?.length === 0 && (
        <div className="pt-4">
          {suggestions && suggestions.length > 0 && (
            <SpellingSuggestions />
          )}

          <p className="doc-text-default font-semibold">
            No results found
          </p>
    
          <p className="pt-2">
            Not the results you expected?
          </p>
    
          <a
            onClick={() => toggleFeedbackDrawer()}
            className="pt-2 underline cursor-pointer">
            Give Feedback
          </a>
        </div>
      )}
    </>
  );
}
