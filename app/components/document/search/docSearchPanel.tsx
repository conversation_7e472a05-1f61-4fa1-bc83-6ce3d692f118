import React, {useContext, useEffect} from 'react';
import DocSearchForm from './docSearchForm';
import SearchResults from './searchResults';
import PreviousItemPanel from './previousItemPanel';
import {DocumentSearchContext} from '@contexts/documentSearchContext';
import SvgGradientLoader from '../../loaders/svgGradientLoader';

interface DocSearchPanelProps {
  panelTitle?: string;
};

const DocSearchPanel: React.FC<DocSearchPanelProps> = ({panelTitle}) => {
  const {results, loading} = useContext(DocumentSearchContext);

  return (
    <>
      <h3 className="text-2xl font-bold hidden lg:block">{panelTitle}</h3>

      <DocSearchForm />

      {loading && (
        <div className="w-full flex justify-center pt-4">
          <SvgGradientLoader />
        </div>
      )}

      {!loading && (
        <>
          <SearchResults results={results} />

          <PreviousItemPanel />
        </>
      )}
    </>
  );
};

export default DocSearchPanel;
