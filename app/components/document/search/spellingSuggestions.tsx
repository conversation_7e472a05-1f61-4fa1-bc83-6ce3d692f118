import React, {useContext, useMemo} from 'react';
import {DocumentSearchContext} from '@contexts/documentSearchContext';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';

type Option = {
  text: string,
  highlighted: string
};

const DocSearchPanel = ({}) => {
  const {id} = useProcessedDocument();

  const {
    results,
    loading,
    suggestions,
    setSearchWord,
    triggerSearch,
    setWordSubmitted,
  } = useContext(DocumentSearchContext);

  const searchForSuggestion = (suggestion: string) => {
    setWordSubmitted(true);
    setSearchWord(suggestion);
    triggerSearch(id, suggestion);
  };

  const hasSuggestions = useMemo(() => {
    if (!suggestions || !suggestions.length) return false;

    const options = suggestions.map((suggestion: { options: Array<any> }) => {
      return suggestion.options;
    }).flat();

    if (!options || !options.length) return false;

    return true;
  }, [suggestions]);

  if (!hasSuggestions) return null;

  return (
    <div className="spelling-suggestions text-sm mb-6">
      {!loading && results?.length === 0 && suggestions.length && (
        <p>
          Did you mean:&nbsp;

          {suggestions.map((phrase: { text: string, options: Option[] }, index) => {
            if (!phrase.options.length) return null;

            return (
              <span key={index}>
                {phrase.options.map((option, i) => {
                  return (
                    <span
                      key={i}
                      className="suggestion underline cursor-pointer"
                      onClick={() => searchForSuggestion(option.text)}
                      dangerouslySetInnerHTML={{ 
                        __html: option.highlighted,
                      }} />
                  )
                })}
              </span>
            )
          })}

          ?
        </p>
      )}
    </div>
  );
};

export default DocSearchPanel;
