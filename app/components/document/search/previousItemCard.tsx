/* eslint-disable react-hooks/exhaustive-deps */

import {useContext} from 'react';
import {ArrowSquareRight} from 'iconsax-react';
import {DocumentSearchContext} from '@contexts/documentSearchContext';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';

export interface PreviousItemCardProps {
  title: string;
};

export default function PreviousItemCard({title}: PreviousItemCardProps) {
  const {id} = useProcessedDocument();

  const {
    setSearchWord,
    setWordSubmitted,
    triggerSearch,
  } = useContext(DocumentSearchContext);

  const searchForWord = () => {
    setWordSubmitted(true);
    setSearchWord(title);
    triggerSearch(id, title);
  };

  return (
    <div
      onClick={() => searchForWord()}
      className="flex justify-between items-center result-item-card cursor-pointer rounded-lg py-4 px-4">
      <h2 className="doc-text-default font-medium">{title}</h2>
      <ArrowSquareRight variant="Bold" />
    </div>
  );
};
