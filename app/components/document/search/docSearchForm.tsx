import {CloseCircle, SearchNormal} from 'iconsax-react';
import TailoIconButton from '@components/buttons/tailoIconButton';
import TextInput from '@components/forms/inputs/textInput';
import {DocumentSearchContext} from '@contexts/documentSearchContext';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';

import {ChangeEvent, FormEvent, FormEventHandler, useContext} from 'react';

export default function DocSearchForm() {
  const {id} = useProcessedDocument();

  const {
    loading,
    searchedWord,
    setSearchWord,
    triggerSearch,
    wordSubmitted,
    setWordSubmitted,
    clearSearchedWord,
    setErrorSearching,
    addWordToRecentSearch,
  } = useContext(DocumentSearchContext);

  const onChange = (e: ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();

    setSearchWord(e.target.value);
  };

  const onSubmit: FormEventHandler<HTMLFormElement> = (event: FormEvent) => {
    event.preventDefault();

    if (!searchedWord) return;

    triggerSearch(id);
    addWordToRecentSearch(searchedWord);
    setWordSubmitted(true);
  };

  const searchButton = (
    <TailoIconButton
      size="small"
      type="submit"
      label="search for word or phrase"
      disabled={!searchedWord}
      handler={onSubmit}>
      <SearchNormal variant="Linear" />
    </TailoIconButton>
  );

  const clearButton = (
    <TailoIconButton
      size="small"
      type="reset"
      label="clear selected word"
      handler={() => {
        clearSearchedWord();
        setWordSubmitted(false);
        setErrorSearching(false);
      }}>
      <CloseCircle variant="Linear" />
    </TailoIconButton>
  );

  return (
    <form className="w-full pb-2" onSubmit={onSubmit}>
      <TextInput
        id="doc-search-input"
        label=""
        className="mt-2 mb-4"
        value={searchedWord}
        onChange={onChange}
        placeholder="Search word or phrase..."
        iconRight={
          wordSubmitted && !loading ? <>{clearButton}</> : searchButton
        }
        autoComplete="off"
      />
    </form>
  );
}
