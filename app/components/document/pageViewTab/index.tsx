import React from 'react';
import Viewer from '../viewer/viewer';
import Toc from '../../toc';
import useDrawer from '@libs/store/drawer';
import usePinAtom from '@libs/store/pin';

export default function PageViewTab() {
  const {pinned} = usePinAtom();
  const {getOpenDrawerId} = useDrawer();

  const moveRight = 'col-start-4 col-span-8';
  const centerView = 'col-start-3 col-span-8';
  const moveLeft = 'col-start-2 col-span-8';

  const drawer = getOpenDrawerId();
  const isDrawerOpen = drawer === 'document_tools_drawer';
  const tocPinned = pinned === true;

  return (
    <div className="flex lg:grid grid-cols-12 justify-center lg:px-8 w-full">
      <Toc />
      <div
        className={`block 
        ${tocPinned ? moveRight : isDrawerOpen ? moveLeft : centerView}
        `}>
        <div className="reading-environment-wrapper lg:mt-[122px] lg:mb-16 rounded-lg">
          <Viewer
            enableContextMenu={true}
            wrapperClassName="w-full lg:px-8 py-16"
          />
        </div>
      </div>
    </div>
  );
}
