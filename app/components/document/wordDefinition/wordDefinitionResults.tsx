import {WordDefinitionResponse} from '@libs/store/document/wordDefinition/types';
import WordDefinitionResultsPanel from './wordDefinitionResultsPanel';

const WordDefinitionResults = ({
  results,
}: {
  results?: WordDefinitionResponse;
}) => {
  return (
    results && (
      <div className="w-full h-full flex justify-start items-center">
        <WordDefinitionResultsPanel results={results} />
      </div>
    )
  );
};

export default WordDefinitionResults;
