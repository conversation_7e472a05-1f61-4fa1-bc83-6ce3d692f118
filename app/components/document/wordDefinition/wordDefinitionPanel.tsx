import React from 'react';
import WordDefinitionForm from './wordDefinitionForm';
import WordDefinitionResults from './wordDefinitionResults';
import WordDefinitionPanelTitle from './wordDefinitionPanelTitle';
import {useWordDefinitionContext} from '@contexts/wordDefinitionContext';

interface WordDefinitionPanelProps {
  panelTitle?: string;
}

const WordDefinitionPanel: React.FC<WordDefinitionPanelProps> = ({
  panelTitle,
}) => {
  const {loading, results, error} = useWordDefinitionContext();

  return (
    <div>
      {panelTitle && <WordDefinitionPanelTitle panelTitle={panelTitle} />}

      <WordDefinitionForm
        label="Word to define"
        loading={loading}
        error={error}
      />

      <WordDefinitionResults results={results} />

      <p className="pt-4 pb-4 mb-24 text-xs text-center">
        Source: Collins Dictionary
      </p>
    </div>
  );
};

export default WordDefinitionPanel;
