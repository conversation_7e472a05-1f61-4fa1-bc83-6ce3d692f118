import TailoIconButton from '@components/buttons/tailoIconButton';
import {VolumeHigh} from 'iconsax-react';
import React, {useRef} from 'react';

interface WordPlayerPanelProps {
  phoneticAudioLink?: string;
}

const WordPlayerPanel: React.FC<WordPlayerPanelProps> = ({
  phoneticAudioLink = '', // Stops there being any issues if the API returns nothing for these
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);

  const playAudioPhonetic = () => {
    if (audioRef.current) {
      audioRef.current.play();
    }
  };
  return (
    <>
      {/* Currently Searched Word with Audio player */}
      {phoneticAudioLink !== '' && (
        <TailoIconButton
          handler={playAudioPhonetic}
          size="small"
          label="Phonetic audio player for the chosen word">
          <VolumeHigh
            variant="Bold"
            className="w-6 h-6"
            onClick={playAudioPhonetic}
          />
          <audio ref={audioRef} src={phoneticAudioLink} />
        </TailoIconButton>
      )}
    </>
  );
};

export default WordPlayerPanel;
