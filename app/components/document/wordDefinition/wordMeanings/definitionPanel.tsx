import React from 'react';
import ExamplePanel from './examplePanel';

interface DefinitionPanelProps {
  definition: string;
  example?: string[];
  synonyms?: string[];
  antonyms?: string[];
}

const DefinitionPanel: React.FC<DefinitionPanelProps> = ({
  definition,
  example,
  synonyms,
  antonyms,
}) => {
  return (
    <div className="p-2 definition-part-of-speech rounded">
      <p className="doc-text-default">{definition}</p>
      {example
        ? example.map((example, index) => (
            <ExamplePanel exampleText={example} key={index} />
          ))
        : null}
      {synonyms && synonyms.length > 0 && (
        <p>
          <strong>Synonyms:</strong> {synonyms.join(', ')}
          {antonyms && antonyms.length > 0 && (
            <>
              <br />
              <strong>Antonyms:</strong> {antonyms.join(', ')}
            </>
          )}
        </p>
      )}
    </div>
  );
};

export default DefinitionPanel;
