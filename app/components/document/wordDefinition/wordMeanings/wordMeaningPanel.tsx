import React from 'react';
import DefinitionPanel from './definitionPanel';
import {WordDefinitionDefinitions} from '@libs/store/document/wordDefinition/types';

interface WordMeaningPanelProps {
  partOfSpeech: string;
  definitions: WordDefinitionDefinitions[];
}

const WordMeaningPanel: React.FC<WordMeaningPanelProps> = ({
  partOfSpeech,
  definitions,
}) => {
  return (
    <div className="pt-3">
      <div className="pill pill-info my-2" aria-live="polite">
        <p>{partOfSpeech}</p>
      </div>
      {definitions.map((definition, index) => (
        <DefinitionPanel
          definition={definition.definition}
          example={definition.example}
          synonyms={definition.synonyms}
          antonyms={definition.antonyms}
          key={index}
        />
      ))}
    </div>
  );
};

export default WordMeaningPanel;
