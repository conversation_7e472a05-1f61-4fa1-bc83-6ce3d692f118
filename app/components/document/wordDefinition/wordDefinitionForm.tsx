import React, {useEffect, useMemo, useState} from 'react';
import Button from '@components/buttons/button';
import TextInput from '@components/forms/inputs/textInput';
import MessagesPanel from '@components/messagesPanel/messagesPanel';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';
import {useWordDefinitionContext} from '@contexts/wordDefinitionContext';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID, DrawerTab} from '@libs/store/drawer/types';

interface WordDefinitionFormProps {
  label: string;
  loading: boolean;
  error: string | null;
}

const WordDefinitionForm: React.FC<WordDefinitionFormProps> = ({
  label,
  loading,
  error,
}) => {
  const {
    currentlySearchedWord,
    setCurrentlySearchedWord,
    fetchSelectedWordDefinition,
  } = useWordDefinitionContext();
  const {docToolsDrawerContent, drawStateById} = useDrawer();
  const {trackDefineWordInPanel} = useDocumentAnalytics();
  const [inputValue, setInputValue] = useState<string>(currentlySearchedWord);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    trackDefineWordInPanel(inputValue);
    setCurrentlySearchedWord(inputValue);
  };

  useMemo(() => {
    if (
      currentlySearchedWord &&
      drawStateById(DRAWER_ID.DOCUMENT_TOOLS) === 'open' &&
      docToolsDrawerContent.content === DrawerTab.WORD_DEFINITION
    ) {
      if (currentlySearchedWord.split(' ').length > 1) {
        setCurrentlySearchedWord('');
        return;
      }
      fetchSelectedWordDefinition();
    }
  }, [
    currentlySearchedWord,
    docToolsDrawerContent.content,
    drawStateById(DRAWER_ID.DOCUMENT_TOOLS),
  ]);

  useEffect(() => {
    setInputValue(currentlySearchedWord);
  }, [currentlySearchedWord]);

  return (
    <div className="w-full pt-4">
      <form className="form-control w-full" onSubmit={handleSubmit}>
        <TextInput
          id="word-to-define-input"
          label={label}
          value={inputValue}
          onChange={e => setInputValue(e.currentTarget.value)}
          className="mb-4"
        />
        <Button
          variant="primary"
          className="btn-full"
          size="sm"
          aria-label={loading ? 'Definition is loading' : 'Find definition'}
          loading={loading}
          type="submit"
          disabled={inputValue === ''}>
          Find definition
        </Button>
      </form>
      {error && (
        <MessagesPanel
          message={error}
          type="danger"
          messageTitle={'There has been an error searching for this word'}
          hideCloseButton
        />
      )}
    </div>
  );
};

export default WordDefinitionForm;
