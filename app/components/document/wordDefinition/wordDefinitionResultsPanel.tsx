import React, {useCallback, useEffect, useState} from 'react';
import WordPlayerPanel from './wordMeanings/wordPlayerPanel';
import WordMeaningPanel from './wordMeanings/wordMeaningPanel';
import {WordDefinitionResponse} from '@libs/store/document/wordDefinition/types';

import {
  getValidAudioFile,
  getValidPhoneticWordValue,
} from '@libs/utils/wordDefinition/helper';

interface WordDefinitionResultsPanelProps {
  results: WordDefinitionResponse;
  useAudioPlayer?: boolean;
}

const WordDefinitionResultsPanel: React.FC<WordDefinitionResultsPanelProps> = ({
  results,
  useAudioPlayer = false,
}) => {
  const [phoneticAudioLink, setPhoneticAudioLink] = useState<
    string | undefined
  >(undefined);
  const [phoneticWordValue, setPhoneticWordValue] = useState<
    string | undefined
  >(undefined);
  const getPhoneticAudioLink = useCallback(() => {
    if (results.phonetics) {
      setPhoneticAudioLink(getValidAudioFile(results.phonetics));
    }
  }, [results.phonetics]);

  const getPhoneticWordValue = useCallback(() => {
    if (results.phonetics) {
      setPhoneticWordValue(getValidPhoneticWordValue(results.phonetics));
    }
  }, [results.phonetics]);

  useEffect(() => {
    if (results.phonetics) {
      useAudioPlayer && getPhoneticAudioLink();
      getPhoneticWordValue();
    }
  }, [
    getPhoneticAudioLink,
    getPhoneticWordValue,
    results.phonetics,
    useAudioPlayer,
  ]);

  return (
    <div className="pt-3 w-full">
      <div className="flex-col">
        <div className="flex flex-row items-center">
          <h4 className="text-xl font-bold doc-text-default">{results.word}</h4>
          {useAudioPlayer && (
            <WordPlayerPanel phoneticAudioLink={phoneticAudioLink} />
          )}
        </div>
        {/* Phonetic of the word */}
        {phoneticWordValue && <p>{phoneticWordValue}</p>}
      </div>
      <div className="pt-3">
        {/* Origin */}
        {results.origin && <p>{results.origin}</p>}
      </div>
      {/* Meanings */}
      {results.meanings && (
        <>
          {results.meanings.map((meaning, index) => (
            <WordMeaningPanel
              partOfSpeech={meaning.part_of_speech}
              definitions={meaning.definitions}
              key={index}
            />
          ))}
        </>
      )}
    </div>
  );
};

export default WordDefinitionResultsPanel;
