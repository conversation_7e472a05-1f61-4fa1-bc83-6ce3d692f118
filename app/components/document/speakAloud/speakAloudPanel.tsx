import React, {useEffect, useState} from 'react';
import useDrawer from '@libs/store/drawer';
import useSettings from '@libs/store/settings';
import Button from '@components/buttons/button';
import { Voice } from '@libs/store/settings/types';
import UserService from '@libs/services/user/service';
import { DRAWER_ID } from '../../../libs/store/drawer/types';

interface SpeakAloudPanelProps {
  panelTitle: string;
  voiceOptions?: string[];
};

export default function SpeakAloudPanel(props: SpeakAloudPanelProps) {
  const { panelTitle } = props;

  const { isSpeakAloudDrawerActive } = useDrawer();
  const {settings, updateSettings} = useSettings();

  const [success, setSuccess] = useState<boolean>();
  const [loading, setLoading] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>();
  const [voice, setVoice] = useState<string>(settings.Voice ?? 'Brian');

  const userService = new UserService();

  const updateVoiceInSettings = () => {
    setLoading(true);

    // Take a Cut of the current settings
    const previousSettings = settings;

    // Update the voice locally.
    updateSettings({
      ...settings,
      Voice: voice as Voice,
    });

    // Update the settings on the server
    userService.updateUserSettings({...settings, Voice: voice as Voice})
      .then(() => {
        setLoading(false);
        setSuccess(true);
      })
      .catch(() => {
        setLoading(false);

        setErrorMessage(
          'Failed to update settings, changes have been reverted.',
        );

        // Revert what we tried to change
        updateSettings({
          ...settings,
          ...previousSettings,
        });
    });
  };

  useEffect(() => {
    if (isSpeakAloudDrawerActive) return;

    setSuccess(false);
  }, [isSpeakAloudDrawerActive]);

  return (
    <div className="pt-8 lg:pt-0">
      <h3 className="hidden pb-6 text-2xl font-bold lg:block">{panelTitle}</h3>

      <div>
        <label htmlFor="voice" className="label-text doc-text-default font-semibold pb-1">Voice</label>
          <select
            name="voice"
            onChange={(event) => setVoice(event.target.value)}
            defaultValue={voice}
            aria-label="Select a voice"
            className="input input-md w-full"
          >
            <optgroup label="UK">
              <option value="Brian">Brian</option>
              <option value="Amy">Amy</option>
              <option value="Emma">Emma</option>
            </optgroup>
            
            <optgroup label="US">
              <option value="Joanna">Joanna</option>
              <option value="Matthew">Matthew</option>
              <option value="Salli">Salli</option>
            </optgroup>
          </select>
        

        {errorMessage ? (
          <p>{errorMessage}</p>
        ) : null}
      </div>

      <Button
        variant="primary"
        size="sm"
        onClick={() => updateVoiceInSettings()}
        aria-label={loading ? 'Definition is loading' : 'Find definition'}
        loading={loading}
        className="btn-full mb-6 mt-4">
        <span>Save settings</span>
      </Button>

      {success && (
        <p className="text-sm">
          Successfully updated your voice settings. Your changes will take effect on next page refresh.
        </p>
      )}
    </div>
  );
}
