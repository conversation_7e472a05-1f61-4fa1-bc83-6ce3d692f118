'use client';

import {useEffect, useState} from 'react';
import Accordion from '@components/accordion';
import useQuotas from '@components/quotas/useQuotas';
import {convertHoursToMinutes} from '@libs/utils/tts/helper';

export default function Quota() {
  const {data, isLoading: quotaLoading} = useQuotas();

  const [quota, setQuota] = useState({
    used: 0,
    allowance: 0,
  });

  useEffect(() => {
    if (!data) return;

    setQuota({
      used: data?.tts?.used,
      allowance: convertHoursToMinutes(data?.tts?.allowance),
    });
  }, [data]);

  return (
    <Accordion name="minutes-remaining">
      <div id="accordion-title">
        <p className="text-sm font-semibold">Speak Aloud: Minutes remaining</p>
      </div>

      <div id="accordion-content">
        {!data || quotaLoading ? (
          <p className="text-sm">Loading usage...</p>
        ) : (
          <>
            <progress
              className="progress-bar-rail progress"
              value={10}
              max="100"></progress>

            <p className="pb-3 text-xs font-semibold">
              {quota?.used}/{quota?.allowance} MINUTES USED
            </p>

            <p className="text-sm">
              Your minutes will reset at the end of the month
            </p>
          </>
        )}
      </div>
    </Accordion>
  );
}
