import formatDuration from 'format-duration';
import {useEffect, useState, useRef} from 'react';

import {
  CloseCircle,
  ArrowLeft2,
  ArrowRight2,
  PauseCircle,
  PlayCircle,
} from 'iconsax-react';

import Button from '@components/buttons/button';
import SteppedSlider from '@components/sliders/steppedSlider';
import SpeakAloudCloseModal from '@components/modals/speakAloud/close/index';
import {useGlobalAudioPlayer} from 'react-use-audio-player';

import useSpeakAloud, {
  SectionStatus,
  SpeakAloudState,
} from '@libs/store/speakAloud';
import {useSearchParams} from 'next/navigation';
import AudioProgressBar from './audioProgressBar';
import useQuotas from '@components/quotas/useQuotas';
import TailoIconButton from '@components/buttons/tailoIconButton';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
import {useTextToSpeechAnalytics} from '@libs/services/tts/useTextToSpeechAnalytics';

export default function ControlContainer() {
  const frameRef = useRef<number>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentAudioTime, setCurrentAudioTime] = useState(0);
  const [currentAudioDuration, setCurrentAudioDuration] = useState(0);

  const {isMobile} = useIsMobile();

  const {
    speakAloudState,
    currentParagraph,
    audioPlayerSettings,
    updateSectionStatus,
    updateSpeakAloudState,
    updateCurrentParagraph,
    determineNextParagraph,
    updateAudioPlayerSettings,
    determinePreviousParagraph,
  } = useSpeakAloud();

  const {trackReadAloudPlay} = useTextToSpeechAnalytics();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const {data} = useQuotas();

  const {play, pause, load, getPosition, duration, setRate, stopped} =
    useGlobalAudioPlayer();

  useEffect(() => {
    if (!currentParagraph) return;

    const next = determineNextParagraph();

    if (!next) {
      updateSectionStatus(SectionStatus.END);
    }

    if (next) {
      updateSectionStatus(SectionStatus.MIDDLE);
    }

    const previous = determinePreviousParagraph();

    if (!previous) {
      updateSectionStatus(SectionStatus.START);
    }

    load(currentParagraph.tts.audio_file, {
      ...audioPlayerSettings,
      ...{
        onload: () => {
          play();
        },
        onend: () => {
          const next = determineNextParagraph();

          if (!next) {
            updateSpeakAloudState(SpeakAloudState.PAUSE);

            return;
          }

          updateCurrentParagraph(next);
        },
      },
    });
  }, [load, currentParagraph]);

  useEffect(() => {
    if (speakAloudState !== SpeakAloudState.PLAY) return;

    const animate = () => {
      setCurrentAudioTime(getPosition());
      setCurrentAudioDuration(duration);
      frameRef.current = requestAnimationFrame(animate);
    };

    frameRef.current = requestAnimationFrame(animate);

    return () => {
      if (!frameRef.current) return;
      cancelAnimationFrame(frameRef.current);
    };
  }, [speakAloudState, currentParagraph, getPosition, duration]);

  if (isMobile) return null;

  return (
    <div className="tts fixed bottom-16 lg:bottom-0 w-full h-24 z-max">
      <AudioProgressBar
        currentAudioTime={currentAudioTime}
        currentAudioDuration={currentAudioDuration}
      />
      <div
        className="controls-container flex h-full w-full items-center justify-between"
        style={{zIndex: 9999}}>
        <span className="ml-6 w-6 md:w-20">
          <span className="hidden font-bold md:block">
            {formatDuration(currentAudioTime * 1000)}/
            {formatDuration(currentAudioDuration * 1000)}
          </span>
        </span>
        <div className="flex items-center justify-center gap-8">
          <div className="flex flex-1 items-center gap-2 w-full p-2 ">
            <TailoIconButton
              label="Previous"
              size="small"
              disabled={determinePreviousParagraph() === null}
              handler={() => {
                const previous = determinePreviousParagraph();

                if (!previous) return;

                updateSpeakAloudState(SpeakAloudState.PLAY);

                updateCurrentParagraph(previous);
              }}>
              <ArrowLeft2 variant="Bold" />
            </TailoIconButton>

            {speakAloudState === SpeakAloudState.PLAY && (
              <TailoIconButton
                size="large"
                label="Pause"
                handler={() => {
                  updateSpeakAloudState(SpeakAloudState.PAUSE);
                  pause();
                }}>
                <PauseCircle className="h-10 w-10" variant="Bold" />
              </TailoIconButton>
            )}

            {speakAloudState === SpeakAloudState.PAUSE && (
              <TailoIconButton
                size="large"
                label="Play"
                handler={() => {
                  updateSpeakAloudState(SpeakAloudState.PLAY);
                  const quotaRemaining = data.tts.allowance - data.tts.used;
                  trackReadAloudPlay(
                    tabParam ?? '',
                    quotaRemaining,
                    'TTS Player',
                  );
                  play();
                }}>
                <PlayCircle className="h-10 w-10" variant="Bold" />
              </TailoIconButton>
            )}

            <TailoIconButton
              label="Next"
              size="small"
              disabled={determineNextParagraph() === null}
              handler={() => {
                const next = determineNextParagraph();

                if (!next) return;

                updateSpeakAloudState(SpeakAloudState.PLAY);

                updateCurrentParagraph(next);
              }}>
              <ArrowRight2 variant="Bold" />
            </TailoIconButton>
          </div>

          <div className="hidden md:block">
            <SteppedSlider
              stepValues={[0.75, 1, 1.25, 1.5, 1.75, 2.0]}
              defaultValue={audioPlayerSettings.initialRate as number}
              onStepChange={step => {
                setRate(step as number);

                updateAudioPlayerSettings({
                  ...audioPlayerSettings,
                  initialRate: step as number,
                });
              }}
              label=""
              accessibilityLabel="Speed slider"
            />
          </div>
        </div>

        <Button
          accessibilityLabel="Close the text to speech controls container"
          title="Close the text to speech controls container"
          size="sm"
          className="lg:mr-2 icon-btn-sm"
          variant="tertiary">
          <CloseCircle
            onClick={() => setIsModalOpen(true)}
          />
        </Button>

        <SpeakAloudCloseModal
          isOpen={isModalOpen}
          closeModal={() => {
            setIsModalOpen(false);
          }}
        />
      </div>
    </div>
  );
}
