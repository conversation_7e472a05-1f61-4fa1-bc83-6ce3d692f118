import documentClientServices from '@libs/services/document/documentClinetServices';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';
import useDocuments from '@libs/services/document/useDocuments';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';
import trimFileNameExtension from '@libs/utils/text/trimFileNameExtension';
import {useState} from 'react';

export default function useSummaryTab() {
  const {documents} = useDocuments();
  const {document: documentData, error, id} = useProcessedDocument();
  const {trackDocumentDownload} = useDocumentAnalytics();
  const documentName = documents.find(doc => doc.id === id)?.name;
  const toc = documentData?.details.toc || [];
  // If a document doesn't start with an h1, we use the document name as the title
  let title = trimFileNameExtension(documentName || '');
  if (documentData?.details.title) {
    title = documentData.details.title;
  } else if (toc.length > 0 && toc[0]?.tag === 'h1') {
    // Leaving in for backwards compatability
    title = toc[0].title;
  }

  const [isLoading, setIsLoading] = useState(false);

  const getFileUrl = (isPdf: boolean) => {
    setIsLoading(true);
    return documentClientServices
      .downloadDocument(id)
      .then(res => {
        const fileUrl = res.message.url;

        trackDocumentDownload(isPdf, true);
        return fileUrl;
      })
      .catch(err => {
        console.error('Error downloading the document:', err);
        trackDocumentDownload(isPdf, false, err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return {title, documentData, error, id, toc, isLoading, getFileUrl};
}
