'use client';
import React from 'react';
import useSummaryTab from './useSummaryTab';
import TableOfContentItem from '@components/toc/item/tableOfContentItem';
import CustomisationReactiveWrapper from '@components/customisation/customisationReactiveWrapper';
import {AppDynamicRoutes} from '@libs/services/auth/routes';
import {DocOverviewNavTabs} from '@components/nav/documentOverview/documentNavigation/documentNavigationContent';

export default function OverviewToc({
  inMobileView = false,
}: {
  inMobileView?: boolean;
}) {
  const {id, toc} = useSummaryTab();

  return (
    <section
      className={`${inMobileView ? 'lg:hidden' : 'hidden lg:block lg:pb-8'}`}>
      <CustomisationReactiveWrapper>
        <h3 id="overview-sections" className="font-black !py-4">
          Sections
        </h3>
      </CustomisationReactiveWrapper>
      <ul aria-labelledby="overview-sections" className="">
        {toc.map((li, index, list) => (
          <TableOfContentItem
            key={li.id}
            {...li}
            type="link"
            linkDestination={`${AppDynamicRoutes.document(id)}?tab=${
              DocOverviewNavTabs.PageView
            }`}
            linkHref={`#${li.id}-title`}
            isFirstItem={index === 0}
            isLastItem={index === list.length - 1}
            isSummaryTab={true}
          />
        ))}
      </ul>
    </section>
  );
}
