import ToggleButton from '@components/buttons/toggleButton';
import React from 'react';

interface SectionViewProps {
  toggled: boolean;
  handleToggle: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function SectionView(props: SectionViewProps) {
  const {toggled, handleToggle} = props;
  return (
    <div className="flex w-full lg:max-w-lg justify-between items-center p-4 panel">
      <div className="w-48 sm:w-full font-semibold leading-tight">
        Enable section by section view
      </div>
      <ToggleButton checked={toggled} handleToggle={handleToggle} />
    </div>
  );
}
