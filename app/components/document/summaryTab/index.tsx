'use client';
import React from 'react';
import SummaryToc from './summaryToc';
import QuickSummary from './quickSummary';
import DocumentTitle from './documentTitle';
import useSummaryTab from './useSummaryTab';
import Error from '@components/errorBoundary/error';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
import ReadingTimeWidget from '@components/document/readingTime';
import SpeakAloudSectionButton from '@components/buttons/custom/speakAloud/speakAloudSectionButton';
import OpenFileButton from '@components/buttons/custom/document/openFileButton';
import useDocuments from '@libs/services/document/useDocuments';

export default function SummaryTab() {
  const {isMobile} = useIsMobile();
  const {documentData, error, title, id} = useSummaryTab();
  const {getDocumentById} = useDocuments();
  const summaryContent = [...(documentData?.details.summary ?? [])];
  const wordCount = documentData?.details?.wordCount ?? 0;
  const documentType = getDocumentById(id)?.type;

  summaryContent.unshift(title);
  if (error) return <Error error={error} />;

  if (!documentData) return null;

  return (
    <div className="px-4 pb-8 summary-body lg:rounded-lg pt-6 lg:pt-12 md:px-8 lg:mb-16 lg:mt-[122px] lg:w-[800px] m-auto">
      <div className="flex justify-between items-center gap-1">
        <div className=" flex items-center flex-row gap-1 ">
          {wordCount > 1 && (
            <ReadingTimeWidget wordCount={wordCount} className="py-3" />
          )}

          {!isMobile && wordCount > 1 && (
            <SpeakAloudSectionButton
              documentId={id}
              section={documentData?.details}
              showText={true}
            />
          )}
        </div>
        <OpenFileButton fileType={documentType} />
      </div>

      <DocumentTitle title={title} />
      <QuickSummary documentData={documentData} enableContextMenu={true} />
      <SummaryToc inMobileView />
      <SummaryToc />
    </div>
  );
}
