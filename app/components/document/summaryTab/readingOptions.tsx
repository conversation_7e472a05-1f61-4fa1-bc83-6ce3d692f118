import LineBreak from '@components/lineBreak/lineBreak';
import {ArrowRight} from 'iconsax-react';
import SectionView from './sectionView';
import {useState} from 'react';
import {useParams} from 'next/navigation';
import {DocOverviewNavTabs} from '@components/nav/documentOverview/documentNavigation/documentNavigationContent';
import {AppDynamicRoutes} from '@libs/services/auth/routes';
import Link from 'next/link';
import {useStoredIds} from '@libs/store/document/readingMarker';
import {useSectionViewConfig} from '@libs/store/document/readingOptions';
import useSummaryTab from './useSummaryTab';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';

export default function ReadingOptions() {
  const {id} = useParams();
  const {documentData} = useSummaryTab();
  const {trackContinueReading} = useDocumentAnalytics();
  const documentId = id as string;
  const {getStoredIdsPerDocument} = useStoredIds(documentId);
  const {getSectionViewConfig, updateSectionViewConfig} =
    useSectionViewConfig(documentId);
  const {currentId: storedReadingMarkerId} = getStoredIdsPerDocument();
  const {usingSectionView} = getSectionViewConfig();
  const [toggled, setToggled] = useState(true); // initial toggle state before user has interacted with it, gets stored in local storage after user interaction
  const handleToggle = () => {
    setToggled(!toggled);
    updateSectionViewConfig(!toggled);
  };
  const sectionViewConfig = usingSectionView ?? toggled;
  const startReading = `${AppDynamicRoutes.document(documentId)}?tab=${
    sectionViewConfig
      ? DocOverviewNavTabs.SectionView
      : DocOverviewNavTabs.PageView
  }${storedReadingMarkerId ? `#${storedReadingMarkerId}` : ''}`;

  const trackStartReading = () => {
    // Track start reading event
    if (documentData) {
      trackContinueReading(
        'Continue Reading',
        sectionViewConfig ? 'Section View' : 'Document View',
        documentData.details.wordCount,
      );
    }
  };

  return (
    <div className="flex flex-col w-full items-center">
      <LineBreak />
      <div className="flex flex-col lg:flex-row w-full justify-between gap-8">
        <SectionView toggled={sectionViewConfig} handleToggle={handleToggle} />
        <Link
          className="font-semibold rounded-lg btn btn-primary"
          href={startReading}
          onClick={trackStartReading}>
          {storedReadingMarkerId ? (
            <span>Continue reading</span>
          ) : (
            <span>Start reading</span>
          )}
          <ArrowRight variant="Bold" className="mr-2" />
        </Link>
      </div>
      <LineBreak />
    </div>
  );
}
