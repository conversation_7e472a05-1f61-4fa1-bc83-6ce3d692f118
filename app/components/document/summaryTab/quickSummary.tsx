import CustomisationReactiveWrapper from '@components/customisation/customisationReactiveWrapper';
import React from 'react';
import {IDocumentExtractedData} from '@libs/store/document/types';
import {Magicpen} from 'iconsax-react';
import useSpeakAloud, {SpeakAloudState} from '@libs/store/speakAloud';
import AIPanel from '@components/panel/AIPanel';

export default function QuickSummary({
  documentData,
  enableContextMenu = false,
}: {
  documentData: IDocumentExtractedData;
  enableContextMenu?: boolean;
}) {
  const {currentParagraph, speakAloudState} = useSpeakAloud();

  const transform = (text: string) => {
    let transformed = text;

    const patterns = [
      {
        pattern: /\*{2}([^,*]+)\*{2}/g,
        replacement: '<strong>$1</strong>',
      },
    ];

    patterns.forEach(pattern => {
      transformed = transformed.replace(pattern.pattern, pattern.replacement);
    });

    return transformed;
  };

  return (
    <div className="pt-8 mb-4">
      <CustomisationReactiveWrapper enableContextMenu={enableContextMenu}>
        <div className="overview-gradient-border">
          <div className="quick-summary-container">
            <div className="flex justify-between items-center mb-5">
              <h3
                className="doc-text-default !pb-0 !pt-0 font-bold"
                id="quick-summary">
                Quick summary
              </h3>
              <AIPanel label="AI-generated" className="py-2" />
            </div>
            <div
              className={`${
                currentParagraph?.id === 'summary' &&
                speakAloudState !== SpeakAloudState.OFF
                  ? ' active-tts-paragraph'
                  : null
              }`}>
              <ul
                className="list-disc ml-4 mb-2"
                aria-labelledby="quick-summary">
                {documentData.details.summary?.map((item, index) => {
                  return (
                    <li key={`quick-summary-${index}`}>
                      <p
                        dangerouslySetInnerHTML={{__html: transform(item)}}></p>
                    </li>
                  );
                })}
              </ul>
            </div>
            <p className="text-sm !pb-0">
              AI features can make mistakes.{' '}
              <a
                href="https://tailoapp.com/2024/09/03/embracing-the-future-of-reading-with-tailo/"
                target="_blank"
                className="ai-underline">
                Learn More
              </a>
            </p>
          </div>
        </div>
      </CustomisationReactiveWrapper>
    </div>
  );
}
