import React from 'react';

interface ReadingMarkerWrapperProps {
  children: React.ReactNode;
  currentId: string | undefined;
  id: string;
}

export default function ReadingMarkerWrapper(props: ReadingMarkerWrapperProps) {
  const {children, currentId, id} = props;
  return (
    <div
      className={`flex relative hover:highlighted-block transition-colors duration-300 ${
        currentId === id ? 'highlighted-block px-4 -mx-4' : ''
      }`}>
      {children}
    </div>
  );
}
