import {ArrowDown, ArrowUp} from 'iconsax-react';
import React from 'react';
import useTOCSections from '@components/toc/useTOCSections';
import TailoIconButton from '@components/buttons/tailoIconButton';

export default function ReadingMarker({
  currentId,
  prevId,
  nextId,
  index,
  lastContentIndex,
  useSectionView = false,
}: {
  currentId: string;
  prevId: () => void;
  nextId: () => void;
  index: number;
  lastContentIndex: number;
  useSectionView?: boolean;
}) {
  const {nextSectionButtonDisabled} = useTOCSections();
  return (
    <div
      id={`rm-${currentId}`}
      className="absolute reading-marker-border border-l-4 -left-1 w-1 h-full">
      <div className="hidden -ml-12 lg:flex flex-col justify-center h-full">
        <TailoIconButton
          handler={prevId}
          size="small"
          label="Move marker up"
          disabled={index === 0}>
          <ArrowUp variant="Bold" />
        </TailoIconButton>
        <TailoIconButton
          handler={nextId}
          size="small"
          label="Move marker down"
          disabled={
            useSectionView
              ? nextSectionButtonDisabled && index === lastContentIndex
              : index === lastContentIndex
          }>
          <ArrowDown variant="Bold" />
        </TailoIconButton>
      </div>
    </div>
  );
}
