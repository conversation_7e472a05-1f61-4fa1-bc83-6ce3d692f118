import {IContentNode} from '@libs/store/document/types';
import {useCallback, useEffect} from 'react';
import useTOCSections from '@components/toc/useTOCSections';
import {useStoredIds} from '@libs/store/document/readingMarker';

export default function useReadingMarker(
  content: IContentNode[],
  documentId: string,
  useSectionView: boolean = false,
  enableReadingMarker = false,
) {
  const {getStoredIdsPerDocument, updateStoredIdsPerDocument} =
    useStoredIds(documentId);
  const {getSection} = useTOCSections();
  const {currentId: storedCurrentId} = getStoredIdsPerDocument();
  const filteredContent = content.filter(el => el.type !== 'section');

  const prevId = () => {
    const currentIndex = filteredContent.findIndex(
      item => item.id === storedCurrentId,
    );
    if (currentIndex > 0) {
      const prevItem = filteredContent[currentIndex - 1];
      const parentSectionId =
        'parentSectionId' in prevItem ? prevItem.parentSectionId : prevItem.id;
      updateId(prevItem.id, parentSectionId);
    }
  };
  const nextId = async () => {
    const currentIndex = filteredContent.findIndex(
      item => item.id === storedCurrentId,
    );
    if (currentIndex < filteredContent.length - 1) {
      const nextItem = filteredContent[currentIndex + 1];
      const parentSectionId =
        'parentSectionId' in nextItem ? nextItem.parentSectionId : nextItem.id;
      updateId(nextItem.id, parentSectionId);
    } else if (useSectionView) {
      // Need to wait for function to return correct next section id
      const nextSectionId = getSection('Next');
      updateId(nextSectionId, nextSectionId); // parentSectionId should be the same as nextSectionId in this instance
    }
  };

  const updateId = useCallback(
    (currentId: string, parentSectionId: string) => {
      updateStoredIdsPerDocument(currentId, parentSectionId);
    },
    [updateStoredIdsPerDocument],
  );

  const updateScrollPosition = useCallback(() => {
    const element = document.getElementById(`rm-${storedCurrentId}`);
    if (element) {
      const observer = new IntersectionObserver(entries => {
        if (!entries[0].isIntersecting || entries[0].intersectionRatio < 1) {
          element.scrollIntoView({behavior: 'smooth'});
        }
      });

      observer.observe(element);

      // Cleanup function to unobserve the element when the component unmounts
      return () => observer.unobserve(element);
    }
  }, [storedCurrentId]);

  // sets marker to first content block if no stored id
  useEffect(() => {
    if (enableReadingMarker === false) return;
    // if (content[0] && !storedCurrentId) {
    //   updateId(content[0].id, content[0].id); // temp disabled due to race condition with local storage on page load being undefined
    // }
    updateScrollPosition();
  }, [enableReadingMarker, updateScrollPosition]);

  return {
    storedCurrentId,
    prevId,
    nextId,
    updateId,
  };
}
