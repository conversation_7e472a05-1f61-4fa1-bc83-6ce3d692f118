import React from 'react';
import {IHeadingData} from '@libs/store/document/types';
import ContentsItem from './contentsItem';

export default function ContentsList({
  list,
  closeDrawer,
}: {
  list: IHeadingData[];
  closeDrawer?: () => void;
}) {
  if (!list) return null;

  return (
    <ul id="h-list" className="form-control space-y-3 py-4 px-2">
      {list.map((heading, index) => {
        return (
          <li key={index.toString()}>
            <ContentsItem
              id={heading.id}
              text={heading.title}
              cb={closeDrawer}
            />
          </li>
        );
      })}
    </ul>
  );
}
