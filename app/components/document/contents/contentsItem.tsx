'use client';

import useViewerState from '@libs/store/viewer';
import Link from 'next/link';
import React, {FC} from 'react';

interface ContentsItemProps {
  id: string;
  text: string;
  cb?: () => void;
}

const ContentsItem: FC<ContentsItemProps> = ({id, text, cb}) => {
  const {currentContentItem, updateCurrentContentItem} = useViewerState();

  const handleClick = () => {
    updateCurrentContentItem(id);
    cb && cb();
  };
  const isHighlighted = currentContentItem === id;

  const href = `#${id}`;
  return (
    <Link href={href} passHref legacyBehavior>
      <a
        onClick={handleClick}
        href={href}
        className={`${
          isHighlighted ? 'opacity-100' : 'opacity-50'
        } underline line-clamp-2`}>
        {text}
      </a>
    </Link>
  );
};

export default ContentsItem;
