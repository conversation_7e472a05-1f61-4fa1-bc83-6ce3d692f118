import React, {<PERSON>} from 'react';
import ContentIcon from './contentIcon';

export interface ContentsButtonProps {
  isOpen: boolean;
  onClick: () => void;
}

const ContentsButton: FC<ContentsButtonProps> = ({isOpen, onClick}) => {
  return (
    <>
      <label
        className="collapse-title btn btn-primary w-full rounded-lg justify-start font-normal"
        onClick={onClick}>
        <ContentIcon /> Contents
      </label>
      <input
        type="checkbox"
        name="content-accordion"
        id="content-checkbox"
        aria-expanded={isOpen}
        aria-controls="h-list"
      />
    </>
  );
};

export default ContentsButton;
