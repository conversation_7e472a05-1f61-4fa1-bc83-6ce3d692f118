import Fab from '@components/buttons/fab';
import ContentIcon from './contentIcon';

export const clearanceForTabs = 'bottom-[76px]'; // summary/original tabs appear on mobile
export default function ContentsFab({
  handleOnClick,
}: {
  handleOnClick: () => void;
}) {
  return (
    <Fab
      size="large"
      onClick={handleOnClick}
      fab={true}
      accessibilityLabel={'Contents'}
      className={clearanceForTabs}>
      <ContentIcon />
    </Fab>
  );
}
