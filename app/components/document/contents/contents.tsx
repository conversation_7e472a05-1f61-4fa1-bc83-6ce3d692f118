'use client';

import React, {FC} from 'react';
import ContentsButton from './contentsButton';
import BottomSheet from '@components/modals/bottomSheet/bottomSheet';
import ContentsFab from '@components/document/contents/contentsFab';
import ContentsList from './contentsList';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';
import Error from '@components/errorBoundary/error';
import ContentPlaceholder from '../placeholder/contentPlaceholder';
import {DRAWER_ID} from '@libs/store/drawer/types';
import useDrawer from '@libs/store/drawer';
interface ContentsProps {
  id: string;
  url?: string;
}

const Contents: FC<ContentsProps> = ({url = ''}) => {
  const {document: documentData, isLoading, error} = useProcessedDocument(url);
  const {openDrawer, closeDrawer, drawStateById} = useDrawer();
  const drawerId = DRAWER_ID.DOCUMENT_CONTENTS_LIST;
  const showContents = drawStateById(drawerId) === 'open';
  const handleOnClick = () => {
    drawStateById(drawerId) === 'open'
      ? closeDrawer(drawerId)
      : openDrawer(drawerId);
  };

  if (isLoading) return <ContentPlaceholder />;

  if (error) return <Error error={error} />;

  if (!documentData) return null;

  const {toc} = documentData.details;

  return (
    <>
      <aside className="hidden lg:flex sm:sticky sm:top-6 sm:h-screen">
        <div className="w-[300px] p-4">
          <div className="collapse collapse-arrow rounded-none">
            <ContentsButton isOpen={showContents} onClick={handleOnClick} />
            <div className="collapse-content">
              <div className="h-screen pb-60 overflow-scroll custom-srollbar ">
                <ContentsList list={toc} />
              </div>
            </div>
          </div>
        </div>
      </aside>
      <aside className="lg:hidden">
        <ContentsFab handleOnClick={handleOnClick} />
        <BottomSheet drawerId={drawerId}>
          <h2 className="font-semibold">Table of content</h2>
          <ContentsList list={toc} closeDrawer={handleOnClick} />
        </BottomSheet>
      </aside>
    </>
  );
};

export default Contents;
