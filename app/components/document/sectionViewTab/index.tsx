import React from 'react';
import SectionContent from './sectionContent';
import Toc from '../../toc';
import SectionViewNavigationButtons from './sectionViewNavigationButtons';
import usePinAtom from '@libs/store/pin';
import useDrawer from '@libs/store/drawer';

export default function SectionViewTab() {
  const {pinned} = usePinAtom();
  const {getOpenDrawerId} = useDrawer();

  const moveRight = 'col-start-4 col-span-8';
  const centerView = 'col-start-3 col-span-8';
  const moveLeft = 'col-start-2 col-span-8';

  const drawer = getOpenDrawerId();
  //Makes sure that the viewer doesn't shift when the left drawer opens
  const isDrawerOpen = drawer === 'document_tools_drawer';

  const tocPinned = pinned === true;

  return (
    <div className="flex lg:grid grid-cols-12 justify-center pb-2 lg:pb-0 lg:px-8 w-full">
      <Toc />
      <div
        className={`block 
        ${tocPinned ? moveRight : isDrawerOpen ? moveLeft : centerView}
        `}>
        <SectionContent />
        <SectionViewNavigationButtons />
      </div>
    </div>
  );
}
