'use client';
import React from 'react';
import ViewerPlaceholder from '../placeholder/viewerPlaceholder';
import useTOCSections from '@components/toc/useTOCSections';
import Viewer from '../viewer/viewer';

export default function SectionContent() {
  const {isLoading, sectionsIdsToDisplay} = useTOCSections();

  if (isLoading) return <ViewerPlaceholder />;

  return (
    <>
      <div className="reading-environment-wrapper lg:mt-[122px] lg:rounded-t-lg">
        <Viewer
          enableContextMenu={true}
          wrapperClassName="w-full lg:px-8 py-16"
          explicitSectionsIdsToDisplay={sectionsIdsToDisplay}
        />
      </div>
    </>
  );
}
