'use client';
import Button from '@components/buttons/button';
import {ArrowLeft, ArrowRight} from 'iconsax-react';
import React from 'react';
import useTOCSections from '@components/toc/useTOCSections';
import ButtonLink from '@components/buttons/buttonLink';

export default function SectionViewNavigationButtons() {
  const {
    updateActiveSection,
    getSection,
    prevSectionButtonDisabled,
    nextSectionButtonDisabled,
  } = useTOCSections();

  const previousSection = getSection('Previous');
  const nextSection = getSection('Next');

  return (
    <div className="space-y-2 md:space-y-0 md:flex md:justify-between md:items-center pb-20 md:pb-24 lg:pb-6 sm:px-4 md:px-6 lg:mb-16 reading-environment-wrapper rounded-b-lg">
      <ButtonLink
        className="btn-md btn-full md:w-auto"
        variant="secondary"
        onClick={() => updateActiveSection(previousSection)}
        href="" // this controls parent sections only, which don't use anchors in section view
        disabled={prevSectionButtonDisabled}>
        <ArrowLeft variant="Bold" />
        Previous section
      </ButtonLink>
      <ButtonLink
        className="btn-md btn-full md:w-auto"
        variant="primary"
        onClick={() => updateActiveSection(nextSection)}
        href="" // this controls parent sections only, which don't use anchors in section view
        disabled={nextSectionButtonDisabled}>
        Next section
        <ArrowRight variant="Bold" />
      </ButtonLink>
    </div>
  );
}
