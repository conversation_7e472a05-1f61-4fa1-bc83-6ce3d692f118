import React from 'react';
import {useRouter} from 'next/navigation';
import {useAnalytics} from 'use-analytics';
import UserService from '@libs/services/user/service';

export default function useAccount() {
  const router = useRouter();
  const {track, reset} = useAnalytics();
  const userService = new UserService();

  const [processing, setProcessing] = React.useState(false);
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [errorOccurred, setErrorOccurred] = React.useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const signOutAndClearStore = () => {
    track('Logout');
    reset();
    router.push('/logout?deleted-account=true');
  };

  const deleteAccount = () => {
    setProcessing(true);
    userService
      .deleteUser()
      .then(deletedSuccessfully => {
        if (!deletedSuccessfully) {
          //Catches errors return from server response
          setErrorOccurred(true);
        } else {
          signOutAndClearStore();
        }
      })
      // Catches server errors
      .catch(e => setErrorOccurred(true))
      .finally(() => {
        setProcessing(false);
        closeModal();
      });
  };

  return {
    processing,
    isModalOpen,
    openModal,
    closeModal,
    onDeleteConfirmation: deleteAccount,
    errorOccurred,
  };
}
