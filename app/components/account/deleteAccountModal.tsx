import Button from '@components/buttons/button';
import BaseModal from '@components/modals/BaseModal';
import ModalButtonsContainer from '@components/modals/ModalButtonsContainer';
import {Warning2} from 'iconsax-react';

const DeleteAccountModal = ({
  isOpen,
  closeModal,
  onDelete,
  processing,
}: {
  isOpen: boolean;
  closeModal: () => void;
  onDelete: () => void;
  processing: boolean;
}) => {
  return (
    <BaseModal
      isOpen={isOpen}
      onClose={closeModal}
      width="sm:w-[550px]"
      accessibilityLabel="Confirm account deletion">
      <div className="p-2  text-center space-y-6">
        <div>
          <div className="hidden sm:flex justify-center items-center pb-1">
            <Warning2 variant="Bulk" className="w-12 h-12 theme-warning" />
          </div>
          <h1 className=" text-center text-2xl font-bold leading-relaxed ">
            Confirm account deletion
          </h1>
        </div>
        <div className="text-xl">
          <p className="pb-5">
            Are you sure you want to delete your Tailo account?
          </p>
          <p>
            We will mark your account for deletion and it will be completely
            erased after 30 days. You can cancel this process at any point
            during this 30 day period.
          </p>
        </div>
        <ModalButtonsContainer>
          <Button
            type="submit"
            variant="secondary"
            onClick={closeModal}
            className="btn-full">
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onDelete}
            className="btn-full"
            loading={processing}>
            Delete <span className="hidden sm:inline-block">account</span>
          </Button>
        </ModalButtonsContainer>
      </div>
    </BaseModal>
  );
};

export default DeleteAccountModal;
