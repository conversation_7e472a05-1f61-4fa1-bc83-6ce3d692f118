'use client';

import React from 'react';
import Initials from '@components/icons/initials';
import useUserDataFromSession, {
  UserType,
} from '@libs/utils/user/useUserDataFromSession';

function AccountHeading() {
  const {userFirstName, userLastName, userType} = useUserDataFromSession();
  const fullName = `${userFirstName}  ${userLastName}`;
  return (
    <div className="flex-col justify-start items-center gap-2 flex py-4">
      <Initials size="large" />
      <div className="flex flex-col justify-start items-center ">
        <h1 className=" text-2xl font-bold leading-10 ">{fullName}</h1>
        <p className=" text-base font-normal leading-normal">
          {userType === UserType.BetaUser ? (
            <>
              <span aria-hidden>👑 </span>
              {'BETA User'}
            </>
          ) : (
            'ADMIN User'
          )}
        </p>
      </div>
    </div>
  );
}

export default AccountHeading;
