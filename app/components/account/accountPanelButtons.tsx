import {useAbility} from '@casl/react';
import DropdownItemButton from '@components/menus/dropdown/dropdownItems/dropdownItemButton';
import DropdownItemLink from '@components/menus/dropdown/dropdownItems/dropdownItemLink';
import useAccountMenu from '@components/nav/account/useAccountMenu';
import {PermissionsContext} from '@components/providers/permissions/provider';
import {AppPrivateRoutes} from '@libs/services/auth/routes';
import {LogoutCurve, ProfileCircle, Security} from 'iconsax-react';

export const AccountPanelButtons = () => {
  const {handleLogOut, handleAccountNavigation} = useAccountMenu();
  const ability = useAbility(PermissionsContext);

  return (
    <>
      {ability.can('read', 'account') && (
        <DropdownItemButton
          forBottomDrawer={true}
          onClick={handleAccountNavigation}
          sharedStyles="btn btn-sm btn-tertiary w-full px-0">
          <ProfileCircle variant="Bold" />
          My Details
        </DropdownItemButton>
      )}
      {ability.can('read', 'admin') && (
        <DropdownItemLink
          href={AppPrivateRoutes.Admin}
          forBottomDrawer={true}
          sharedStyles="btn btn-sm btn-tertiary w-full px-0">
          <Security variant="Bold" />
          Admin
        </DropdownItemLink>
      )}

      <DropdownItemButton
        forBottomDrawer={true}
        onClick={handleLogOut}
        sharedStyles="btn btn-sm btn-tertiary w-full px-0">
        <LogoutCurve variant="Bold" />
        Log out
      </DropdownItemButton>
    </>
  );
};
