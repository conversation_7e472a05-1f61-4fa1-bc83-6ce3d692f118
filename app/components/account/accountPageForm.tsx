'use client';

import React from 'react';
import Button from '@components/buttons/button';
import TextInput from '@components/forms/inputs/textInput';
import useUserDataFromSession from '@libs/utils/user/useUserDataFromSession';
import useAccount from './useAccount';
import DeleteAccountModal from './deleteAccountModal';
import GdprConsentBox from './gdprConsentBox';

function AccountForm() {
  const {userFirstName, userLastName, userEmail} = useUserDataFromSession();
  const {processing, isModalOpen, openModal, closeModal, onDeleteConfirmation} =
    useAccount();

  return (
    <>
      <form className="form-control w-full" onSubmit={e => e.preventDefault()}>
        <TextInput
          id="firstName"
          value={userFirstName}
          label="First name"
          onChange={undefined}
          disabled={true}
          className="w-full mb-4"
        />
        <TextInput
          id="LastName"
          value={userLastName}
          label="Last name"
          onChange={undefined}
          disabled={true}
          className="w-full mb-4"
        />
        <TextInput
          id="email"
          value={userEmail}
          label="Email address"
          onChange={undefined}
          disabled={true}
          className="w-full mb-4"
        />
        <GdprConsentBox />
        <div className=" w-full block sm:flex flex-row flex:space-between sm:flex-row sm:justify-between sm:items-center sm:flex-wrap">
          <Button
            size="sm"
            className=""
            variant="destructive"
            onClick={openModal}>
            Delete my account
          </Button>
          <Button
            size="sm"
            className=""
            disabled={true}
            variant="primary">
            Save changes
          </Button>
        </div>
      </form>
      <DeleteAccountModal
        closeModal={closeModal}
        isOpen={isModalOpen}
        onDelete={onDeleteConfirmation}
        processing={processing}
      />
    </>
  );
}

export default AccountForm;
