'use client';

import React from 'react';
import MessagesPanel from '@components/messagesPanel/messagesPanel';
import useAccount from './useAccount';

const deletionErrorMessage = (
  <>
    <p>There was an error in deleting your account.</p>
    <p className="pt-6">
      If you would like your account deleted, please contact us at &nbsp;
      <a className="link" href="mailto:<EMAIL>">
        <EMAIL>
      </a>
      &nbsp; and let us know.
    </p>
  </>
);

export default function AccountMessages() {
  const {errorOccurred} = useAccount();
  return (
    <div className="space-y-5">
      {errorOccurred && (
        <MessagesPanel
          type="danger"
          messageTitle="Account deletion error"
          message={deletionErrorMessage}
          hideCloseButton={true}
        />
      )}
      <MessagesPanel
        type="warning"
        messageTitle="Details can't yet be changed"
        message="Profile details are not currently editable. This will be updated soon!"
        hideCloseButton={true}
      />
    </div>
  );
}
