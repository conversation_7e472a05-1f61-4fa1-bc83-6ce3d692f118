import React from 'react';

type Shadows =
  | 'shadow-sm-d'
  | 'shadow-d'
  | 'shadow-md-d'
  | 'shadow-lg-d'
  | 'shadow-xl-d'
  | 'shadow-2xl-d'
  | 'shadow-sm-u'
  | 'shadow-u'
  | 'shadow-md-u'
  | 'shadow-lg-u'
  | 'shadow-xl-u'
  | 'shadow-2xl-u'
  | 'shadow-l'
  | 'shadow-md-l'
  | 'shadow-solid-d'
  | 'shadow-solid-md-d';

export default function ShadowBox({shadow}: {shadow: Shadows}) {
  return (
    <div
      className={`w-40 h-24 inline-flex justify-center items-center mx-4 my-12 bg-slate-100  ${shadow}`}>
      {shadow}
    </div>
  );
}
