import type {<PERSON><PERSON>, <PERSON>Obj} from '@storybook/react';
import ShadowBox from './shadowBox';
const meta: Meta<typeof ShadowBox> = {
  title: 'Shadows',
  component: ShadowBox,
};
export default meta;
type Story = StoryObj<typeof ShadowBox>;

// The shadows don't show on dark background. It is intentional and should work only for lighter components in that theme.
export const AllDarkTheme: Story = {
  render: () => (
    <div data-theme="dark">
      <div className="app-bg">
        <ShadowBox shadow="shadow-sm-d" />
        <ShadowBox shadow="shadow-d" />
        <ShadowBox shadow="shadow-md-d" />
        <ShadowBox shadow="shadow-lg-d" />
        <ShadowBox shadow="shadow-xl-d" />
        <ShadowBox shadow="shadow-2xl-d" />
        <br />
        <ShadowBox shadow="shadow-sm-u" />
        <ShadowBox shadow="shadow-u" />
        <ShadowBox shadow="shadow-md-u" />
        <ShadowBox shadow="shadow-lg-u" />
        <ShadowBox shadow="shadow-xl-u" />
        <ShadowBox shadow="shadow-2xl-u" />
        <br />
        <ShadowBox shadow="shadow-l" />
        <ShadowBox shadow="shadow-md-l" />
        <ShadowBox shadow="shadow-solid-d" />
        <ShadowBox shadow="shadow-solid-md-d" />
      </div>
    </div>
  ),
};
