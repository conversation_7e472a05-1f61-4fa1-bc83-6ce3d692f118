import React from 'react';

export default function Placeholder() {
  return (
    <div className="animate-pulse">
      <div className="opposite-bg w-full p-12 rounded-md space-y-3 opacity-40  mt-4">
        <div className="w-48 h-8 panel-bg rounded-md" />
        <div className="w-3/4 h-8 panel-bg rounded-md" />
        <div className="flex-1 h-8 panel-bg rounded-md" />
        <div className="w-8 h-8 panel-bg rounded-md" />
        <div className="h-12" />
        <div className="w-1/4 h-8 panel-bg rounded-md" />
        <div className="w-3/4 h-8 panel-bg rounded-md" />
        <div className="w-1/4 h-8 panel-bg rounded-md" />
        <div className="flex-1 h-8 panel-bg rounded-md" />
        <div className="w-1/2 h-8 panel-bg rounded-md" />
      </div>
      <p className="sr-only">Loading summary</p>
    </div>
  );
}
