'use client';
import useSummaries from '@libs/services/summary/useSummaries';
import {useParams} from 'next/navigation';
import React from 'react';
import P from '@components/document/viewer/elements/typography/p';
import BasePill from '@components/pills/basePill';

export default function PromptInfo() {
  const params = useParams();
  const documentId = params.id as string;
  const summaryId = params.summaryId as string;

  const {summaryById} = useSummaries(documentId);
  const summaryInfo = summaryById(summaryId);

  if (!summaryInfo) return null;

  const {prompt, status, builderMethod, errorDetails} = summaryInfo;

  return (
    <div className="flex flex-col">
      <h2 className="sr-only">Prompt</h2>
      <div className="panel p-6">
        <P>{prompt}</P>
        <div className="sm:flex items-center border-t opposite-border w-full mt-4 pt-2">
          <div className="flex flex-1">
            <h3>Prompt type:</h3>
            <p className="pl-2 capitalize">{builderMethod}</p>
          </div>
          <div className="flex flex-1 sm:justify-end items-center pt-3">
            <h3 className="pr-3">Status:</h3>
            <BasePill variant={status} errorMessage={errorDetails?.message} />
          </div>
        </div>
      </div>
    </div>
  );
}
