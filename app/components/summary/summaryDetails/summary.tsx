import React from 'react';
import SummaryText from './summaryText';
import useSummaries from '@libs/services/summary/useSummaries';
import {useParams} from 'next/navigation';
import {IStatus} from '@libs/store/status/types';
import P from '@components/document/viewer/elements/typography/p';

export default function Summary() {
  const params = useParams();
  const documentId = params.id as string;
  const summaryId = params.summaryId as string;

  const {summaryById} = useSummaries(documentId);
  const summaryInfo = summaryById(summaryId);
  if (!summaryInfo) return null;

  // Allows for calling the summary endpoint for a specific id only when the status is set to READY.
  switch (summaryInfo.status) {
    case IStatus.Ready:
    case IStatus.Complete:
      return <SummaryText />;
    case IStatus.Error:
      return (
        <div className="pt-12">
          <P> Something went wrong!</P>
        </div>
      );
    default:
      return (
        <div className="pt-12">
          <P> We are processing your summarisation...</P>
        </div>
      );
  }
}
