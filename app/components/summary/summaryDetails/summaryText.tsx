'use client';
import useSummary from '@libs/services/summary/useSummary';
import {useParams} from 'next/navigation';
import React from 'react';
import Error from '@components/errorBoundary/error';
import Placeholder from './placeholder';
import P from '@components/document/viewer/elements/typography/p';
import Header from '@components/document/viewer/elements/typography/headers';

export default function SummaryText() {
  const params = useParams();
  const summaryId = params.summaryId as string;

  const {isLoading, error, summaryDetails} = useSummary(summaryId);

  if (isLoading) return <Placeholder />;

  if (error) return <Error error={error} />;

  //Temporary formatting on FE in agreement with BE;
  const textSectioned = summaryDetails?.summary.split(/\r?\n|\r|\n/g) || [];

  return (
    <div className="py-4 px-2">
      <Header variant="h2">Summarisation</Header>
      {textSectioned.map((p, index) => (
        <P key={p + index}>{p}</P>
      ))}
    </div>
  );
}
