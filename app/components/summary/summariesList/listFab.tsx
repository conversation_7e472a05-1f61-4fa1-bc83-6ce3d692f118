'use client';
import React from 'react';
import Fab from '@components/buttons/fab';
import {clearanceForTabs} from '@components/document/contents/contentsFab';
import BottomSheet from '@components/modals/bottomSheet/bottomSheet';
import {FaListUl} from 'react-icons/fa';
import Content from './content';
import {DRAWER_ID} from '@libs/store/drawer/types';
import useDrawer from '@libs/store/drawer';

export default function ListFab() {
  const {openDrawer, closeDrawer, drawStateById} = useDrawer();
  const drawerId = DRAWER_ID.SUMMARIES_HISTORY;
  const handleOnClick = () => {
    drawStateById(drawerId) ? closeDrawer(drawerId) : openDrawer(drawerId);
  };
  return (
    <>
      <Fab
        size="large"
        onClick={handleOnClick}
        fab={true}
        accessibilityLabel={'Summaries list opens in drawer'}
        className={clearanceForTabs}>
        <FaListUl />
      </Fab>
      <BottomSheet drawerId={drawerId}>
        <h2 className="font-semibold pb-4">Summaries history</h2>
        <Content closeDrawer={handleOnClick} />
      </BottomSheet>
    </>
  );
}
