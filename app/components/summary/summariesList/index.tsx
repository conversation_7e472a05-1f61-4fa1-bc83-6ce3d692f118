import React from 'react';
import Content from './content';
import ListFab from './listFab';
import {FaListUl} from 'react-icons/fa';

export default function SummariesList() {
  return (
    <>
      <aside className="hidden lg:flex flex-col sticky top-24 w-[300px] h-screen pt-4 pl-6 overflow-hidden ">
        <h2
          id="summaries-list"
          className="font-semibold p-2 panel uppercase flex space-x-3 items-center">
          <FaListUl />
          <span>Summary History</span>
        </h2>
        <div className="h-full py-4  overflow-scroll">
          <Content />
        </div>
      </aside>
      <div className="lg:hidden">
        <ListFab />
      </div>
    </>
  );
}
