'use client';
import {AppDynamicRoutes} from '@libs/services/auth/routes';
import useSummaries from '@libs/services/summary/useSummaries';
import {formatDateWithAt} from '@libs/utils/dates/dateFormats';
import Link from 'next/link';
import {useParams} from 'next/navigation';
import React from 'react';

export default function Content({closeDrawer}: {closeDrawer?: () => void}) {
  const params = useParams();
  const documentId = params.id as string;
  const summaryId = params.summaryId;
  const {summaries} = useSummaries(documentId);

  if (!summaries) return null;

  return (
    <ul aria-labelledby="summaries-list">
      {summaries.map((summary, index) => {
        const href = AppDynamicRoutes.summary(documentId, summary.id);
        const isActive = summaryId === summary.id;
        return (
          <Link href={href} passHref legacyBehavior key={summary.id}>
            <a
              onClick={closeDrawer}
              href={href}
              className={`underline line-clamp-2 py-1 ${
                isActive ? 'opacity-100' : 'opacity-50'
              }`}>
              <span className="inline-flex panel w-8 h-8 items-center justify-center mr-2">
                {index + 1}
              </span>
              {formatDateWithAt(summary.createdAt)}
            </a>
          </Link>
        );
      })}
    </ul>
  );
}
