import {isEmail} from 'react-multi-email';
import {useState, ChangeEvent} from 'react';
import UserService from '@libs/services/user/service';
import {AppPublicRoutes} from '@libs/services/auth/routes';
import {useRouter, useSearchParams} from 'next/navigation';
import {useOnboardingAnalytics} from '@components/onboarding/useOnboardingAnalytics';

function useSignIn() {
  const userService = new UserService();

  const searchParams = useSearchParams();
  const {trackMagicLinkSignin} = useOnboardingAnalytics();
  const email = searchParams.get('email');
  const hasDeletedAccountParam = searchParams.get('deleted-account');
  const [emailValue, setEmailValue] = useState(email || '');
  const [error, setError] = useState<string | undefined>();
  const [processing, setProcessing] = useState(false);
  const router = useRouter();

  const updateEmailValue = (e: ChangeEvent<HTMLInputElement>) => {
    if (error) setError('');
    setEmailValue(e.currentTarget.value);
  };

  async function goToVerifyEmail() {
    if (!isEmail(emailValue)) {
      return setError('Please enter a valid email address.');
    }
    setProcessing(true);
    userService
      .sendUserEmailForMagicLink(emailValue, true, false)
      .then(res => {
        if (res && res.message === 'OK') {
          router.push(
            `${AppPublicRoutes.CheckEmail}?email=${encodeURIComponent(
              emailValue,
            )}`,
          );
        } else if (res && res.code === 5) {
          setError(res.message);
        }
      })
      .catch(e => {
        //TODO: This will need refined at some point
        setError('There has been an issue signing in with this email address.');
        console.log('There has been an error signing in.', e);
      })
      .finally(() => {
        trackMagicLinkSignin(error);
        setProcessing(false);
      });
  }

  return {
    emailValue,
    error,
    goToVerifyEmail,
    updateEmailValue,
    hasDeletedAccountParam,
    processing,
  };
}

export default useSignIn;
