'use client';

import Link from 'next/link';
import useSignIn from './useSignIn';
import React, {useEffect} from 'react';
import Button from '@components/buttons/button';
import TextInput from '../forms/inputs/textInput';
import {AppPublicRoutes} from '@libs/services/auth/routes';
import MessagesPanel from '@components/messagesPanel/messagesPanel';
import {useOnboardingAnalytics} from '@components/onboarding/useOnboardingAnalytics';

const deletedAccountMessage = (
  <>
    <p className="pb-6">
      Your account has been marked for deletion and will be erased completely in
      30 days.
    </p>
    <p>
      You can log back in to prevent this from happening, any time between now
      and <span className="font-semibold"> &lt; todays date + 30 &gt;</span>.
    </p>
  </>
);

const SignInPanel = () => {
  const {
    emailValue,
    error,
    goToVerifyEmail,
    updateEmailValue,
    hasDeletedAccountParam,
    processing,
  } = useSignIn();

  const {trackOnboardingPageView, trackCreateAccountPress} =
    useOnboardingAnalytics();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    goToVerifyEmail();
  };

  useEffect(() => {
    trackOnboardingPageView('MagicLinkSignin');
  }, [trackOnboardingPageView]);

  return (
    <div className="flex flex-col items-center w-80 sm:w-96">
      {hasDeletedAccountParam && (
        <div className="w-full pb-6">
          <MessagesPanel
            type="danger"
            messageTitle="Account marked for deletion"
            message={deletedAccountMessage}
            hideCloseButton
          />
        </div>
      )}
      <form
        className="w-full p-4 sm:p-6 login-panel border flex-col justify-start items-center gap-4 inline-flex mb-4 sm:mb-8"
        onSubmit={handleSubmit}>
        <h1 className="text-center text-slate-50 text-2xl font-bold">
          Sign in to Tailo
        </h1>

        {error && (
          <MessagesPanel
            type="danger"
            messageTitle="Sign in error"
            message={error}
            hideCloseButton
          />
        )}

        <TextInput
          id="sign-in-email-address"
          label="Email address"
          value={emailValue}
          onChange={updateEmailValue}
          required={true}
          type="email"
        />
        <Button
          loading={processing}
          variant="primary"
          disabled={emailValue === '' || processing}
          className="btn-full"
          type="submit">
          <span className="hidden sm:block">Sign in with email</span>
          <span className="sm:hidden">Sign in</span>
        </Button>
      </form>
      <h2 className="text-center text-white text-xl font-bold leading=[25px]">
        {"Don't have an account?"}
      </h2>
      <Link
        className="text-center text-white text-base font-semibold underline leading-normal"
        href={AppPublicRoutes.OnboardingStart}
        onClick={trackCreateAccountPress}>
        Create a new account
      </Link>
    </div>
  );
};

export default SignInPanel;
