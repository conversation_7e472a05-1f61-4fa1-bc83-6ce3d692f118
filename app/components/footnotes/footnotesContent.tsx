import React from 'react';
import {Footnote} from './types';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';

interface FootnotesContentProps {
  footnotes: Footnote[];
}

const FootnotesContent: React.FC<FootnotesContentProps> = ({footnotes}) => {
  const {isMobile} = useIsMobile();

  return (
    <div className={`p-4 ${isMobile ? 'pb-[115px]' : ''}`}>
      {footnotes.map((footnote, index) => (
        <div key={index} className="mb-4">
          {footnote.items.map((item, idx) => (
            <div key={idx} className="flex gap-4">
              <p className="doc-text-default text-sm">{item.superscript}</p>
              <p className="doc-text-default text-xl leading-[30px]">
                {item.text}
              </p>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default FootnotesContent;
