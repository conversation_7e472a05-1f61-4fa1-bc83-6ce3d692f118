'use client';
import BaseModal from '@components/modals/BaseModal';
import {CloseCircle} from 'iconsax-react';
import TailoIconButton from '@components/buttons/tailoIconButton';
import FootnotesContent from './footnotesContent';
import {useFootnotes} from '@contexts/footnotesContext';

export default function FootnotesModal({
  isOpen,
  closeModal,
}: {
  isOpen: boolean;
  closeModal: () => void;
}) {
  // const {footnotes} = useFootnotes();

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={closeModal}
      width="w-[50%]"
      accessibilityLabel="Quit footnote modal?">
      <div className="flex-col">
        <div className="text-center flex justify-between items-center pb-1">
          <h3 className="text-2xl font-bold leading-relaxed">Footnotes</h3>
          <TailoIconButton
            size="medium"
            label="close footnotes modal"
            handler={closeModal}>
            <CloseCircle className="w-8 h-8" />
          </TailoIconButton>
        </div>
        {/* <FootnotesContent footnotes={footnotes} /> */}
      </div>
    </BaseModal>
  );
}
