import {FootnoteRelationship} from './types';

export interface FootnotesDocLinkProps {
  value: string;
  onPress: (relations?: FootnoteRelationship[]) => void;
  relationships?: FootnoteRelationship[];
}

export default function FootnotesDocLink({
  value,
  onPress,
  relationships,
}: FootnotesDocLinkProps) {
  return (
    <span className="inline-flex relative items-center -top-2 min-w-[1em] align-middle">
      <span
        className="doc-link-default !border-b-0 flex flex-col justify-between items-center text-sm font-semibold doc-text-default rounded-md pt-1 cursor-pointer h-full py-[0.1em] min-w-[1.2em]"
        onClick={() => onPress(relationships)}>
        {/* Text content positioned at the top */}
        <span className="leading-none px-2">{value}</span>
      </span>
    </span>
  );
}
