import React, {<PERSON>} from 'react';
import OnboardingNavBar from './navbar';
import OnboardingStep from './steps/onboardingStep';
import OnboardingDocumentPreview from './documentPreview/onboardingDocumentPreview';
import OnboardingSidebar from './sidebar';

interface OnboardingDynamicPageTemplateProps {
  step: string;
}

const OnboardingDynamicPageTemplate: FC<OnboardingDynamicPageTemplateProps> = ({
  step,
}) => {
  return (
    <div className="onboarding-page flex">
      <div className="h-screen overflow-auto doc-scrollbar flex flex-col flex-1 pb-8 onboarding-page-content">
        <div>
          <OnboardingNavBar />
        </div>
        <OnboardingStep step={step} />
        {/* We want the content to be at least 500px height on smaller screens to facilitate reading */}
        <div className="flex-1 lg:min-h-[500px]">
          <OnboardingDocumentPreview />
        </div>
      </div>
      <OnboardingSidebar />
    </div>
  );
};

export default OnboardingDynamicPageTemplate;
