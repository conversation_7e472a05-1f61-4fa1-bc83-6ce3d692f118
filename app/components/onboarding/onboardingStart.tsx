'use client';

import <PERSON><PERSON> from 'lottie-react';
import React, {useEffect} from 'react';
import {useOnboardingAnalytics} from './useOnboardingAnalytics';
import Animation from 'public/static/animations/welcome-screen-animation.json';

import {
  BrushSquare,
  DocumentFilter,
  Book1,
  Headphone,
  Watch,
} from 'iconsax-react';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
import {OnboardingStartButtons} from './onboardingStartButtons';
import useSignUp from './register/useSignUp';

export default function OnboardingStart() {
  const {trackOnboardingPageView} = useOnboardingAnalytics();
  const {isLargeTablet} = useIsMobile();
  const {
    trackingId,
    getTicketData,
    setValidTicket,
    updateEmailValue,
    updateTrackingId,
  } = useSignUp();

  useEffect(() => {
    trackingId && trackOnboardingPageView('StartOnboarding', trackingId);
  }, [trackingId, trackOnboardingPageView]);

  useEffect(() => {
    getTicketData().then(res => {
      if (!res) {
        setValidTicket(false);
        return;
      }
      updateEmailValue(res.emailAddress);
      updateTrackingId(res.trackingId);
    });
  }, []);

  return (
    <div className="min-w-full min-h-screen md:flex md:justify-center md:items-center">
      <div className="w-full lg:max-w-[67rem] lg:flex lg:flex-col">
        <div className="w-full lg:max-w-[67rem] lg:flex">
          <div className="md:mr-10 px-4 py-4 pb-0">
            <h1 className="font-bold text-3xl leading-10 lg:text-4xl lg:leading-[3rem] pb-4">
              Transform your reading experience with Tailo
            </h1>

            <p className="text-base font-normal mb-8">
              <strong>Every reader is unique</strong>. Tailo offers a flexible
              reading environment with visual customisation options and
              AI-powered tools to help you read.
            </p>

            <ul className="md:grid md:gap-3 md:grid-cols-2 mb-8">
              <li className="mb-2 md:mb-0">
                <BrushSquare className="inline mr-2 text-tailo-green-500" />{' '}
                Customisable interface
              </li>

              <li className="mb-2 md:mb-0">
                <DocumentFilter className="inline mr-2 text-tailo-green-500" />{' '}
                AI-generated summaries
              </li>

              <li className="mb-2 md:mb-0">
                <Watch className="inline mr-2 text-tailo-green-500" /> Reading
                time estimates
              </li>

              <li className="mb-2 md:mb-0">
                <Book1 className="inline mr-2 text-tailo-green-500" /> Word
                definition
              </li>

              <li className="mb-2 md:mb-0">
                <Headphone className="inline mr-2 text-tailo-green-500" />{' '}
                Text-to-speech
              </li>

              <li className="mb-2 md:mb-0">
                <BrushSquare className="inline mr-2 text-tailo-green-500" /> and
                many more to come...
              </li>
            </ul>

            <p>
              We&apos;ll start by personalising your reading experience. You can
              adjust these anytime in settings.
            </p>
            {isLargeTablet && <OnboardingStartButtons />}
          </div>

          <div className="px-2 py-2">
            <Lottie
              autoplay={true}
              animationData={Animation}
              className="w-100 h-100 self-center"
              role="img"
              aria-label="An animation of a confetti cannon"
            />
          </div>
        </div>
        {!isLargeTablet && <OnboardingStartButtons />}
      </div>
    </div>
  );
}
