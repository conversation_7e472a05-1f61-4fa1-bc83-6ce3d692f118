'use client';

import React from 'react';
import SvgStepLink from '@components/buttons/custom/onboardingSteps/desktop/svgStepLink';
import {useOnboardingNavigation} from '../useOnboardingNavigation';
import ButtonLink from '@components/buttons/buttonLink';
import {AppPublicRoutes} from '@libs/services/auth/routes';
import {useOnboardingAnalytics} from '@components/onboarding/useOnboardingAnalytics';
import useSignUp from '@components/onboarding/register/useSignUp';

export default function DesktopOnboardingNavigationSteps() {
  const {
    steps,
    stepsState,
    currentStepHref,
    getStepStateValue,
    completedAllSteps,
    handleBackToSummary,
  } = useOnboardingNavigation();
  const {ticketValue} = useSignUp();
  const encodedTicket = encodeURIComponent(ticketValue);

  return (
    <div className="onboarding-step-nav-desktop-container overflow-x-hidden overflow-auto border-l separator hidden lg:flex w-80 min-h-screen h-full pt-6 justify-center">
      <ul
        className="space-y-3"
        aria-label="Desktop onboarding navigation steps">
        {steps.map(step => (
          <li key={step.id}>
            <SvgStepLink
              href={step.href}
              heading={step.desktopStepLabel}
              value={getStepStateValue(step.id) as string}
              extendTab={step.extendTab}
              completed={stepsState[step.href]?.completed}
              current={step.href === currentStepHref}
            />
          </li>
        ))}
        {completedAllSteps && (
          <li className="flex justify-center pt-4">
            <ButtonLink
              href={
                AppPublicRoutes.OnboardingEnd +
                (encodedTicket ? `?ticket=${encodedTicket}` : '')
              }
              variant="secondary"
              className="btn btn-md btn-full"
              onClick={handleBackToSummary}>
              Back to summary
            </ButtonLink>
          </li>
        )}
      </ul>
    </div>
  );
}
