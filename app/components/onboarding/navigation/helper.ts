import {Step} from '@libs/store/onboarding/types';
import {INavigationStep} from './types';

const tabExtensionByStep: {[key: string]: number} = {
  [Step.Theme]: 0,
  [Step.Font]: -5,
  [Step.FontSize]: 5,
  [Step.LineSpacing]: 13,
  [Step.Alignment]: 9,
};
const desktopStepLabel: {[key: string]: string} = {
  [Step.Theme]: 'Theme',
  [Step.Font]: 'Font',
  [Step.FontSize]: 'Text size',
  [Step.LineSpacing]: 'Line spacing',
  [Step.Alignment]: 'Alignment',
};
const mobileStepLabel: {[key: string]: string} = {
  [Step.Theme]: 'Theme',
  [Step.Font]: 'Font',
  [Step.FontSize]: 'Text',
  [Step.LineSpacing]: 'Spacing',
  [Step.Alignment]: 'Alignment',
};

export function getNavigationSteps() {
  const navigationSteps: INavigationStep[] = [];
  (Object.entries(Step) as unknown as Array<keyof typeof Step>).forEach(
    (entry, index) => {
      const href = entry[1];
      navigationSteps.push({
        id: entry[0],
        index,
        href,
        extendTab: tabExtensionByStep[href],
        desktopStepLabel: desktopStepLabel[href],
        mobileStepLabel: mobileStepLabel[href],
      });
    },
  );
  return navigationSteps;
}
