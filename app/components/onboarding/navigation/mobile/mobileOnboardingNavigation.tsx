'use client';
import MobileStepTrackerLink from '@components/buttons/custom/onboardingSteps/mobile/mobileStepTrackerLink';
import {useOnboardingNavigation} from '../useOnboardingNavigation';

export const MOBILE_STEPS_ID = 'mobile-onboarding-steps';

export default function MobileOnboardingNavigation() {
  const {steps, currentStepHref, stepsState, currentStepIndex} =
    useOnboardingNavigation();

  return (
    // It's a part of the nav wrapper list item
    <li
      className="onboarding-step-nav-mobile-container flex w-full lg:hidden overflow-x-scroll overflow-y-hidden no-scrollbar relative"
      id={MOBILE_STEPS_ID}>
      <ul
        aria-label="mobile onboarding navigation steps"
        className="mobile-onboarding-nav flex w-full min-w-[400px] justify-between px-12 sm:px-16">
        {steps.map((step, index) => (
          //first item to have no flex-1
          <li
            key={step.id}
            className={`inline-flex ${index !== 0 ? 'flex-1' : ''}`}>
            <MobileStepTrackerLink
              step={index + 1}
              stepName={step.mobileStepLabel}
              href={step.href}
              completed={stepsState[step.href]?.completed}
              current={step.href === currentStepHref}
            />
          </li>
        ))}
      </ul>
    </li>
  );
}
