'use client';

import {useParams, usePathname, useRouter} from 'next/navigation';
import useNavigation, {
  ONBOARDING_STEPS_STORAGE_KEY,
} from '@libs/store/onboarding/navigation';
import useSettings from '@libs/store/settings';
import {getNavigationSteps} from './helper';
import React from 'react';
import {MOBILE_STEPS_ID} from './mobile/mobileOnboardingNavigation';
import {Step} from '@libs/store/onboarding/types';
import {AppPublicRoutes} from '@libs/services/auth/routes';
import {getThemeColours} from '../results/helper';
import {useOnboardingAnalytics} from '../useOnboardingAnalytics';

export function useOnboardingNavigation() {
  const steps = getNavigationSteps();
  const pathname = usePathname();
  const {trackBackToSummaryPress, trackExpandPreviewPress} =
    useOnboardingAnalytics();
  const params = useParams();
  const router = useRouter();
  const {settings} = useSettings();
  const {steps: stepsState, updateStep} = useNavigation();
  const currentStep = steps.find(step => step.href === params.step);
  const currentStepIndex = currentStep?.index || 0;
  const completedAllSteps = steps.every(
    step => stepsState[step.href].completed === true,
  );

  const nextStep = steps[currentStepIndex + 1];
  const previousStep = steps[currentStepIndex - 1];

  const updateCurrentStep = () => {
    if (currentStep) {
      const currentStepState = stepsState[currentStep.href];
      if (currentStepState && !currentStepState.completed) {
        const step = currentStep.href as Step;
        updateStep(step, {completed: true});
      }
    }
  };

  const handleOnNext = () => updateCurrentStep();

  const handleBackToSummary = () => {
    if (currentStep) {
      trackBackToSummaryPress(currentStep.desktopStepLabel);
    }
  };

  const handleExpandPreviewModal = () => {
    if (currentStep) {
      trackExpandPreviewPress(currentStep.desktopStepLabel);
    }
  };

  React.useEffect(() => {
    if (previousStep && currentStepIndex > 0) {
      //The jotai doesn't persist the state on first render, so we need to check if the step was completed grabbing the session storage - it's not a problem for most of the use case of the library but might be worth looking into alternatives
      const onboardingStepsSessionItem = sessionStorage.getItem(
        ONBOARDING_STEPS_STORAGE_KEY,
      );
      // If userS haven't started the onboarding or trying to access the steps out of the flow order we redirect them to the start
      if (!onboardingStepsSessionItem) {
        return router.push(AppPublicRoutes.OnboardingStart);
      } else {
        const stepsSessionState = JSON.parse(onboardingStepsSessionItem);
        // The current step is never completed until the Next button is clicked so we need to check the previous step
        const previousStepCompleted =
          stepsSessionState[previousStep.href].completed;
        if (!previousStepCompleted) {
          return router.push(AppPublicRoutes.OnboardingStart);
        }
      }
    }

    const mobileSteps = document.getElementById(MOBILE_STEPS_ID);

    if (mobileSteps) {
      if (currentStepIndex > Math.floor(steps.length / 2)) {
        mobileSteps.scrollLeft = window.innerWidth * 2;
      } else {
        mobileSteps.scrollLeft = 0;
      }
    }
  }, []);

  const getStepStateValue = (id: string) => {
    const value = settings[id as keyof typeof settings];
    if (id === 'Theme') {
      return getThemeColours(settings.Theme).displayName || value;
    }
    return value;
  };

  return {
    stepsState,
    currentStep,
    currentStepHref: params.step,
    settings,
    steps,
    currentStepIndex,
    nextStep,
    previousStep,
    completedAllSteps,
    handleOnNext,
    getStepStateValue,
    handleBackToSummary,
    handleExpandPreviewModal,
  };
}
