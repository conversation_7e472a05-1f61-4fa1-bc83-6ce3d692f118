'use client';
import useSettings from '@libs/store/settings';
import {useEffect} from 'react';
import {useOnboardingAnalytics} from '../useOnboardingAnalytics';
import {getThemeColours} from './helper';
import React from 'react';
import useSignUp from '../register/useSignUp';

export function useResultPanel() {
  const {Theme, Font, FontSize, LineSpacing, Alignment} = useSettings();
  const {trackingId} = useSignUp();

  const selectedThemeColours = getThemeColours(Theme);

  const {trackOnboardingPageView} = useOnboardingAnalytics();

  const [collapseContainer, setCollapseContainer] = React.useState(true);

  const toggleCollapseContainer = () => setCollapseContainer(prev => !prev);

  useEffect(() => {
    trackingId && trackOnboardingPageView('OnboardingEnd', trackingId);
  }, [trackingId, trackOnboardingPageView]);

  return {
    selectedThemeColours,
    Font,
    FontSize,
    LineSpacing,
    Alignment,
    collapseContainer,
    toggleCollapseContainer,
  };
}
