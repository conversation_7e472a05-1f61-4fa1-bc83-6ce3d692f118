import ButtonLink from '@components/buttons/buttonLink';
import {
  Font,
  FontSize,
  LineSpacing,
  TextAlignment,
} from '@libs/store/settings/types';
import TextInput from '@components/forms/inputs/textInput';
import {ISelectedThemeDetails} from './types';
import {useOnboardingAnalytics} from '../useOnboardingAnalytics';
import {DocumentDownload} from 'iconsax-react';

interface ResultItemPanelProps {
  title: string;
  href: string;
  settingsItem: string | Font | FontSize | LineSpacing | TextAlignment;
  additionalContent?: string;
  selectedThemeColours?: ISelectedThemeDetails;
  copyTextHexButton?: JSX.Element;
  copyBgHexButton?: JSX.Element;
  disabled?: boolean;
}

export default function ResultItemPanel({
  title,
  href,
  settingsItem,
  additionalContent,
  selectedThemeColours,
  copyBgHexButton,
  copyTextHexButton,
  disabled = false,
}: ResultItemPanelProps) {
  const {trackChangeOnboardingSettings} = useOnboardingAnalytics();

  const handleChangeClick = () => {
    trackChangeOnboardingSettings(title);
  };
  const fontPanel = title === 'Font';
  return (
    <div className="w-full pb-6">
      <div className="inline-block !border-0">
        <p className="text-sm font-semibold modal-bg doc-text-default py-1 px-2 rounded-tl-md rounded-tr-md self-center justify-center !border-0">
          {title}
        </p>
      </div>
      <div className="modal-bg p-4 rounded-tr rounded-br rounded-bl !border-0">
        <div className="flex flex-row justify-between items-center">
          <div className="flex items-center gap-4">
            <h2 className="text-base lg:text-xl font-bold leading-tight lg:leading-loose doc-text-default">
              {settingsItem}
              {additionalContent && (
                <span className="font-normal">{` (${additionalContent})`}</span>
              )}
            </h2>
            {fontPanel && (
              <a
                href={`https://fonts.google.com/?query=${settingsItem}`}
                target="_blank" className="btn icon-btn-sm btn-tertiary">
                <DocumentDownload variant="Bold" />
              </a>
            )}
          </div>
          <ButtonLink
            disabled={disabled}
            variant="secondary"
            href={disabled ? '' : href}
            className="btn btn-sm"
            onClick={handleChangeClick}>
            Change
          </ButtonLink>
        </div>
        {selectedThemeColours && copyBgHexButton && copyTextHexButton && (
          <div className="flex flex-col lg:flex-row pt-4">
            <TextInput
              id="selected-text-colour"
              label="Text colour:"
              value={selectedThemeColours.textColour}
              iconRight={copyTextHexButton}
              className="font-semibold"
              inputStyling="opposite-bg doc-text-inverted border-0"
              readOnlyInput={true}
              disabled={disabled}
            />
            <TextInput
              id="selected-bg-colour"
              label="Background colour:"
              value={selectedThemeColours.bgColour}
              iconRight={copyBgHexButton}
              className="lg:pl-6 font-semibold"
              inputStyling="app-bg doc-text-default border-0"
              readOnlyInput={true}
              disabled={disabled}
            />
          </div>
        )}
      </div>
    </div>
  );
}
