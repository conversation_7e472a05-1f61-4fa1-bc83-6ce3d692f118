import {FontSize, LineSpacing, ThemeColour} from '@libs/store/settings/types';
import {ISelectedThemeDetails} from './types';
import DarkBg from 'public/static/images/backgrounds/dark-min.png';
import LightBg from 'public/static/images/backgrounds/light-min.png';
import YellowBg from 'public/static/images/backgrounds/yellow-min.png';
import PaleGreenBg from 'public/static/images/backgrounds/green-min.png';
import PaleBlueBg from 'public/static/images/backgrounds/blue-min.png';
import PalePinkBg from 'public/static/images/backgrounds/pink-min.png';
import PeachBg from 'public/static/images/backgrounds/peach-min.png';

export const getThemeColours = (theme: ThemeColour): ISelectedThemeDetails => {
  switch (theme) {
    case ThemeColour.Light:
      return {
        displayName: 'Light',
        textColour: '#020617',
        bgColour: '#FFFFFF',
        bgImage: LightBg,
      };
    case ThemeColour.Yellow:
      return {
        displayName: 'High Contrast Yellow',
        textColour: '#000000',
        bgColour: '#FACC15',
        bgImage: YellowBg,
      };
    case ThemeColour.PaleGreen:
      return {
        displayName: 'Pale Green',
        textColour: '#1A2E05',
        bgColour: '#ECFCCB',
        bgImage: PaleGreenBg,
      };
    case ThemeColour.PaleBlue:
      return {
        displayName: 'Pale Blue',
        textColour: '#172554',
        bgColour: '#DBEAFE',
        bgImage: PaleBlueBg,
      };
    case ThemeColour.PalePink:
      return {
        displayName: 'Pale Pink',
        textColour: '#4C0519',
        bgColour: '#FFE4E6',
        bgImage: PalePinkBg,
      };
    case ThemeColour.Peach:
      return {
        displayName: 'Peach',
        textColour: '#1B1A18',
        bgColour: '#FED7AA',
        bgImage: PeachBg,
      };
    default:
    case ThemeColour.Dark:
      return {
        displayName: 'Dark',
        textColour: '#F8FAFC',
        bgColour: '#020617',
        bgImage: DarkBg,
      };
  }
};

export const getTextSizePx = (textSize: FontSize) => {
  switch (textSize) {
    case FontSize.Small:
      return '16px';
    case FontSize.Large:
      return '24px';
    default:
    case FontSize.Medium:
      return '20px';
  }
};

export const getLineSpacing = (lineSpacing: LineSpacing) => {
  switch (lineSpacing) {
    case LineSpacing.Tight:
      return '1.25';
    case LineSpacing.Relaxed:
      return '1.625';
    default:
    case LineSpacing.Optimised:
      return '1.5';
  }
};
