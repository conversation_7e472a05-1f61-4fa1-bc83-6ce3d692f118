'use client';
import {AppDynamicRoutes} from '@libs/services/auth/routes';
import {Step} from '@libs/store/onboarding/types';
import {getLineSpacing, getTextSizePx} from './helper';
import ResultItemPanel from './resultItemPanel';
import CopyBgHexCodeButton from '@components/buttons/onboarding/copyBgHexCodeButton';
import CopyTextHexCodeButton from '@components/buttons/onboarding/copyTextHexCodeButton';
import Image from 'next/image';
import MobileCollapseContainer from './mobileCollapseContainer';
import {useResultPanel} from './useResultPanel';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
import useSignUp from '../register/useSignUp';

export default function ResultsPanel() {
  const {
    selectedThemeColours,
    Font,
    FontSize,
    LineSpacing,
    Alignment,
    collapseContainer,
    toggleCollapseContainer,
  } = useResultPanel();
  const {emailValue} = useSignUp();

  const {isLargeTablet} = useIsMobile();
  const disabled = isLargeTablet && collapseContainer;

  return (
    <div className="lg:rounded-2xl w-full lg:max-w-[40rem] h-full relative overflow-hidden">
      <MobileCollapseContainer
        collapseContainer={collapseContainer}
        toggleCollapseContainer={toggleCollapseContainer}>
        <Image
          alt=""
          src={selectedThemeColours.bgImage}
          layout="fill"
          objectFit="cover"
          objectPosition="center"
          className="z-10"
          priority={true}
        />
        <div className="relative z-20">
          <ResultItemPanel
            disabled={disabled}
            title="Theme"
            href={AppDynamicRoutes.onboarding(Step.Theme, emailValue)}
            settingsItem={selectedThemeColours.displayName}
            selectedThemeColours={selectedThemeColours}
            copyBgHexButton={<CopyBgHexCodeButton disabled={disabled} />}
            copyTextHexButton={<CopyTextHexCodeButton disabled={disabled} />}
          />
          <ResultItemPanel
            disabled={disabled}
            title="Font"
            href={AppDynamicRoutes.onboarding(Step.Font, emailValue)}
            settingsItem={Font}
          />
          <ResultItemPanel
            disabled={disabled}
            title="Text size"
            href={AppDynamicRoutes.onboarding(Step.FontSize, emailValue)}
            settingsItem={FontSize}
            additionalContent={getTextSizePx(FontSize)}
          />
          <ResultItemPanel
            disabled={disabled}
            title="Line spacing"
            href={AppDynamicRoutes.onboarding(Step.LineSpacing, emailValue)}
            settingsItem={LineSpacing}
            additionalContent={getLineSpacing(LineSpacing)}
          />
          <ResultItemPanel
            disabled={disabled}
            title="Alignment"
            href={AppDynamicRoutes.onboarding(Step.Alignment, emailValue)}
            settingsItem={Alignment}
          />
        </div>
      </MobileCollapseContainer>
    </div>
  );
}
