import Button from '@components/buttons/button';
import React from 'react';
import {ArrowUp2, ArrowDown2} from 'iconsax-react';

export default function MobileCollapseContainer({
  children,
  collapseContainer,
  toggleCollapseContainer,
}: {
  children: React.ReactNode;
  collapseContainer: boolean;
  toggleCollapseContainer: () => void;
}) {
  const containerHeightClasses = collapseContainer ? 'h-56' : 'h-[920px]'; // Replace with the'auto' value when the animation is disabled
  return (
    <>
      <div
        className={`${containerHeightClasses} w-full p-4 pb-16 lg:h-auto lg:h-lvh lg:px-6 lg:py-20 relative overflow-hidden transition-height duration-500 ease-in-out lg:transition-none`}
        aria-hidden={collapseContainer}>
        {children}
        {/* The cover prevents accessing the interactive items */}
        {collapseContainer && (
          <div className="lg:hidden absolute bg-gradient w-full h-full top-0 left-0 z-20" />
        )}
      </div>
      <div className="p-4 relative z-20 lg:hidden h-1">
        <Button
          variant="primary"
          size="sm"
          onClick={toggleCollapseContainer}
          className="w-full relative -top-[70px] box-border">
          {collapseContainer ? (
            <ArrowDown2 size="18" />
          ) : (
            <ArrowUp2 size="18" />
          )}
          {collapseContainer ? 'Show' : 'Hide'}
        </Button>
      </div>
    </>
  );
}
