import ButtonLink from '@components/buttons/buttonLink';
import {AppPublicRoutes, AppDynamicRoutes} from '@libs/services/auth/routes';
import {Step} from '@libs/store/onboarding/types';
import {ArrowSquareRight} from 'iconsax-react';
import {useOnboardingAnalytics} from './useOnboardingAnalytics';
import useSignUp from './register/useSignUp';
import Link from 'next/link';

export const OnboardingStartButtons = () => {
  const {trackOnboardingGetStarted, trackOnboardingSkip} =
    useOnboardingAnalytics();

  const {ticketValue} = useSignUp();
  const encodedTicket = encodeURIComponent(ticketValue);

  return (
    <>
      <div className="flex flex-col md:flex-row gap-4 lg:gap-6 mt-8">
        <ButtonLink
          variant="primary"
          href={AppDynamicRoutes.onboarding(Step.Theme, encodedTicket)}
          className="btn btn-md btn-full md:w-auto"
          onClick={trackOnboardingGetStarted}>
          Get started
          <ArrowSquareRight variant="Bold" className="h-5 w-5" />
        </ButtonLink>
        <ButtonLink
          variant="secondary"
          href={
            AppPublicRoutes.OnboardingEnd +
            `${encodedTicket ? `?ticket=${encodedTicket}` : ''}`
          }
          className="btn btn-md btn-full md:w-auto "
          onClick={trackOnboardingSkip}>
          Skip for now
        </ButtonLink>        
      </div>
      <p className="text-xl font-semibold doc-text-default pt-10">
        <Link href={AppPublicRoutes.SignIn} className="doc-link-default">
          Got an Account? - Log in
        </Link>
      </p>
    </>
  );
};
