import React from 'react';
import FontPicker from '@components/customisation/fontPicker/fontPicker';
import FontSizePicker from '@components/customisation/fontSizePicker/fontSizePicker';
import LineSpacePicker from '@components/customisation/lineSpacePicker/lineSpacePicker';
import TextAlignPicker from '@components/customisation/textAlignPicker/textAlignPicker';
import ThemePicker from '@components/customisation/themePicker/themePicker';

export interface CurrentStepProps {
  step: string;
}

const CurrentStep = (props: CurrentStepProps) => {
  const {step} = props;
  let stepEl = <></>;

  switch (step) {
    case 'theme':
      stepEl = (
        <ThemePicker
          className="lg:justify-between pb-4 lg:pb-0"
          gap="gap-2.5 lg:gap-5"
          buttonSize="w-16 h-16"
        />
      );
      break;
    case 'font':
      stepEl = <FontPicker dropdown className="w-full" inputLabel="Font" />;
      break;
    case 'font-size':
      stepEl = <FontSizePicker className="w-full" buttonStyles="h-16 box-border" />;
      break;
    case 'line-spacing':
      stepEl = <LineSpacePicker className="w-full" buttonStyles="h-16 box-border" />;
      break;
    case 'text-alignment':
      stepEl = <TextAlignPicker className="w-full" buttonStyles="h-16 box-border" />;
      break;
  }

  return step !== 'theme' ? (
    <div className="flex flex-col lg:flex-row lg:justify-between gap-2.5 pb-4 lg:pb-0 w-full">
      {stepEl}
    </div>
  ) : (
    stepEl
  );
};

export default CurrentStep;
