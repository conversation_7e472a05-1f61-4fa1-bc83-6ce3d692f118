'use client';

import React from 'react';
import {useOnboardingNavigation} from '../navigation/useOnboardingNavigation';
import ButtonLink from '@components/buttons/buttonLink';
import {AppPublicRoutes} from '@libs/services/auth/routes';
import {ArrowSquareLeft, ArrowSquareRight} from 'iconsax-react';
import {usePathname} from 'next/navigation';
import {useOnboardingAnalytics} from '../useOnboardingAnalytics';
import useSignUp from '../register/useSignUp';

export default function StepLinks() {
  const {
    nextStep,
    previousStep,
    handleOnNext,
    completedAllSteps,
    handleBackToSummary,
  } = useOnboardingNavigation();
  const {ticketValue} = useSignUp();
  const encodedTicket = encodeURIComponent(ticketValue);

  const onboardingEmailParam = ticketValue ? `?ticket=${encodedTicket}` : '';

  return (
    <>
      <div className="flex justify-between items-center space-x-3">
        <ButtonLink
          href={
            previousStep
              ? previousStep.href + onboardingEmailParam
              : AppPublicRoutes.OnboardingStart + onboardingEmailParam
          }
          variant="secondary"
          className="btn btn-sm btn-full lg:w-auto lg:btn-md">
          <ArrowSquareLeft variant="Bold" className="h-5 w-5" /> Back
        </ButtonLink>
        <ButtonLink
          href={
            nextStep
              ? nextStep.href + onboardingEmailParam
              : AppPublicRoutes.OnboardingEnd + onboardingEmailParam
          }
          hasIcon
          variant="primary"
          className="btn btn-sm btn-full lg:w-auto lg:btn-md"
          onClick={handleOnNext}>
          Next <ArrowSquareRight variant="Bold" className="h-5 w-5" />
        </ButtonLink>
      </div>
      {completedAllSteps && (
        <ButtonLink
          href={
            AppPublicRoutes.OnboardingEnd +
            (encodedTicket ? `?ticket=${encodedTicket}` : '')
          }
          variant="secondary"
          className="btn btn-sm btn-full lg:hidden"
          onClick={handleBackToSummary}>
          Back to summary
        </ButtonLink>
      )}
    </>
  );
}
