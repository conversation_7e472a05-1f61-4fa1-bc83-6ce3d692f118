'use client';
import React, {useEffect} from 'react';
import StepLinks from './stepLinks';
import CurrentStep from './currentStep';
import currentQuestion from './currentQuestion';
import {useOnboardingAnalytics} from '../useOnboardingAnalytics';
import useSignUp from '../register/useSignUp';

export interface OnboardingStepProps {
  step: string;
  className?: string;
}

const OnboardingStep = (props: OnboardingStepProps) => {
  const {step, className = ''} = props;
  const {trackOnboardingPageView} = useOnboardingAnalytics();
  const {trackingId} = useSignUp();

  useEffect(() => {
    trackingId && trackOnboardingPageView(step, trackingId);
  }, [trackingId, step, trackOnboardingPageView]);

  return (
    <div className="onboarding-step-container px-4 lg:px-8 py-6 sm:pb-16">
      <div className="flex flex-col items-center flex-wrap">
        <div className="flex flex-col justify-center gap-4 lg:gap-8 max-w-[671px]">
          <div className="flex flex-wrap justify-center pb-2">
            <h1 className="doc-text-default text-xl lg:text-3xl font-bold">
              {currentQuestion(step)}
            </h1>
          </div>
          <CurrentStep step={step} />
          <StepLinks />
        </div>
      </div>
    </div>
  );
};

export default OnboardingStep;
