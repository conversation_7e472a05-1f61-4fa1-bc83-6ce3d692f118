import {StepQuestion} from '@libs/store/onboarding/types';

export interface currentQuestionProps {
  step: string;
}

const currentQuestion = (step: string) => {
  let question = '';
  switch (step) {
    case 'theme':
      question = StepQuestion.Theme;
      break;
    case 'font':
      question = StepQuestion.Font;
      break;
    case 'font-size':
      question = StepQuestion.FontSize;
      break;
    case 'line-spacing':
      question = StepQuestion.LineSpacing;
      break;
    case 'text-alignment':
      question = StepQuestion.TextAlignment;
      break;
  }
  return question;
};

export default currentQuestion;
