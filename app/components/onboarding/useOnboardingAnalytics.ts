import {ISettings} from '@libs/store/settings/types';
import useUserDataFromSession from '@libs/utils/user/useUserDataFromSession';
import {useAnalytics} from 'use-analytics';

export const useOnboardingAnalytics = () => {
  const {track, user, identify} = useAnalytics();
  const {trackingId: trackingIdSession} = useUserDataFromSession();

  const trackOnboardingPageView = (
    page: string,
    trackingId?: string | undefined,
  ) => {
    trackingId && identify(trackingId);
    track(`page: ${page}`);
  };

  const trackOnboardingSkip = () => {
    track('OnboardingSkip');
  };

  const trackOnboardingGetStarted = () => {
    track('OnboardingGetStarted');
  };

  const trackExpandPreviewPress = (page: string) => {
    track('OnboardingExpandView', {page: page});
  };

  const trackBackToSummaryPress = (page: string) => {
    track('BackToSummary', {page: page});
  };

  const trackCreateAccount = (settings: ISettings) => {
    track('CreateAccount', {SelectedSettings: settings});
  };

  const trackCreateAccountError = (error: string) => {
    track('CreateAccountError', {Error: error});
  };

  const trackChangeOnboardingSettings = (changedSetting: string) => {
    track('ChangeOnboardingSettings', {Type: changedSetting});
  };

  const trackNoEmailVerify = () => {
    track('NoEmailVerify');
  };

  const trackVerifyPageView = (pageName: string, verifyFlow: string) => {
    track(`page: ${pageName}`, {flow: verifyFlow});
  };

  const trackRefreshVerifyManually = () => {
    track('RefreshManually');
  };

  const trackCreateAccountPress = () => {
    track('NoAccountCreateAccount');
  };

  const trackMagicLinkSignin = (errors?: string) => {
    track('MagicLinkSignin', {Errors: errors});
  };

  const trackUsersOccupation = (
    occupation: string,
    trackingId: string = '',
  ) => {
    identify(trackingIdSession ?? trackingId, {
      Occupation: occupation,
    });
  };

  return {
    trackOnboardingPageView,
    trackOnboardingSkip,
    trackOnboardingGetStarted,
    trackExpandPreviewPress,
    trackBackToSummaryPress,
    trackCreateAccount,
    trackCreateAccountError,
    trackChangeOnboardingSettings,
    trackNoEmailVerify,
    trackVerifyPageView,
    trackRefreshVerifyManually,
    trackCreateAccountPress,
    trackMagicLinkSignin,
    trackUsersOccupation,
  };
};
