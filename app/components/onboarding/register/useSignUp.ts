import {IOccupation} from './types';
import {isEmail} from 'react-multi-email';
import {useAnalytics} from 'use-analytics';
import {ChangeEvent, useEffect, useState} from 'react';
import useSettings from '@libs/store/settings';
import UserService from '@libs/services/user/service';
import {AppPrivateRoutes, AppPublicRoutes} from '@libs/services/auth/routes';
import {useRouter, useSearchParams} from 'next/navigation';
import {useOnboardingAnalytics} from '../useOnboardingAnalytics';
import {signIn} from 'next-auth/react';

function useSignUp() {
  const userService = new UserService();

  const {settings} = useSettings();
  const {trackCreateAccount, trackCreateAccountError, trackUsersOccupation} =
    useOnboardingAnalytics();
  const searchParams = useSearchParams();
  const ticketParam = searchParams.get('ticket');
  const emailParam = searchParams.get('email');
  const firstNameParam = searchParams.get('first-name');
  const lastNameParam = searchParams.get('last-name');
  const [ticketValue, setTicketValue] = useState(ticketParam || '');
  const [emailValue, setEmailValue] = useState(emailParam || '');
  const [firstName, setFirstName] = useState(firstNameParam || '');
  const [lastName, setLastName] = useState(lastNameParam || '');
  const [occupation, setOccupation] = useState<IOccupation | null>(null);
  const [title, setTitle] = useState('');
  const [error, setError] = useState<string | undefined>();
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [acceptedGDPR, setAcceptedGDPR] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [isValidTicket, setIsValidTicket] = useState(!!ticketValue);
  const router = useRouter();
  const [trackingId, setTrackingId] = useState('');

  const disableRegisterButton =
    acceptedTerms === false ||
    emailValue === '' ||
    processing ||
    firstName === '' ||
    lastName === '';

  const returnToPreviousPage = () => {
    history.back();
  };

  const isClientSide = typeof window !== 'undefined';

  // Load form values from sessionStorage (only on the client side)
  useEffect(() => {
    if (isClientSide) {
      const storedEmail = sessionStorage.getItem('email') || emailParam || '';
      const storedFirstName =
        sessionStorage.getItem('firstName') || firstNameParam || '';
      const storedLastName =
        sessionStorage.getItem('lastName') || lastNameParam || '';
      const storedOccupation = sessionStorage.getItem(
        'occupation',
      ) as IOccupation | null;
      const storedTitle = sessionStorage.getItem('title') || '';
      const storedAcceptedTerms =
        sessionStorage.getItem('acceptedTerms') === 'true';
      const storedAcceptedGDPR =
        sessionStorage.getItem('acceptedGDPR') === 'true';
      const storedTrackingId = sessionStorage.getItem('trackingId') || '';

      setEmailValue(storedEmail);
      setFirstName(storedFirstName);
      setLastName(storedLastName);
      setOccupation(storedOccupation);
      setTitle(storedTitle);
      setAcceptedTerms(storedAcceptedTerms);
      setAcceptedGDPR(storedAcceptedGDPR);
      setTrackingId(storedTrackingId);
    }
  }, [emailParam, firstNameParam, lastNameParam]);

  // Save form data to sessionStorage on every change if input comes from form
  const updateEmailValue = (email: string | ChangeEvent<HTMLInputElement>) => {
    const value = typeof email === 'string' ? email : email.currentTarget.value;
    setEmailValue(value);
    if (isClientSide) {
      sessionStorage.setItem('email', value);
    }
  };

  const updateTrackingId = (trackingId: string) => {
    setTrackingId(trackingId);
    if (isClientSide) {
      sessionStorage.setItem('trackingId', trackingId);
    }
  };

  const importEmailValue = (email: string) => {
    setEmailValue(email);
  };

  const setValidTicket = (valid: boolean) => {
    setIsValidTicket(valid);
  };

  const updateFirstName = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value;
    setFirstName(value);
    if (isClientSide) {
      sessionStorage.setItem('firstName', value);
    }
  };

  const updateLastName = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value;
    setLastName(value);
    if (isClientSide) {
      sessionStorage.setItem('lastName', value);
    }
  };

  const updateAcceptedTerms = () => {
    const newValue = !acceptedTerms;
    setAcceptedTerms(newValue);
    if (isClientSide) {
      sessionStorage.setItem('acceptedTerms', newValue.toString());
    }
  };

  const updateAcceptedGDPR = () => {
    const newValue = !acceptedGDPR;
    setAcceptedGDPR(newValue);
    if (isClientSide) {
      sessionStorage.setItem('acceptedGDPR', newValue.toString());
    }
  };

  const updateOccupation = (option: string) => {
    // Temporary fix for occupation title not being required for 'Other' as the BE can not accept an empty string
    if (option === IOccupation.Other) setTitle('N/A');
    setOccupation(option as IOccupation);
    if (isClientSide) {
      sessionStorage.setItem('occupation', option);
    }
    trackUsersOccupation(option, trackingId);
    document.getElementById('occupation-title')?.focus();
  };

  const updateTitle = (e: ChangeEvent<HTMLInputElement>) => {
    setTitle(e.currentTarget.value);
    if (isClientSide) {
      sessionStorage.setItem('title', e.currentTarget.value);
    }
  };

  async function goToVerifyNewUser() {
    if (!isEmail(emailValue)) {
      return setError('Please enter a valid email address.');
    } else if (firstName === '' || lastName === '') {
      return setError('Please enter your first and last name.');
    } else if (!acceptedTerms) {
      return setError('Please accept the terms and conditions.');
    } else if (!occupation) {
      return setError(
        'Please select your current employment or student status.',
      );
    } else if (!title) {
      let errorMessage = '';
      switch (occupation) {
        case IOccupation.Working:
          errorMessage = 'Please enter your job title.';
          break;
        case IOccupation.Studying:
          errorMessage = 'Please enter your course title.';
          break;
        case IOccupation.Other:
          break;
      }
      if (errorMessage) return setError(errorMessage);
    }
    setProcessing(true);
    userService
      .sendNewUserForMagicLink(
        emailValue,
        acceptedTerms,
        true,
        firstName,
        lastName,
        settings,
        occupation,
        title,
        acceptedGDPR,
        isValidTicket ? ticketValue : undefined, // if ticket wasn't valid when retrieving email then don't send
      )
      .then(res => {
        if (res && res.message === 'OK') {
          if (res.data) {
            signIn('authenticated-jwt', {
              redirect: false, //Allows to handle errors on the same page
              authData: JSON.stringify(res.data),
            }).then(async res => {
              if (res?.error) {
                trackCreateAccountError(res.error);
                setError(res.error);
                return null;
              }

              trackCreateAccount(settings);
              let callbackUrl = AppPrivateRoutes.Dashboard;
              return router.push(callbackUrl);
            });
            return;
          }

          trackCreateAccount(settings);

          const encodedEmail = encodeURIComponent(emailValue);
          const encodedFirstName = encodeURIComponent(firstName);
          const encodedLastName = encodeURIComponent(lastName);

          router.push(
            `${AppPublicRoutes.VerifyEmail}?email=${encodedEmail}&first-name=${encodedFirstName}&last-name=${encodedLastName}&registration=true`,
          );
        } else if (res && res.code === 5) {
          trackCreateAccountError(res.message);
          setError(res.message);
        }
      })
      .catch(e => {
        setError(
          'There has been an issue signing you up with the details provided. Please check and try again',
        );
        console.log('There has been an error signing up.', e);
      })
      .finally(() => {
        setProcessing(false);
        sessionStorage.clear();
      });
  }

  async function getTicketData() {
    if (!ticketValue) {
      return null;
    }

    return userService.getRegistrationTicketDetails(ticketValue).then(res => {
      if (!res || !res.data) {
        return null;
      }
      return res.data;
    });
  }

  return {
    emailValue,
    firstName,
    lastName,
    error,
    acceptedTerms,
    acceptedGDPR,
    disableRegisterButton,
    goToVerifyNewUser,
    updateEmailValue,
    updateAcceptedTerms,
    updateAcceptedGDPR,
    updateFirstName,
    updateLastName,
    returnToPreviousPage,
    processing,
    occupation,
    updateOccupation,
    title,
    updateTitle,
    importEmailValue,
    ticketValue,
    getTicketData,
    isValidTicket,
    setValidTicket,
    trackingId,
    updateTrackingId,
  };
}

export default useSignUp;
