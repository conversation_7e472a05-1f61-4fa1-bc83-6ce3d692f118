'use client';
import Button from '@components/buttons/button';
import CheckBox from '@components/forms/checkbox/checkbox';
import DropdownSelect from '@components/forms/dropdown/dropdownSelect';
import TextInput from '@components/forms/inputs/textInput';
import MessagesPanel from '@components/messagesPanel/messagesPanel';
import CheckBoxTermsLabel from '@components/onboarding/register/checkBoxTermsLabel';
import useSignUp from '@components/onboarding/register/useSignUp';
import {ArrowSquareLeft} from 'iconsax-react';
import {IOccupation} from './types';
import {useEffect, useState} from 'react';

export default function RegisterPanel() {
  const {
    error,
    emailValue,
    firstName,
    lastName,
    acceptedTerms,
    acceptedGDPR,
    disableRegisterButton,
    updateEmailValue,
    updateAcceptedTerms,
    updateAcceptedGDPR,
    updateFirstName,
    updateLastName,
    goToVerifyNewUser,
    returnToPreviousPage,
    processing,
    occupation,
    updateOccupation,
    title,
    updateTitle,
    isValidTicket,
    getTicketData,
    setValidTicket,
  } = useSignUp();

  useEffect(() => {
    if (isValidTicket && !emailValue) {
      getTicketData().then(res => {
        if (!res) {
          setValidTicket(false);
          return;
        }

        updateEmailValue(res.emailAddress);
      });
    }
  }, []);

  return (
    <div className="flex flex-col p-4 max-w-[32rem]">
      <h1 className="text-3xl lg:text-5xl font-bold leading-9 lg:leading-10 pb-6">
        Almost there!
      </h1>
      <h2 className="lg:text-2xl text-lg font-bold">
        Create a Tailo account to start using your preferences.
      </h2>
      <p className="text-sm py-6" aria-hidden={true}>
        * indicates a mandatory field
      </p>
      <div className="space-y-4">
        {error && (
          <MessagesPanel
            type="danger"
            messageTitle="Sign up error"
            message={error}
          />
        )}
        <fieldset className="space-y-4">
          <TextInput
            id="sign-in-first-name"
            label="First name"
            className="w-full"
            value={firstName}
            onChange={updateFirstName}
            required={true}
            type="text"
          />
          <TextInput
            id="sign-in-last-name"
            label="Last name"
            className="w-full"
            value={lastName}
            onChange={updateLastName}
            required={true}
            type="text"
          />
          <TextInput
            id="sign-in-email-address"
            label="Email address"
            className="w-full"
            value={emailValue}
            onChange={updateEmailValue}
            required={true}
            type="email"
            disabled={isValidTicket}
          />
        </fieldset>
        <fieldset className="space-y-4">
          <DropdownSelect
            id="occupation"
            inputLabel="I am currently..."
            required={true}
            onSelect={updateOccupation}
            defaultOption={occupation || ''}
            options={Object.values(IOccupation)}
          />
          {occupation && occupation !== IOccupation.Other && (
            <TextInput
              focusOnRender
              id="occupation-title"
              label={
                occupation === IOccupation.Working
                  ? 'Job title'
                  : 'Course title'
              }
              className="w-full"
              value={title}
              onChange={updateTitle}
              required={true}
              type="text"
            />
          )}
        </fieldset>
        <fieldset className="space-y-4">
          <CheckBox
            id="terms-and-conditions-checkbox"
            label={<CheckBoxTermsLabel />}
            checked={acceptedTerms}
            onPress={updateAcceptedTerms}
            required={true}
          />
          <CheckBox
            id="gdpr-checkbox"
            label="I agree to receive product update messages"
            checked={acceptedGDPR}
            onPress={updateAcceptedGDPR}
            required={false}
          />
        </fieldset>
      </div>
      <div className="flex flex-col-reverse lg:flex-row justify-between pt-8">
        <Button
          variant="secondary"
          onClick={returnToPreviousPage}
          className="mt-4 hidden lg:flex lg:mt-0">
          <ArrowSquareLeft variant="Bold" className="h-5 w-5" />
          Back
        </Button>
        <Button
          variant="primary"
          disabled={disableRegisterButton}
          loading={processing}
          onClick={goToVerifyNewUser}
          className="">
          Create account
        </Button>
      </div>
    </div>
  );
}
