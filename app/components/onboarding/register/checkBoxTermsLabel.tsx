import { AppSharedRoutes } from '@libs/services/auth/routes'
import Link from 'next/link'
import React from 'react'

const CheckBoxTermsLabel = () => {
  return (
    <>
      {'I agree to the '} 
      <Link href={AppSharedRoutes.TermsOfService} className='doc-link-default' target="_blank">terms of service</Link> 
      {' and '}
      <Link href={AppSharedRoutes.PrivacyStatement} className='doc-link-default' target="_blank">privacy statement</Link>
    </>
  )
}

export default CheckBoxTermsLabel