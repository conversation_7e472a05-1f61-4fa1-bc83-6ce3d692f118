import React from 'react';
import {CloseCircle} from 'iconsax-react';
import Viewer from '@components/document/viewer/viewer';
import {AppSharedRoutes} from '@libs/services/auth/routes';
import useDocumentPreviewModal from './useDocumentPreviewModal';
import IconButton from '@components/buttons/iconButton';

interface OnboardingDocumentPreviewModalProps {
  width?: string;
  height?: string;
  className?: string;
}

const OnboardingDocumentPreviewModal = ({
  width = 'w-full',
  height = '',
  className = '',
}: OnboardingDocumentPreviewModalProps) => {
  const {handleCollapsePreview, documentPreviewModalId} =
    useDocumentPreviewModal();
  return (
    <dialog
      id={documentPreviewModalId}
      className={`modal ${width} ${height} ${className}`}>
      <div className="max-w-desktop m-auto w-full flex justify-end pt-4 pr-4">
        <IconButton
          className="btn btn-tertiary sticky mx-2"
          onClick={handleCollapsePreview}
          accessibilityLabel="Collapse preview"
          size="medium">
          <CloseCircle variant="Bold" className="w-8 h-8" />
        </IconButton>
      </div>
      <div className="overflow-auto doc-scrollbar onboarding-doc-pop-out pt-16 w-3/5 rounded-t-md max-w-desktop m-auto doc-text-default">
        <Viewer url={AppSharedRoutes.DocumentExample} />
      </div>
    </dialog>
  );
};

export default OnboardingDocumentPreviewModal;
