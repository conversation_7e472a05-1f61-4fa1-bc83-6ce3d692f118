'use client';
import React from 'react';
import OnboardingDocumentPreviewModal from './onboardingDocumentPreviewModal';
import {AppSharedRoutes} from '@libs/services/auth/routes';
import Viewer from '@components/document/viewer/viewer';
import useDocumentPreviewModal from './useDocumentPreviewModal';
import {Maximize3} from 'iconsax-react';
import OnboardingDocumentPreviewScroll from './onboardingDocumentPreviewScroll';
import IconButton from '@components/buttons/iconButton';

const OnboardingDocumentPreview = () => {
  const {handleExpandPreview} = useDocumentPreviewModal();

  return (
    <div className="onboarding-document-preview-container w-full lg:w-[95%] max-h-full flex justify-center p-4 lg:p-0 lg:overflow-auto doc-scrollbar theme-border lg:shadow-md-d lg:rounded-lg max-w-desktop lg:m-auto">
      <div
        id="onboarding-document-preview"
        className="relative onboarding-document-preview w-full h-full lg:w-4/5 lg:mt-8 pt-4 lg:pt-16 rounded-t-md">
        <Viewer url={AppSharedRoutes.DocumentExample} />

        <OnboardingDocumentPreviewModal
          height="max-h-[100dvh]"
          className="onboarding-document-preview-container"
        />
        {/* Hides the button on start of scrolling and scrolling down in desktop view */}
        <OnboardingDocumentPreviewScroll />
      </div>
      <div className="expand-preview-btn-container sticky top-4 hidden lg:flex flex-col items-end m-4 h-1">
        <IconButton
          className="btn btn-tertiary mx-2 absolute left-5"
          onClick={handleExpandPreview}
          accessibilityLabel="Expand preview"
          title="Expand Preview"
          size="medium">
          <Maximize3 variant="Bold" />
        </IconButton>
      </div>
    </div>
  );
};

export default OnboardingDocumentPreview;
