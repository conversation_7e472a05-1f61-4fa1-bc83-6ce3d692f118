import {IUseDocumentPreviewModal} from './types';
import {useOnboardingNavigation} from '../navigation/useOnboardingNavigation';

export default function useDocumentPreviewModal(): IUseDocumentPreviewModal {
  interface IExpandPreview {
    showModal: () => void;
  }

  interface ICollapsePreview {
    close: () => void;
  }

  const documentPreviewModalId = 'onboarding-preview-modal';
  const {handleExpandPreviewModal} = useOnboardingNavigation();

  const handleExpandPreview = () => {
    (
      document.getElementById(
        documentPreviewModalId,
      ) as unknown as IExpandPreview
    ).showModal();
    handleExpandPreviewModal();
  };

  const handleCollapsePreview = () => {
    (
      document.getElementById(
        documentPreviewModalId,
      ) as unknown as ICollapsePreview
    ).close();
  };

  return {
    handleExpandPreview,
    handleCollapsePreview,
    documentPreviewModalId,
  };
}
