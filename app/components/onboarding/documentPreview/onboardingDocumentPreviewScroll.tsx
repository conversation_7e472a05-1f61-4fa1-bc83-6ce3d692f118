import IconButton from '@components/buttons/iconButton';
import {ArrowUp} from 'iconsax-react';
import React from 'react';

const handleScroll = () => {
  const element = document.getElementById(
    'onboarding-document-preview',
  ) as HTMLElement;
  element.scrollIntoView({behavior: 'smooth'});
};

const OnboardingDocumentPreviewScroll = () => {
  return (
    <div className="scroll-btn-container sticky bottom-0 lg:bottom-6 flex flex-col items-end mx-6">
      {/* Hides the button on start of scrolling and scrolling down in desktop view */}
      <IconButton
        onClick={handleScroll}
        fab
        className="rounded-full"
        size="medium"
        accessibilityLabel="Scroll up">
        <ArrowUp className="" variant="Linear" />
      </IconButton>
    </div>
  );
};

export default OnboardingDocumentPreviewScroll;
