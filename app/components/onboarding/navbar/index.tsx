import NavWrapper from '@components/nav/navWrapper';
import React from 'react';
import {AppPublicRoutes} from '@libs/services/auth/routes';
import MobileOnboardingNavigation from '../navigation/mobile/mobileOnboardingNavigation';

const onboardingLogoLink = AppPublicRoutes.OnboardingStart;
const onboardingAccessibilityLabel = 'Go back to onboarding start page';

export default function OnboardingNavBar({finalPage = false}: {finalPage?: boolean}) {
  return (
    <div className="onboarding-nav-container pt-3 overflow-hidden">
      <NavWrapper
        className="bg-transparent"
        logoLink={onboardingLogoLink}
        accessibilityLabel={onboardingAccessibilityLabel}>
        {finalPage ? null : (
          <>
            <li aria-hidden="true" className='absolute left-14 block sm:hidden w-full h-full bg-gradient-to-r nav-from-gradient from-0% to-transparent to-[8%] z-20 pointer-events-none' />
            <MobileOnboardingNavigation />
            <li aria-hidden="true" className='absolute right-0 block sm:hidden w-full h-full bg-gradient-to-l nav-from-gradient from-0% to-transparent to-[8%] z-20 pointer-events-none' />
          </>
        )}
      </NavWrapper>
    </div>
  );
}
