import Fab from '@components/buttons/fab';
import Bullets from '@components/customIcons/bullets';
import React from 'react';

export default function TableOfContentMobileButton({
  className = '',
  handleClick,
}: {
  className?: string;
  handleClick: () => void;
}) {
  return (
    <div className="sticky bottom-4 flex justify-end z-nav">
      {/* Hides the button on start of scrolling and scrolling down in desktop view */}
      <Fab
        size="large"
        onClick={handleClick}
        fab={true}
        accessibilityLabel="Table of content"
        // The bottom class is used to position the button at the bottom of the screen above the nav tabs and buttons on mobile
        className="!bottom-[72px] !right-4">
        <Bullets className="svg-default w-5 h-5" />
      </Fab>
    </div>
  );
}
