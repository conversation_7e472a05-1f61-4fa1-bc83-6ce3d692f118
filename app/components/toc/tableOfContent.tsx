import React from 'react';
import TableOfContentItem from './item/tableOfContentItem';
import useTOCSections from './useTOCSections';

export default function TableOfContent({className = ''}: {className?: string}) {
  // Replace with data from documentData when BE work is complete
  const {toc, activeSectionId, handleItemClick} = useTOCSections();
  return (
    <ul aria-label="table of content" className={className}>
      {toc.map((li, index, list) => (
        <TableOfContentItem
          key={li.id}
          {...li}
          type="link"
          linkHref={`#${li.id}-title`}
          isFirstItem={index === 0}
          isLastItem={index === list.length - 1}
          activeId={activeSectionId || toc[0].id}
          onClick={handleItemClick}
        />
      ))}
    </ul>
  );
}
