import ContentPlaceholder from '@components/document/placeholder/contentPlaceholder';
import {useState, useEffect, useCallback} from 'react';
import TableOfContentToggleButton from './item/tableOfContentToggleButton';
import CollapsedToc from './collapsedToc';
import usePinAtom from '@libs/store/pin';
import useTOCSections from './useTOCSections';
import {DRAWER_ID} from '@libs/store/drawer/types';
import useDrawer from '@libs/store/drawer';

export default function TableOfContentContainer({
  className,
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) {
  const {isLoading} = useTOCSections();
  const {pinned, togglePin, setPinned} = usePinAtom();
  const {drawStateById} = useDrawer();
  const [wasPinned, setWasPinned] = useState(false);
  const isDrawerOpen = drawStateById(DRAWER_ID.DOCUMENT_TOOLS) === 'open';

  const setPinnedState = useCallback(() => {
    // refactor this to use a return early approach if possible without breaking the logic
    if (isDrawerOpen && pinned) {
      setWasPinned(true);
      setPinned(false);
    } else if (!isDrawerOpen && wasPinned) {
      togglePin();
      setWasPinned(false);
    }
  }, [isDrawerOpen, pinned, setPinned, togglePin, wasPinned]);

  useEffect(() => {
    setPinnedState();
  }, [setPinnedState]);

  if (isLoading) return <ContentPlaceholder />;

  return (
    // TODO: Reduce column space when doc tools is open
    <div
      className={`lg:flex hidden group relative ${
        isDrawerOpen ? 'col-span-2' : 'col-span-1 hover:col-span-3'
      } ${pinned ? 'col-span-3' : ''} transition-all duration-300`}>
      <div
        className={`fixed top-24 ${
          isDrawerOpen
            ? 'opacity-100'
            : pinned
            ? 'opacity-0'
            : 'group-hover:opacity-0 opacity-100'
        } transition-all duration-300`}>
        <CollapsedToc />
      </div>
      <div
        className={`fixed top-0 mt-20 bottom-4 p-2 toc-background overflow-auto ${
          pinned
            ? 'max-w-[23%] xl:max-w-[20%]'
            : 'max-w-[2%] hover:max-w-[23%] lg:max-w-[2%] xl:hover:max-w-[20%]'
        } max-w-[23%] lg:pr-8 opacity-0 ${
          isDrawerOpen
            ? 'hidden'
            : pinned
            ? 'opacity-100'
            : 'group-hover:opacity-100'
        }  transition-all duration-300`}>
        <TableOfContentToggleButton
          pinned={pinned}
          togglePin={togglePin}
          className="mb-1"
        />
        <aside
          className={`w-full flex flex-col flex-wrap pr-4 ${className}`}>
          {children}
        </aside>
      </div>
    </div>
  );
}
