import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';
import {useActiveSectionId} from '@libs/store/document/activeSectionId';
import {useStoredIds} from '@libs/store/document/readingMarker';
import {useParams, useSearchParams} from 'next/navigation';
import React from 'react';

export default function useTOCSections() {
  const {id} = useParams();
  const documentId = id as string;
  const tabId = useSearchParams().get('tab');
  const {isLoading, document: documentData} = useProcessedDocument();
  const {getActiveSectionIdPerDocument, updateActiveSectionIdPerDocument} =
    useActiveSectionId(documentId);
  const {getStoredIdsPerDocument} = useStoredIds(documentId);
  const {
    currentId: storedReadingMarkerCurrentId,
    parentSectionId: storedReadingMarkerParentSectionId,
  } = getStoredIdsPerDocument();
  const {trackSectionChange, trackTableOfContentsPress} =
    useDocumentAnalytics();

  const toc = documentData?.details.toc || [];

  const {
    parentSectionId,
    activeSectionId,
    allNestedIds = [],
  } = getActiveSectionIdPerDocument();

  const getNestedIds = (parentSectionId: string) => {
    return (
      toc.find(item => item.id === parentSectionId)?.sections.map(s => s.id) ||
      []
    );
  };

  const handleItemClick = (
    parentSectionId: string,
    activeSectionId: string,
  ) => {
    const allNestedIds = getNestedIds(parentSectionId);
    trackTableOfContentsPress(toc.length);
    updateActiveSectionIdPerDocument(
      parentSectionId,
      activeSectionId,
      allNestedIds,
    );
  };

  const getActiveIndex = () => {
    // We only move between Level1 sections
    // Level 2 sections/ subsections are links
    const activeId = parentSectionId || activeSectionId;
    return toc.findIndex(item => item.id === activeId);
  };

  const updateActiveSection = (id: string) => {
    const allNestedIds = getNestedIds(id);
    updateActiveSectionIdPerDocument(id, id, allNestedIds);
  };

  const getSection = (direction: 'Next' | 'Previous') => {
    if (!toc.length) console.error('Table of contents is empty');

    const activeIndex = getActiveIndex();
    const targetIndex =
      direction === 'Next' ? activeIndex + 1 : activeIndex - 1;

    if (targetIndex < 0 || targetIndex >= toc.length) {
      return toc[activeIndex]?.id || toc[0].id;
    }

    trackSectionChange(direction);
    return toc[targetIndex].id;
  };

  React.useEffect(() => {
    const currentHashId = window.location.hash.substring(1).split('-')[0];
    const hashIsValid =
      currentHashId && toc.some(section => section.id === currentHashId);
    if (toc.length === 0) return;
    if (hashIsValid) {
      updateActiveSection(currentHashId);
    } else {
      if (!storedReadingMarkerCurrentId && !activeSectionId) {
        const firstSectionId = toc[0].id;
        updateActiveSection(firstSectionId);
      } else {
        const sectionId = storedReadingMarkerParentSectionId || parentSectionId;
        const currentId = storedReadingMarkerCurrentId || activeSectionId;
        const allNestedIds = getNestedIds(parentSectionId);
        updateActiveSectionIdPerDocument(sectionId, currentId, allNestedIds);
      }
    }
  }, []);

  const prevSectionButtonDisabled = parentSectionId === toc[0]?.id;
  const nextSectionButtonDisabled = parentSectionId === toc[toc.length - 1]?.id;

  return {
    isLoading,
    toc,
    parentSectionId,
    activeSectionId,
    handleItemClick,
    updateActiveSection,
    getSection,
    prevSectionButtonDisabled,
    nextSectionButtonDisabled,
    sectionsIdsToDisplay: [parentSectionId, ...allNestedIds],
    tabId,
  };
}
