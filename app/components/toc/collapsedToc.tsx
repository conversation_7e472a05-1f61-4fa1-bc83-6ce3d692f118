import useTOCSections from './useTOCSections';
import {ITableOfContentItem} from '@libs/store/document/types';
import useDrawer from '@libs/store/drawer';
import React from 'react';
import {DRAWER_ID} from '@libs/store/drawer/types';

export default function CollapsedToc() {
  const {toc, activeSectionId} = useTOCSections();
  const {updateDrawer} = useDrawer();

  const parentLineStyles = 'toc-parent-line';
  const childLineStyles = 'toc-child-line';
  const grandChildLineStyles = 'toc-grandchild-line';

  const handleClick = () => {
    updateDrawer(DRAWER_ID.DOCUMENT_TOOLS, 'closed');
  };

  const renderSections = (sections: ITableOfContentItem[], level = 0) => {
    return sections.map((section: any) => {
      // Line styles based on the level of the section.
      let className: string;

      switch (level) {
        case 0:
          className = parentLineStyles;
          break;
        case 1:
          className = childLineStyles;
          break;
        case 2:
        default:
          className = grandChildLineStyles;
          break;
      }

      const activeIdStyles =
        activeSectionId === section.id ? 'toc-active-element' : '';

      const sectionElement = (
        <li key={section.id} className={`${className} ${activeIdStyles}`}></li>
      );

      // Recursively render nested sections until sections length is 0.
      const nestedSections =
        section.sections && section.sections.length > 0 ? (
          <ul className="flex flex-col gap-4">
            {renderSections(section.sections, level + 1)}
          </ul>
        ) : null;

      return (
        <React.Fragment key={section.id}>
          {sectionElement}
          {nestedSections}
        </React.Fragment>
      );
    });
  };

  const lines = renderSections(toc);

  // Component should not be read out in place of the actual TOC by SR as it is a visual-only element.
  return (
    <ul
      onClick={handleClick}
      id="collapsed-toc"
      className="flex flex-col gap-4"
      aria-hidden="true">
      {lines}
    </ul>
  );
}
