'use client';
import {ITableOfContentItem} from '@libs/store/document/types';
import {useParams} from 'next/navigation';
import React from 'react';

export default function useActiveElInViewOnScroll(
  scrollableElementId: string,
  toc: ITableOfContentItem[],
  updateActiveId: (id: string) => void,
) {
  const params = useParams();
  const flattenSections = (toc: ITableOfContentItem[]): string[] =>
    toc.map(item => [item.id, ...flattenSections(item.sections)]).flat();

  const sectionIds = flattenSections(toc);

  const elementIsVisibleInViewport = (el: HTMLElement) => {
    const {bottom} = el.getBoundingClientRect();
    const {innerHeight} = window;
    // Is visible when it passes the half-screen mark
    return bottom <= innerHeight / 2;
  };

  const onScroll = (event: Event) => {
    sectionIds.forEach(sectionId => {
      const el = document.getElementById(sectionId);
      if (!el) return;
      if (elementIsVisibleInViewport(el)) {
        updateActiveId(sectionId);
      }
    });
  };

  React.useEffect(() => {
    const scrollableEl =
      document.getElementById(scrollableElementId) || document;
    if (!scrollableEl || sectionIds.length === 0) return;

    scrollableEl.addEventListener('scroll', onScroll);
    return () => {
      scrollableEl.removeEventListener('scroll', onScroll);
    };
    // params ensure that the effect is re-run when users switch between pages/tabs
  }, [params]);
}
