import {ITableOfContentItem} from '../../libs/store/document/types';

export const tableOfContentDataExample: ITableOfContentItem[] = [
  {title: 'Introduction', id: '1', tag: 'h2', sections: []},
  {title: 'Methodology and Data', id: '2', tag: 'h2', sections: []},
  {
    title: 'Results',
    id: '3',
    tag: 'h2',
    sections: [
      {
        title: 'Results subsection 1',
        id: '7',
        tag: 'h3',
        sections: [],
      },
      {
        title:
          'Results subsection 2 with longer title for testing user settings application',
        id: '8',
        tag: 'h3',
        sections: [],
      },
    ],
  },
  {title: 'Robustness Tests', id: '4', tag: 'h2', sections: []},
  {
    title: 'Conclusions',
    id: '5',
    tag: 'h2',
    sections: [
      {
        title: 'Conclusions subsection 1',
        id: '17',
        tag: 'h3',
        sections: [],
      },
      {
        title: 'Conclusions subsection 2',
        id: '18',
        tag: 'h3',
        sections: [],
      },
    ],
  },
  {title: 'References', id: '6', tag: 'h2', sections: []},
];
