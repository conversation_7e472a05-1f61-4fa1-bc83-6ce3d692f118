import {
  ArrowSquareDown,
  <PERSON><PERSON><PERSON>reUp,
  <PERSON>Down2,
  ArrowUp2,
} from 'iconsax-react';
import React, {useEffect} from 'react';

export default function TableOfContentItemToggleButton({
  toggleCollapse,
  isCollapsed,
  setIsCollapsed,
  className,
  isFirstItem,
  isLastItem,
  isActive,
  isSummaryTab,
}: {
  toggleCollapse: () => void;
  isCollapsed: boolean;
  setIsCollapsed: (value: boolean) => void;
  className?: string;
  isFirstItem?: boolean;
  isLastItem?: boolean;
  isActive?: boolean;
  isSummaryTab?: boolean;
}) {
  const summaryTabStyles = `btn btn-tertiary icon-btn-sm items-start rounded-none summary-expand-btn
  ${isFirstItem ? 'rounded-tr-md' : ''}
  ${isLastItem ? 'rounded-br-md' : ''}
  ${className}`;

  useEffect(() => {
    if (!isSummaryTab) setIsCollapsed(!isActive);
  }, [isActive, setIsCollapsed, isSummaryTab]);

  return (
    <button
      aria-label={`${isCollapsed ? 'Open' : 'Close'} nested list items`}
      onClick={toggleCollapse}
      className={`${isSummaryTab ? summaryTabStyles : 'btn btn-tertiary icon-btn-sm items-start ml-1 mt-1 mb-1'} `}>
      {isSummaryTab ? (
        isCollapsed ? (
          <ArrowSquareDown variant="Bold" aria-hidden className="w-4 h-4" />
        ) : (
          <ArrowSquareUp variant="Bold" aria-hidden className="w-4 h-4" />
        )
      ) : isCollapsed ? (
        <ArrowDown2 variant="Outline" aria-hidden className="w-4 h-4" />
      ) : (
        <ArrowUp2 variant="Outline" aria-hidden className="w-4 h-4" />
      )}
    </button>
  );
}
