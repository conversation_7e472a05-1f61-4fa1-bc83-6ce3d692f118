import React from 'react';
import Pin from '@components/customIcons/pin';

export default function TableOfContentToggleButton({
  className = '',
  pinned,
  togglePin,
}: {
  className?: string;
  pinned: boolean;
  togglePin: () => void;
}) {
  const title = `${pinned ? 'Unpin' : 'Pin'} table of contents`;

  return (
    <button
      aria-label={title}
      title={title}
      onClick={togglePin}
      className={`btn btn-secondary btn-xs ${className}`}>
      <Pin className="rounded-md svg-default" />
      <span>{pinned ? 'Unpin' : 'Pin'}</span>
    </button>
  );
}
