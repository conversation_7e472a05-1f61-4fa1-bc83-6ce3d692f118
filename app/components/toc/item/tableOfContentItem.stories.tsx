import type {Meta, StoryObj} from '@storybook/react';
import TableOfContentItem from './tableOfContentItem';
import {tableOfContentDataExample} from '../data';

const meta: Meta<typeof TableOfContentItem> = {
  title: 'Lists/Table of Content',
  component: TableOfContentItem,
};

export default meta;
type Story = StoryObj<typeof TableOfContentItem>;

const items = (
  <>
    <TableOfContentItem
      {...tableOfContentDataExample[0]}
      linkHref=""
      type="link"
      isFirstItem
      isLastItem={false}
      activeId="1"
      isUserSettingsResponsive={false}
    />
    <TableOfContentItem
      {...tableOfContentDataExample[1]}
      type="button"
      onClick={() => {}}
      isFirstItem={false}
      isLastItem={false}
      activeId="2"
      isUserSettingsResponsive={false}
    />
    <TableOfContentItem
      {...tableOfContentDataExample[2]}
      type="button"
      onClick={() => {}}
      isFirstItem={false}
      isLastItem={false}
      activeId="7"
      isUserSettingsResponsive={false}
    />
    <TableOfContentItem
      {...tableOfContentDataExample[3]}
      linkHref=""
      type="link"
      isFirstItem={false}
      isLastItem={false}
      isUserSettingsResponsive={false}
    />
    <TableOfContentItem
      {...tableOfContentDataExample[4]}
      linkHref=""
      type="link"
      isLastItem
      isFirstItem={false}
      isUserSettingsResponsive={false}
    />
  </>
);

export const TableOfContentItemStory: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="app-bg doc-text-default p-4">{items}</div>
      </div>
      <div data-theme="light">
        <div className="app-bg doc-text-default p-4">{items}</div>
      </div>
      <div data-theme="yellow">
        <div className="app-bg doc-text-default p-4">{items}</div>
      </div>
      <div data-theme="pale-green">
        <div className="app-bg doc-text-default p-4">{items}</div>
      </div>
      <div data-theme="pale-pink">
        <div className="app-bg doc-text-default p-4">{items}</div>
      </div>
      <div data-theme="pale-blue">
        <div className="app-bg doc-text-default p-4">{items}</div>
      </div>
      <div data-theme="peach">
        <div className="app-bg doc-text-default p-4">{items}</div>
      </div>
    </>
  ),
};
