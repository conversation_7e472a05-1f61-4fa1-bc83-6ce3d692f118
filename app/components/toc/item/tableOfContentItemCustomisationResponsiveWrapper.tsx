import CustomisationReactiveWrapper from '@components/customisation/customisationReactiveWrapper';
import React from 'react';

export default function TableOfContentItemCustomisationResponsiveWrapper({
  isUserSettingsResponsive = true,
  children,
}: {
  isUserSettingsResponsive?: boolean;
  children: React.ReactNode;
}) {
  if (isUserSettingsResponsive) {
    return <span className="toc-items">{children}</span>;
  }

  return <span className="text-left block p-2 px-4">{children}</span>;
}
