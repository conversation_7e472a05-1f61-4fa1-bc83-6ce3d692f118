import {ITableOfContentItem} from '@libs/store/document/types';
import React from 'react';
import TableOfContentItemLinkWrapper from './tableOfContentItemLinkWrapper';
import TableOfContentItemButtonWrapper from './tableOfContentItemButtonWrapper';
import TableOfContentItemToggleButton from './tableOfContentItemToggleButton';
import useTableOfContentItem from './useTableOfContentItem';
import NestedTableOfContent from './nestedTableOfContent';
import useTOCSections from '../useTOCSections';
import {DocOverviewNavTabs} from '@components/nav/documentOverview/documentNavigation/documentNavigationContent';

interface SharedTableOfContentItemProps extends ITableOfContentItem {
  sections: ITableOfContentItem[];
  parentId?: string;
  isFirstItem: boolean;
  isLastItem: boolean;
  activeId?: string; //styling expected on Section view and Document view tabs
  isUserSettingsResponsive?: boolean;
  isSummaryTab?: boolean;
}
type ConditionalTableOfContentItemProps =
  | {
      type: 'button';
      onClick: (parentSectionId: string, itemId: string) => void;
    }
  | {
      linkHref: string;
      linkDestination?: string; //navigating to a different page/tab
      type: 'link';
      onClick?: (parentSectionId: string, itemId: string) => void;
    }; //page url
export type ITableOfContentItemProps = SharedTableOfContentItemProps &
  ConditionalTableOfContentItemProps;

export default function TableOfContentItem(props: ITableOfContentItemProps) {
  const {isUserSettingsResponsive = true} = props;
  const {
    toggleCollapse,
    isCollapsed,
    setIsCollapsed,
    handleOnClick,
    collapsibleListRef,
  } = useTableOfContentItem(props);
  const {tabId, sectionsIdsToDisplay} = useTOCSections();

  // Item styling
  // Negative margin is used to eliminate thicker border on adjacent items
  const hasActiveSections = sectionsIdsToDisplay.some(id => id === props.id);
  const hasActiveSectionsInNestedItems =
    hasActiveSections &&
    props.sections.filter(section => section.sections.length > 0).length > 0;
  const activeSection = props.id === props.activeId;
  const firstItemStyle = !!props.isFirstItem ? ' rounded-t-md ' : '';
  const lastItemStyle = !!props.isLastItem ? ' rounded-b-md ' : '';
  const itemStyle = !!props.parentId
    ? ''
    : ` ${props.isSummaryTab ? 'border separator relative' : 'separator'} ` +
      firstItemStyle +
      lastItemStyle;

  const activeStyle =
    !props.isSummaryTab && activeSection
      ? `relative before:content-[""] before:absolute before:left-0 before:top-0 before:w-1 before:h-full before:left-0 ${
          props.isSummaryTab ? 'before:border-l-4' : ' '
        } ${
          hasActiveSections && props.activeId !== sectionsIdsToDisplay[0]
            ? 'font-semibold'
            : ''
        } before:separator`
      : '';

  const hasNestedItems = props.sections.length > 0;
  const collapsibleItemStyle = hasNestedItems
    ? isCollapsed
      ? 'links-wrapper links-wrapper-hidden'
      : 'links-wrapper links-wrapper-expanded'
    : '';

  const interactiveItemStyle = !props.isSummaryTab
    ? `${
        props.id === sectionsIdsToDisplay[0]
          ? 'active-toc-parent-text '
          : 'inactive-toc-parent-text'
      } ${activeStyle} ${
        hasActiveSections && props.id !== sectionsIdsToDisplay[0] ? '' : ''
      } ${hasActiveSectionsInNestedItems ? 'pl-10' : ''}`
    : '';

  const handleSectionItemClick = () => {
    handleOnClick(props.parentId ?? props.id, props.id);
  };

  const linkDestination =
    props.type === 'link' && props.linkDestination ? props.linkDestination : '';

  return (
    <li className={`flex ${itemStyle} -mt-[1px]`}>
      <span
        className={`flex-1 ${collapsibleItemStyle} ${
          props.isSummaryTab ? '' : 'toc-items-text'
        }`}>
        {props.type === 'link' ? (
          <TableOfContentItemLinkWrapper
            href={
              tabId === DocOverviewNavTabs.SectionView && !props.parentId
                ? '#'
                : (props.linkDestination || '') + props.linkHref ||
                  '' + '#' + props.id
            }
            onClick={handleSectionItemClick}
            label={props.title}
            className={interactiveItemStyle}
            isUserSettingsResponsive={isUserSettingsResponsive}
            isActive={activeSection}
          />
        ) : (
          <TableOfContentItemButtonWrapper
            onClick={handleSectionItemClick}
            label={props.title}
            className={interactiveItemStyle}
            isUserSettingsResponsive={isUserSettingsResponsive}
            isActive={activeSection}
          />
        )}
        <NestedTableOfContent
          parentId={props.id}
          parentTitle={props.title}
          sections={props.sections}
          hasNestedItems={hasNestedItems}
          isCollapsed={isCollapsed}
          collapsibleListRef={collapsibleListRef}
          handleOnClick={handleOnClick}
          activeId={props.activeId}
          linkDestination={linkDestination}
        />
      </span>
      {hasNestedItems && (
        <TableOfContentItemToggleButton
          isFirstItem={props.isFirstItem}
          isLastItem={props.isLastItem}
          toggleCollapse={toggleCollapse}
          isCollapsed={isCollapsed}
          setIsCollapsed={setIsCollapsed}
          className={
            isUserSettingsResponsive
              ? 'customisation-responsive-toc-li-toggle'
              : 'p-2'
          }
          isActive={hasActiveSections}
          isSummaryTab={props.isSummaryTab}
        />
      )}
    </li>
  );
}
