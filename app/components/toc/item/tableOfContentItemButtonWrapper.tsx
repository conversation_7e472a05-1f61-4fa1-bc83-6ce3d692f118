import CustomisationReactiveWrapper from '@components/customisation/customisationReactiveWrapper';
import React from 'react';
import TableOfContentItemCustomisationResponsiveWrapper from './tableOfContentItemCustomisationResponsiveWrapper';

/**
 * To be used to display the sections in the Section view tab
 */
export default function TableOfContentItemButtonWrapper({
  onClick,
  label,
  className,
  isUserSettingsResponsive = true,
  isActive,
}: {
  onClick?: () => void;
  label: string;
  className?: string;
  isUserSettingsResponsive?: boolean;
  isActive?: boolean;
}) {
  return (
    <button
      className={`block w-full focus-outer ${className}`}
      onClick={onClick}
      aria-label="Displays the selected section on the screen">
      <TableOfContentItemCustomisationResponsiveWrapper
        isUserSettingsResponsive={isUserSettingsResponsive}>
        {isActive ? (
          <div className="flex gap-1">
            <span>-</span>
            <span className="hover:underline">{label}</span>
          </div>
        ) : (
          <span className="hover:underline">{label}</span>
        )}
      </TableOfContentItemCustomisationResponsiveWrapper>
    </button>
  );
}
