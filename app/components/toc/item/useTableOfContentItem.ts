import React from 'react';
import {ITableOfContentItemProps} from './tableOfContentItem';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import {MAIN_SCROLLABLE_ELEMENT_ID} from '@components/nav/documentOverview';

export default function useTableOfContentItem(props: ITableOfContentItemProps) {
  const {closeDrawer} = useDrawer();

  const handleOnClick = (parentId: string, itemId: string) => {
    closeDrawer(DRAWER_ID.TOC_SECTION_VIEW);
    if (props.onClick) {
      props.onClick(parentId, itemId);

      if (props.type === 'button' && parentId === itemId) {
        // Ensures that when users switch between sections they are always scrolled to the top of the section when the buttons (L1) are clicked
        const scrollableContainer = document.getElementById(
          MAIN_SCROLLABLE_ELEMENT_ID,
        );
        scrollableContainer?.scrollTo(0, 0);
      }
    }
  };

  // Collapsing logic for nested items
  const collapsibleListRef = React.useRef<HTMLUListElement>(null);
  const sectionsIncludeActiveId =
    props.sections.filter(item => item.id === props.activeId).length > 0;
  const [isCollapsed, setIsCollapsed] = React.useState(
    !sectionsIncludeActiveId,
  );

  const toggleCollapse = () => {
    // We need to set focus on the first interactive element in the nested list to ensure the logical flow; otherwise the focus skips the nested list
    const interactiveEl =
      collapsibleListRef.current?.firstElementChild?.getElementsByClassName(
        '',
      )[0] as HTMLLinkElement | HTMLButtonElement;
    // setTimeout needed to ensure focus is set after the collapse animation
    setTimeout(() => interactiveEl?.focus(), 100);

    setIsCollapsed(!isCollapsed);
  };
  return {
    toggleCollapse,
    isCollapsed,
    setIsCollapsed,
    handleOnClick,
    collapsibleListRef,
  };
}
