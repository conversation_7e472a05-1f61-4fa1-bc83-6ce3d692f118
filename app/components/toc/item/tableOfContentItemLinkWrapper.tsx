import Link from 'next/link';
import React from 'react';
import TableOfContentItemCustomisationResponsiveWrapper from './tableOfContentItemCustomisationResponsiveWrapper';

/**
 * To be used to navigate user between tabs with the right section in view
 */
export default function TableOfContentItemLinkWrapper({
  href,
  label,
  className,
  isUserSettingsResponsive = true,
  isActive = false,
  onClick,
}: {
  href: string;
  label: string;
  className?: string;
  isUserSettingsResponsive?: boolean;
  isActive?: boolean;
  onClick?: () => void;
}) {
  return (
    <Link
      className={`inline-block w-full hover:rounded-md 
     
        ${className}`}
      href={href}
      onClick={onClick}>
      <TableOfContentItemCustomisationResponsiveWrapper
        isUserSettingsResponsive={isUserSettingsResponsive}>
        <div className="flex gap-1 items-center">
          <span className="relative">{label}</span>
        </div>
      </TableOfContentItemCustomisationResponsiveWrapper>
    </Link>
  );
}
