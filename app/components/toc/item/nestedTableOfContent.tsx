import {ITableOfContentItem} from '@libs/store/document/types';
import React from 'react';
import TableOfContentItem from './tableOfContentItem';
import {useSearchParams} from 'next/navigation';

export default function NestedTableOfContent({
  hasNestedItems,
  isCollapsed,
  collapsibleListRef,
  handleOnClick,
  sections,
  parentId,
  parentTitle,
  activeId,
  linkDestination = '',
}: {
  hasNestedItems: boolean;
  isCollapsed: boolean;
  collapsibleListRef: React.RefObject<HTMLUListElement>;
  handleOnClick: (parentId: string, itemId: string) => void;
  sections: ITableOfContentItem[];
  parentId: string;
  parentTitle: string;
  activeId: string | undefined;
  linkDestination?: string;
}) {
  const searchParams = useSearchParams();
  const isSummaryTab = searchParams.get('tab') === 'summary';

  const summaryTabStyles = `block w-full separator border-t -mb-[1px] p-0 ${
    isCollapsed ? 'hidden' : ''
  }`;

  const documentTabStyles = `block w-full p-0 ${
    isCollapsed ? 'hidden' : 'block'
  }`;

  if (!hasNestedItems) return null;
  return (
    <ul
      ref={collapsibleListRef}
      aria-label={`Nested list for item: ${parentTitle}`}
      className={isSummaryTab ? summaryTabStyles : documentTabStyles}>
      {sections.map(item => {
        const handleSubsectionItemClick = () => {
          handleOnClick(parentId, item.id);
        };

        return (
          <TableOfContentItem
            key={item.id}
            {...item}
            parentId={parentId}
            linkHref={`${linkDestination}#${item.id}-title`}
            type="link"
            onClick={handleSubsectionItemClick}
            isFirstItem={false}
            isLastItem={false}
            activeId={activeId}
            isSummaryTab={isSummaryTab}
          />
        );
      })}
    </ul>
  );
}
