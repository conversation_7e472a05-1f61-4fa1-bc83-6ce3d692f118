import BottomSheet from '@components/modals/bottomSheet/bottomSheet';
import {DRAWER_ID} from '@libs/store/drawer/types';
import React from 'react';
import TableOfContentMobileButton from '@components/toc/tableOfContentMobileButton';
import useDrawer from '@libs/store/drawer';
import TableOfContent from '@components/toc/tableOfContent';

export default function TocMobile() {
  const {openDrawer, getOpenDrawerId} = useDrawer();
  // Note: the drawer opening can not be tied to the toggling of the section button on desktop as the drawer state allows only for one drawer to be open at a time
  const openMobileSections = () => {
    openDrawer(DRAWER_ID.TOC_SECTION_VIEW);
  };

  return (
    <div className="lg:hidden">
      {getOpenDrawerId() !== DRAWER_ID.FOOTNOTES && (
        <TableOfContentMobileButton handleClick={openMobileSections} />
      )}
      <BottomSheet
        accessibilityLabel="Table of content"
        drawerId={DRAWER_ID.TOC_SECTION_VIEW}>
        <TableOfContent />
      </BottomSheet>
    </div>
  );
}
