import React from 'react';
import useAccordion from './useAccordion';
import {ArrowSquareDown, ArrowSquareUp} from 'iconsax-react';

interface AccordionProps {
  children: React.ReactNode;
  name: string;
  className?: string;
}

export default function Accordion(props: AccordionProps) {
  const {children, name, className} = props;
  const {isOpen, handleClick, handleKeyDown} = useAccordion();
  const handleCheckboxChange = () => {
    handleClick();
  };
  const childrenArr = React.Children.toArray(children);
  const title = childrenArr.find(
    (child: any) => child.props.id === 'accordion-title',
  );
  const content = childrenArr.find(
    (child: any) => child.props.id === 'accordion-content',
  );
  return (
    <div
      id={`${name}-accordion`}
      className={`collapse accordion rounded-md app-bg theme-border-subdued ${className}`}
      onChange={handleClick}
      onKeyDown={handleKeyDown}>
      <input
        type="checkbox"
        checked={isOpen}
        onChange={handleCheckboxChange}
        aria-label={`${name}-accordion expand`}
        aria-expanded={isOpen}
        aria-controls="collapse-content"
      />
      <div
        className={`!accordion-title ${
          isOpen ? 'accordion-title-active' : ''
        } collapse-title !min-h-[1px] h-min p-3 flex justify-between`}>
        {title}
        {isOpen ? (
          <ArrowSquareUp variant="Bold" aria-hidden />
        ) : (
          <ArrowSquareDown variant="Bold" aria-hidden />
        )}
      </div>
      <div
        className={`collapse-content text-left ${isOpen ? '!pb-3' : ''}`}
        tabIndex={isOpen ? 0 : undefined}>
        {content}
      </div>
    </div>
  );
}
