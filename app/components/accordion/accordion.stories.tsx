import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import Accordion from './index';

const meta: Meta<typeof Accordion> = {
  title: 'Accordion',
  component: Accordion,
};

export default meta;
type Story = StoryObj<typeof Accordion>;

export const AccordionStory: Story = {
  render: args => (
    <>
      <div data-theme="dark" className="pb-4 space-y-4">
        <Accordion {...args}>
          <div id="accordion-title">
            <p className="text-sm font-semibold">Minutes remaining</p>
          </div>
          <div id="accordion-content">
            <progress
              className="progress progress-bar-rail"
              value={10}
              max="100"></progress>
            <p className="pb-3 text-xs font-semibold">0/0 MINUTES USED</p>
            <p className="text-sm">
              Your read aloud minutes will refresh on (date)
            </p>
          </div>
        </Accordion>
      </div>
      <div data-theme="light" className="pb-4">
        <Accordion {...args}>
          <div id="accordion-title">
            <p className="text-sm font-semibold">Minutes remaining</p>
          </div>
          <div id="accordion-content">
            <progress
              className="progress progress-bar-rail"
              value={10}
              max="100"></progress>
            <p className="pb-3 text-xs font-semibold">0/0 MINUTES USED</p>
            <p className="text-sm">
              Your read aloud minutes will refresh on (date)
            </p>
          </div>
        </Accordion>
      </div>
      <div data-theme="yellow" className="pb-4">
        <Accordion {...args}>
          <div id="accordion-title">
            <p className="text-sm font-semibold">Minutes remaining</p>
          </div>
          <div id="accordion-content">
            <progress
              className="progress progress-bar-rail"
              value={10}
              max="100"></progress>
            <p className="pb-3 text-xs font-semibold">0/0 MINUTES USED</p>
            <p className="text-sm">
              Your read aloud minutes will refresh on (date)
            </p>
          </div>
        </Accordion>
      </div>
      <div data-theme="pale-green" className="pb-4">
        <Accordion {...args}>
          <div id="accordion-title">
            <p className="text-sm font-semibold">Minutes remaining</p>
          </div>
          <div id="accordion-content">
            <progress
              className="progress progress-bar-rail"
              value={10}
              max="100"></progress>
            <p className="pb-3 text-xs font-semibold">0/0 MINUTES USED</p>
            <p className="text-sm">
              Your read aloud minutes will refresh on (date)
            </p>
          </div>
        </Accordion>
      </div>
      <div data-theme="pale-pink" className="pb-4">
        <Accordion {...args}>
          <div id="accordion-title">
            <p className="text-sm font-semibold">Minutes remaining</p>
          </div>
          <div id="accordion-content">
            <progress
              className="progress progress-bar-rail"
              value={10}
              max="100"></progress>
            <p className="pb-3 text-xs font-semibold">0/0 MINUTES USED</p>
            <p className="text-sm">
              Your read aloud minutes will refresh on (date)
            </p>
          </div>
        </Accordion>
      </div>
      <div data-theme="pale-blue" className="pb-4">
        <Accordion {...args}>
          <div id="accordion-title">
            <p className="text-sm font-semibold">Minutes remaining</p>
          </div>
          <div id="accordion-content">
            <progress
              className="progress progress-bar-rail"
              value={10}
              max="100"></progress>
            <p className="pb-3 text-xs font-semibold">0/0 MINUTES USED</p>
            <p className="text-sm">
              Your read aloud minutes will refresh on (date)
            </p>
          </div>
        </Accordion>
      </div>
      <div data-theme="peach">
        <Accordion {...args}>
          <div id="accordion-title">
            <p className="text-sm font-semibold">Minutes remaining</p>
          </div>
          <div id="accordion-content">
            <progress
              className="progress progress-bar-rail"
              value={10}
              max="100"></progress>
            <p className="pb-3 text-xs font-semibold">0/0 MINUTES USED</p>
            <p className="text-sm">
              Your read aloud minutes will refresh on (date)
            </p>
          </div>
        </Accordion>
      </div>
    </>
  ),
};

AccordionStory.args = {
  name: 'storybook-test',
  className: 'doc-text-default',
};
