import MessagesPanel from '@components/messagesPanel/messagesPanel';
import React from 'react';

export default function DeleteEmailsErrors({
  error,
  onClose,
}: {
  error: string;
  onClose: () => void;
}) {
  if (!error) return null;

  return (
    <div className="fixed top-12 left-0 py-4 w-full z-[9999]">
      <div className="flex justify-end items-center max-w-[1200px] m-auto px-6">
        <MessagesPanel
          type="danger"
          messageTitle="Delete emails error"
          message={error}
          onClose={onClose}
        />
      </div>
    </div>
  );
}
