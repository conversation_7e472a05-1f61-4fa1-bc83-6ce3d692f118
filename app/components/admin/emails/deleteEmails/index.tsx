import Button from '@components/buttons/button';
import BaseModal from '@components/modals/BaseModal';
import ModalButtonsContainer from '@components/modals/ModalButtonsContainer';
import {Trash, Warning2} from 'iconsax-react';
import React from 'react';

export default function DeleteEmails({
  selectedEmails,
  handleDelete,
  deleting,
  clearSelection,
}: {
  selectedEmails: string[];
  handleDelete: () => void;
  deleting: boolean;
  clearSelection: () => void;
}) {
  const [modalOpen, setModalOpen] = React.useState(false);
  const closeModal = () => setModalOpen(false);
  const openModal = () => setModalOpen(true);
  const onCancel = () => {
    clearSelection();
    closeModal();
  };
  const onConfirm = () => {
    closeModal();
    handleDelete();
  };

  if (selectedEmails.length === 0) return null;

  return (
    <>
      <div className="fixed bottom-0 left-0 py-4 w-full panel-bg z-40">
        <div className="flex justify-end items-center max-w-[1200px] m-auto px-6">
          <p className="pr-4 pt-3">
            Total of emails {deleting ? 'being deleted' : 'selected'}:{' '}
            {selectedEmails.length}
          </p>
          <Button
            variant="destructive"
            loading={deleting}
            onClick={openModal}
            className="btn-md mt-3">
            Delete email{`${selectedEmails.length > 1 ? 's' : ''}`}
          </Button>
        </div>
      </div>
      <BaseModal isOpen={modalOpen} onClose={onCancel} accessibilityLabel="Confirm emails deletion">
        <div className="flex-col">
          <div className="text-center flex justify-center items-center pb-1">
            <Warning2 variant="Bulk" className={`w-12 h-12 theme-warning`} />
          </div>
          <h3 className=" text-center pb-4 text-2xl font-bold leading-relaxed">
            Confirm emails deletion
          </h3>
          <p className="mx-4 text-center pb-4 text-xl font-normal leading-loose">
            You are about to delete {selectedEmails.length} email
            {selectedEmails.length === 1 ? '' : 's'}.
          </p>

          <ModalButtonsContainer>
            <Button
              type="submit"
              variant="secondary"
              onClick={onCancel}
              className="flex-1">
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={onConfirm}
              className="flex-1">
              <Trash variant="Bold" /> Delete
            </Button>
          </ModalButtonsContainer>
        </div>
      </BaseModal>
    </>
  );
}
