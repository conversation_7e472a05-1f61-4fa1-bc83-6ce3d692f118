'use client';
import Panel from '@components/panel/Panel';
import React from 'react';
import useEmails from './useEmails';
import SvgGradientLoader from '../../loaders/svgGradientLoader';
import DeleteEmails from './deleteEmails';
import DeleteEmailsErrors from './deleteEmails/deleteEmailsErrors';

export default function Emails() {
  const {
    openTab,
    setOpenTab,
    tabs,
    emailsLoading,
    selectedEmails,
    deleteSelectedEmails,
    deleting,
    error,
    clearError,
    clearSelection,
  } = useEmails();

  return (
    <div className="pb-32">
      <Panel className="mt-6 p-6">
        <ul
          className="flex mb-0 list-none"
          role="tablist"
          aria-label="Beat user emails list">
          {tabs.map((tab, index) => (
            <li className="flex-auto text-sm text-center" key={tab.label}>
              <a
                className={
                  'uppercase px-5 py-3 block opposite-border ' +
                  (openTab === index
                    ? 'bg-transparent theme-text border-t-2 border-r-2 border-l-2 cursor-none shadow-none rounded-t-lg'
                    : 'theme-text cursor-pointer border-b-2')
                }
                onClick={e => {
                  e.preventDefault();
                  setOpenTab(index);
                }}
                data-toggle="tab"
                href={`#link${index}`}
                role="tablist">
                <span> {tab.label}</span>
                {!emailsLoading && <span> ({tab.total})</span>}
              </a>
            </li>
          ))}
        </ul>

        <div className="py-8">
          {tabs.map((tab, index) => (
            <div
              key={tab.label}
              className={openTab === index ? 'block' : 'hidden'}
              id={`link${index}`}>
              {emailsLoading ? (
                <div className="w-full flex justify-center items-center min-h-16">
                  <SvgGradientLoader />
                </div>
              ) : (
                tab.content
              )}
            </div>
          ))}
        </div>
      </Panel>

      <DeleteEmailsErrors error={error} onClose={clearError} />
      <DeleteEmails
        selectedEmails={selectedEmails}
        handleDelete={deleteSelectedEmails}
        deleting={deleting}
        clearSelection={clearSelection}
      />
    </div>
  );
}
