import CheckBox from '@components/forms/checkbox/checkbox';
import {IBetaEmail} from '@libs/store/admin/betaEmails/types';
import React from 'react';

export default function Item({
  email,
  index,
  toggleSelection,
  selected,
}: {
  email: IBetaEmail;
  index: number;
  toggleSelection: (email: string) => void;
  selected: boolean;
}) {
  const {email: betaEmail, claimedBy} = email;

  const handleCheckboxPress = () => toggleSelection(betaEmail);

  return (
    <li className="grid grid-cols-[40px_1fr_1fr_50px] app-bg-subdued py-4 px-6 my-2 items-center rounded-md">
      <span className="w-10">{index}</span>
      <span>{betaEmail}</span>
      <span className="text-sm">{claimedBy}</span>
      <span className="pl-5">
        <CheckBox
          label=""
          ariaLabel="Select email"
          id={betaEmail}
          checked={selected}
          onPress={handleCheckboxPress}
        />
      </span>
    </li>
  );
}
