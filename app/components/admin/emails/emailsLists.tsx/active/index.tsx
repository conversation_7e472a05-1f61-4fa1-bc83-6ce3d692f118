import {IBetaEmail} from '@libs/store/admin/betaEmails/types';
import React from 'react';
import Item from './item';

export default function ActiveEmailsList({
  emails,
  selectedEmails,
  toggleSelection,
}: {
  emails: IBetaEmail[];
  selectedEmails: string[];
  toggleSelection: (email: string) => void;
}) {
  return (
    <ul aria-label="Active beta users emails">
      {emails.map((email, index) => {
        const selected = selectedEmails.includes(email.email);
        return (
          <Item
            key={email.email}
            email={email}
            index={index + 1}
            toggleSelection={toggleSelection}
            selected={selected}
          />
        );
      })}
    </ul>
  );
}
