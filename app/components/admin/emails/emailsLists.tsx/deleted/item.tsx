import DateWithAt from '@components/dates/dateWithAt';
import {IBetaEmail} from '@libs/store/admin/betaEmails/types';
import React from 'react';

export default function Item({
  email,
  index,
}: {
  email: IBetaEmail;
  index: number;
}) {
  const {email: betaEmail, deletedBy, dateDeleted} = email;

  return (
    <li className="grid grid-cols-[40px_1fr_1fr_auto] app-bg-subdued py-4 px-6 my-2 items-center rounded-md">
      <span className="w-10">{index}</span>
      <span>{betaEmail}</span>
      <span className="text-sm">Deleted by: {deletedBy}</span>
      <span className="text-sm" arial-label="Email added on:">
        {dateDeleted && <DateWithAt date={dateDeleted} />}
      </span>
    </li>
  );
}
