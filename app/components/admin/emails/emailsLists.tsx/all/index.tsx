import {IBetaEmail} from '@libs/store/admin/betaEmails/types';
import React, {ChangeEvent} from 'react';
import Item from './item';
import SearchEmails from './searchEmails';

export default function AllEmailsList({
  emails,
  selectedEmails,
  toggleSelection,
}: {
  emails: IBetaEmail[];
  selectedEmails: string[];
  toggleSelection: (email: string) => void;
}) {
  const [search, setSearch] = React.useState('');
  const searchEmails = (e: ChangeEvent<HTMLInputElement>) =>
    setSearch(e.currentTarget.value);

  return (
    <>
      <SearchEmails search={search} searchEmails={searchEmails} />
      <ul aria-label="All beta users emails">
        {emails
          .filter(e =>
            e.email.toLowerCase().includes(search.toLocaleLowerCase()),
          )
          .map((email, index) => {
            const selected = selectedEmails.includes(email.email);
            return (
              <Item
                key={email.email}
                email={email}
                index={index + 1}
                toggleSelection={toggleSelection}
                selected={selected}
              />
            );
          })}
      </ul>
    </>
  );
}
