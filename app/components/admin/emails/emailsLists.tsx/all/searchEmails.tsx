import React from 'react';
import {FiSearch} from 'react-icons/fi';
import TextInput from '@components/forms/inputs/textInput';

export default function SearchEmails({
  search,
  searchEmails,
}: {
  search: string;
  searchEmails: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) {
  return (
    <TextInput
      className="w-full"
      id="beta-emails-search"
      label="Search emails"
      value={search}
      onChange={searchEmails}
      iconRight={<FiSearch className="mx-4" />}
    />
  );
}
