import DateWithAt from '@components/dates/dateWithAt';
import CheckBox from '@components/forms/checkbox/checkbox';
import ErrorPill from '@components/pills/errorPill';
import ProcessingPill from '@components/pills/processingPill';
import ReadyPill from '@components/pills/readyPill';
import {IBetaEmail} from '@libs/store/admin/betaEmails/types';
import {formatDateWithAt} from '@libs/utils/dates/dateFormats';
import React from 'react';

export default function Item({
  email,
  index,
  toggleSelection,
  selected,
}: {
  email: IBetaEmail;
  index: number;
  toggleSelection: (email: string) => void;
  selected: boolean;
}) {
  const {
    email: betaEmail,
    addedBy,
    dateCreated,
    claimedBy,
    deletedBy,
    dateDeleted,
  } = email;

  let status = <ProcessingPill label="Inactive" />;
  if (claimedBy) {
    status = <ReadyPill label="Active" />;
  }
  if (dateDeleted) {
    status = (
      <ErrorPill
        label="Deleted"
        errorMessage={`Deleted by ${deletedBy} at ${formatDateWithAt(
          dateDeleted,
        )}`}
      />
    );
  }

  const handleCheckboxPress = () => toggleSelection(betaEmail);

  return (
    <li className="grid grid-cols-[40px_1fr_1fr_1fr_120px_50px] app-bg-subdued py-4 px-6 my-2 items-center rounded-md">
      <span className="w-10">{index}</span>
      <span>{betaEmail}</span>
      <span className="text-sm">Added by: {addedBy}</span>
      <span className="text-sm" arial-label="Email added on:">
        <DateWithAt date={dateCreated} />
      </span>
      <span arial-label="status">{status}</span>
      <span className="pl-5">
        <CheckBox
          label=""
          disabled={!!dateDeleted}
          ariaLabel="select email"
          id={betaEmail}
          checked={selected}
          onPress={handleCheckboxPress}
        />
      </span>
    </li>
  );
}
