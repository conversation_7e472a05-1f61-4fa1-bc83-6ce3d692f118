import useBetaEmails from '@libs/services/admin/betaEmails/useBetaEmails';
import React from 'react';
import AllEmailsList from './emailsLists.tsx/all';
import InactiveEmailsList from './emailsLists.tsx/inactive';
import ActiveEmailsList from './emailsLists.tsx/active';
import DeletedEmailsList from './emailsLists.tsx/deleted';
import useBetaEmailsServices from '@libs/services/admin/betaEmails/useBetaEmailsService';

interface IBetaEmailTab {
  label: string;
  total: number;
  content: React.ReactNode;
}

export default function useEmails() {
  const [openTab, setOpenTab] = React.useState(0);
  const {betaEmails, isLoading} = useBetaEmails();
  const [selectedEmails, setSelectedEmails] = React.useState<string[]>([]);
  const {deleteEmails} = useBetaEmailsServices();
  const [deleting, setDeleting] = React.useState<boolean>(false);
  const [error, setError] = React.useState<string>('');

  const toggleSelection = (email: string) => {
    if (selectedEmails.includes(email)) {
      setSelectedEmails(selectedEmails.filter(e => e !== email));
    } else {
      setSelectedEmails([...selectedEmails, email]);
    }
  };

  const activeEmails = betaEmails.filter(email => email.claimedBy);
  const inactiveEmails = betaEmails.filter(
    email => !email.claimedBy && !email.dateDeleted,
  );
  const deletedEmails = betaEmails.filter(email => email.dateDeleted);

  const tabs: IBetaEmailTab[] = [
    {
      label: 'All emails',
      content: (
        <AllEmailsList
          emails={betaEmails}
          selectedEmails={selectedEmails}
          toggleSelection={toggleSelection}
        />
      ),
      total: betaEmails.length,
    },
    {
      label: 'Inactive emails',
      content: (
        <InactiveEmailsList
          emails={inactiveEmails}
          selectedEmails={selectedEmails}
          toggleSelection={toggleSelection}
        />
      ),
      total: inactiveEmails.length,
    },
    {
      label: 'Active emails',
      content: (
        <ActiveEmailsList
          emails={activeEmails}
          selectedEmails={selectedEmails}
          toggleSelection={toggleSelection}
        />
      ),
      total: activeEmails.length,
    },
    {
      label: 'Deleted emails',
      content: <DeletedEmailsList emails={deletedEmails} />,
      total: deletedEmails.length,
    },
  ];

  const clearError = () => setError('');
  const clearSelection = () => setSelectedEmails([]);

  const deleteSelectedEmails = () => {
    clearError();
    setDeleting(true);
    deleteEmails(selectedEmails)
      .catch((err: Error) => {
        setError(err.message);
      })
      .finally(() => {
        setSelectedEmails([]);
        setDeleting(false);
      });
  };

  return {
    openTab,
    setOpenTab,
    tabs,
    emailsLoading: isLoading,
    selectedEmails,
    deleteSelectedEmails,
    error,
    clearError,
    deleting,
    clearSelection,
  };
}
