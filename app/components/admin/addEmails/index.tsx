'use client';

import Button from '@components/buttons/button';
import MessagesPanel from '@components/messagesPanel/messagesPanel';
import Panel from '@components/panel/Panel';
import React from 'react';
import {ReactMultiEmail, isEmail} from 'react-multi-email';
import 'react-multi-email/dist/style.css';
import useAddEmails from './useAddEmails';

export default function AddEmails() {
  const {
    emails,
    setEmails,
    invalidEmails,
    setInvalidEmails,
    error,
    handleEmailsSubmit,
    submitting,
  } = useAddEmails();

  const invalidEmailsError = invalidEmails.length > 0 && emails.length > 0;
  const showError = error || invalidEmailsError;
  const invalidEmailsString = invalidEmails.join(', ');
  const errorMessage = error
    ? error
    : `The following email(s) are invalid and have been removed from the list: ${invalidEmailsString}`;

  return (
    <Panel className="p-6">
      <form>
        <h2 className="pb-2">Add alpha user approved email(s)</h2>

        {showError && (
          <MessagesPanel
            type="danger"
            messageTitle="Invalid Email(s)"
            message={errorMessage}
            hideCloseButton
          />
        )}
        <ReactMultiEmail
          id="add-beta-emails"
          style={{
            backgroundColor: 'transparent',
            borderWidth: 2.5,
            borderRadius: 10,
          }}
          className="rounded-lg form-input doc-text-default"
          inputClassName="bg-transparent"
          emails={emails}
          onChange={(_emails: string[]) => {
            setEmails(_emails);
          }}
          autoFocus={true}
          getLabel={(email, index, removeEmail) => {
            return (
              <div data-tag key={index}>
                <div
                  data-tag-item
                  className="doc-text-subdued text-sm font-normal">
                  {email}
                </div>
                <span
                  className="text-red-600"
                  data-tag-handle
                  onClick={() => removeEmail(index)}>
                  ×
                </span>
              </div>
            );
          }}
          validateEmail={email => {
            // List of invalid emails
            console.log(email.trim());
            if (!isEmail(email) && !invalidEmails.includes(email)) {
              setInvalidEmails(prev => [...prev, email]);
            }
            return isEmail(email);
          }}
        />
        <div className="flex justify-end">
          <Button
            variant="primary"
            disabled={emails.length === 0 || submitting}
            loading={false}
            onClick={handleEmailsSubmit}
            className="btn-md mt-3">
            Submit
          </Button>
        </div>
      </form>
    </Panel>
  );
}
