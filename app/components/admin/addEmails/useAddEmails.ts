import useBetaEmailsServices from '@libs/services/admin/betaEmails/useBetaEmailsService';
import React from 'react';

export default function useAddEmails() {
  const [emails, setEmails] = React.useState<string[]>([]);
  const [invalidEmails, setInvalidEmails] = React.useState<string[]>([]);
  const {addEmails} = useBetaEmailsServices();
  const [error, setError] = React.useState<string>('');
  const [submitting, setSubmitting] = React.useState<boolean>(false);

  const handleEmailsSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSubmitting(true);

    addEmails(emails)
      .catch((err: Error) => {
        setError(err.message);
      })
      .finally(() => {
        setInvalidEmails([]);
        setEmails([]);
        setSubmitting(false);
      });
  };

  return {
    emails,
    setEmails,
    invalidEmails,
    setInvalidEmails,
    handleEmailsSubmit,
    error,
    submitting,
  };
}
