import React from 'react';

export default function SvgGradientLoader({
  size = 80,
  strokeWidth = 20,
  className = 'verify-spinner',
  srLabel = 'Loading...',
  roundedHead = false, //ensures smoother gradient transition between the head and the tail of the spinner
}: {
  size?: number;
  strokeWidth?: number;
  className?: string; //takes TW class for text, eg. text-red-500
  srLabel?: string;
  roundedHead?: boolean;
}) {
  return (
    <div role="status">
      <svg
        width={size}
        height={size}
        viewBox="-20 -20 240 240"
        className={className}
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="spinner-secondHalf">
            <stop offset="0%" stopOpacity="0" stopColor="currentColor" />
            <stop offset="100%" stopOpacity="0.5" stopColor="currentColor" />
          </linearGradient>
          <linearGradient id="spinner-firstHalf">
            <stop offset="0%" stopOpacity="1" stopColor="currentColor" />
            <stop offset="100%" stopOpacity="0.5" stopColor="currentColor" />
          </linearGradient>
        </defs>

        <g strokeWidth={strokeWidth}>
          <path
            stroke="url(#spinner-secondHalf)"
            d="M 4 100 A 96 96 0 0 1 196 100"
          />
          <path
            stroke="url(#spinner-firstHalf)"
            d="M 196 100 A 96 96 0 0 1 4 100"
          />

          {roundedHead && (
            <path
              stroke="currentColor"
              strokeLinecap="round"
              d="M 4 100 A 96 96 0 0 1 4 98"
            />
          )}
        </g>

        <animateTransform
          from="0 0 0"
          to="360 0 0"
          attributeName="transform"
          type="rotate"
          repeatCount="indefinite"
          dur="1300ms"
        />
      </svg>
      <span className="sr-only">{srLabel}</span>
    </div>
  );
}
