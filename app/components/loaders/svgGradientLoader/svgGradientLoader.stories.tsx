import type {Meta, StoryObj} from '@storybook/react';
import SvgGradientLoader from '.';

const meta: Meta<typeof SvgGradientLoader> = {
  title: 'Loaders/SvgGradientLoader',
  component: SvgGradientLoader,
  parameters: {
    controls: {expanded: true},
  },
};

export default meta;
type Story = StoryObj<typeof SvgGradientLoader>;

export const SvgGradientLoaderDefault: Story = {
  render: args => <SvgGradientLoader {...args} />,
};
