import {IconButtonSize} from '@components/buttons/iconButton';
import React from 'react';

const ButtonSpinner = ({size}: {size?: IconButtonSize}) => {
  let spinnerSize = '';
  switch (size) {
    case 'small':
      spinnerSize = 'h-4 w-4';
      break;
    case 'medium':
      spinnerSize = 'h-6 w-6';
      break;
    case 'large':
    default:
      spinnerSize = 'h-8 w-8';
  }
  return (
    <div
      className={`inline-block ${spinnerSize} animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]`}
      role="status">
      {/* This adds a span that helps with SR to know that it is loading */}
      <span className="sr-only">Loading...</span>
    </div>
  );
};

export default ButtonSpinner;
