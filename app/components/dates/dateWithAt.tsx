import {formatDateWithAt} from '@libs/utils/dates/dateFormats';
import React from 'react';

export default function DateWithAt({
  date,
  className = '',
}: {
  date: Date;
  className?: string;
}) {
  const isNotChromium = !(
    /Chrome/.test(navigator.userAgent) || /Chromium/.test(navigator.userAgent)
  );
  const correctDateFormat = isNotChromium
    ? date.toString()
    : new Date(date).toISOString();
  return (
    <time
      dateTime={correctDateFormat}
      className={`text-xs font-normal uppercase tracking-widest card-content-subdued ${className}`}>
      {formatDateWithAt(correctDateFormat)}
    </time>
  );
}
