import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import Tailo<PERSON>ull<PERSON>ogo from './tailoFullLogo';

const meta: Meta<typeof TailoFullLogo> = {
  title: 'Logos/TailoFullLogo',
  component: TailoFullLogo,
  parameters: {
    controls: {expanded: true},
  },
};

export default meta;
type Story = StoryObj<typeof TailoFullLogo>;

export const TailoFullLogoDefault: Story = {
  render: args => (
    <>
      <div data-theme="dark">
        <div className="app-bg">
          <TailoFullLogo {...args} />
        </div>
      </div>
      <div data-theme="light">
        <div className="app-bg">
          <TailoFullLogo {...args} />
        </div>
      </div>
      <div data-theme="yellow">
        <div className="app-bg">
          <TailoFullLogo {...args} />
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="app-bg">
          <TailoFullLogo {...args} />
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="app-bg">
          <TailoFullLogo {...args} />
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="app-bg">
          <TailoFullLogo {...args} />
        </div>
      </div>
      <div data-theme="peach">
        <div className="app-bg">
          <TailoFullLogo {...args} />
        </div>
      </div>
    </>
  ),
};

TailoFullLogoDefault.argTypes = {
  className: {control: {type: 'text'}},
  width: {control: {type: 'number'}},
  height: {control: {type: 'number'}},
  ariaLabel: {control: {type: 'text'}},
};
TailoFullLogoDefault.args = {
  className: 'doc-text-default',
  width: 281,
  height: 58,
  ariaLabel: 'Tailo Logo',
};
