'use client';
import {LinkProps} from 'next/link';
import React from 'react';
import IconButtonLink from '@components/buttons/iconButtonLink';
import TailoFullLogo from './tailoFullLogo';
import Tailo<PERSON>etterformLogo from './tailoLetterformLogo';

export interface ITailoLogoLink {
  href: string;
  onClick?: () => void;
  linkProps?: Omit<LinkProps, 'href' | 'className'>;
  accessibilityLabel: string;
  variant: 'full' | 'letterform';
  className?: string;
}
export default function TailoLogoLink(props: ITailoLogoLink) {
  const {href, onClick, linkProps, accessibilityLabel, variant, ...logoProps} =
    props;
  return (
    <IconButtonLink
      href={href}
      onClick={onClick}
      accessibilityLabel={accessibilityLabel}
      size="medium">
      {variant === 'full' ? (
        <TailoFullLogo {...logoProps} />
      ) : (
        <TailoLetterformLogo fillColour="logo-fill-bg" {...logoProps} />
      )}
    </IconButtonLink>
  );
}
