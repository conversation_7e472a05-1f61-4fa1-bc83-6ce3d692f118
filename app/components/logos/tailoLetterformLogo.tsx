'use client';
import React from 'react';
interface TailoLetterformLogoProps {
  className?: string;
  width?: number;
  height?: number;
  ariaLabel?: string;
  fillColour?: string;
}

export default function TailoLetterformLogo(props: TailoLetterformLogoProps) {
  const {
    className = 'active:scale-90 h-8 w-8',
    width = 128,
    height = 128,
    ariaLabel = 'Tailo Logo',
    fillColour = 'fill-white',
  } = props;

  return (
    <svg
      viewBox="0 0 128 128"
      fill="none"
      className={`${className} ${fillColour}`}
      aria-label={ariaLabel}
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 0C5.37258 0 0 5.37258 0 12V116C0 122.627 5.37258 128 12 128H116C122.627 128 128 122.627 128 116V12C128 5.37258 122.627 0 116 0H12ZM30.1538 64.5386C30.1538 45.4404 45.6706 30.1818 64.5058 30.1818C82.8772 30.1818 97.8462 42.8937 97.8462 63.0228C97.8462 67.7128 96.3686 71.2522 94.7019 74.6473C94.5126 75.0329 94.3145 75.4287 94.1131 75.8313L94.1129 75.8317C93.4594 77.1375 92.7705 78.5139 92.2324 79.8404C91.5036 81.6366 90.9231 83.6237 90.9231 85.8184C90.9231 89.8149 92.1989 92.6473 93.1477 94.6386L93.2819 94.92L93.282 94.9202C93.703 95.8022 93.9456 96.3104 94.0861 96.7725C94.1777 97.0739 94.078 97.1126 93.8213 97.2123C93.7967 97.2218 93.7707 97.2319 93.7433 97.2429C93.035 97.5256 91.8859 97.7613 90.2997 97.8093C87.1546 97.9043 82.8982 97.2438 78.4497 95.5767C74.5761 94.1251 70.6861 91.966 67.3117 89.0022C70.6289 86.7268 73.7604 83.6603 76.5447 79.745C83.3941 70.1134 85.3203 61.5765 83.7216 54.8378C82.1122 48.0538 77.0422 43.6748 71.3012 42.4535C65.5572 41.2315 59.1761 43.1806 55.0421 48.7665C50.9435 54.3048 49.3966 62.9148 51.9314 74.5109C52.9895 79.3519 54.8961 83.5081 57.3413 87.0411C53.1062 88.3012 48.8743 88.1376 45.1037 86.8916C36.8043 84.1489 30.1538 75.995 30.1538 64.5386ZM76.2987 101.369C71.2463 99.4752 65.9901 96.4535 61.5868 92.0929C55.4527 94.5759 49.0214 94.694 43.1807 92.7638C32.2759 89.1602 24 78.5784 24 64.5386C24 41.9688 42.3294 24 64.5058 24C85.892 24 104 39.1064 104 63.0228C104 69.1512 102.016 73.7253 100.221 77.3815C99.9783 77.8762 99.7463 78.3401 99.5255 78.7816C98.9031 80.0259 98.3695 81.0929 97.9311 82.1735C97.3618 83.5768 97.0769 84.7289 97.0769 85.8184C97.0769 88.39 97.8524 90.194 98.6985 91.9697C98.7179 92.0105 98.7377 92.0518 98.7578 92.0939C98.7859 92.1525 98.8147 92.2123 98.844 92.2733C99.2191 93.0539 99.6842 94.0217 99.9716 94.9672C100.332 96.1515 100.517 97.5904 99.9959 99.1595C99.3096 101.228 97.55 102.375 96.0155 102.988C94.4171 103.626 92.4964 103.927 90.4849 103.988C86.4347 104.111 81.3846 103.275 76.2987 101.369ZM71.5373 76.1516C68.9315 79.8159 66.0468 82.5294 63.0556 84.4206C60.7099 81.2943 58.9001 77.5683 57.942 73.185C55.6308 62.6119 57.3187 56.0518 59.9808 52.4547C62.6075 48.9053 66.5129 47.7537 70.0262 48.5012C73.5427 49.2493 76.7008 51.9098 77.7354 56.271C78.7809 60.6775 77.7671 67.3913 71.5373 76.1516Z"
        fill="currentColor"
      />
    </svg>
  );
}
