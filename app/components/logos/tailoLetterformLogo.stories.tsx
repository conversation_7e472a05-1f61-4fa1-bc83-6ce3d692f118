import type {Meta, StoryObj} from '@storybook/react';
import TailoLetterform<PERSON>ogo from './tailoLetterformLogo';

const meta: Meta<typeof TailoLetterformLogo> = {
  title: 'Logos/TailoLetterformLogo',
  component: TailoLetterformLogo,
  parameters: {
    controls: {expanded: true},
  },
};

export default meta;
type Story = StoryObj<typeof TailoLetterformLogo>;

export const TailoLetterformLogoDefault: Story = {
  render: args => (
    <div className="flex">
      <div data-theme="dark">
        <div className="app-bg p-8">
          <TailoLetterformLogo {...args} />
        </div>
      </div>
      <div data-theme="light">
        <div className="app-bg p-8">
          <TailoLetterformLogo {...args} />
        </div>
      </div>
      <div data-theme="yellow">
        <div className="app-bg p-8">
          <TailoLetterformLogo {...args} />
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="app-bg p-8">
          <TailoLetterformLogo {...args} />
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="app-bg p-8">
          <TailoLetterformLogo {...args} />
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="app-bg p-8">
          <TailoLetterformLogo {...args} />
        </div>
      </div>
      <div data-theme="peach">
        <div className="app-bg p-8">
          <TailoLetterformLogo {...args} />
        </div>
      </div>
    </div>
  ),
};

TailoLetterformLogoDefault.argTypes = {
  className: {control: {type: 'text'}},
  width: {control: {type: 'number'}},
  height: {control: {type: 'number'}},
  ariaLabel: {control: {type: 'text'}},
};
TailoLetterformLogoDefault.args = {
  className: 'doc-text-default',
  fillColour: 'logo-fill-bg',
  width: 128,
  height: 128,
  ariaLabel: 'Tailo Logo',
};
