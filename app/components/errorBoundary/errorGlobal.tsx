'use client';

import {useEffect} from 'react';
import * as Sen<PERSON> from '@sentry/browser';
import ThemedPageWrapper from '@components/wrappers/themed/themedPageWrapper';

export default function GlobalError({
  error,
}: {
  error: Error & {digest?: string};
}) {
  useEffect(() => {
    Sentry.captureException(error);
    console.error(error);
  }, [error]);

  return (
    <html>
      <body className="w-screen h-screen">
        <ThemedPageWrapper className="flex items-center justify-center">
          <div>
            <h2 className="text-lg text-center">Something went wrong!</h2>
          </div>
        </ThemedPageWrapper>
      </body>
    </html>
  );
}
