'use client';

import {useEffect} from 'react';
import * as Sentry from '@sentry/browser';

export default function Error({error}: {error: Error & {digest?: string}}) {
  useEffect(() => {
    Sentry.captureException(error);
    console.error(error);
  }, [error]);

  return (
    <div className="w-full h-full flex-col justify-center items-center text-center">
      <h2 className="text-lg pb-8">Something went wrong!</h2>
    </div>
  );
}
