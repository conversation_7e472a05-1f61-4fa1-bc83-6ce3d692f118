'use client';
import IconButton from '@components/buttons/iconButton';
import DrawerTrigger from '@components/modals/drawer/drawerTrigger';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import {BrushSquare} from 'iconsax-react';
import {usePathname} from 'next/navigation';
import React from 'react';
import {useAnalytics} from 'use-analytics';

interface CustomiseButtonProps {
  onClick?: () => void;
  size?: 'small' | 'medium' | 'large';
}

const CustomiseButton = (props: CustomiseButtonProps) => {
  const {onClick, size = 'medium'} = props;
  const {openDrawer, closeDrawer, drawStateById} = useDrawer();
  const {track} = useAnalytics();
  const pathname = usePathname();

  const handleClick = () => {
    if (drawStateById(DRAWER_ID.CUSTOMISATION) === 'open') {
      closeDrawer(DRAWER_ID.CUSTOMISATION);
      return;
    }
    trackMenuOpen();
    if (onClick) return onClick();
    openDrawer(DRAWER_ID.CUSTOMISATION);
  };

  const trackMenuOpen = () => {
    track('VisualSettingsOpen', {
      PageSource: pathname,
    });
  };

  return (
    <>
      <div className="hidden lg:flex">
        <DrawerTrigger
          drawerId={DRAWER_ID.CUSTOMISATION}
          ariaLabel="Open customisation menu"
          className="btn h-fit p-2 btn-tertiary"
          onClick={handleClick}>
          <BrushSquare variant="Bold" className="w-6 h-6" />
        </DrawerTrigger>
      </div>
      <div className="lg:hidden">
        <IconButton
          size={size}
          onClick={handleClick}
          accessibilityLabel="Customise">
          <BrushSquare variant="Bold" className="w-6 h-6" />
        </IconButton>
      </div>
    </>
  );
};

export default CustomiseButton;
