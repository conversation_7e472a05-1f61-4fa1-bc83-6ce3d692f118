'use client';
import React, {FC} from 'react';
import NavLink from './navLink';
import useSummaries from '@libs/services/summary/useSummaries';
import {usePathname} from 'next/navigation';
import {AppDynamicRoutes} from '@libs/services/auth/routes';

interface OriginalSummaryTabsProps {
  id: string;
}

const OriginalSummaryTabs: FC<OriginalSummaryTabsProps> = ({id}) => {
  const {latestSummary} = useSummaries(id);
  const currentRoute = usePathname();

  const className =
    'py-4  sm:px-4 flex-1 justify-center items-center text-center z-30';

  //TODO update the tab query when it is confirmed how user gets here
  const originalHref = AppDynamicRoutes.document(id);
  return (
    <>
      <NavLink
        className={className}
        href={originalHref}
        isActive={originalHref === currentRoute}>
        Original
      </NavLink>
      {latestSummary && (
        <NavLink
          className={className}
          href={AppDynamicRoutes.summary(id, latestSummary.id)}
          isActive={currentRoute.includes(originalHref + '/summary/')}>
          Summary
        </NavLink>
      )}
    </>
  );
};

export default OriginalSummaryTabs;
