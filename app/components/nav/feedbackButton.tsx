import {MessageText1} from 'iconsax-react';
import React from 'react';
import IconButton from '@components/buttons/iconButton';
import DrawerTrigger from '@components/modals/drawer/drawerTrigger';
import {DRAWER_ID} from '@libs/store/drawer/types';
import useDrawer from '@libs/store/drawer';
import {usePathname} from 'next/navigation';
export default function FeedbackButton({
  size = 'medium',
  onClick,
  isMobileView,
}: {
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
  isMobileView?: boolean;
}) {
  const {openDrawer, closeDrawer, drawStateById} = useDrawer();
  const pathname = usePathname();

  const handleClick = () => {
    if (drawStateById(DRAWER_ID.FEEDBACK) === 'open') {
      closeDrawer(DRAWER_ID.FEEDBACK);
      return;
    }
    if (onClick) return onClick();
    openDrawer(DRAWER_ID.FEEDBACK);
  };

  return (
    <>
      {isMobileView ? (
        <IconButton
          size={size}
          onClick={handleClick}
          accessibilityLabel="Feedback">
          <MessageText1 variant="Bold" className="w-6 h-6" />
        </IconButton>
      ) : (
        <DrawerTrigger
          drawerId={DRAWER_ID.FEEDBACK}
          ariaLabel="Open feedback panel"
          className="btn h-fit p-2 btn-tertiary"
          onClick={handleClick}>
          <MessageText1 variant="Bold" className="w-6 h-6" />
        </DrawerTrigger>
      )}
    </>
  );
}
