import TailoLogoLink from '@components/logos/tailoLogoLink';
import {liStyle} from './shared';
import {AppPrivateRoutes, AppPublicRoutes} from '@libs/services/auth/routes';
import {getServerSession} from 'next-auth/next';
import {FC} from 'react';

interface NavWrapperProps {
  children?: React.ReactNode;
  className?: string;
  logoLink?: string;
  accessibilityLabel?: string;
}

const NavWrapper: FC<NavWrapperProps> = async ({
  children,
  className,
  logoLink,
  accessibilityLabel,
}) => {
  const session = await getServerSession();
  return (
    <header className="sticky top-0 z-40">
      <nav
        className={`${
          className || 'app-navbar-bg border-b'
        } separator pl-1 pr-2 sm:pl-4`}>
        <ul className="flex items-center w-full">
          <li className={`${liStyle} ${className}`}>
            <TailoLogoLink
              href={
                logoLink ||
                (session ? AppPrivateRoutes.Dashboard : AppPublicRoutes.SignIn)
              }
              accessibilityLabel={
                accessibilityLabel || session
                  ? 'Go back to dashboard'
                  : 'Go back to Login page'
              }
              variant="letterform"
              className="w-8 h-8 sm:w-10 sm:h-10"
            />
          </li>
          {children}
        </ul>
      </nav>
    </header>
  );
};

export default NavWrapper;
