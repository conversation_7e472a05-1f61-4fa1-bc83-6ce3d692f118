import UserCircle from '@components/customIcons/userCircle';
import {useState} from 'react';
import {Security, Flag2} from 'iconsax-react';
import LogoutButton from '../logoutButton';
import Link from 'next/link';
import {FiMoreHorizontal} from 'react-icons/fi';
import OutsideClickHandler from '@components/menus/dropdown/dropdownItems/outsideClickHandler';
import TailoIconButton from '@components/buttons/tailoIconButton';

export default function DropDownNavigation() {
  const [isOpen, setIsOpen] = useState(false);
  const navRoutes = [
    {
      name: 'My details',
      icon: <UserCircle />,
      route: '/account',
    },
    // Add help route back in once it is implemented
    {
      name: 'Help',
      icon: <Flag2 variant="Bold" />,
      route: 'https://tailoapp.com/support/',
    },
    {
      name: 'Admin',
      icon: <Security variant="Bold" />,
      route: '/admin',
    },
    {
      name: 'Logout',
      icon: <LogoutButton />,
      route: '/logout',
    },
  ];
  const handleOutsideClick = () => {
    setIsOpen(false);
  };
  return (
    <>
      <div
        className="dropdown relative"
        role="button"
        onClick={() => {
          setIsOpen(!isOpen);
        }}>
        <TailoIconButton
          label="Open menu dropdown"
          isActive={isOpen}
          size="small">
          <FiMoreHorizontal className="w-6 h-6" />
        </TailoIconButton>
        {isOpen && (
          <OutsideClickHandler onOutsideClick={handleOutsideClick}>
            <ul className="dropdown-panel z-max">
              {navRoutes.map(route => (
                <li
                  key={route.name}
                  className=" text-base dropdown-nav-links w-full">
                  <Link
                    href={route.route}
                    className="btn btn-sm btn-tertiary w-full">
                    <span className="flex-none">{route.icon}</span>
                    <span className="flex-none w-full whitespace-nowrap">
                      {route.name}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </OutsideClickHandler>
        )}
      </div>
    </>
  );
}
