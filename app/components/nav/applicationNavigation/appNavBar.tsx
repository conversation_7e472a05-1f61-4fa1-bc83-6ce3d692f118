'use client';
import {useRef} from 'react';
import useElementScrollDirection from '@libs/utils/scrolling/useElementScrollDirection';
import DocumentNavigation from '../documentOverview/documentNavigation/documentNavigation';
import {AppPrivateRoutes} from '@libs/services/auth/routes';
import Tailo<PERSON>etterform<PERSON>ogo from '@components/logos/tailoLetterformLogo';
import {usePathname} from 'next/navigation';
import Link from 'next/link';

export default function AppNavBar() {
  const scrollableRef = useRef<HTMLDivElement>(null);
  const {scrollingDown} = useElementScrollDirection(scrollableRef);
  const pathname = usePathname();

  return (
    <nav className="nav-bg p-1 w-full fixed flex justify-between items-center nav-border z-nav top-0">
      {!pathname.includes('/document') && (
        <Link
          href={AppPrivateRoutes.Dashboard}
          className="p-0.5 ml-2 border-2 border-transparent rounded-md small-tailo-logo">
          <TailoLetterformLogo />
        </Link>
      )}
      <DocumentNavigation
        animOnScrollClass={
          scrollingDown ? 'translate-y-20 lg:translate-y-0' : 'translate-y-0'
        }
      />
    </nav>
  );
}
