import TailoLogoLink from '@components/logos/tailoLogoLink';
import {AppPrivateRoutes} from '@libs/services/auth/routes';
import React from 'react';
import CustomiseButton from '../customiseButton';
import {liStyleApplicationNavMobile} from '../shared';
import {AnyAbility} from '@casl/ability';
import AccountMenu from '../account/accountMenu';
import useWindowSize from '@libs/utils/windowSize/useIsMobile';
import IconButtonLink from '@components/buttons/iconButtonLink';
import {ArrowLeft2} from 'iconsax-react';
import DocumentToolsMenu from '../documentOverview/documentToolsMenu';
import NavBottomSheet from './navBottomSheet';
import FeedbackButton from '../feedbackButton';

interface MobileViewProps {
  animOnScrollClass: string;
  ability: AnyAbility;
  dashboard: boolean;
  privatePages?: boolean;
}

export default function MobileView(props: MobileViewProps) {
  const {animOnScrollClass, ability, dashboard, privatePages = false} = props;
  // we need to use the windowSize hook as tw media queries do not stop the bottom sheet from firing the underlying JS to disable scrolling
  const {isLargeTablet} = useWindowSize();
  return (
    // Absolute styling is used to hide the nav on smaller screens when scrolling down
    // The width of the container on larger view ports is set in base.scss file
    <>
      <nav
        aria-label="main"
        className={`block lg:hidden h-full w-full border-b separator app-navbar-bg z-nav-app grid-area-overview-app-nav absolute top-0 lg:relative transition-transform duration-500 ${
          !dashboard ? animOnScrollClass : ''
        }`}>
        <ul className="flex justify-between items-center lg:hidden w-full app-navbar-bg px-2 md:px-4">
          {!dashboard ? (
            <li>
              <IconButtonLink
                href={AppPrivateRoutes.Dashboard}
                accessibilityLabel="Back to dashboard"
                size="small">
                <ArrowLeft2 />
              </IconButtonLink>
            </li>
          ) : (
            <li>
              <TailoLogoLink
                href={AppPrivateRoutes.Dashboard}
                accessibilityLabel="Go back to dashboard"
                variant="letterform"
                className="w-8 h-8"
              />
            </li>
          )}
          {privatePages && (
            <li>
              <ul className="flex">
                <li className={liStyleApplicationNavMobile}>
                  <FeedbackButton size="small" isMobileView />
                </li>
                <li className={liStyleApplicationNavMobile}>
                  <CustomiseButton size="small" />
                </li>
                {dashboard && ability.can('read', 'account') && (
                  <li className={liStyleApplicationNavMobile}>
                    <AccountMenu />
                  </li>
                )}
                {!dashboard && (
                  <li className={liStyleApplicationNavMobile}>
                    <DocumentToolsMenu />
                  </li>
                )}
              </ul>
            </li>
          )}
        </ul>
      </nav>
      {/* we need to use the isLargeTablet check here as well as tw media queries so bottom sheet JS does not fire and we don't see 2 buttons for a moment */}
      {isLargeTablet && (
        // bottom sheets outside nav to avoid positioning issues due to the scroll animation styling
        <NavBottomSheet />
      )}
    </>
  );
}
