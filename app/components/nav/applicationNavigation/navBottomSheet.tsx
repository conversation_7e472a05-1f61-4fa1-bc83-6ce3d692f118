import React from 'react';
import AccountMenuContent from '../account/accountMenuContents';
import Customisation from '@components/customisation/customisation';
import BottomSheet from '@components/modals/bottomSheet/bottomSheet';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import DocumentToolsMenuContent from '../documentOverview/documentToolsNavigation/mobileDocumentTools/documentToolsMenuContent';
import DocumentToolsContentContainer from '../documentOverview/documentToolsNavigation/mobileDocumentTools/documentToolsContainer';
import BottomSheetFullHeight from '@components/modals/bottomSheet/bottomSheetFullHeight';
import {FeedbackContentView} from '@components/feedback/feedbackContentView';
import FootnotesContent from '@components/footnotes/footnotesContent';
import {useFootnotes} from '@contexts/footnotesContext';

export default function NavBottomSheet() {
  const {getOpenDrawerId, docToolsDrawerContent} = useDrawer();
  const drawerId = getOpenDrawerId() as DRAWER_ID;

  // const {footnotes} = useFootnotes();

  switch (drawerId) {
    case DRAWER_ID.CUSTOMISATION:
      return (
        <BottomSheet
          drawerId={DRAWER_ID.CUSTOMISATION}
          accessibilityLabel="Customisation menu">
          <Customisation />
        </BottomSheet>
      );
    case DRAWER_ID.FEEDBACK:
      return (
        <BottomSheetFullHeight
          header="Feedback"
          drawerId={DRAWER_ID.FEEDBACK}
          accessibilityLabel="Feedback form">
          <FeedbackContentView />
        </BottomSheetFullHeight>
      );
    case DRAWER_ID.ACCOUNT:
      return (
        <BottomSheet
          accessibilityLabel="Account menu"
          drawerId={DRAWER_ID.ACCOUNT}>
          <AccountMenuContent forBottomDrawer={true} />
        </BottomSheet>
      );
    case DRAWER_ID.DOCUMENT_TOOLS_MENU:
      return (
        <BottomSheet
          accessibilityLabel="Document tools menu"
          drawerId={DRAWER_ID.DOCUMENT_TOOLS_MENU}
          hideBackdrop>
          <DocumentToolsMenuContent />
        </BottomSheet>
      );
    case DRAWER_ID.DOCUMENT_TOOLS:
      return (
        <BottomSheetFullHeight
          header={docToolsDrawerContent.content}
          accessibilityLabel="Document tools"
          drawerId={DRAWER_ID.DOCUMENT_TOOLS}>
          <DocumentToolsContentContainer
            currentTab={docToolsDrawerContent.content}
          />
        </BottomSheetFullHeight>
      );
    case DRAWER_ID.FOOTNOTES:
      return (
        <BottomSheetFullHeight
          header="Footnotes"
          drawerId={DRAWER_ID.FOOTNOTES}
          accessibilityLabel="Footnotes">
          {/* <FootnotesContent footnotes={footnotes} /> */}
        </BottomSheetFullHeight>
      );
    default:
      return null;
  }
}
