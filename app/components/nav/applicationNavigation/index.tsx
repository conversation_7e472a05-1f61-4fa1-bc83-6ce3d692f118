'use client';
import React from 'react';
import {useAbility} from '@casl/react';
import {PermissionsContext} from '@components/providers/permissions/provider';
import MobileView from './mobileView';

interface ApplicationNavigationProps {
  animOnScrollClass?: string;
  dashboard?: boolean;
  privatePages?: boolean;
}

export default function ApplicationNavigation(
  props: ApplicationNavigationProps,
) {
  const {
    animOnScrollClass = 'translate-y-0',
    dashboard = false,
    privatePages = true,
  } = props;
  const ability = useAbility(PermissionsContext);

  return (
    <>
      <div className="grid-area-overview-app-nav p-0">
        <MobileView
          animOnScrollClass={animOnScrollClass}
          ability={ability}
          dashboard={dashboard}
          privatePages={privatePages}
        />
      </div>
      <div className="hidden" id="skip-to-main-content"></div>
    </>
  );
}
