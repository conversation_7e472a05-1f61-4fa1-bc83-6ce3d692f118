import React from 'react';
import ApplicationNavigation from '../applicationNavigation';
import {getServerSession} from 'next-auth';

const SharedScreenNavigation: React.FC = async () => {
  const session = await getServerSession();
  return (
    <nav className="lg:w-16 h-12 lg:h-screen top-0 sticky border-b lg:border-b-0 separator app-navbar-bg lg:app-bg z-nav">
      <ApplicationNavigation privatePages={!!session} dashboard />
    </nav>
  );
};

export default SharedScreenNavigation;
