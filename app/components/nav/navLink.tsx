'use client';

import Link from 'next/link';
import {FC, ReactNode} from 'react';
import {liStyle} from './shared';

interface NavLinkProps {
  href: string;
  children: ReactNode;
  className?: string; // to add to regular className
  activeClassName?: string; // to add to activeStyle
  isActive: boolean;
}

const NavLink: FC<NavLinkProps> = ({
  href,
  className,
  activeClassName,
  children,
  isActive,
}) => {
  const activeStyle = `opposite-border  ${activeClassName}`;
  const hoverStyle = 'hover:opposite-border';

  return (
    <li
      className={`${liStyle}  ${
        isActive ? activeStyle : ''
      } ${hoverStyle} border-b-4 border-transparent mt-2 ${className}`}>
      <Link href={href}>{children}</Link>
    </li>
  );
};

export default NavLink;
