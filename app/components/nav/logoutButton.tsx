import React from 'react';
import {useRouter} from 'next/navigation';
import {LogoutCurve} from 'iconsax-react';
import {useAnalytics} from 'use-analytics';
import IconButton from '@components/buttons/iconButton';

export default function LogoutButton() {
  const router = useRouter();
  const {track, reset} = useAnalytics();

  const handleLogOut = () => {
    track('Logout');
    reset();
    router.push('/logout');
  };

  return (
    <span onClick={handleLogOut}>
      {' '}
      <LogoutCurve variant="Bold" className="w-6 h-6" />
    </span>
  );
}
