import {useAnalytics} from 'use-analytics';
import {useRouter} from 'next/navigation';
import {AppPrivateRoutes} from '@libs/services/auth/routes';

export default function useAccountMenu() {
  const {track, reset} = useAnalytics();
  const router = useRouter();

  const handleLogOut = () => {
    track('Logout');
    reset();
    router.push('/logout');
  };

  const handleAccountNavigation = () => {
    router.push(AppPrivateRoutes.Account);
  };

  return {
    handleLogOut,
    handleAccountNavigation,
  };
}
