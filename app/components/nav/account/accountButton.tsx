import React from 'react';
import IconButton from '@components/buttons/iconButton';
import useUserDataFromSession from '@libs/utils/user/useUserDataFromSession';
import IconButtonLink from '@components/buttons/iconButtonLink';

interface AccountButtonProps {
  onClick?: () => void;
  href?: string;
}

export default function AccountButton({onClick, href}: AccountButtonProps) {
  const {userInitials} = useUserDataFromSession();

  if (href)
    return (
      <IconButtonLink
        href={href}
        accessibilityLabel="Open account menu"
        onClick={onClick}
        size="medium">
        <div className="account-btn rounded-full w-8 h-8 justify-center items-center flex text-xs">
          {userInitials}
        </div>
      </IconButtonLink>
    );
  return (
    <IconButton
      accessibilityLabel="Open account menu"
      onClick={onClick}
      size="medium">
      <div className="account-btn rounded-full w-8 h-8 justify-center items-center flex text-xs">
        {userInitials}
      </div>
    </IconButton>
  );
}
