import useDrawer from '@libs/store/drawer';
import {useSearchParams} from 'next/navigation';
import {liStyleDocToolsNavDesktop} from '../../shared';
import DocTools from '@components/document/tools/docTools';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
import {Book1, MusicPlay, SearchStatus, MessageQuestion} from 'iconsax-react';
import {DRAWER_ID, DrawerTab} from '@libs/store/drawer/types';
import TailoIconButton from '@components/buttons/tailoIconButton';
import {useSearchAnalytics} from '@libs/services/search/useSearchAnalytics';

// The width of the container on larger view ports is set in base.css
export default function DocumentToolsNavigation() {
  const {isMobile} = useIsMobile();

  const searchParams = useSearchParams();

  const isSummaryView = searchParams.get('tab') === 'summary';

  const {
    drawStateById,
    openDrawer,
    isSearchDrawerActive,
    docToolsDrawerContent,
    updateDocToolsDrawerContent,
    updateDrawer,
    isTailoFeedbackDrawerActive,
  } = useDrawer();
  const {trackOpenSearch, trackCloseSearch} = useSearchAnalytics();

  const drawerOpen = (drawerId: DRAWER_ID) =>
    drawStateById(drawerId) === 'open';

  const setDrawerContentToDefine = (drawerId: DrawerTab) => {
    updateDocToolsDrawerContent(drawerId);
  };

  const disableButton = (content: DrawerTab) => {
    return (
      drawerOpen(DRAWER_ID.DOCUMENT_TOOLS) &&
      docToolsDrawerContent.content === content
    );
  };

  const handleDrawerTab = (drawerTab: DrawerTab) => {
    if (!drawerOpen(DRAWER_ID.DOCUMENT_TOOLS)) {
      if (drawerTab === DrawerTab.DOC_SEARCH) {
        trackOpenSearch('DocumentToolsNav');
      }
      openDrawer(DRAWER_ID.DOCUMENT_TOOLS);
    }
    if (
      drawerOpen(DRAWER_ID.DOCUMENT_TOOLS) &&
      drawerTab === docToolsDrawerContent.content
    ) {
      trackCloseSearch();
      updateDrawer(DRAWER_ID.DOCUMENT_TOOLS, 'closed');
    }
    setDrawerContentToDefine(drawerTab);
  };

  const drawerOpenWithContent = (drawerId: DRAWER_ID, content: DrawerTab) => {
    return drawerOpen(drawerId) && docToolsDrawerContent.content === content;
  };

  const isDocumentToolsDrawerActive = drawerOpenWithContent(
    DRAWER_ID.DOCUMENT_TOOLS,
    DrawerTab.WORD_DEFINITION,
  );

  const isExplainThisDrawerActive = drawerOpenWithContent(
    DRAWER_ID.DOCUMENT_TOOLS,
    DrawerTab.EXPLAIN_THIS,
  );

  return (
    <div
      className={`lg:flex hidden group relative ${
        drawerOpen(DRAWER_ID.DOCUMENT_TOOLS) ? 'col-span-3' : 'col-span-2'
      }`}>
      <div
        className={`${
          drawerOpen(DRAWER_ID.DOCUMENT_TOOLS) ? 'w-full' : 'w-0'
        } fixed max-w-[25%] flex justify-end top-[58px] right-0`}>
        <div
          className={` ${
            drawerOpen(DRAWER_ID.DOCUMENT_TOOLS) && docToolsDrawerContent
              ? 'w-full'
              : 'w-0 hidden'
          }  max-w-xs doc-tools-panel`}>
          <DocTools />
        </div>
        <nav
          aria-label="Document tools navigation"
          className={`absolute bottom-0 z-nav hidden h-[calc(100vh-58px)] transition-transform duration-200 ease-linear lg:relative lg:block`}>
          {!isTailoFeedbackDrawerActive && (
            <ul className="closed-right-drawer hidden h-full flex-col justify-between lg:flex">
              <li>
                <ul className="hidden flex-col justify-start lg:flex">
                  <li className={`${liStyleDocToolsNavDesktop}`}>
                    <TailoIconButton
                      label="Define"
                      handler={() => {
                        handleDrawerTab(DrawerTab.WORD_DEFINITION);
                      }}
                      size="small"
                      isActive={isDocumentToolsDrawerActive}>
                      <Book1
                        variant={`${
                          isDocumentToolsDrawerActive ? 'Bold' : 'Linear'
                        }`}
                        className="h-6 w-6"
                      />
                    </TailoIconButton>
                  </li>

                  {!isSummaryView && (
                    <li className={`${liStyleDocToolsNavDesktop}`}>
                      <TailoIconButton
                        label="Search"
                        handler={() => {
                          handleDrawerTab(DrawerTab.DOC_SEARCH);
                        }}
                        size="small"
                        isActive={isSearchDrawerActive}>
                        <SearchStatus
                          variant={`${
                            isSearchDrawerActive ? 'Bold' : 'Linear'
                          }`}
                          className="h-6 w-6"
                        />
                      </TailoIconButton>
                    </li>
                  )}
                  <li className={`${liStyleDocToolsNavDesktop}`}>
                    <TailoIconButton
                      label="Explain this"
                      handler={() => {
                        handleDrawerTab(DrawerTab.EXPLAIN_THIS);
                      }}
                      size="small"
                      isActive={isExplainThisDrawerActive}>
                      <MessageQuestion
                        variant={`${
                          isExplainThisDrawerActive ? 'Bold' : 'Linear'
                        }`}
                        className="h-6 w-6"
                      />
                    </TailoIconButton>
                  </li>
                </ul>
              </li>
            </ul>
          )}
        </nav>
      </div>
    </div>
  );
}
