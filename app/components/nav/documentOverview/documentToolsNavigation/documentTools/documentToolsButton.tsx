import DocumentToolsTrigger from './documentToolsTrigger';

interface DocumentToolsButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disableButton?: boolean; // disables the button if its the its the active view
  ariaLabel?: string;
  className?: string;
  mobileButtonStyles?: string;
  isActive?: boolean;
}

function DocumentToolsButton({
  children,
  onClick,
  disableButton = false,
  ariaLabel,
  className = '',
  mobileButtonStyles = '',
  isActive = false,
}: DocumentToolsButtonProps) {
  return (
    <DocumentToolsTrigger
      disableButton={disableButton}
      ariaLabel={ariaLabel}
      onClick={onClick}
      className={className}
      mobileButtonStyles={mobileButtonStyles}
      isActive={isActive}>
      {children}
    </DocumentToolsTrigger>
  );
}

export default DocumentToolsButton;
