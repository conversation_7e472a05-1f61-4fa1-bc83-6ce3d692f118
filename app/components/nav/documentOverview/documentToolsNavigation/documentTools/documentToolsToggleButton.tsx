import {SidebarRight, SidebarLeft} from 'iconsax-react';
import DocumentToolsButton from './documentToolsButton';

interface DocumentToolsToggleButtonProps {
  drawerState: boolean;
}

function DocumentToolsToggleButton({
  drawerState,
}: DocumentToolsToggleButtonProps) {
  const open = drawerState;
  return (
    <DocumentToolsButton
      ariaLabel={
        open ? 'Close document tools drawer' : 'Open document tools drawer'
      }>
      {open ? (
        <SidebarRight variant="Linear" className="h-12 w-12 p-1" />
      ) : (
        <SidebarLeft variant="Linear" className="h-12 w-12 p-1" />
      )}
    </DocumentToolsButton>
  );
}

export default DocumentToolsToggleButton;
