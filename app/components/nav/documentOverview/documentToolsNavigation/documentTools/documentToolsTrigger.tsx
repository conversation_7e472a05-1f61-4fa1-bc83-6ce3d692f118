'use client';
import DrawerTrigger from '@components/modals/drawer/drawerTrigger';
import {DRAWER_ID} from '@libs/store/drawer/types';
import useDocumentToolsDrawer from './useDocumentToolsDrawer';

interface DocumentToolsTriggerProps {
  children: React.ReactNode;
  disableButton?: boolean; // disables the button if its the its the active view
  ariaLabel?: string;
  onClick?: () => void;
  className?: string;
  mobileButtonStyles?: string;
  isActive?: boolean;
}

function DocumentToolsTrigger(props: DocumentToolsTriggerProps) {
  const {
    children,
    disableButton = false,
    ariaLabel = 'Open document tools menu',
    onClick,
    className = '',
    mobileButtonStyles = '',
    isActive = false,
  } = props;
  const {handleDocumentToolsDrawer} = useDocumentToolsDrawer();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      handleDocumentToolsDrawer();
    }
  };

  return (
    <div className={className || 'hidden lg:flex justify-center'}>
      <DrawerTrigger
        onClick={handleClick}
        drawerId={DRAWER_ID.DOCUMENT_TOOLS}
        ariaLabel={ariaLabel}
        className={
          mobileButtonStyles ||
          'btn icon-btn-sm btn-tertiary'
        }
        disableButton={disableButton}
        isActive={isActive}>
        {children}
      </DrawerTrigger>
    </div>
  );
}

export default DocumentToolsTrigger;
