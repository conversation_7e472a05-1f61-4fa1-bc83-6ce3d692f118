import {useWordDefinitionContext} from '@contexts/wordDefinitionContext';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID, DrawerTab} from '@libs/store/drawer/types';

export default function useDocumentToolsDrawer() {
  const {openDrawer, closeDrawer, drawStateById, docToolsDrawerContent} =
    useDrawer();
  const {clearWordDefinition, currentlySearchedWord} =
    useWordDefinitionContext();
  const handleDocumentToolsDrawer = () => {
    if (drawStateById(DRAWER_ID.DOCUMENT_TOOLS) === 'open') {
      if (
        docToolsDrawerContent.content === DrawerTab.WORD_DEFINITION &&
        currentlySearchedWord
      ) {
        clearWordDefinition();
      }
      closeDrawer(DRAWER_ID.DOCUMENT_TOOLS);
    } else {
      openDrawer(DRAWER_ID.DOCUMENT_TOOLS);
    }
  };
  return {handleDocumentToolsDrawer};
}
