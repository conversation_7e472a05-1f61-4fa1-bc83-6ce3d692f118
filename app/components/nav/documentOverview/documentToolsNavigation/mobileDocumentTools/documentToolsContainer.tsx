import {DrawerTab} from '@libs/store/drawer/types';
import WordDefinitionContent from '../wordDefinition/wordDefinitionContent';
import DocSearchPanel from '@components/document/search/docSearchPanel';
import ExplainThisPanel from '@components/document/explainThis/explainThisPanel';
import TailoFeedbackPanel from '@components/feedback/tailoFeedbackPanel';

function DocumentToolsContentContainer({currentTab}: {currentTab: DrawerTab}) {
  switch (currentTab) {
    case DrawerTab.DOC_SEARCH:
      return (
        <div className="sticky left-0 top-14 w-full px-4 overflow-scroll">
          <DocSearchPanel panelTitle="Search" />
        </div>
      );
    case DrawerTab.EXPLAIN_THIS:
      return (
        <div className="sticky left-0 top-14 w-full px-4 overflow-scroll">
          <ExplainThisPanel panelTitle="Explain" />
        </div>
      );
    case DrawerTab.TAILO_FEEDBACK:
      return (
        <div className="sticky left-0 top-14 w-full px-4 overflow-scroll">
          <TailoFeedbackPanel />
        </div>
      );
    case DrawerTab.WORD_DEFINITION:
    default:
      return (
        <div className="sticky left-0 top-14 w-full px-4 overflow-scroll">
          <WordDefinitionContent />
        </div>
      );
  }
}

export default DocumentToolsContentContainer;
