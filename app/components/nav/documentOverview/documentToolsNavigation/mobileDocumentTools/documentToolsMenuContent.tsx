import useDrawer from '@libs/store/drawer';
import {DRAWER_ID, DrawerTab} from '@libs/store/drawer/types';
import {
  Book1,
  MusicPlay,
  SearchStatus,
  MessageQuestion,
  MessageText1,
} from 'iconsax-react';
import DocumentToolsButton from '../documentTools/documentToolsButton';
import PromptDivider from '@components/modals/summariseDoc/promptBuilder/promptDivider';
import {AccountPanelButtons} from '@components/account/accountPanelButtons';
import {useSearchAnalytics} from '@libs/services/search/useSearchAnalytics';

function DocumentToolsMenuContent() {
  const {
    closeDrawer,
    openDrawer,
    updateDocToolsDrawerContent,
    docToolsDrawerContent,
  } = useDrawer();
  const {trackOpenSearch, trackCloseSearch} = useSearchAnalytics();
  const closeMenuDrawer = () => {
    if (docToolsDrawerContent.content === DrawerTab.DOC_SEARCH) {
      trackCloseSearch();
    }

    closeDrawer(DRAWER_ID.DOCUMENT_TOOLS_MENU);
  };

  const setDrawerContent = (drawerTab: DrawerTab) => {
    if (drawerTab === DrawerTab.DOC_SEARCH) {
      trackOpenSearch('DocumentToolsMenu');
    }

    updateDocToolsDrawerContent(drawerTab);
    openToolsDrawer();
  };

  const openToolsDrawer = () => {
    closeMenuDrawer();
    openDrawer(DRAWER_ID.DOCUMENT_TOOLS);
  };

  const setSpeakAloudTab = () => setDrawerContent(DrawerTab.SPEAK_ALOUD);
  const setWordDefinitionTab = () =>
    setDrawerContent(DrawerTab.WORD_DEFINITION);
  const setSearchTab = () => setDrawerContent(DrawerTab.DOC_SEARCH);
  const setExplainThisTab = () => setDrawerContent(DrawerTab.EXPLAIN_THIS);
  const setFeedbackTab = () => setDrawerContent(DrawerTab.TAILO_FEEDBACK);

  const docToolsStyles = 'block w-full pb-2 lg:hidden';
  const buttonStyles =
    'flex flex-row align-center items-center p-1 btn-tertiary text-base disabled:doc-text-default';

  return (
    <>
      <h2 id="tool-menu-label" className="font-semibold pb-2">
        Document tools
      </h2>
      <ul aria-labelledby="tool-menu-label">
        <li className="flex items-center">
          <DocumentToolsButton
            ariaLabel="Open the word definition lookup"
            onClick={setWordDefinitionTab}
            className={docToolsStyles}
            mobileButtonStyles={buttonStyles}>
            <Book1 variant="Bold" className="h-8 w-8 pr-2" />
            <p>Word definition lookup</p>
          </DocumentToolsButton>
        </li>
        <li className="flex flex-col items-center">
          <DocumentToolsButton
            ariaLabel="Open the read aloud settings"
            onClick={setSpeakAloudTab}
            className={docToolsStyles}
            mobileButtonStyles={buttonStyles}>
            <MusicPlay variant="Bold" className="h-8 w-8 pr-2" />
            <p>Read aloud settings</p>
          </DocumentToolsButton>
        </li>
        <li className="flex items-center">
          <DocumentToolsButton
            ariaLabel="Open the document search"
            onClick={setSearchTab}
            className={docToolsStyles}
            mobileButtonStyles={buttonStyles}>
            <SearchStatus variant="Bold" className="h-8 w-8 pr-2" />
            <p>Search</p>
          </DocumentToolsButton>
        </li>
        <li>
          <DocumentToolsButton
            ariaLabel="Open the explain this panel"
            onClick={setExplainThisTab}
            className={docToolsStyles}
            mobileButtonStyles={buttonStyles}>
            <MessageQuestion variant="Bold" className="h-8 w-8 pr-2" />
            <p>Explain</p>
          </DocumentToolsButton>
        </li>
        <li className="flex items-center">
          <DocumentToolsButton
            ariaLabel="Open the feedback form"
            onClick={setFeedbackTab}
            className={docToolsStyles}
            mobileButtonStyles={buttonStyles}>
            <MessageText1 variant="Bold" className="h-8 w-8 pr-2" />
            <p>Feedback</p>
          </DocumentToolsButton>
        </li>
        <PromptDivider />
      </ul>
      <h2 id="account-menu-label" className="font-semibold pb-2">
        Account
      </h2>
      <ul aria-labelledby="account-menu-label">
        <AccountPanelButtons />
      </ul>
    </>
  );
}

export default DocumentToolsMenuContent;
