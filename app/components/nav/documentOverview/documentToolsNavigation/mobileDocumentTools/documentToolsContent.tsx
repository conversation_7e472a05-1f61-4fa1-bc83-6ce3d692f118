import Button from '@components/buttons/button';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';

function DocumentToolsContent({
  header,
  content,
}: {
  header: string;
  content: React.ReactElement;
}) {
  const {closeDrawer} = useDrawer();

  const closeMenu = () => {
    closeDrawer(DRAWER_ID.DOCUMENT_TOOLS);
  };

  return (
    <>
      <div className="absolute flex flex-row left-0 right-0 -top-1 w-screen h-14 py-3 app-bg-tertiary justify-center items-center">
        <h2 className="font-bold">{header}</h2>
        <Button
          variant="tertiary"
          className="absolute right-0 underline font-semibold"
          onClick={closeMenu}>
          Done
        </Button>
      </div>
      <div className="overflow-auto">{content}</div>
    </>
  );
}

export default DocumentToolsContent;
