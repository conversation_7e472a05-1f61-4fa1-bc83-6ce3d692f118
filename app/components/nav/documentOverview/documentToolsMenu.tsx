'use client';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import {usePathname} from 'next/navigation';
import TailoIconButton from '@components/buttons/tailoIconButton';
import {FiMoreHorizontal} from 'react-icons/fi';

export default function DocumentToolsMenu() {
  const {openDrawer} = useDrawer();
  const pathname = usePathname();

  const openMenu = () => {
    pathname.includes('/document')
      ? openDrawer(DRAWER_ID.DOCUMENT_TOOLS_MENU)
      : openDrawer(DRAWER_ID.ACCOUNT);
  };

  return (
    <TailoIconButton
      label="Open document tools menu"
      handler={openMenu}
      size="small">
      <FiMoreHorizontal className="w-6 h-6" />
    </TailoIconButton>
  );
}
