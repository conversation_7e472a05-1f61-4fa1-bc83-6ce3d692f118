'use client';
import React from 'react';
import DocumentToolsNavigation from './documentToolsNavigation/documentToolsNavigation';
import CustomisationDrawer from '@components/customisation/customisationDrawer';
import DocToolsDrawer from '@components/feedback/feedbackDrawer';

/** Layout achieved with grid and grid areas defined in the base.css file;
 * Padding top and bottom on grid-area-overview-main are used to ensure the content is fully visible with the nav bars present - they get hidden on scroll
 *
 */

export const MAIN_SCROLLABLE_ELEMENT_ID = 'main-scrollable';
export default function DocumentOverviewNavigation({
  children,
}: {
  children: React.ReactNode;
}) {
  const scrollableRef = React.useRef<HTMLDivElement>(null);
  return (
    <div>
      <div className="flex justify-center document-viewer-background">
        <div
          ref={scrollableRef}
          className="w-full overflow-auto flex py-14 lg:py-0"
          id={MAIN_SCROLLABLE_ELEMENT_ID}>
          {/* min-h ensures that the children content is stretched and the navbars on mobile stayed positioned correctly */}
          <div className="flex-1 min-h-screen">{children}</div>
        </div>
        <DocumentToolsNavigation />
        <CustomisationDrawer />
        <DocToolsDrawer />
      </div>
    </div>
  );
}
