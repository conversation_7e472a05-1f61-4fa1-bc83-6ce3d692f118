'use client';
import React from 'react';
import {Bookmark, DocumentText, Notepad2} from 'iconsax-react';
import {useSearchParams} from 'next/navigation';
import DocumentNavigationItem from './documentNavigationItem';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';

export const enum DocOverviewNavTabs {
  Summary = 'summary',
  SectionView = 'section-view',
  PageView = 'document-view',
}

export interface IDocNavTab {
  id: DocOverviewNavTabs;
  label: string;
  mobileLabel: string;
  icon: JSX.Element;
  iconActive: JSX.Element;
  disabled: boolean;
}
const iconSize = 24;
const docNavTabs: IDocNavTab[] = [
  {
    id: DocOverviewNavTabs.Summary,
    label: 'Summary',
    mobileLabel: 'Summary',
    icon: <Bookmark size={iconSize} />,
    iconActive: <Bookmark size={iconSize} variant="Bold" />,
    disabled: false,
  },
  {
    id: DocOverviewNavTabs.PageView,
    label: 'Document',
    mobileLabel: 'Document',
    icon: <DocumentText size={iconSize} />,
    iconActive: <DocumentText size={iconSize} variant="Bold" />,
    disabled: false,
  },
  {
    id: DocOverviewNavTabs.SectionView,
    label: 'Sections',
    mobileLabel: 'Section',
    icon: <Notepad2 size={iconSize} />,
    iconActive: <Notepad2 size={iconSize} variant="Bold" />,
    disabled: false,
  },
];

export default function DocumentNavigationContent() {
  const searchParams = useSearchParams();
  const currentTabId = searchParams.get('tab');
  const {document} = useProcessedDocument();
  const toc = document?.details.toc || [];

  const availableTabs = docNavTabs.filter(tab => {
    if (tab.id === DocOverviewNavTabs.SectionView) {
      return toc.length > 0; // Include Section tab only if toc is available
    }
    return true;
  });

  const mobileListStyle = 'flex h-full w-full gap-4';
  //TODO: Make just 1 list style. No need for desktop and mobile
  const desktopListStyle = '';

  return (
    <ul className={`${mobileListStyle} ${desktopListStyle}`}>
      {availableTabs.map(tab => {
        const isActiveTab = currentTabId === tab.id;

        return (
          <DocumentNavigationItem
            key={tab.id}
            {...tab}
            isActiveTab={isActiveTab}
          />
        );
      })}
    </ul>
  );
}
