'use client';

import Link from 'next/link';
import {ArrowLeft2} from 'iconsax-react';
import useDrawer from '@libs/store/drawer';
import {usePathname} from 'next/navigation';
import { useSettings } from '@contexts/settings';
import DocumentToolsMenu from '../documentToolsMenu';
import {MessageText1,BrushSquare,Setting3} from 'iconsax-react';
import TailoIconButton from '@components/buttons/tailoIconButton';
import DocumentNavigationContent from './documentNavigationContent';
import NavBottomSheet from '@components/nav/applicationNavigation/navBottomSheet';
import DropDownNavigation from '@components/nav/applicationNavigation/dropdownNavigation';

// Absolute styling is used to hide the nav on smaller screens when scrolling down

export default function DocumentNavigation({
  animOnScrollClass,
}: {
  animOnScrollClass: string;
}) {
  const pathname = usePathname();

  const {
    toggleFeedbackDrawer,
    isFeedbackDrawerActive,
    toggleCustomisationDrawer,
    isCustomisationDrawerActive,
  } = useDrawer();

  const { open, setOpen } = useSettings();

  return (
    <>
      <div className="flex w-full justify-between items-center">
        <div className="flex items-center gap-6">
          {pathname.includes('/document') && (
            <div className="flex items-center gap-4 p-0.5 ml-2">
              <Link href="/dashboard">
                <TailoIconButton label="Return to dashboard" size="small">
                  <ArrowLeft2 />
                </TailoIconButton>
              </Link>

              <div className="nav-content-styles fixed w-full bottom-0 left-0">
                <DocumentNavigationContent />
              </div>
            </div>
          )}
        </div>

        <div className="flex gap-2 mr-2 items-center">
          <TailoIconButton
            label={
              isFeedbackDrawerActive
                ? 'Close Feedback drawer'
                : 'Open Feedback drawer'
            }
            handler={toggleFeedbackDrawer}
            size="small"
            isActive={isFeedbackDrawerActive}>
            <MessageText1 variant="Bold" />
          </TailoIconButton>

          <TailoIconButton
            label={
              isCustomisationDrawerActive
                ? 'Close Customisation drawer'
                : 'Open Customisation drawer'
            }
            handler={toggleCustomisationDrawer}
            size="small"
            isActive={isCustomisationDrawerActive}>
            <BrushSquare variant="Bold" />
          </TailoIconButton>

          <TailoIconButton
            label={
              isCustomisationDrawerActive
                ? 'Close settings drawer'
                : 'Open settings drawer'
            }
            handler={() => setOpen(!open)}
            size="small"
            isActive={isCustomisationDrawerActive}>
            <Setting3 variant="Bold" />
          </TailoIconButton>

          {/* Show the doc tools menu on mobile */}
          <div className="lg:hidden">
            <DocumentToolsMenu />
          </div>
          {/* show the drop down nav on desktop */}
          <div className="hidden lg:flex">
            <DropDownNavigation />
          </div>
        </div>
      </div>
      <div className="lg:hidden">
        <NavBottomSheet />
      </div>
    </>
  );
}
