import React from 'react';
import Link from 'next/link';
import {useParams} from 'next/navigation';
import {DocOverviewNavTabs, IDocNavTab} from './documentNavigationContent';
import {AppDynamicRoutes} from '@libs/services/auth/routes';
import useSummaryTab from '@components/document/summaryTab/useSummaryTab';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';
import {useActiveSectionId} from '@libs/store/document/activeSectionId';

interface IDocumentNavigationItemProps extends IDocNavTab {
  isActiveTab: boolean;
}
export default function DocumentNavigationItem(
  props: IDocumentNavigationItemProps,
) {
  const params = useParams();
  const {documentData} = useSummaryTab();
  const {trackContinueReading} = useDocumentAnalytics();
  const documentId = params.id as string;
  const {getActiveSectionIdPerDocument} = useActiveSectionId(documentId);
  const {isActiveTab, id, icon, iconActive, label, mobileLabel, disabled} =
    props;
  const href = disabled
    ? ''
    : `${AppDynamicRoutes.document(documentId)}?tab=${id}
      `;

  const handleClick = () => {
    if (documentData) {
      trackContinueReading(
        'Changed Reading Tab',
        label,
        documentData.details.wordCount,
      );
    }
  };

  // btn-tertiary class is used for focus and active state styling following the design's token
  let bgStyle = '';
  if (isActiveTab) bgStyle = ''; //TODO: Underline needs theming
  if (disabled) bgStyle = '';

  const itemMobileStyle =
    'w-full flex-col justify-center items-center h-full gap-2 inline-flex pb-1 focus-visible:border-2 focus-border';

  const labelMobileStyle =
    'font-semibold tracking-widest uppercase text-xs lg:hidden leading-none';
  const labeDesktopStyle =
    'hidden font-semibold lg:inline-block tracking-normal text-base';

  const underlineMobileStyle = `absolute bottom-[-12px] left-0  w-full ${
    isActiveTab ? 'h-1 nav-underline' : ''
  }`;

  const underlineDesktopStyle = `lg:h-1 ${bgStyle} ${
    isActiveTab ? '' : 'lg:group-hover:hidden lg:group-active:hidden'
  }`;

  if (
    id === DocOverviewNavTabs.SectionView &&
    documentData?.details.toc.length === 0
  ) {
    return null;
  }

  return (
    <li
      key={id}
      className="flex w-1/3 lg:w-auto lg:items-end">
      <Link
        href={href}
        onClick={handleClick}
        // We use group class to style children on link's state change

        className={`group relative focus:outline-0 ${itemMobileStyle}  ${
          disabled ? '!text-disabled' : ''
        }`}
        aria-current={isActiveTab ? 'page' : false}>
        <span className="lg:hidden flex justify-center">
          {isActiveTab ? iconActive : icon}
        </span>
        <span className={labeDesktopStyle}>{label}</span>
        <span className={labelMobileStyle}>{mobileLabel}</span>
        {/* We shift the element down on larger screens to hide the border for active tab */}
        <span className={`${underlineMobileStyle} ${underlineDesktopStyle}`} />
        {/* To imitate ring on focus of items in desktop view */}
        <span className="hidden lg:group-focus-visible:block w-[106%] h-11 absolute -top-[5px] -left-[3%] rounded-t-lg" />
      </Link>
    </li>
    
  );
}
