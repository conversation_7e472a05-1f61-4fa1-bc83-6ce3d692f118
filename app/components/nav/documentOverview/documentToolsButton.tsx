import IconButton from '@components/buttons/iconButton';
import React from 'react';
import {FiMoreHorizontal} from 'react-icons/fi';

interface DocumentToolsButtonProps {
  onClick: () => void;
  className?: string;
}

export default function DocumentToolsButton(props: DocumentToolsButtonProps) {
  const {onClick, className = ''} = props;
  return (
    <IconButton
      accessibilityLabel="Open document tools menu"
      onClick={onClick}
      size="small"
      className={`${className}`}>
      <FiMoreHorizontal />
    </IconButton>
  );
}
