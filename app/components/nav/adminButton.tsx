import IconButtonLink from '@components/buttons/iconButtonLink';
import {Security} from 'iconsax-react';
import React from 'react';

interface AdminButtonProps {
  href: string;
  onClick?: () => void;
}

export default function AdminButton(props: AdminButtonProps) {
  const {href, onClick} = props;
  return (
    <IconButtonLink
      size="medium"
      href={href}
      accessibilityLabel="Open admin menu"
      onClick={onClick}>
      <Security variant="Bold" className="w-8 h-8" />
    </IconButtonLink>
  );
}
