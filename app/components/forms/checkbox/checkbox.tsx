import React, {FC, useEffect, useRef, useState} from 'react';

interface CheckBoxProps {
  id: string;
  label: React.ReactNode;
  checked: boolean;
  onPress: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
  description?: string;
  errorHint?: string;
  ariaLabel?: string;
}

const CheckBox: FC<CheckBoxProps> = ({
  id,
  label,
  checked,
  onPress,
  disabled,
  required = false,
  description,
  errorHint,
  ariaLabel,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (errorHint && inputRef.current) {
      inputRef.current.focus();
    }
  }, [errorHint]);

  const toggleChecked = (e: React.ChangeEvent<HTMLInputElement>) => {
    onPress(e);
  };

  return (
    <div className="form-control">
      <div className="flex">
        <div className="flex items-center h-5">
          <input
            className={`input appearance-none peer relative shrink-0 w-6 h-6 border-2
                      ${
                        errorHint ? 'form-error-border' : ''
                      } rounded mt-1 
                      ${disabled ? '' : 'cursor-pointer'}`}
            id={id}
            ref={inputRef}
            aria-describedby="helper-checkbox-text"
            type="checkbox"
            checked={checked}
            onChange={toggleChecked}
            disabled={disabled}
          />
          <svg
            className={`absolute w-6 h-6 pointer-events-none hidden peer-checked:block svg-stroke mt-1 p-1 outline-none justify-center self-center`}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="4"
            strokeLinecap="round"
            strokeLinejoin="round"
            aria-hidden="true">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <label
          aria-label={ariaLabel}
          htmlFor={id}
          className={`flex flex-col ml-2 w-full text-base font-semibold leading-normal ${
            disabled ? 'disabled-label-text' : 'doc-text-default'
          }`}>
          <span className="doc-text-default">
            {label}
            {required && '*'}
          </span>
          <span
            id="helper-checkbox-text"
            className="doc-text-subdued text-sm font-normal leading-[18px]">
            {description}
          </span>
        </label>
      </div>
      {errorHint && (
        <div
          role="alert"
          className="text-sm font-normal leading-[18px] w-full relative top-1 px-2 py-1.5 input-error-box rounded-md text-white form-error">
          {errorHint}
        </div>
      )}
    </div>
  );
};

export default CheckBox;
