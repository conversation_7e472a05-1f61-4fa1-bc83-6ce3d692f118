import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import <PERSON><PERSON><PERSON> from "./checkbox";

const meta: Meta = {
  title: 'Inputs/CheckBox',
  component: CheckBox,
  parameters: {
    controls: {expanded: true},
  },
};
export default meta;

type Story = StoryObj<typeof CheckBox>;

export const CheckBoxStory: Story = {
  render: args => {
    return (
      <>
        <div data-theme="dark">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <CheckBox
              {...args} 
              id="id-1"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-1b"
              checked={false}
              disabled={true}
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-1a"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
              errorHint="Error Message"
            />
          </div>
        </div>
        <div data-theme="light">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <CheckBox
              {...args} 
              id="id-2"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-2b"
              checked={false}
              disabled={true}
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-2a"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
              errorHint="Error Message"
            />
          </div>
        </div>
        <div data-theme="yellow">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <CheckBox
              {...args} 
              id="id-3"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3b"
              checked={false}
              disabled={true}
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3a"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
              errorHint="Error Message"
            />
          </div>
        </div>
        <div data-theme="pale-green">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <CheckBox
              {...args} 
              id="id-3"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3b"
              checked={false}
              disabled={true}
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3a"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
              errorHint="Error Message"
            />
          </div>
        </div>
        <div data-theme="pale-pink">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <CheckBox
              {...args} 
              id="id-3"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3b"
              checked={false}
              disabled={true}
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3a"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
              errorHint="Error Message"
            />
          </div>
        </div>
        <div data-theme="pale-blue">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <CheckBox
              {...args} 
              id="id-3"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3b"
              checked={false}
              disabled={true}
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3a"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
              errorHint="Error Message"
            />
          </div>
        </div>
        <div data-theme="peach">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <CheckBox
              {...args} 
              id="id-3"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3b"
              checked={false}
              disabled={true}
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
            />
            <CheckBox
              {...args} 
              id="id-3a"
              label="Checkbox label"
              onPress={() => console.log('pressed it')}
              description='Description goes here'
              errorHint="Error Message"
            />
          </div>
        </div>
      </>
    )
  }
}

CheckBoxStory.args = {
  required: false,
  checked: false || true,
}
