import {Meta, StoryObj} from '@storybook/react';
import TextArea from './textArea';

const meta: Meta = {
  title: 'Inputs/TextArea',
  component: TextArea,
  parameters: {
    controls: {expanded: true},
  },
};
export default meta;

type Story = StoryObj<typeof TextArea>;

export const TextAreaStory: Story = {
  render: args => {
    return (
      <>
        <div data-theme="dark">
          <div className="flex flex-wrap items-center app-navbar-bg space-x-14 p-10 py-5 w-1/2">
            <TextArea {...args} />
          </div>
        </div>
        <div data-theme="light">
          <div className="flex flex-wrap items-center app-navbar-bg space-x-14 p-10 py-5 w-1/2">
            <TextArea {...args} />
          </div>
        </div>
        <div data-theme="yellow">
          <div className="flex flex-wrap items-center app-navbar-bg space-x-14 p-10 py-5 w-1/2">
            <TextArea {...args} />
          </div>
        </div>
        <div data-theme="pale-green">
          <div className="flex flex-wrap items-center app-navbar-bg space-x-14 p-10 py-5 w-1/2">
            <TextArea {...args} />
          </div>
        </div>
        <div data-theme="pale-pink">
          <div className="flex flex-wrap items-center app-navbar-bg space-x-14 p-10 py-5 w-1/2">
            <TextArea {...args} />
          </div>
        </div>
        <div data-theme="pale-blue">
          <div className="flex flex-wrap items-center app-navbar-bg space-x-14 p-10 py-5 w-1/2">
            <TextArea {...args} />
          </div>
        </div>
        <div data-theme="peach">
          <div className="flex flex-wrap items-center app-navbar-bg space-x-14 p-10 py-5 w-1/2">
            <TextArea {...args} />
          </div>
        </div>
      </>
    );
  },
};

TextAreaStory.args = {
  label: 'Feedback (required)',
  value: '',
  hintLabel: 'Please provide as much detail as you can.',
  disabled: false,
  errorHint: '',
  success: false,
};
