import {useEffect, useRef} from 'react';

interface TextAreaProps {
  label: string;
  value: string;
  placeholder?: string;
  onChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  required?: boolean;
  hintLabel?: string;
  disabled?: boolean;
  errorHint?: string;
  success?: boolean;
  inputStyling?: string;
}

export default function TextArea({
  label,
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  hintLabel = '',
  errorHint,
  success = false,
  inputStyling,
}: TextAreaProps) {
  const errorRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (errorHint && errorRef.current) {
      errorRef.current.focus();
    }
  }, [errorHint]);

  return (
    <label className="form-control w-full">
      <div className="label">
        <span className="text-base font-semibold doc-text-default">{label}</span>
      </div>
      <textarea
        className={`textarea textarea-md resize-none ${
          inputStyling ?? 'border-2 bg-transparent'
        } ${
          errorHint
            ? 'border-red-500'
            : success
            ? 'form-input-success'
            : 'form-input'
        }
            ${disabled && ''}`}
        value={value}
        required={required}
        onChange={onChange}
        disabled={disabled}
        placeholder={placeholder}
      />
      <div className="label">
        {errorHint ? (
          <span
            ref={errorRef}
            role="alert"
            className="label-text-alt w-full relative top-[2px] p-1 bg-red-700 rounded-md text-white">
            {errorHint}
          </span>
        ) : (
          <span className="text-sm leading-none doc-text-default">
            {hintLabel}
          </span>
        )}
      </div>
    </label>
  );
}
