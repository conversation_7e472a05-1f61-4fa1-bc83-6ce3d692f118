'use client';
import {getFontClassName} from '@libs/store/settings/fonts';
import {Font} from '@libs/store/settings/types';
import {ArrowDown2, ArrowUp2, TickCircle} from 'iconsax-react';
import {FC, useEffect, useRef} from 'react';
import Select, {components, StylesConfig} from 'react-select';

export type OptionType = {
  value: string;
  label: string;
  fontClass?: string;
};

export type DropdownOptionsType = {
  value: string;
  label: string;
}[];

interface DropdownSelectProps {
  onSelect: (option: string) => void;
  defaultOption: string;
  options: string[] | DropdownOptionsType;
  id?: string;
  inputLabel?: string;
  required?: boolean;
  requiredStyling?: boolean;
  errorHint?: string;
  disabled?: boolean;
  fontPicker?: boolean;
}

const DropdownSelect: FC<DropdownSelectProps> = ({
  options,
  defaultOption,
  onSelect,
  id,
  inputLabel,
  required = false,
  requiredStyling = required,
  errorHint,
  disabled = false,
  fontPicker,
}) => {
  // having some TS issues with the library
  const handleSelect = (option: any) => {
    if (option && option.value) {
      onSelect(option.value);
    }
  };

  const errorRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (errorHint && errorRef.current) {
      errorRef.current.focus();
    }
  }, [errorHint]);

  const optionsForSelect: OptionType[] = options.map(
    (item: any | {value: any; label: string}) => {
      if (typeof item === 'object') {
        return {
          value: item.value,
          label: item.label,
        };
      }

      return {
        value: item,
        label: item,
      };
    },
  );

  const optionsForSelectWithFontClass: OptionType[] = Object.values(Font).map(
    font => ({
      value: font,
      label: font,
      fontClass: getFontClassName(font),
    }),
  );

  const indexForDefault = optionsForSelect.findIndex(
    item => item.value === defaultOption,
  );

  const DropdownIndicator = (props: any) => {
    const {menuIsOpen} = props.selectProps;
    const icon = menuIsOpen ? <ArrowUp2 size={20} /> : <ArrowDown2 size={20} />;
    return (
      <components.DropdownIndicator {...props}>
        {icon}
      </components.DropdownIndicator>
    );
  };

  // These styles enable keyboard navigation and focus states
  const controlStyles = {
    base: `input input-md ${
      errorHint ? 'form-error-border' : ''
    }`,
    focus: 'ring select-ring', // Requires a separate class to work with the dynamically generated styles;
    disabled: '',
  };
  const menuStyle =
    'form-dropdown-select w-full shadow-lg-d rounded-lg text-base font-normal';
  const optionStyles = {
    base: 'p-3 w-full items-center justify-center flex justify-between hover:cursor-pointer relative',
    focus: 'select-option-focus', // Requires a separate class to work with the dynamically generated styles
  };

  const SingleValue: FC<any> = ({data}) => {
    return (
      <div className={`font-class ${data.fontClass} -mt-6 truncate`}>
        {data.label}
      </div>
    );
  };

  return (
    <>
      <label htmlFor={id} className="w-full">
        <span className="label-text doc-text-default font-semibold mb-1">
          {inputLabel}
          {requiredStyling ? <span aria-hidden="true">&nbsp;*</span> : ''}
        </span>
        <Select
          id={id}
          placeholder={defaultOption}
          unstyled={true}
          styles={{
            input: base => ({
              ...base,
              'input:focus': {
                boxShadow: 'none',
              },
            }),
            // On mobile, the label will truncate automatically, so we want to override that behaviour.
            multiValueLabel: base => ({
              ...base,
              whiteSpace: 'normal',
              overflow: 'visible',
            }),
          }}
          options={
            fontPicker ? optionsForSelectWithFontClass : optionsForSelect
          }
          onChange={handleSelect}
          isDisabled={disabled}
          value={
            fontPicker
              ? optionsForSelectWithFontClass[indexForDefault]
              : optionsForSelect[indexForDefault]
          }
          classNames={{
            control: ({isFocused, isDisabled}) =>
              `${isFocused && controlStyles.focus} ${
                isDisabled && controlStyles.disabled
              } ${controlStyles.base}`,
            menuList: () => menuStyle,
            option: ({isFocused}) =>
              `${isFocused && optionStyles.focus} ${optionStyles.base}`,
          }}
          components={{
            IndicatorSeparator: () => null,
            DropdownIndicator,
            SingleValue,
            Option: ({children, ...props}) => (
              <components.Option
                {...props}
                className={`optionStyle ${props.data.fontClass}`}>
                <span className={props.data.fontClass}>{children}</span>
                {props.isSelected && (
                  <span className="absolute flex top-0 right-0 h-full">
                    <TickCircle
                      variant="Bold"
                      className="self-center h-4 w-4 mr-4"
                    />
                  </span>
                )}
              </components.Option>
            ),
          }}
        />
        {errorHint && (
          <span
            ref={errorRef}
            role="alert"
            className="label-text-alt w-full relative top-[1px] p-1 bg-red-700 rounded-md text-white">
            {errorHint}
          </span>
        )}
      </label>
    </>
  );
};

export default DropdownSelect;
