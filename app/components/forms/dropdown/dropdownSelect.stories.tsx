import React from 'react';
import {Meta, StoryObj} from '@storybook/react';
import DropdownSelect from './dropdownSelect';

const meta: Meta = {
  title: 'Inputs/DropdownSelect',
  component: DropdownSelect,
  parameters: {
    controls: {expanded: true},
  },
};
export default meta;

type Story = StoryObj<typeof DropdownSelect>;

export const DropdownSelectStory: Story = {

  render: args => {
    return (
      <>
        <div data-theme="dark">
          <div className="flex flex-wrap items-center panel-bg space-x-14 pl-10 py-5">
            <div className='w-96'>
              <DropdownSelect {...args} />
            </div>
          </div>
        </div>
        <div data-theme="light">
          <div className="flex flex-wrap items-center panel-bg space-x-14 pl-10 py-5">
            <div className='w-96'>
              <DropdownSelect {...args} />
            </div>
          </div>
        </div>
        <div data-theme="yellow">
          <div className="flex flex-wrap items-center panel-bg space-x-14 pl-10 py-5">
            <div className='w-96'>
              <DropdownSelect {...args} />
            </div>
          </div>
        </div>
        <div data-theme="pale-green">
          <div className="flex flex-wrap items-center panel-bg space-x-14 pl-10 py-5">
            <div className='w-96'>
              <DropdownSelect {...args} />
            </div>
          </div>
        </div>
        <div data-theme="pale-pink">
          <div className="flex flex-wrap items-center panel-bg space-x-14 pl-10 py-5">
            <div className='w-96'>
              <DropdownSelect {...args} />
            </div>
          </div>
        </div>
        <div data-theme="pale-blue">
          <div className="flex flex-wrap items-center panel-bg space-x-14 pl-10 py-5">
            <div className='w-96'>
              <DropdownSelect {...args} />
            </div>
          </div>
        </div>
        <div data-theme="peach">
          <div className="flex flex-wrap items-center panel-bg space-x-14 pl-10 py-5">
            <div className='w-96'>
              <DropdownSelect {...args} />
            </div>
          </div>
        </div>
      </>
    );
  },
};

DropdownSelectStory.args = {
  options: ['Option 1', 'Option 2', 'Option 3'],
  defaultOption: 'Option 1',
  onSelect: (option: string) => {
    console.log(option);
  },
  inputLabel: 'Label',
  required: false,
  errorHint: '',
  disabled: false,
}
