import React, {FC, ReactNode, useEffect, useRef} from 'react';

interface TextInputProps {
  id: string;
  name?: string;
  label: string;
  value: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void | undefined;
  success?: boolean;
  required?: boolean;
  type?: 'text' | 'email' | 'password';
  minLength?: number;
  maxLength?: number;
  autoComplete?: string;
  disabled?: boolean;
  iconLeft?: ReactNode;
  iconRight?: ReactNode;
  placeholder?: string;
  hint?: string;
  errorHint?: string;
  className?: string;
  inputStyling?: string;
  readOnlyInput?: boolean;
  focusOnRender?: boolean;
}

const TextInput: FC<TextInputProps> = ({
  id,
  name = id,
  label,
  value,
  onChange,
  success,
  required = false,
  minLength,
  maxLength,
  type = 'text',
  autoComplete = '',
  disabled,
  iconLeft,
  iconRight,
  placeholder,
  hint,
  errorHint,
  className = 'w-full', //Was originall 'max-w-sm'
  inputStyling,
  readOnlyInput = false,
  focusOnRender = false,
}) => {
  const errorRef = useRef<HTMLSpanElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (errorHint && errorRef.current) {
      errorRef.current.focus();
    }
  }, [errorHint]);

  useEffect(() => {
    if (focusOnRender) inputRef.current?.focus();
  }, []);

  return (
    <>
      <div className={`form-control w-full ${className}`}>
        <label htmlFor={id} className="label flex-col items-start px-0">
          <span className="label-text doc-text-default font-semibold pb-1">
            {label}{required ? <span aria-hidden="true">&nbsp;*</span> : ''}
          </span>
          <span className="flex flex-row relative w-full items-center">
            {iconLeft && <span className="absolute left-1">{iconLeft}</span>}
            <input
              ref={inputRef}
              id={id}
              name={name}
              autoComplete={autoComplete}
              required={required}
              minLength={minLength}
              maxLength={maxLength}
              type={type}
              value={value}
              onChange={onChange}
              disabled={disabled}
              placeholder={placeholder}
              readOnly={readOnlyInput}
              className={`input input-md w-full ${
                inputStyling ?? 'border-2 bg-transparent'
              } ${iconLeft ? 'pl-11' : ''} ${
                errorHint
                  ? 'border-red-500'
                  : success
                  ? 'form-input-success'
                  : 'form-input'
              }
            ${disabled && ''}`}
            />
            {iconRight && (
              <span className="absolute flex right-1 pr-1">{iconRight}</span>
            )}
          </span>
          {hint && !errorHint && (
            <span className="label-text relative doc-text-subdued">{hint}</span>
          )}
          {errorHint && (
            <span
              ref={errorRef}
              role="alert"
              className="label-text-alt w-full relative top-[2px] p-1 bg-red-700 rounded-md text-white">
              {errorHint}
            </span>
          )}
        </label>
      </div>
    </>
  );
};

export default TextInput;
