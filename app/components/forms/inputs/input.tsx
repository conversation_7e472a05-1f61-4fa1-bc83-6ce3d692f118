export interface InputProps {
  type?: 'text' | 'password' | 'number' | 'email';
  className?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  label: string;
  labelClassName?: string;
  id: string;
}
export default function Input({
  type = 'text',
  className = '',
  value,
  onChange,
  error = '',
  label,
  labelClassName = '',
  id,
}: InputProps) {
  const borderClass = error
    ? 'border-2 border-red-600'
    : 'border-2 border-slate-500';
  return (
    <div>
      <label
        htmlFor={id}
        className={`text-slate-50 text-sm font-semibold pb-4 ${labelClassName}`}>
        {label}
      </label>
      <input
        id={id}
        type={type}
        //TODO when the designs are finalised move the themed oriented styles to the themed files
        // To ensure the ring on focus and prevent the shifting of the element keep in default style: outline-none ring-4 ring-transparent and in focused:  focus:ring-4 focus:ring-tailo-green-500; It's true for all focusable elements with small variations.
        className={`block w-full h-12 p-4 bg-slate-700 rounded-lg  text-slate-50 text-base font-normal leading-tight outline-none ring-4 ring-transparent focus:ring-4 focus:ring-tailo-green-500 ${borderClass} ${className} `}
        onChange={onChange}
        value={value}
      />
      {!!error && (
        <div aria-label="alert" className="bg-red-600 rounded p-2 mt-1">
          <p className="text-red-50 text-sm font-normal">{error}</p>
        </div>
      )}
    </div>
  );
}
