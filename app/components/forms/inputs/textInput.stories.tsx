import React from 'react';
import {Meta, StoryObj} from '@storybook/react';
import TextInput from './textInput';
import { Eye, HambergerMenu } from 'iconsax-react';
import IconButton from '@components/buttons/iconButton';

const meta: Meta = {
  title: 'Inputs/TextInput',
  component: TextInput,
  parameters: {
    controls: {expanded: true},
  },
};
export default meta;

type Story = StoryObj<typeof TextInput>;

export const TextInputStory: Story = {

  render: args => {

    const leftIcon = (
      <IconButton accessibilityLabel='Dragger' size='small' disabled={args.disabled}>
        <HambergerMenu variant='Outline' className='h-6 w-6' />
      </IconButton>
    );

    const rightIcon = (
      <IconButton accessibilityLabel='Eye' size='small' disabled={args.disabled}>
        <Eye variant='Bold' className='h-6 w-6' />
      </IconButton>
    )

    return (
      <>
        <div data-theme="dark">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />
            <TextInput {...args} iconLeft={leftIcon} iconRight={rightIcon}  />
          </div>
        </div>
        <div data-theme="light">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />
            <TextInput {...args} iconLeft={leftIcon} iconRight={rightIcon}  />
          </div>
        </div>
        <div data-theme="yellow">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />
            <TextInput {...args} iconLeft={leftIcon} iconRight={rightIcon}  />
          </div>
        </div>
        <div data-theme="pale-green">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />
            <TextInput {...args} iconLeft={leftIcon} iconRight={rightIcon}  />
          </div>
        </div>
        <div data-theme="pale-pink">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />
            <TextInput {...args} iconLeft={leftIcon} iconRight={rightIcon}  />
          </div>
        </div>
        <div data-theme="pale-blue">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />
            <TextInput {...args} iconLeft={leftIcon} iconRight={rightIcon}  />
          </div>
        </div>
        <div data-theme="peach">
          <div className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />
            <TextInput {...args} iconLeft={leftIcon} iconRight={rightIcon}  />
          </div>
        </div>
      </>
    );
  },
};

TextInputStory.args = {
  disabled: false,
  success: false,
  required: false,
  errorHint: undefined || '',
  hint: undefined || '',
  autoComplete: 'yes' || 'no',
  id: '',
  label: 'Example' ,
  type: 'text' || 'password' || 'email',
  value: ''
}

export const EmailInputStory: Story = {

  render: args => {
    return (
      <>
        <div data-theme="dark">
          <form className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} type='email' />
          </form>
        </div>
        <div data-theme="black-on-white">
          <form className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />
          </form>
        </div>
        <div data-theme="yellow">
          <form className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />            
          </form>
        </div>
        <div data-theme="pale-green">
          <form className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />            
          </form>
        </div>
        <div data-theme="pale-pink">
          <form className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />            
          </form>
        </div>
        <div data-theme="pale-blue">
          <form className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />            
          </form>
        </div>
        <div data-theme="peach">
          <form className="flex flex-wrap items-center app-bg space-x-14 pl-10 py-5">
            <TextInput {...args} />            
          </form>
        </div>
      </>
    );
  }
}

EmailInputStory.args = {
  disabled: false,
  success: false,
  required: true,
  errorHint: undefined || '',
  hint: undefined || '',
  autoComplete: 'yes' || 'no',
  id: '',
  label: 'Email Example' ,
  type: 'email',
  value: '',
}
