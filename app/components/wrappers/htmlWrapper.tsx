import {defaultFontClassName} from '@libs/store/settings/fonts';
import ReportAccessibilityWrapper from '@libs/utils/accessibility/reportAccessibilityWrapper';

export default function HtmlWrapper({children}: {children: React.ReactNode}) {
  return (
    <html lang="en">
      <body>
        <div className={defaultFontClassName}>{children}</div>
        <div id="appRoot" />
        <ReportAccessibilityWrapper />
      </body>
    </html>
  );
}
