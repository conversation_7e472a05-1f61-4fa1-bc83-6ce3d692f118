'use client';

import React, {ElementType} from 'react';
import SkipToContentButton from '@components/buttons/skipToContentButton';
import CookieConsent from '@components/cookieConsent';
import {DEFAULT_SETTINGS_ATTRIBUTE_VALUE} from '@libs/utils/settings/helper';

export default function ThemedPageWrapper({
  children,
  className = '',
  as: Tag = 'main',
}: {
  children: React.ReactNode;
  defaultSettings?: string;
  className?: string;
  as?: ElementType;
}) {
  const theme = DEFAULT_SETTINGS_ATTRIBUTE_VALUE;

  return (
    <Tag data-theme={theme}>
      {Tag === 'main' && (
        <>
          <SkipToContentButton />
          <CookieConsent />
        </>
      )}
      <div
        className={`login-app-bg doc-text-default min-w-full min-h-screen ${className}`}>
        {children}
      </div>
    </Tag>
  );
}
