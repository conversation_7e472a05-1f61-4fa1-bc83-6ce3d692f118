import React, {ElementType} from 'react';
import {getServerSession} from 'next-auth/next';
import UserService from '@libs/services/user/service';
import {authOptions} from '@libs/services/auth/authOptions';
import PrivatePagesClientThemeWrapper from './privatePagesClientThemeWrapper';

/**
 * This wrapper allows to set the theme for the private pages on the server side.
 */
export interface PrivatePagesThemeWrapperProps {
  children: React.ReactNode;
  className?: string;
  as?: ElementType;
}

export default async function PrivatePagesServerThemeWrapper(
  props: PrivatePagesThemeWrapperProps,
) {
  const session = await getServerSession(authOptions);

  // Load the user's settings from session on setup.
  const userService = new UserService(true, session);
  const theme = await userService.fetchUserSettings();

  return <PrivatePagesClientThemeWrapper theme={theme} {...props} />;
}
