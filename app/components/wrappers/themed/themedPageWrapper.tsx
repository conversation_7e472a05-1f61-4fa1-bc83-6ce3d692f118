'use client';
import React, {ElementType} from 'react';
import {useSearchParams} from 'next/navigation';
import useSettings from '@libs/store/settings';
import SkipToContentButton from '@components/buttons/skipToContentButton';
import CookieConsent from '@components/cookieConsent';

export default function ThemedPageWrapper({
  children,
  defaultSettings,
  className = '',
  as: Tag = 'main',
}: {
  children: React.ReactNode;
  defaultSettings?: string;
  className?: string;
  as?: ElementType;
}) {
  const searchParams = useSearchParams();
  const themeParams = searchParams.get('theme');
  const {settingsAttrString} = useSettings();
  const theme = defaultSettings ? defaultSettings : settingsAttrString;
  return (
    <Tag data-theme={themeParams || theme}>
      {Tag === 'main' && (
        <>
          <SkipToContentButton />
          <CookieConsent />
        </>
      )}
      <div
        className={`app-bg doc-text-default min-w-full min-h-screen ${className}`}>
        {children}
      </div>
    </Tag>
  );
}
