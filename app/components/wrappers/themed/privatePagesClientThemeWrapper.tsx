'use client';

import {useAnalytics} from 'use-analytics';
import useSettings from '@libs/store/settings';
import React, {useState, useEffect} from 'react';
import CookieConsent from '@components/cookieConsent';
import {getSettingsAttributeValue} from '@libs/utils/settings/helper';
import SkipToContentButton from '@components/buttons/skipToContentButton';
import {PrivatePagesThemeWrapperProps} from './privatePagesServerThemeWrapper';
import {ISettings} from '@libs/store/settings/types';
import useUserDataFromSession from '@libs/utils/user/useUserDataFromSession';
import isMobileDevice from '@libs/utils/os/is-mobile-device';

interface PrivatePagesClientThemeWrapperProps
  extends PrivatePagesThemeWrapperProps {
  theme: ISettings;
}

/**
 * This wrapper allows to update the UI based on settings
 * update via session. The session update method doesn't
 * trigger a re-render of the component, so we need to use
 * a state to listen for those changes.
 */
export default function PrivatePagesClientThemeWrapper({
  children,
  className = '',
  as: Tag = 'main',
  theme,
}: PrivatePagesClientThemeWrapperProps) {
  // Check if we're on iOS, android.
  const isMobile = isMobileDevice();
  const isMobileClass = isMobile ? 'is-mobile' : '';
    
  const {settings, updateSettings} = useSettings();
  const {identify, user} = useAnalytics();
  const [dataTheme, setDataTheme] = useState<string>('');
  const {trackingId} = useUserDataFromSession();

  useEffect(() => {
    // Fetch the theme from the settings state.
    const newTheme = getSettingsAttributeValue(settings);

    // Set the theme in the local scope.
    setDataTheme(newTheme);

    // Log Amplitude events.
    // Update the analytics user properties.
    identify(trackingId ?? '', {
      VisualSettingsChanged: true,
      ...settings,
    });
  }, [settings]);

  useEffect(() => {
    updateSettings(theme);
    // Fetch the theme from the settings state.
    const newTheme = getSettingsAttributeValue(theme);

    // Set the theme in the local scope.
    setDataTheme(newTheme);

    // Log Amplitude events.
    // Update the analytics user properties.
    identify(trackingId ?? '', {
      VisualSettingsChanged: true,
      ...settings,
    });
  }, []);

  return (
    <Tag data-theme={dataTheme ?? theme}>
      <SkipToContentButton />
      <CookieConsent />
      <div
        className={`app-bg doc-text-default min-w-full min-h-screen ${className} ${isMobileClass}`}>
        {children}
      </div>
    </Tag>
  );
}
