import React from 'react';
import ThemedPageWrapper from './themedPageWrapper';
import {getServerSession} from 'next-auth/next';
import PrivatePagesServerThemeWrapper from './privatePagesServerThemeWrapper';

export default async function SharedPagesThemedWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession();
  // we are using session settings that are set during onboarding or where there is no auth session, otherwise we use settings defined within private page wrapper (local/remote)
  if (session)
    return (
      <PrivatePagesServerThemeWrapper>
        <div className="lg:flex">{children}</div>
      </PrivatePagesServerThemeWrapper>
    );
  // TODO Awaiting design for new nav UI on public pages
  return (
    <ThemedPageWrapper>
      <div className="lg:flex">{children}</div>
    </ThemedPageWrapper>
  );
}
