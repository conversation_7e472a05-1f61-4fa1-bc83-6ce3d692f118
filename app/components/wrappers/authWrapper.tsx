'use client';
import {useSession} from 'next-auth/react';
import {useEffect} from 'react';
import {useRouter, redirect} from 'next/navigation';
import {AppPrivateRoutes, AppPublicRoutes} from '@libs/services/auth/routes';

/** Logs users out when the attempt to refresh the access token fails due to expiry of the refresh token */

export const Auth = ({children}: {children: React.ReactNode}) => {
  const {data: session} = useSession();
  const router = useRouter();

  useEffect(() => {
    // Check if the failed refresh error has occurred and then sign out user
    if (session?.user.error === 'RefreshAccessTokenError') {
      // return redirect(AppPrivateRoutes.SignOut);
      router.push('/logout');
      return;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user.error]);

  if (session === null) {
    return redirect(AppPublicRoutes.SignIn);
  }

  return <>{children}</>;
};
