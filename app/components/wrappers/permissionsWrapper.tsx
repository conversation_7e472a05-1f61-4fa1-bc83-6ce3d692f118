'use client';
import React from 'react';
import PermissionsProvider from '../providers/permissions/provider';
import {useSession} from 'next-auth/react';

export default function PermissionsWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const {data: session} = useSession();
  const permissions = session?.user.permissions || [];

  return (
    <PermissionsProvider userPermissions={permissions}>
      <>{children}</>
    </PermissionsProvider>
  );
}
