'use client';
import Analytics from 'analytics';
import amplitudePlugin from '@analytics/amplitude';
import {FC} from 'react';
import {AnalyticsProvider, AnalyticsProviderProps} from 'use-analytics';

export const appVersion = 'Internal'; //TODO change for beta etc. Not sure of a better way of doing this...

const analytics = Analytics({
  app: 'awesome-app',
  plugins: [
    amplitudePlugin({
      apiKey: process.env.NEXT_PUBLIC_AMPLITUDE_API_KEY,
      // See options at https://bit.ly/3dRdZnE
    }),
  ],
});

/** Logs users out when the attempt to refresh the access token fails due to expiry of the refresh token */
interface AnalyticsWrapperProps
  extends Omit<AnalyticsProviderProps, 'instance'> {}
export const AnalyticsWrapper: FC<AnalyticsWrapperProps> = ({...props}) => {
  // https://github.com/DavidWells/analytics/tree/master/packages/analytics-plugin-amplitude

  return (
    <AnalyticsProvider instance={analytics}>{props.children}</AnalyticsProvider>
  );
};
