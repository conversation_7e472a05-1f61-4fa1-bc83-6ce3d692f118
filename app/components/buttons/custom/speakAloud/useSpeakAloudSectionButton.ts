import * as Pusher from 'pusher-js';
import {useEffect, useState} from 'react';
import {useSession} from 'next-auth/react';
import {useTextToSpeech} from '@contexts/tts';
import useSettings from '@libs/store/settings';
import {useSearchParams} from 'next/navigation';
import useQuotas from '@components/quotas/useQuotas';
import {ISendTextToSpeech} from '@libs/services/tts/types';
import {useGlobalAudioPlayer} from 'react-use-audio-player';
import {startTimer} from '@libs/utils/wordDefinition/helper';
import useSpeakAloud, {SpeakAloudState} from '@libs/store/speakAloud';
import textToSpeechService from '@libs/services/tts/textToSpeechService';
import {useTextToSpeechAnalytics} from '@libs/services/tts/useTextToSpeechAnalytics';

import {
  IDocumentSkeleton,
  IUpdateAudioFile,
} from '@libs/store/speakAloud/types';

import {
  IDocumentDetails,
  IContentTextNode,
  ISectionData,
} from '@libs/store/document/types';

export default function UseSpeakAloudSectionButton({
  documentId,
  section,
}: {
  documentId: string;
  section: ISectionData | IDocumentDetails;
}) {
  const session = useSession();
  const {settings} = useSettings();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  const {sectionToAutoplay, setSectionToAutoplay} = useTextToSpeech();
  const {trackReadAloudDownload, trackReadAloudProcessed, trackReadAloudPlay} =
    useTextToSpeechAnalytics();

  const {refresh: refreshQuota, data} = useQuotas();

  const [readyAudio, setReadyAudio] = useState(false);

  const {
    documentSkeleton,
    updateDocumentSkeleton,
    updateSpeakAloudState,
    updateCurrentParagraph,
  } = useSpeakAloud();

  const [isDownloading, setIsDownloading] = useState(false);

  const {play} = useGlobalAudioPlayer();

  const sectionId = 'content' in section ? section.id : 'summary';

  const updateDownloadedAudio = (data: IUpdateAudioFile) => {
    // Update the audio if it's a section.
    if (sectionId !== 'summary') {
      updateDocumentSkeleton((prev: IDocumentSkeleton) => {
        return {
          ...prev,
          sections: prev.sections.map(section => {
            return {
              ...section,
              paragraphs: section.paragraphs.map((p: any) => {
                const paragraph = p as IContentTextNode;

                if (data.paragraph_id === paragraph.id) {
                  return {
                    ...paragraph,
                    tts: {
                      status: data.status,
                      audio_file: data.audio_file,
                    },
                  };
                }

                return paragraph;
              }),
            };
          }),
        };
      });
    }

    // Update the audio if we're in the summary.
    if (sectionId === 'summary') {
      updateDocumentSkeleton((prev: IDocumentSkeleton) => {
        return {
          ...prev,
          summary: {
            tts: {
              status: data.status,
              audio_file: data.audio_file,
            },
          },
        };
      });
    }

    refreshQuota();
  };

  const downloadSectionAudio = async () => {
    // Set next section to autoplay when ready.
    setSectionToAutoplay(sectionId);

    const stopTimer = startTimer();

    let requests: Array<Promise<ISendTextToSpeech>> = [];

    const pusherKey = process.env.NEXT_PUBLIC_PUSHER_KEY ?? '';

    const pusher = new Pusher.default(pusherKey, {
      cluster: 'eu',
    });

    // Subscribe to the user's pusher channel.
    if (session?.data?.user) {
      const channel = pusher.subscribe(session.data.user.id);

      channel.bind('tts-audio-created', function (data: any) {
        updateDownloadedAudio(data);
      });
    }

    setIsDownloading(true);

    trackReadAloudDownload(tabParam ?? '', settings.Voice);

    try {
      if (sectionId !== 'summary') {
        const sectionContent = section as ISectionData;

        const sectionsToSend = sectionContent.content.map(async p => {
          const content = p as any;

          // If we're dealing with a pargraph of text.
          if (content.text) {
            return textToSpeechService.sendTextToSpeech(
              documentId,
              sectionId,
              content.id,
              content.text,
              {
                voice: settings.Voice,
                language: 'en',
              },
            );
          }

          // If we're dealing with a list of text.
          if (content.items) {
            let text = content.items.map((item: any) => item.value).join(' ');

            return textToSpeechService.sendTextToSpeech(
              documentId,
              sectionId,
              content.id,
              text,
              {
                voice: settings.Voice,
                language: 'en',
              },
            );
          }
        });

        if (sectionsToSend) {
          requests = sectionsToSend as Array<Promise<ISendTextToSpeech>>;
        }
      }

      if (sectionId === 'summary') {
        const overview = section as IDocumentDetails;

        // Concatinate the summary into one audio file.
        if (overview.summary) {
          requests.push(
            textToSpeechService.sendTextToSpeech(
              documentId,
              sectionId,
              'summary',
              overview.summary.join(';'),
              {
                voice: settings.Voice,
                language: 'en',
              },
            ),
          );
        }
      }

      await Promise.all(requests);

      const quotaRemaining = data.tts.allowance - data.tts.used;
      const timeTaken = stopTimer();
      trackReadAloudProcessed(
        tabParam ?? '',
        timeTaken,
        data.tts.used,
        quotaRemaining,
        settings.Voice,
      );
    } catch (error) {
      console.error('Failed to download audio for section', error);
      const quotaRemaining = data.tts.allowance - data.tts.used;
      const timeTaken = stopTimer();
      trackReadAloudProcessed(
        tabParam ?? '',
        timeTaken,
        data.tts.used,
        quotaRemaining,
        settings.Voice,
        error as string,
      );
    }
  };

  const playSection = () => {
    updateSpeakAloudState(SpeakAloudState.PLAY);

    // If we're handling playing of the summary/overview.
    if (sectionId === 'summary' && documentSkeleton?.summary?.tts) {
      updateCurrentParagraph({
        id: 'summary',
        tts: documentSkeleton?.summary?.tts,
      });

      return;
    }

    // Find the current section in our skeleton.
    const section = documentSkeleton?.sections.find(
      section => section.id === sectionId,
    );

    // Calculate which paragraph should be played first.
    const paragraph = section?.paragraphs[0];

    // TODO: Handle the errors here better for user experience.
    if (!paragraph?.tts.audio_file) return;

    // Set the current paragraph.
    updateCurrentParagraph(paragraph);

    // Play the audio.
    const quotaRemaining = data.tts.allowance - data.tts.used;
    trackReadAloudPlay(tabParam ?? '', quotaRemaining);
    play();
  };

  useEffect(() => {
    if (!readyAudio) return;

    setIsDownloading(false);
  }, [readyAudio]);

  // Set the audio on change of state.
  useEffect(() => {
    if (!documentSkeleton && !sectionId) return;

    if (sectionId === 'summary') {
      const isReady = documentSkeleton?.summary?.tts?.status === 'ready';

      // If we're ready and we should autoplay.
      if (isReady && sectionId === sectionToAutoplay) {
        playSection();
      }

      setReadyAudio(isReady);

      return;
    }

    if (sectionId !== 'summary') {
      const section = documentSkeleton?.sections.find(
        section => section.id === sectionId,
      );

      if (!section) return;

      const paragraphs = section.paragraphs.filter(p => p.tts !== undefined);

      const isReady = paragraphs.every((p: any) => p?.tts?.status === 'ready');

      // If we're ready and we should autoplay.
      if (isReady && sectionId === sectionToAutoplay) {
        playSection();
      }

      setReadyAudio(isReady);
    }
  }, [documentSkeleton, sectionId]);

  return {
    readyAudio,
    playSection,
    downloadSectionAudio,
    isDownloading,
  };
}
