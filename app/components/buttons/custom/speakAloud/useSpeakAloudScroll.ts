import {useEffect, useRef} from 'react';
import useSpeakAloud from '@libs/store/speakAloud';

export const useSpeakAloudScroll = (id: string) => {
  const {currentParagraph} = useSpeakAloud();
  const refs = useRef<HTMLElement>(null);

  const allowScroll = () => {
    if (currentParagraph?.id === id) {
      refs.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  };
  useEffect(() => {
    allowScroll();
  }, [currentParagraph, id]);
  return refs;
};
