import Button from '@components/buttons/button';
import {PlayCircle, MusicPlay} from 'iconsax-react';
import UseSpeakAloudSectionButton from './useSpeakAloudSectionButton';
import {ISectionData, IDocumentDetails} from '@libs/store/document/types';

interface SpeakAloudSectionButtonProps {
  documentId: string;
  section: ISectionData | IDocumentDetails;
  showText?: boolean;
}

export default function SpeakAloudSectionButton(
  props: SpeakAloudSectionButtonProps,
) {
  const {documentId, section, showText} = props;

  const {readyAudio, isDownloading, playSection, downloadSectionAudio} =
    UseSpeakAloudSectionButton({documentId, section});

  return (
    <Button
      accessibilityLabel="Read aloud"
      variant="secondary"
      size="sm"
      onClick={readyAudio ? playSection : downloadSectionAudio}
      loading={isDownloading}
      loadingText="Loading..."
      disabled={isDownloading}>
      {readyAudio ? (
        <>
          <MusicPlay variant="Bold" className="" />
          {showText && <span>Read aloud</span>}
        </>
      ) : (
        <>
          <MusicPlay className="" />
          {showText && <span>Read aloud</span>}
        </>
      )}
    </Button>
  );
}
