import Button from '@components/buttons/button';
import useSummaryTab from '@components/document/summaryTab/useSummaryTab';
import {DocumentMimeType} from '@libs/services/document/useDocuments';
import {DocumentDownload, ExportSquare} from 'iconsax-react';

export default function OpenFileButton({fileType}: {fileType?: string}) {
  const {isLoading, getFileUrl} = useSummaryTab();
  const isPdf = fileType === DocumentMimeType.PDF;

  return (
    <Button
      accessibilityLabel={isPdf ? 'Open original file' : 'Download file'}
      variant="tertiary"
      size="sm"
      className=""
      onClick={() =>
        getFileUrl(isPdf).then(fileUrl => {
          if (isPdf) {
            // Open the PDF in a new tab
            window.open(fileUrl, '_blank');
          } else {
            // Trigger a download for DOCX
            const link = document.createElement('a');
            link.href = fileUrl;
            link.download = 'document.docx'; // Default filename if none is provided
            link.click();
          }
        })
      }
      loading={isLoading}
      loadingText="Loading..."
      disabled={isLoading}>
      {isPdf ? (
        <>
          <span>Open original file</span>
          <ExportSquare variant="Bold" className="w-4 h-4" />
        </>
      ) : (
        <>
          <span>Download file</span>
          <DocumentDownload variant="Bold" className="w-4 h-4" />
        </>
      )}
    </Button>
  );
}
