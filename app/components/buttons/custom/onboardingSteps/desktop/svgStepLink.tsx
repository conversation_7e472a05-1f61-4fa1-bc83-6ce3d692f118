import React from 'react';
import SvgOutline from './svgOutline';
import Link from 'next/link';
import {TickCircle} from 'iconsax-react';

export default function SvgStepLink({
  href,
  current,
  completed,
  heading,
  value,
  extendTab = 0,
}: {
  href: string;
  value: string;
  current: boolean;
  completed: boolean;
  heading: string;
  extendTab?: number; // Use it to adjust the tab's width for different headings
}) {
  const disabled = !current && !completed;
  const completedAndNotCurrent = completed && !current;
  const stateClass = disabled
    ? 'disabled'
    : completedAndNotCurrent
    ? 'completed'
    : '';

  return (
    <Link
      href={disabled ? '' : href}
      role="link" // needed when using the aria-disabled attribute
      aria-disabled={disabled}
      aria-label={`${heading}: ${value}. ${current ? 'Current step.' : ''} ${
        completed ? 'Completed step.' : ''
      }`}
      className={`relative block desktop-onboarding-step-link ${stateClass} h-[92px] w-64 }`}>
      <div className="absolute top-0 left-0" aria-hidden>
        <SvgOutline extendTab={extendTab} />
      </div>
      <div
        className={`absolute top-0 left-0 p-3 pl-5 ${
          current ? 'doc-text-inverted' : 'doc-text-default'
        } aria-hidden`}>
        <h2 className="font-bold">{heading}</h2>
        <div className="flex justify-start items-center mt-3">
          {current || completed ? (
            <TickCircle
              className={`w-7 h-7 mr-1 ${
                completedAndNotCurrent
                  ? 'doc-text-default'
                  : 'doc-text-inverted'
              }`}
              variant={completed ? 'Bold' : 'Outline'}
            />
          ) : null}
          <p className="text-xl font-bold capitalize line-clamp-1">{value}</p>
        </div>
      </div>
    </Link>
  );
}
