import type {Meta, StoryObj} from '@storybook/react';
import SvgOutline from './svgOutline';

const meta: Meta<typeof SvgOutline> = {
  title: 'Button/Custom/SvgOutline',
  component: SvgOutline,
  parameters: {
    controls: {expanded: true},
  },
};

export default meta;
type Story = StoryObj<typeof SvgOutline>;

export const SvgOutlineDefault: Story = {
  render: args => <SvgOutline {...args} />,
};

SvgOutlineDefault.argTypes = {
  extendTab: {control: {type: 'number', min: 0, max: 40, step: 1}},
  className: {control: {type: 'text'}},
  outlineClassName: {control: {type: 'text'}},
};
SvgOutlineDefault.args = {
  className: 'stroke-slate-950 fill-none',
};
