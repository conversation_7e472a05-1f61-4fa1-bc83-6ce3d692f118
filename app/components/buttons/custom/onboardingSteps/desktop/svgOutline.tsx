export interface SvgOutlineProps {
  extendTab?: number; //-10 to 40
  className?: string;
  outlineClassName?: string;
}
const DEFAULT_EXTEND_TAB = 20;
export default function SvgOutline({
  extendTab = DEFAULT_EXTEND_TAB,
  className = '',
  outlineClassName = '',
}: SvgOutlineProps) {
  const size = 130;
  const height = size;
  const width = (size * 80.858162) / 29.047045;

  if (extendTab < -10 || extendTab > 50) {
    extendTab = DEFAULT_EXTEND_TAB;
  }
  return (
    <div
      style={{
        width,
        height,
      }}>
      <svg
        width={width}
        height={height}
        viewBox="5 3 80.858162 29.047045" // 5 and 4 position the svg in the top left corner based on the size value = 130
        version="1.1"
        id="svg23119"
        xmlns="http://www.w3.org/2000/svg">
        <defs id="defs23116" />
        <g id="layer1" transform="translate(-47.179746,-87.376059)">
          <g
            id="g7633"
            transform="matrix(0.81973435,0,0,0.81973435,11.815343,18.113185)">
            <path
              className={`inner-outline ${className}`}
              strokeWidth={0.65}
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit={4}
              d={`m 50.338872,91.386009 c 0,0 -0.03859,14.505431 0.12551,20.780441 0.0075,0.28391 0.206259,0.54883 0.409377,0.74852 0.205158,0.20171 0.476483,0.40352 0.765021,0.40265 15.989355,-0.0483 58.59247,-0.0935 63.74123,-0.14274 0.29158,-0.002 0.60844,-0.0937 0.82578,-0.28695 0.19122,-0.17003 0.33344,-0.43471 0.33312,-0.68975 -0.004,-3.4809 0.0243,-8.94358 -0.0516,-13.492903 -0.004,-0.259659 -0.20308,-0.502792 -0.40593,-0.666411 -0.21262,-0.171505 -0.50141,-0.269028 -0.7752,-0.269987 -9.52145,-0.03345 
               ${-41.716458 + extendTab},-0.0364 
               ${-41.716458 + extendTab},-0.0364 
              0,0 -0.01005,-4.303162 -0.05615,-6.40074 -0.0064,-0.293022 -0.176466,-0.651118 -0.398398,-0.887583 -0.21957,-0.233948 -0.554553,-0.44728 -0.851404,-0.448993 -9.930061,-0.05738 
              ${-9.971812 - extendTab},0.0041 
              ${-20.528408 - extendTab},-0.01746 
              -0.330528,-6.64e-4 -0.678336,0.152572 -0.924368,0.397164 -0.246034,0.244592 -0.39497,0.654072 -0.492074,1.011 -4e-6,1.7e-5 -2e-6,4.1e-5 -2e-6,4.1e-5 z`}
              id="path20390"
            />
            <path
              fill="none"
              strokeWidth={1}
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit={4}
              className={`outer-outline  ${outlineClassName}`}
              d={`m 49.800386,112.31543 c 0.0075,0.29635 0.326535,0.73844 0.612653,1.00823 0.305819,0.28837 0.822356,0.59139 1.114221,0.59048 16.173792,-0.0504 58.60184,-0.0976 63.80999,-0.14899 0.29495,-0.002 1.01945,-0.16167 1.36413,-0.50223 0.32154,-0.31771 0.48408,-1.00063 0.48375,-1.26686 -0.004,-3.63339 0.0253,-8.32173 -0.0515,-13.070358 -0.004,-0.271035 -0.15741,-0.816211 -0.4154,-1.119161 -0.30723,-0.360753 -0.95996,-0.699577 -1.23692,-0.700578 -9.63127,-0.03491 
              ${-41.312931 + extendTab},-0.0063 
              ${-41.312931 + extendTab},-0.0063 
              0,0 0.02312,-3.794818 -0.02344,-5.984289 -0.0066,-0.305859 -0.290265,-0.928907 -0.637388,-1.236246 -0.337566,-0.298877 -0.968161,-0.467861 -1.268436,-0.469649 -10.044604,-0.0599 
              ${-9.928253 - extendTab},-0.07119 
              ${
                -20.606618 - extendTab
              },-0.09365 -0.334341,-6.94e-4 -0.952207,0.162304 -1.423026,0.703542 -0.520312,0.527841 -0.53006,2.13944 -0.502542,2.557726 0.02724,0.414012 -0.034,14.231593 0.09344,19.738333 z`}
              id="path1518"
            />
          </g>
        </g>
      </svg>
    </div>
  );
}
