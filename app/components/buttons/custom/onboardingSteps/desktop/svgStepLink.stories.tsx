import type {Meta, StoryObj} from '@storybook/react';
import SvgStepLink from './svgStepLink';

const meta: Meta<typeof SvgStepLink> = {
  title: 'Button/Custom/SvgStepLink',
  component: SvgStepLink,
  parameters: {
    controls: {expanded: true},
  },
};

export default meta;
type Story = StoryObj<typeof SvgStepLink>;

export const SvgStepLinkDefault: Story = {
  render: args => (
    <div>
      <div data-theme="dark">
        <div className="flex app-navbar-bg p-8 space-x-14">
          <SvgStepLink {...args} />
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-navbar-bg p-8 space-x-14">
          <SvgStepLink {...args} />
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-navbar-bg p-8 space-x-14">
          <SvgStepLink {...args} />
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-navbar-bg p-8 space-x-14">
          <SvgStepLink {...args} />
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-navbar-bg p-8 space-x-14">
          <SvgStepLink {...args} />
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-navbar-bg p-8 space-x-14">
          <SvgStepLink {...args} />
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-navbar-bg p-8 space-x-14">
          <SvgStepLink {...args} />
        </div>
      </div>
    </div>
  ),
};

SvgStepLinkDefault.argTypes = {};
SvgStepLinkDefault.args = {
  href: '/onboarding/step-1',
  heading: 'Label',
  value: 'Value',
  extendTab: -3,
};
