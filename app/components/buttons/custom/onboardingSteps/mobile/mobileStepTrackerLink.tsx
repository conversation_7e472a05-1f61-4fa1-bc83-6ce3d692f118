import {AppDynamicRoutes} from '@libs/services/auth/routes';
import {Step} from '@libs/store/onboarding/types';
import {TickCircle} from 'iconsax-react';
import Link from 'next/link';

interface MobileStepTrackerLinkProps {
  step: number;
  stepName: string;
  href: string;
  current: boolean;
  completed: boolean;
  onClick?: () => void;
}

export default function MobileStepTrackerLink({
  step,
  stepName,
  href,
  current,
  completed,
  onClick,
}: MobileStepTrackerLinkProps) {
  const disabled = !current && !completed;

  return (
    <Link
      className={`${
        disabled ? 'pointer-events-none' : ''
      } outline-0 relative w-full`}
      role="link"
      href={href}
      onClick={onClick}
      aria-disabled={disabled}
      tabIndex={0}>
      <div className="flex flex-col items-center h-10 relative flex-1">
        {step !== 1 && (
          <div
            className={`top-[10px] z-0 w-full h-1 relative ${
              disabled
                ? 'mobile-step-indicator-inactive'
                : 'mobile-step-indicator'
            }`}
          />
        )}
        <div
          className={`flex w-6 h-6 ${
            disabled
              ? 'mobile-step-indicator-inactive'
              : 'mobile-step-indicator'
          } rounded-full items-center justify-center absolute right-0 z-10`}>
          <h2
            className={`text-xs font-bold uppercase ${
              disabled ? 'doc-text-inactive' : 'doc-text-default'
            } pt-1 absolute top-6 z-10`}>
            {stepName}
          </h2>
          {completed || current ? (
            <TickCircle
              className="h-4 w-4 mobile-step-indicator z-10"
              variant={completed && !current ? 'Bold' : 'Outline'}
            />
          ) : (
            <p className="text-base font-bold mt-[1px] z-10">{step}</p>
          )}
        </div>
      </div>
    </Link>
  );
}
