import {Meta, StoryObj} from '@storybook/react';
import MobileStepTrackerLink from './mobileStepTrackerLink';

const meta: Meta<typeof MobileStepTrackerLink> = {
  title: 'Button/Custom/MobileStepTrackerLink',
  component: MobileStepTrackerLink,
};

export default meta;
type Story = StoryObj<typeof MobileStepTrackerLink>;

export const StepTrackerStory: Story = {
  render: args => (
    <>
      <div data-theme="dark" className="pb-4 space-y-4">
        <div className="flex app-bg p-14 space-x-14">
          <MobileStepTrackerLink {...args} />
        </div>
      </div>
      <div data-theme="light" className="pb-4">
        <div className="flex app-bg p-14 space-x-14">
          <MobileStepTrackerLink {...args} />
        </div>
      </div>
      <div data-theme="yellow" className="pb-4">
        <div className="flex app-bg p-14 space-x-14">
          <MobileStepTrackerLink {...args} />
        </div>
      </div>
      <div data-theme="pale-green" className="pb-4">
        <div className="flex app-bg p-14 space-x-14">
          <MobileStepTrackerLink {...args} />
        </div>
      </div>
      <div data-theme="pale-pink" className="pb-4">
        <div className="flex app-bg p-14 space-x-14">
          <MobileStepTrackerLink {...args} />
        </div>
      </div>
      <div data-theme="pale-blue" className="pb-4">
        <div className="flex app-bg p-14 space-x-14">
          <MobileStepTrackerLink {...args} />
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-14 space-x-14">
          <MobileStepTrackerLink {...args} />
        </div>
      </div>
    </>
  ),
};

StepTrackerStory.args = {
  stepName: 'Theme',
  step: 1,
  current: false,
  completed: false,
  href: '',
};
