import React, {ButtonHTMLAttributes, DetailedHTMLProps} from 'react';
import IconButton, {IIconButtonProps} from './iconButton';

/**
 * By default it's placed in the bottom right corner.
 * Use the TW class to override the default positioning.
 */

export interface IFab {
  position?: string;
  className?: string;
}
export default function Fab(
  props: IFab &
    IIconButtonProps &
    DetailedHTMLProps<
      ButtonHTMLAttributes<HTMLButtonElement>,
      HTMLButtonElement
    >,
) {
  const {className = '', position = 'bottom-4 right-2', ...rest} = props;
  return (
    <div className={`fixed ${position} z-40 ${className}`}>
      <IconButton fab={true} className="rounded-full" {...rest} />
    </div>
  );
}
