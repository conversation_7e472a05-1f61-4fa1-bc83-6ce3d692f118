import {ButtonVariant, ButtonSize} from './button';
import {IconButtonSize} from './iconButton';

export function getIconButtonStyles(fab: boolean, size: IconButtonSize) {
  const style = fab ? 'btn-primary justify-center' : 'btn-tertiary justify-center';

  let buttonSize = '';
  let fabShadow = '';

  switch (size) {
    case 'small':
      buttonSize = fab ? 'w-10 h-10' : 'w-10 h-10 ';
      fabShadow = fab ? 'shadow-lg-d focus:shadow-none' : '';
      break;
    case 'medium':
      buttonSize = 'w-12 h-12';
      fabShadow = fab ? 'shadow-xl-d focus:shadow-none' : '';
      break;
    case 'large':
    default:
      buttonSize = fab ? 'w-16 h-16' : 'w-14 h-14';
      fabShadow = fab ? 'shadow-2xl-d focus:shadow-none' : '';
  }

  return {style, buttonSize, fabShadow};
}

export function getButtonVariantStyle(variant: ButtonVariant) {
  let themeStyle = '';
  switch (variant) {
    case 'secondary':
      themeStyle = 'btn-secondary';
      break;
    case 'tertiary':
      themeStyle = 'btn-tertiary';
      break;
    case 'destructive':
      themeStyle = 'btn-destructive';
      break;
    case 'positive':
      themeStyle = 'btn-positive';
      break;
    case 'primary':
    default:
      themeStyle = 'btn-primary';
      break;
  }
  return themeStyle;
}

export function getButtonSize(size: ButtonSize) {
  let buttonSize = '';
  switch (size) {
    case 'xs':
      buttonSize = 'btn-xs';
      break;
    case 'sm':
      buttonSize = 'btn-sm';
      break;
    case 'lg':
      buttonSize = 'btn-lg';
      break;
    case 'md':
    default:
      buttonSize = 'btn-md';
      break;
  }
  return buttonSize;
}