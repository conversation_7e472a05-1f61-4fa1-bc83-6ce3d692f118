import React from 'react';
import {IFab} from './fab';
import IconButtonLink, {IIconButtonLinkProps} from './iconButtonLink';

/**
 * By default it's placed in the bottom right corner.
 * Use the TW class to override the default positioning.
 */

export default function FabLink(props: IFab & IIconButtonLinkProps) {
  const {className = '', position = 'bottom-2 right-2', ...rest} = props;
  return (
    <div className={`fixed ${position} z-40 ${className}`}>
      <IconButtonLink fab={true} className="rounded-full" {...rest} />
    </div>
  );
}
