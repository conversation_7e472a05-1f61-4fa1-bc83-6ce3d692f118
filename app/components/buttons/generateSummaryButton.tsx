'use client';
import {Book1} from 'iconsax-react';
import React from 'react';
import Button from './button';
import {useStoreSummaryForm} from '@libs/store/summarys/useStoreSummaryForm';
import { PromptBuilderMethod } from '@libs/store/summarys/types';

const GenerateSummaryButton = ({className = '', onSummarySubmitted, promptType}: {className?: string, onSummarySubmitted?: () => void, promptType?: PromptBuilderMethod}) => {
  const {submitBuilderForm, submitCustomForm} = useStoreSummaryForm();

  const processBuilderSummaryAndClose = () => {
    //TODO This needs fixed to accomodate putting the user on to the summaries page when it has finished doing its work to the api.
    submitBuilderForm().then(onSummarySubmitted);
  }

  const processCustomSummaryAndClose = () => {
    //TODO This needs fixed to accomodate putting the user on to the summaries page when it has finished doing its work to the api.
    submitCustomForm().then(onSummarySubmitted);
  }

  return (
    <Button
      onClick={promptType === 'builder' ? processBuilderSummaryAndClose : processCustomSummaryAndClose}
      variant={'primary'}
      className={`w-full mt-4 ${className}`}>
      <Book1 className="h-6 w-6" variant="Bold" />
      Generate summary
    </Button>
  );
};

export default GenerateSummaryButton;
