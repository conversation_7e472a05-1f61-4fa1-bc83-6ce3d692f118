export interface TailoIconButtonProps {
  children: React.ReactNode;
  handler?: (e?: any) => void; //optional event argument for forms
  label: string;
  isActive?: string | boolean;
  disabled?: boolean;
  size: 'small' | 'medium' | 'large';
  type?: 'reset' | 'submit';
  className?: string;
  isCardIconButton?: boolean;
}

export default function TailoIconButton({
  children,
  handler,
  label,
  isActive,
  disabled,
  size,
  type,
  className,
  // Sets TailoIconButton to use z-index: 10 (if true), so that it is clickable over the .card-link area
  isCardIconButton,
}: TailoIconButtonProps) {
  const active = isActive === 'open' || isActive === true;

  return (
    <button
      title={label}
      aria-label={label}
      disabled={disabled}
      type={type}
      className={`btn btn-tertiary tailo-icon-button ${
        active ? 'tailo-icon-button-active' : ''
      } ${size === 'small' ? 'icon-btn-sm' : 'icon-btn-md'} ${
        isCardIconButton === true ? 'z-10 relative' : ''
      } ${
        disabled ? 'hover:cursor-default hover:bg-transparent' : ''
      } ${className}`}
      onClick={handler}>
      {children}
    </button>
  );
}
