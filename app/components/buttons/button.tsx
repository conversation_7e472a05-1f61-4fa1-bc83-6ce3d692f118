import React, {ButtonHTMLAttributes, DetailedHTMLProps} from 'react';
import {getButtonVariantStyle, getButtonSize} from './helper';
import SvgGradientLoader from '@components/loaders/svgGradientLoader';

export type ButtonVariant =
  | 'primary'
  | 'secondary'
  | 'tertiary'
  | 'destructive'
  | 'positive';

export type ButtonSize =
  | 'xs'
  | 'sm'
  | 'md'
  | 'lg';

export interface IButtonProps {
  variant: ButtonVariant;
  size?: ButtonSize;
  className?: string;
  loading?: boolean;
  children: React.ReactNode;
  accessibilityLabel?: string;
  loadingText?: string;
}

const Button = (
  props: IButtonProps &
    DetailedHTMLProps<
      ButtonHTMLAttributes<HTMLButtonElement>,
      HTMLButtonElement
    >,
) => {
  const {
    variant,
    size = 'md',
    className = '',
    children,
    loading,
    accessibilityLabel = 'Button',
    loadingText = undefined,
    ...buttonProps
  } = props;
  const themeStyle = getButtonVariantStyle(variant);
  const buttonSize = getButtonSize(size);
  const loadingIconOnly = loading && !loadingText && <SvgGradientLoader className="" />;
  const loadingIconWithText = loading && loadingText && (
    <>
      <SvgGradientLoader size={16} className="doc-text-default" />
      <span>{loadingText}</span>
    </>
  );

  return (
    <button
      {...buttonProps}
      className={`btn ${buttonSize} ${themeStyle} ${className}`}
      aria-label={accessibilityLabel}>
      {loadingIconWithText || loadingIconOnly || children}
    </button>
  );
};

export default Button;
