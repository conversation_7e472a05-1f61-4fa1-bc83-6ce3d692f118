import {Copy} from 'iconsax-react';
import {getThemeColours} from '@components/onboarding/results/helper';
import {useCustomisation} from '@components/customisation/useCustomisation';
import {ThemeColour} from '@libs/store/settings/types';
import TailoIconButton from '../tailoIconButton';

export default function CopyBgHexCodeButton({
  disabled = false,
}: {
  disabled: boolean;
}) {
  const {selectedCategory: Theme} = useCustomisation('Theme');

  const selectedThemeColours = getThemeColours(Theme as ThemeColour);

  const copyBgColour = () => {
    navigator.clipboard.writeText(selectedThemeColours.bgColour);
  };

  return (
    <TailoIconButton
      disabled={disabled}
      size="small"
      handler={copyBgColour}
      label="Copy background colour hex code">
      <Copy variant="Bold" />
    </TailoIconButton>
  );
}
