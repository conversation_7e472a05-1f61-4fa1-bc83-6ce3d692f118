import {getThemeColours} from '@components/onboarding/results/helper';
import {Copy} from 'iconsax-react';
import {useCustomisation} from '@components/customisation/useCustomisation';
import {ThemeColour} from '@libs/store/settings/types';
import TailoIconButton from '../tailoIconButton';

export default function CopyTextHexCodeButton({
  disabled = false,
}: {
  disabled: boolean;
}) {
  const {selectedCategory: Theme} = useCustomisation('Theme');

  const selectedThemeColours = getThemeColours(Theme as ThemeColour);

  const copyTextColour = () => {
    navigator.clipboard.writeText(selectedThemeColours.textColour);
  };

  return (
    <TailoIconButton
      disabled={disabled}
      handler={copyTextColour}
      label="Copy text colour hex code"
      size="small">
      <Copy variant="Bold" />
    </TailoIconButton>
  );
}
