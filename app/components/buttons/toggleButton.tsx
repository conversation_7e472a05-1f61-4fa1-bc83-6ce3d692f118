import React from 'react';

interface ToggleButtonProps {
  checked: boolean;
  handleToggle: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function ToggleButton(props: ToggleButtonProps) {
  let {checked, handleToggle} = props;
  return (
      <label className="relative inline-flex cursor-pointer items-center">
        <input id="switch" type="checkbox" className="peer sr-only" checked={checked}
        onChange={handleToggle} />
        <div className="peer h-6 w-11 rounded-full border after:absolute after:left-[2px] after:top-0.5 after:h-5 after:w-5 after:rounded-full after:border after:transition-all after:content-[''] peer-checked:after:translate-x-full toggle-control"></div>
      </label>
  );
}
