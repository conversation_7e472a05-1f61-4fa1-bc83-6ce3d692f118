import React, {ButtonHTMLAttributes, DetailedHTMLProps} from 'react';
import {getIconButtonStyles} from './helper';
import SvgGradientLoader from '@components/loaders/svgGradientLoader';

export type IconButtonSize = 'small' | 'medium' | 'large';

export interface IIconButtonProps {
  fab?: boolean;
  accessibilityLabel: string;
  className?: string;
  children?: React.ReactNode;
  size: IconButtonSize;
  loading?: boolean;
}

export default function IconButton(
  props: IIconButtonProps &
    DetailedHTMLProps<
      ButtonHTMLAttributes<HTMLButtonElement>,
      HTMLButtonElement
    >,
) {
  const {
    accessibilityLabel,
    className = '',
    fab = false,
    size,
    children,
    loading,
    disabled,
    ...buttonProps
  } = props;
  const {style, buttonSize, fabShadow} = getIconButtonStyles(fab, size);

  return (
    <button
      disabled={disabled}
      aria-disabled={disabled}
      className={`btn icon-btn-sm ${style} ${buttonSize} ${fabShadow} ${className}`}
      aria-label={accessibilityLabel}
      title={accessibilityLabel}
      {...buttonProps}>
      {loading ?  <SvgGradientLoader className="" /> : children}
    </button>
  );
}
