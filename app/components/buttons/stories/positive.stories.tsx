import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';

import Button from '../button';
import { AddSquare } from 'iconsax-react';

const meta: Meta<typeof Button> = {
  title: 'Button/Positive',
  component: Button,
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Positive: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="positive">
            <AddSquare variant="Bold" />
            positive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" disabled>
            <AddSquare variant="Bold" />
            positive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" loading>
            positive loading
          </Button>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="positive">
            <AddSquare variant="Bold" />
            positive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" disabled>
            <AddSquare variant="Bold" />
            positive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" loading>
            positive loading
          </Button>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="positive">
            <AddSquare variant="Bold" />
            positive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" disabled>
            <AddSquare variant="Bold" />
            positive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" loading>
            positive loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="positive">
            <AddSquare variant="Bold" />
            positive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" disabled>
            <AddSquare variant="Bold" />
            positive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" loading>
            positive loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="positive">
            <AddSquare variant="Bold" />
            positive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" disabled>
            <AddSquare variant="Bold" />
            positive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" loading>
            positive loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="positive">
            <AddSquare variant="Bold" />
            positive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" disabled>
            <AddSquare variant="Bold" />
            positive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" loading>
            positive loading
          </Button>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="positive">
            <AddSquare variant="Bold" />
            positive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" disabled>
            <AddSquare variant="Bold" />
            positive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="positive" loading>
            positive loading
          </Button>
        </div>
      </div>
    </>
  ),
};
