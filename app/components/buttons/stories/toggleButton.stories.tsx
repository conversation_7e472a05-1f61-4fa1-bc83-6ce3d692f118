import React from 'react';
import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import ToggleButton from '../toggleButton';

export default {
  title: 'Button/ToggleButton',
  component: ToggleButton,
  parameters: {
    controls: {expanded: true},
  },
} as Meta;

type Story = StoryObj<typeof ToggleButton>;

export const Toggle: Story = {
  render: args => {
    return (
      <div className="flex gap-4">
        <div data-theme="dark" className="flex">
          <div className="app-bg-subdued p-4">
            <ToggleButton
              {...args}
              handleToggle={() => console.log('clicked')}
            />
          </div>
        </div>
        <div data-theme="light" className="flex">
          <div className="app-bg-subdued p-4">
            <ToggleButton
              {...args}
              handleToggle={() => console.log('clicked')}
            />
          </div>
        </div>
        <div data-theme="yellow" className="flex">
          <div className="app-bg-subdued p-4">
            <ToggleButton
              {...args}
              handleToggle={() => console.log('clicked')}
            />
          </div>
        </div>
        <div data-theme="pale-green" className="flex">
          <div className="app-navbar-bg p-4">
            <ToggleButton
              {...args}
              handleToggle={() => console.log('clicked')}
            />
          </div>
        </div>
        <div data-theme="pale-blue" className="flex">
          <div className="app-navbar-bg p-4">
            <ToggleButton
              {...args}
              handleToggle={() => console.log('clicked')}
            />
          </div>
        </div>
        <div data-theme="pale-pink" className="flex">
          <div className="app-navbar-bg p-4">
            <ToggleButton
              {...args}
              handleToggle={() => console.log('clicked')}
            />
          </div>
        </div>
        <div data-theme="peach" className="flex">
          <div className="app-bg-subdued p-4">
            <ToggleButton
              {...args}
              handleToggle={() => console.log('clicked')}
            />
          </div>
        </div>
      </div>
    );
  },
};

Toggle.args = {
  checked: false || true,
};
