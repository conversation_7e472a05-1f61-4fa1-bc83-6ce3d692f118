import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {ElementPlus} from 'iconsax-react';
import Button from '../button';

const meta: Meta<typeof Button> = {
  title: 'Button/WithIcons',
  component: Button,
};

export default meta;
type Story = StoryObj<typeof Button>;

export const AllDarkTheme: Story = {
  render: () => (
    <div data-theme="dark" className="p-8 bg-slate-900 space-y-8 ">
      <div>
        <Button variant="primary">
          <ElementPlus variant="Bold" />
          Primary <ElementPlus />
        </Button>
      </div>
      <div>
        <Button variant="secondary" className="btn-lg ">
          <ElementPlus variant="Bold" className="w-6 h-6" />
          Secondary lg
        </Button>
      </div>
      <div>
        <Button variant="tertiary" className="btn-md">
          <ElementPlus variant="Bold" className="w-5 h-5" />
          Tertiary md
        </Button>
      </div>
      <div>
        <Button variant="destructive" className="btn-sm">
          Destructive sm
          <ElementPlus variant="Bold" className="w-4 h-4" />
        </Button>
      </div>
      <div>
        <Button variant="positive" className="btn-xs">
          Positive xs
          <ElementPlus variant="Bold" className="w-4 h-4" />
        </Button>
      </div>
    </div>
  ),
};
