import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';

import IconButtonLink from '../iconButtonLink';
import {ElementPlus} from 'iconsax-react';

const meta: Meta<typeof IconButtonLink> = {
  title: 'Links/IconButtonLinks',
  component: IconButtonLink,
};

export default meta;
type Story = StoryObj<typeof IconButtonLink>;

export const IconButtonLinksAllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <IconButtonLink href="/" accessibilityLabel="test" size="medium">
            <ElementPlus />
          </IconButtonLink>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <IconButtonLink href="/" accessibilityLabel="test" size="medium">
            <ElementPlus />
          </IconButtonLink>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <IconButtonLink href="/" accessibilityLabel="test" size="medium">
            <ElementPlus />
          </IconButtonLink>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <IconButtonLink href="/" accessibilityLabel="test" size="medium">
            <ElementPlus />
          </IconButtonLink>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <IconButtonLink href="/" accessibilityLabel="test" size="medium">
            <ElementPlus />
          </IconButtonLink>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <IconButtonLink href="/" accessibilityLabel="test" size="medium">
            <ElementPlus />
          </IconButtonLink>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <IconButtonLink href="/" accessibilityLabel="test" size="medium">
            <ElementPlus />
          </IconButtonLink>
        </div>
      </div>
    </>
  ),
};
