import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';

import Button from '../button';
import { AddSquare } from 'iconsax-react';

const meta: Meta<typeof Button> = {
  title: 'Button/Secondary',
  component: Button,
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Secondary: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="secondary">
            <AddSquare variant="Bold" />
            secondary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" disabled>
            <AddSquare variant="Bold" />
            secondary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" loading>
            secondary loading
          </Button>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="secondary">
            <AddSquare variant="Bold" />
            secondary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" disabled>
            <AddSquare variant="Bold" />
            secondary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" loading>
            secondary loading
          </Button>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="secondary">
            <AddSquare variant="Bold" />
            secondary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" disabled>
            <AddSquare variant="Bold" />
            secondary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" loading>
            secondary loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="secondary">
            <AddSquare variant="Bold" />
            secondary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" disabled>
            <AddSquare variant="Bold" />
            secondary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" loading>
            secondary loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="secondary">
            <AddSquare variant="Bold" />
            secondary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" disabled>
            <AddSquare variant="Bold" />
            secondary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" loading>
            secondary loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="secondary">
            <AddSquare variant="Bold" />
            secondary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" disabled>
            <AddSquare variant="Bold" />
            secondary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" loading>
            secondary loading
          </Button>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="secondary">
            <AddSquare variant="Bold" />
            secondary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" disabled>
            <AddSquare variant="Bold" />
            secondary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="secondary" loading>
            secondary loading
          </Button>
        </div>
      </div>
    </>
  ),
};
