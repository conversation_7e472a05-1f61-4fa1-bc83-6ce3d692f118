import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';

import Button from '../button';

const meta: Meta<typeof Button> = {
  title: 'Button/Sizes',
  component: Button,
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Xsmall: Story = {
  render: () => (
    <Button variant="primary" className="btn-xs">
      Button Xs
    </Button>
  ),
};
export const Small: Story = {
  render: () => (
    <Button variant="primary" className="btn-sm">
      Button Small
    </Button>
  ),
};
export const Medium: Story = {
  render: () => (
    <Button variant="primary" className="btn-md">
      Button Medium
    </Button>
  ),
};
export const Large: Story = {
  render: () => (
    <Button variant="primary" className="btn-lg">
      Button Large
    </Button>
  ),
};
export const Default: Story = {
  render: () => <Button variant="primary">Default (md)</Button>,
};
