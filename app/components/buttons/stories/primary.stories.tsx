import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';

import Button from '../button';
import { AddSquare } from 'iconsax-react';

const meta: Meta<typeof Button> = {
  title: 'Button/Primary',
  component: Button,
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Primary: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex items-center app-bg p-8 space-x-14">
          <Button variant="primary">
            <AddSquare variant="Bold" />
            Primary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" disabled>
            <AddSquare variant="Bold" />
            Primary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" loading>
            Primary Loading
          </Button>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex items-center app-bg p-8 space-x-14">
          <Button variant="primary">
            <AddSquare variant="Bold" />
            Primary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" disabled>
            <AddSquare variant="Bold" />
            Primary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" loading>
            Primary disabled
          </Button>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex items-center app-bg p-8 space-x-14">
          <Button variant="primary">
            <AddSquare variant="Bold" />
            Primary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" disabled>
            <AddSquare variant="Bold" />
            Primary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" loading>
            Primary disabled
          </Button>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex items-center app-bg p-8 space-x-14">
          <Button variant="primary">
            <AddSquare variant="Bold" />
            Primary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" disabled>
            <AddSquare variant="Bold" />
            Primary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" loading>
            Primary disabled
          </Button>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex items-center app-bg p-8 space-x-14">
          <Button variant="primary">
            <AddSquare variant="Bold" />
            Primary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" disabled>
            <AddSquare variant="Bold" />
            Primary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" loading>
            Primary disabled
          </Button>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex items-center app-bg p-8 space-x-14">
          <Button variant="primary">
            <AddSquare variant="Bold" />
            Primary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" disabled>
            <AddSquare variant="Bold" />
            Primary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" loading>
            Primary disabled
          </Button>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex items-center app-bg p-8 space-x-14">
          <Button variant="primary">
            <AddSquare variant="Bold" />
            Primary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" disabled>
            <AddSquare variant="Bold" />
            Primary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="primary" loading>
            Primary disabled
          </Button>
        </div>
      </div>
    </>
  ),
};