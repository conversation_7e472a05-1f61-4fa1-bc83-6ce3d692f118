import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {TableDocument} from 'iconsax-react';
import IconButton from '../iconButton';
import ButtonSpinner from '@components/loaders/buttonSpinner';

const meta: Meta<typeof IconButton> = {
  title: 'Button/IconButton',
  component: IconButton,
};

export default meta;
type Story = StoryObj<typeof IconButton>;

export const IconButtonsAllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <IconButton accessibilityLabel="test button" size="small">
            <TableDocument variant="Bold" className="w-6 h-6" />
          </IconButton>
          <p className="doc-text-default"> Small Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="small" />

          <p className="doc-text-default">Medium icon button</p>
          <IconButton accessibilityLabel="test button" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </IconButton>
          <p className="doc-text-default"> Medium Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="medium" />
          <p className="doc-text-default">Large icon button</p>
          <IconButton accessibilityLabel="test button" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
          <p className="doc-text-default"> Large Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="large" />
          <p className="doc-text-default">Disabled icon button</p>
          <IconButton accessibilityLabel="test button" disabled size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <IconButton accessibilityLabel="test button" size="small">
            <TableDocument variant="Bold" className="w-6 h-6" />
          </IconButton>
          <p className="doc-text-default"> Small Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="small" />
          <p className="doc-text-default">Medium icon button</p>
          <IconButton accessibilityLabel="test button" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </IconButton>
          <p className="doc-text-default"> Medium Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="medium" />
          <p className="doc-text-default">Large icon button</p>
          <IconButton accessibilityLabel="test button" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
          <p className="doc-text-default"> Large Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="large" />
          <p className="doc-text-default">Disabled icon button</p>
          <IconButton accessibilityLabel="test button" disabled size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <IconButton accessibilityLabel="test button" size="small">
            <TableDocument variant="Bold" className="w-6 h-6" />
          </IconButton>
          <p className="doc-text-default"> Small Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="small" />
          <p className="doc-text-default">Medium icon button</p>
          <IconButton accessibilityLabel="test button" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </IconButton>
          <p className="doc-text-default"> Medium Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="medium" />
          <p className="doc-text-default">Large icon button</p>
          <IconButton accessibilityLabel="test button" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
          <p className="doc-text-default"> Large Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="large" />
          <p className="doc-text-default">Disabled icon button</p>
          <IconButton accessibilityLabel="test button" disabled size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <IconButton accessibilityLabel="test button" size="small">
            <TableDocument variant="Bold" className="w-6 h-6" />
          </IconButton>
          <p className="doc-text-default"> Small Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="small" />
          <p className="doc-text-default">Medium icon button</p>
          <IconButton accessibilityLabel="test button" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </IconButton>
          <p className="doc-text-default"> Medium Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="medium" />
          <p className="doc-text-default">Large icon button</p>
          <IconButton accessibilityLabel="test button" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
          <p className="doc-text-default"> Large Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="large" />
          <p className="doc-text-default">Disabled icon button</p>
          <IconButton accessibilityLabel="test button" disabled size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <IconButton accessibilityLabel="test button" size="small">
            <TableDocument variant="Bold" className="w-6 h-6" />
          </IconButton>
          <p className="doc-text-default"> Small Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="small" />
          <p className="doc-text-default">Medium icon button</p>
          <IconButton accessibilityLabel="test button" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </IconButton>
          <p className="doc-text-default"> Medium Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="medium" />
          <p className="doc-text-default">Large icon button</p>
          <IconButton accessibilityLabel="test button" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
          <p className="doc-text-default"> Large Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="large" />
          <p className="doc-text-default">Disabled icon button</p>
          <IconButton accessibilityLabel="test button" disabled size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <IconButton accessibilityLabel="test button" size="small">
            <TableDocument variant="Bold" className="w-6 h-6" />
          </IconButton>
          <p className="doc-text-default"> Small Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="small" />
          <p className="doc-text-default">Medium icon button</p>
          <IconButton accessibilityLabel="test button" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </IconButton>
          <p className="doc-text-default"> Medium Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="medium" />
          <p className="doc-text-default">Large icon button</p>
          <IconButton accessibilityLabel="test button" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
          <p className="doc-text-default"> Large Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="large" />
          <p className="doc-text-default">Disabled icon button</p>
          <IconButton accessibilityLabel="test button" disabled size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <IconButton accessibilityLabel="test button" size="small">
            <TableDocument variant="Bold" className="w-6 h-6" />
          </IconButton>
          <p className="doc-text-default"> Small Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="small" />
          <p className="doc-text-default">Medium icon button</p>
          <IconButton accessibilityLabel="test button" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </IconButton>
          <p className="doc-text-default"> Medium Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="medium" />

          <p className="doc-text-default">Large icon button</p>
          <IconButton accessibilityLabel="test button" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
          <p className="doc-text-default"> Large Loading icon button</p>
          <IconButton loading accessibilityLabel="test button" size="large" />
          <p className="doc-text-default">Disabled icon button</p>
          <IconButton accessibilityLabel="test button" disabled size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </IconButton>
        </div>
      </div>
    </>
  ),
};
