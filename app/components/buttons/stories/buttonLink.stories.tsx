import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';

import ButtonLink from '../buttonLink';
import {AddSquare} from 'iconsax-react';

const meta: Meta<typeof ButtonLink> = {
  title: 'Links/Links',
  component: ButtonLink,
};

export default meta;
type Story = StoryObj<typeof ButtonLink>;

export const LinksAllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary">
            Secondary
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary">
            Tertiary
          </ButtonLink>
          <ButtonLink href="/" variant="destructive">
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive">
            Positive
          </ButtonLink>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary">
            Secondary
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary">
            Tertiary
          </ButtonLink>
          <ButtonLink href="/" variant="destructive">
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive">
            Positive
          </ButtonLink>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary">
            Secondary
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary">
            Tertiary
          </ButtonLink>
          <ButtonLink href="/" variant="destructive">
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive">
            Positive
          </ButtonLink>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary">
            Secondary
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary">
            Tertiary
          </ButtonLink>
          <ButtonLink href="/" variant="destructive">
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive">
            Positive
          </ButtonLink>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary">
            Secondary
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary">
            Tertiary
          </ButtonLink>
          <ButtonLink href="/" variant="destructive">
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive">
            Positive
          </ButtonLink>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary">
            Secondary
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary">
            Tertiary
          </ButtonLink>
          <ButtonLink href="/" variant="destructive">
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive">
            Positive
          </ButtonLink>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary">
            Secondary
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary">
            Tertiary
          </ButtonLink>
          <ButtonLink href="/" variant="destructive">
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive">
            Positive
          </ButtonLink>
        </div>
      </div>
    </>
  ),
};
export const LinksWithIconsAllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
    </>
  ),
};
export const LinksSizesAllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <ButtonLink href="/" variant="primary">
            <AddSquare variant="Bold" className="w-6 h-6" />
            Primary
          </ButtonLink>
          <ButtonLink href="/" variant="secondary" className="btn-lg">
            Secondary
            <AddSquare variant="Bold" className="w-5 h-5" />
          </ButtonLink>
          <ButtonLink href="/" variant="tertiary" className="btn-md">
            <AddSquare variant="Bold" className="w-4 h-4" />
            Tertiary
            <AddSquare variant="Bold" className="w-4 h-4" />
          </ButtonLink>
          <ButtonLink href="/" variant="destructive" className="btn-sm">
            <AddSquare className="w-4 h-4" />
            Destructive
          </ButtonLink>
          <ButtonLink href="/" variant="positive" className="btn-xs">
            Positive
            <AddSquare className="w-4 h-4" />
          </ButtonLink>
        </div>
      </div>
    </>
  ),
};
