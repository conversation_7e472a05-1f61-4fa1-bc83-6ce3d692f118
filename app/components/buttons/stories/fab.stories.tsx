import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {TableDocument} from 'iconsax-react';
import Fab from '../fab';

const meta: Meta<typeof Fab> = {
  title: 'Button/Fab',
  component: Fab,
};

export default meta;
type Story = StoryObj<typeof Fab>;

export const FabAllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-14 space-x-14">
          <Fab accessibilityLabel="test button" size="small" position="top-12 left-10">
            <TableDocument variant='Bold' className="w-4 h-4" />
          </Fab>
          <Fab accessibilityLabel="test button" size="medium" position="top-12 left-16">
            <TableDocument variant='Bold' className="w-5 h-5" />
          </Fab>
          <Fab accessibilityLabel="test button" size="large" position="top-10 left-36">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
          <Fab accessibilityLabel="test button" disabled size="large" position="top-10 left-60">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-14 space-x-14">
          <Fab accessibilityLabel="test button" size="small" position="top-40 left-10">
            <TableDocument variant='Bold' className="w-4 h-4" />
          </Fab>
          <Fab accessibilityLabel="test button" size="medium" position="top-40 left-16">
            <TableDocument variant='Bold' className="w-5 h-5" />
          </Fab>
          <Fab accessibilityLabel="test button" size="large" position="top-36 left-36">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
          <Fab accessibilityLabel="test button" disabled size="large" position="top-36 left-60">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-14 space-x-14">
          <Fab accessibilityLabel="test button" size="small" position="top-64 left-10" className="mt-2">
            <TableDocument variant='Bold' className="w-4 h-4" />
          </Fab>
          <Fab accessibilityLabel="test button" size="medium" position="top-64 left-16" className="mt-2">
            <TableDocument variant='Bold' className="w-5 h-5" />
          </Fab>
          <Fab accessibilityLabel="test button" size="large" position="top-64 left-36" className="mt-2">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
          <Fab accessibilityLabel="test button" disabled size="large" position="top-64 left-60" className="mt-2">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-14 space-x-14">
          <Fab accessibilityLabel="test button" size="small" position="top-96 left-10">
            <TableDocument variant='Bold' className="w-4 h-4" />
          </Fab>
          <Fab accessibilityLabel="test button" size="medium" position="top-96 left-16">
            <TableDocument variant='Bold' className="w-5 h-5" />
          </Fab>
          <Fab accessibilityLabel="test button" size="large" position="top-96 left-36">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
          <Fab accessibilityLabel="test button" disabled size="large" position="top-96 left-60">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-14 space-x-14">
          <Fab accessibilityLabel="test button" size="small" position="top-96 left-10" className="mt-28">
            <TableDocument variant='Bold' className="w-4 h-4" />
          </Fab>
          <Fab accessibilityLabel="test button" size="medium" position="top-96 left-16" className="mt-28">
            <TableDocument variant='Bold' className="w-5 h-5" />
          </Fab>
          <Fab accessibilityLabel="test button" size="large" position="top-96 left-36" className="mt-28">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
          <Fab accessibilityLabel="test button" disabled size="large" position="top-96 left-60" className="mt-28">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-14 space-x-14">
          <Fab accessibilityLabel="test button" size="small" position="top-128 left-10" className="-mt-6">
            <TableDocument variant='Bold' className="w-4 h-4" />
          </Fab>
          <Fab accessibilityLabel="test button" size="medium" position="top-128 left-16" className="-mt-6">
            <TableDocument variant='Bold' className="w-5 h-5" />
          </Fab>
          <Fab accessibilityLabel="test button" size="large" position="top-128 left-36" className="-mt-8">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
          <Fab accessibilityLabel="test button" disabled size="large" position="top-128 left-60" className="-mt-8">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-14 space-x-14">
          <Fab accessibilityLabel="test button" size="small" position="top-136 left-10" className="-mt-6">
            <TableDocument variant='Bold' className="w-4 h-4" />
          </Fab>
          <Fab accessibilityLabel="test button" size="medium" position="top-136 left-16" className="-mt-6">
            <TableDocument variant='Bold' className="w-5 h-5" />
          </Fab>
          <Fab accessibilityLabel="test button" size="large" position="top-136 left-36" className="-mt-8">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
          <Fab accessibilityLabel="test button" disabled size="large" position="top-136 left-60" className="-mt-8">
            <TableDocument variant='Bold' className="w-8 h-8" />
          </Fab>
        </div>
      </div>
    </>
  ),
};
