import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';

import Button from '../button';
import { AddSquare } from 'iconsax-react';

const meta: Meta<typeof Button> = {
  title: 'Button/Destructive',
  component: Button,
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Destructive: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="destructive">
            <AddSquare variant="Bold" />
            destructive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" disabled>
            <AddSquare variant="Bold" />
            destructive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" loading>
            destructive loading
          </Button>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="destructive">
            <AddSquare variant="Bold" />
            destructive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" disabled>
            <AddSquare variant="Bold" />
            destructive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" loading>
            destructive loading
          </Button>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="destructive">
            <AddSquare variant="Bold" />
            destructive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" disabled>
            <AddSquare variant="Bold" />
            destructive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" loading>
            destructive loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="destructive">
            <AddSquare variant="Bold" />
            destructive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" disabled>
            <AddSquare variant="Bold" />
            destructive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" loading>
            destructive loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="destructive">
            <AddSquare variant="Bold" />
            destructive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" disabled>
            <AddSquare variant="Bold" />
            destructive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" loading>
            destructive loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="destructive">
            <AddSquare variant="Bold" />
            destructive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" disabled>
            <AddSquare variant="Bold" />
            destructive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" loading>
            destructive loading
          </Button>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="destructive">
            <AddSquare variant="Bold" />
            destructive
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" disabled>
            <AddSquare variant="Bold" />
            destructive disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="destructive" loading>
            destructive loading
          </Button>
        </div>
      </div>
    </>
  ),
};
