import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';

import Button from '../button';

const meta: Meta<typeof Button> = {
  title: 'Button/All',
  component: Button,
};

export default meta;
type Story = StoryObj<typeof Button>;

export const AllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="tertiary">Tertiary</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="positive">Positive</Button>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="tertiary">Tertiary</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="positive">Positive</Button>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="tertiary">Tertiary</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="positive">Positive</Button>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="tertiary">Tertiary</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="positive">Positive</Button>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="tertiary">Tertiary</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="positive">Positive</Button>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="tertiary">Tertiary</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="positive">Positive</Button>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="tertiary">Tertiary</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="positive">Positive</Button>
        </div>
      </div>
    </>
  ),
};
