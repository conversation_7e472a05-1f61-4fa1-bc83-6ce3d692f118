import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';

import Button from '../button';
import { AddSquare } from 'iconsax-react';

const meta: Meta<typeof Button> = {
  title: 'Button/Tertiary',
  component: Button,
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Tertiary: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="tertiary">
            <AddSquare variant="Bold" />
            tertiary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" disabled>
            <AddSquare variant="Bold" />
            tertiary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" loading>
            tertiary loading
          </Button>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="tertiary">
            <AddSquare variant="Bold" />
            tertiary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" disabled>
            <AddSquare variant="Bold" />
            tertiary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" loading>
            tertiary loading
          </Button>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="tertiary">
            <AddSquare variant="Bold" />
            tertiary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" disabled>
            <AddSquare variant="Bold" />
            tertiary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" loading>
            tertiary loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="tertiary">
            <AddSquare variant="Bold" />
            tertiary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" disabled>
            <AddSquare variant="Bold" />
            tertiary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" loading>
            tertiary loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="tertiary">
            <AddSquare variant="Bold" />
            tertiary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" disabled>
            <AddSquare variant="Bold" />
            tertiary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" loading>
            tertiary loading
          </Button>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="tertiary">
            <AddSquare variant="Bold" />
            tertiary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" disabled>
            <AddSquare variant="Bold" />
            tertiary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" loading>
            tertiary loading
          </Button>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <Button variant="tertiary">
            <AddSquare variant="Bold" />
            tertiary
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" disabled>
            <AddSquare variant="Bold" />
            tertiary disabled
            <AddSquare variant="Bold" />
          </Button>
          <Button variant="tertiary" loading>
            tertiary loading
          </Button>
        </div>
      </div>
    </>
  ),
};
