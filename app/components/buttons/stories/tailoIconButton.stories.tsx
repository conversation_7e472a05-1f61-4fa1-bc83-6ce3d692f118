import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {TableDocument} from 'iconsax-react';
import TailoIconButton from '../tailoIconButton';

const meta: Meta<typeof TailoIconButton> = {
  title: 'Button/IconButton',
  component: TailoIconButton,
};

export default meta;
type Story = StoryObj<typeof TailoIconButton>;

export const TailoIconButtonsAllThemes: Story = {
  render: () => (
    <>
      <div data-theme="dark">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <TailoIconButton label="" size="small">
            <TableDocument variant="Bold" />
          </TailoIconButton>

          <p className="doc-text-default">Medium icon button</p>
          <TailoIconButton label="" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </TailoIconButton>

          <p className="doc-text-default">Large icon button</p>
          <TailoIconButton label="" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </TailoIconButton>
        </div>
      </div>
      <div data-theme="light">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <TailoIconButton label="" size="small">
            <TableDocument variant="Bold" />
          </TailoIconButton>

          <p className="doc-text-default">Medium icon button</p>
          <TailoIconButton label="" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </TailoIconButton>

          <p className="doc-text-default">Large icon button</p>
          <TailoIconButton label="" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </TailoIconButton>
        </div>
      </div>
      <div data-theme="yellow">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <TailoIconButton label="" size="small">
            <TableDocument variant="Bold" />
          </TailoIconButton>

          <p className="doc-text-default">Medium icon button</p>
          <TailoIconButton label="" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </TailoIconButton>

          <p className="doc-text-default">Large icon button</p>
          <TailoIconButton label="" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </TailoIconButton>
        </div>
      </div>
      <div data-theme="pale-green">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <TailoIconButton label="" size="small">
            <TableDocument variant="Bold" />
          </TailoIconButton>

          <p className="doc-text-default">Medium icon button</p>
          <TailoIconButton label="" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </TailoIconButton>

          <p className="doc-text-default">Large icon button</p>
          <TailoIconButton label="" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </TailoIconButton>
        </div>
      </div>
      <div data-theme="pale-blue">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <TailoIconButton label="" size="small">
            <TableDocument variant="Bold" />
          </TailoIconButton>

          <p className="doc-text-default">Medium icon button</p>
          <TailoIconButton label="" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </TailoIconButton>

          <p className="doc-text-default">Large icon button</p>
          <TailoIconButton label="" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </TailoIconButton>
        </div>
      </div>
      <div data-theme="pale-pink">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <TailoIconButton label="" size="small">
            <TableDocument variant="Bold" />
          </TailoIconButton>

          <p className="doc-text-default">Medium icon button</p>
          <TailoIconButton label="" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </TailoIconButton>

          <p className="doc-text-default">Large icon button</p>
          <TailoIconButton label="" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </TailoIconButton>
        </div>
      </div>
      <div data-theme="peach">
        <div className="flex app-bg p-8 space-x-14">
          <p className="doc-text-default">Small icon button</p>
          <TailoIconButton label="" size="small">
            <TableDocument variant="Bold" />
          </TailoIconButton>

          <p className="doc-text-default">Medium icon button</p>
          <TailoIconButton label="" size="medium">
            <TableDocument variant="Bold" className="w-8 h-8" />
          </TailoIconButton>

          <p className="doc-text-default">Large icon button</p>
          <TailoIconButton label="" size="large">
            <TableDocument variant="Bold" className="w-10 h-10" />
          </TailoIconButton>
        </div>
      </div>
    </>
  ),
};
