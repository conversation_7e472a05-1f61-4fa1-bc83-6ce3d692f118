import React from 'react';
import Link, {LinkProps} from 'next/link';
import {getButtonVariantStyle} from './helper';
import {IButtonProps} from './button';

export interface ILinkProps {
  href: string;
  hasIcon?: boolean;
  onClick?: () => void;
  disabled?: boolean;
}
export interface IButtonLinkProps extends ILinkProps, IButtonProps {}

const ButtonLink = (props: IButtonLinkProps & LinkProps) => {
  const {
    href,
    hasIcon = false,
    variant,
    className = '',
    onClick,
    children,
    disabled,
    ...linkProps
  } = props;
  const themeStyle = getButtonVariantStyle(variant);
  if (hasIcon) {
    return (
      <Link
        aria-disabled={disabled}
        tabIndex={disabled ? -1 : 0}
        role="link" // needed when using the aria-disabled attribute
        href={href}
        legacyBehavior
        passHref
        {...linkProps}
        data-disabled={disabled}>
        <a
          aria-disabled={disabled}
          role="link" // needed when using the aria-disabled attribute
          href={href}
          {...linkProps}
          data-disabled={disabled} // tailwind doesn't work with disabled attribute on <a> tags as they aren't a native attribute of this element
          onClick={onClick}
          className={`btn ${themeStyle} ${className} ${
            disabled ? 'pointer-events-none' : ''
          }`}>
          {children}
        </a>
      </Link>
    );
  }
  return (
    <Link
      href={href}
      aria-disabled={disabled}
      tabIndex={disabled ? -1 : 0}
      {...linkProps}
      data-disabled={disabled} // tailwind doesn't work with disabled attribute on <a> tags as they aren't a native attribute of this element
      onClick={onClick}
      className={`btn ${themeStyle} ${className} ${
        disabled ? 'pointer-events-none' : ''
      }`}>
      {children}
    </Link>
  );
};

export default ButtonLink;
