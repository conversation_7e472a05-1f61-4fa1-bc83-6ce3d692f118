import React from 'react';
import {getIconButtonStyles} from './helper';
import {IIconButtonProps} from './iconButton';
import Link, {LinkProps} from 'next/link';
import {ILinkProps} from './buttonLink';

export type IconButtonSize = 'small' | 'medium' | 'large';

export interface IIconButtonLinkProps
  extends Omit<ILinkProps, 'hasIcon'>,
    IIconButtonProps {}

export default function IconButtonLink(
  props: IIconButtonLinkProps & LinkProps,
) {
  const {
    href,
    accessibilityLabel,
    className = '',
    fab = false,
    size,
    onClick,
    children,
    ...linkProps
  } = props;
  const {style, buttonSize, fabShadow} = getIconButtonStyles(fab, size);

  return (
    <Link href={href} legacyBehavior passHref {...linkProps}>
      <a
        href={href}
        onClick={onClick}
        aria-label={accessibilityLabel}
        title={accessibilityLabel}
        className={`btn p-1 ${style} ${buttonSize} ${fabShadow} ${className}`}
        {...linkProps}>
        {children}
      </a>
    </Link>
  );
}
