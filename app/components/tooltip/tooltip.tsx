import React from 'react';

interface ITooltipProps {
  value: number | string;
  coords?: number;
}

export default function Tooltip(props: ITooltipProps) {
  const {value, coords} = props;

  return (
    <div
      className="w-auto h-5 p-2 rounded-sm flex flex-grow items-center justify-center absolute tool-tip z-max"
      style={coords !== undefined ? {left: `${coords - 5}%`, bottom: 40} : {}}>
      <h1 className="text-sm">
        {typeof value === 'number' ? `${value}x` : value}
      </h1>
    </div>
  );
}
