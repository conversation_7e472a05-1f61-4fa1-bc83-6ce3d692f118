import {useState, useRef} from 'react';

export const useToolTip = () => {
  const [active, setActive] = useState(false);
  const [newStep, setNewStep] = useState(0);
  const [coords, setCoords] = useState(0);
  const timer = useRef<number>(0);

  const onChange = (value: any) => {
    setNewStep(() => value);
  };

  const onChangeComplete = () => {
    clearTimeout(timer.current);
    const left =
      document.querySelector<HTMLElement>('.rc-slider-handle')?.style.left;

    setCoords(() => parseInt(left || '0', 10));
    setActive(true);

    timer.current = window.setTimeout(() => {
      setActive(false);
    }, 1000);
  };

  return {active, newStep, coords, onChange, onChangeComplete};
};
