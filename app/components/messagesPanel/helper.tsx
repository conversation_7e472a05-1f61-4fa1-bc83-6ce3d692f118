import {Danger, InfoCircle, TickCircle} from 'iconsax-react';
import {ReactNode} from 'react';

export function getMessageItemStyle(type: string) {
  let theme = '';
  let icon: ReactNode = null;

  switch (type) {
    case 'success':
      theme = 'message-bg-positive';
      icon = <TickCircle className="h-5 w-5" variant="Bold" />;
      break;
    case 'danger':
      theme = 'message-bg-negative';
      icon = <Danger className="h-5 w-5" variant="Bold" />;
      break;
    case 'warning':
      theme = 'message-bg-info';
      icon = <InfoCircle className="h-5 w-5" variant="Bold" />;
      break;
  }

  return {theme, icon};
}
