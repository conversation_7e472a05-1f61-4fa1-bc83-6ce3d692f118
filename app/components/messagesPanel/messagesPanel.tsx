import {getMessageItemStyle} from './helper';
import {CloseCircle} from 'iconsax-react';
import TailoIconButton from '@components/buttons/tailoIconButton';

interface MessagePanel {
  type: 'success' | 'warning' | 'danger';
  messageTitle: string;
  message: string | JSX.Element;
  ariaLive?: 'polite' | 'assertive';
  hideCloseButton?: boolean;
  onClose?: () => void;
}

function MessagesPanel({
  type,
  messageTitle,
  message,
  ariaLive = 'polite',
  hideCloseButton = false,
  onClose,
}: MessagePanel) {
  const msgItem = getMessageItemStyle(type);
  return (
    <div
      className={`w-full py-4 pl-4 rounded-lg panel my-2 justify-start items-start inline-flex ${msgItem.theme}`}>
      <div className="flex-col justify-start gap-2.5 flex">{msgItem.icon}</div>
      <div aria-live={ariaLive} className="flex flex-1 flex-row pl-2 pr-5">
        <div className=" text-base font-bold leading-tight mx-1">
          {messageTitle}
          <div className="text-base font-normal leading-normal my-2">
            {message}
          </div>
        </div>
      </div>
      {/* TODO: Replace with button similar to delete modal when implemented */}
      {!hideCloseButton && (
        <TailoIconButton handler={onClose} size="small" label="Close">
          <CloseCircle className="h-6 w-6" variant="Linear" />
        </TailoIconButton>
      )}
    </div>
  );
}

export default MessagesPanel;
