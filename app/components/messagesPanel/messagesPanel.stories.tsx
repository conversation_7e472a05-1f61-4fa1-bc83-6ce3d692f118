import type {Meta, StoryObj} from '@storybook/react';
import MessagesPanel from './messagesPanel';

const meta: Meta<typeof MessagesPanel> = {
  title: 'Containers/MessagesPanel',
  component: MessagesPanel,
  parameters: {
    controls: {expanded: true},
  },
};

export default meta;
type Story = StoryObj<typeof MessagesPanel>;

const defaultArgs = {
  hideCloseButton: false,
  message:
    'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum similique veniam.',
  messageTitle: ' Message title',
};

export const DarkMessagesPanelStory: Story = {
  render: args => {
    return (
      <div data-theme="dark">
        <div className="app-bg p-4 ">
          <MessagesPanel {...args} type="success" />
          <MessagesPanel {...args} type="danger" />
          <MessagesPanel {...args} type="warning" />
        </div>
      </div>
    );
  },
};

DarkMessagesPanelStory.args = defaultArgs;

export const LightMessagesPanelStory: Story = {
  render: args => {
    return (
      <div data-theme="light">
        <div className="app-bg p-4 ">
          <MessagesPanel {...args} type="success" />
          <MessagesPanel {...args} type="danger" />
          <MessagesPanel {...args} type="warning" />
        </div>
      </div>
    );
  },
};

LightMessagesPanelStory.args = defaultArgs;

export const HighContrastYellowMessagesPanelStory: Story = {
  render: args => {
    return (
      <div data-theme="yellow">
        <div className="app-bg p-4 ">
          <MessagesPanel {...args} type="success" />
          <MessagesPanel {...args} type="danger" />
          <MessagesPanel {...args} type="warning" />
        </div>
      </div>
    );
  },
};

HighContrastYellowMessagesPanelStory.args = defaultArgs;

export const PaleBlueMessagesPanelStory: Story = {
  render: args => {
    return (
      <div data-theme="pale-blue">
        <div className="app-bg p-4 ">
          <MessagesPanel {...args} type="success" />
          <MessagesPanel {...args} type="danger" />
          <MessagesPanel {...args} type="warning" />
        </div>
      </div>
    );
  },
};

PaleBlueMessagesPanelStory.args = defaultArgs;

export const PaleGreenMessagesPanelStory: Story = {
  render: args => {
    return (
      <div data-theme="pale-green">
        <div className="app-bg p-4 ">
          <MessagesPanel {...args} type="success" />
          <MessagesPanel {...args} type="danger" />
          <MessagesPanel {...args} type="warning" />
        </div>
      </div>
    );
  },
};

PaleGreenMessagesPanelStory.args = defaultArgs;

export const PalePinkMessagesPanelStory: Story = {
  render: args => {
    return (
      <div data-theme="pale-pink">
        <div className="app-bg p-4 ">
          <MessagesPanel {...args} type="success" />
          <MessagesPanel {...args} type="danger" />
          <MessagesPanel {...args} type="warning" />
        </div>
      </div>
    );
  },
};

PalePinkMessagesPanelStory.args = defaultArgs;

export const PeachMessagesPanelStory: Story = {
  render: args => {
    return (
      <div data-theme="peach">
        <div className="app-bg p-4 ">
          <MessagesPanel {...args} type="success" />
          <MessagesPanel {...args} type="danger" />
          <MessagesPanel {...args} type="warning" />
        </div>
      </div>
    );
  },
};

PeachMessagesPanelStory.args = defaultArgs;
