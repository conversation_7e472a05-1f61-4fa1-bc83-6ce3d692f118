import {Magicpen} from 'iconsax-react';

export interface AIPanelProps {
  id?: string;
  label: string;
  className?: string;
  showText?: boolean;
  handler?: () => void;
}

export default function AIPanel({
  id,
  label,
  className,
  showText = true,
  handler,
}: AIPanelProps) {
  return (
    <button className={`h-12 ${className}`} onClick={handler} key={id}>
      <div
        className={`${
          handler ? ' btn-secondary ' : ''
        }ai-gradient-border border-sm mb-2 h-full w-full`}>
        <div
          className={`h-full rounded-md flex gap-[6px] items-center justify-center 
          ${className} ${showText ? 'px-2' : 'p-4'} ai-notice-container`}>
          <Magicpen variant="Bold" className="w-4 h-4" />
          {showText && <div className="text-base font-semibold ">{label}</div>}
        </div>
      </div>
    </button>
  );
}
