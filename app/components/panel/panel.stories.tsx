import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import Panel from './Panel';

const meta: Meta<typeof Panel> = {
  title: 'Containers/Panel',
  component: Panel,
};

export default meta;
type Story = StoryObj<typeof Panel>;

export const PanelStory: Story = {
  render: () => (
    <>
      <div data-theme="dark" className="pb-4">
        <Panel>
          <p className="p-4 doc-text-default">Test panel</p>
        </Panel>
      </div>
      <div data-theme="black-on-white" className="pb-4">
        <Panel>
          <p className="p-4 doc-text-default">Test panel</p>
        </Panel>
      </div>
      <div data-theme="yellow">
        <Panel>
          <p className="p-4 doc-text-default">Test panel</p>
        </Panel>
      </div>
    </>
  ),
};
