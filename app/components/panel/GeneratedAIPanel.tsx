import {useContext} from 'react';
import AIPanel from './AIPanel';
import ExpandIcon from '@components/customIcons/expandIcon';
import CollapseIcon from '@components/customIcons/collapseIcon';
import TailoIconButton from '@components/buttons/tailoIconButton';
import {useSectionSummariesAnalytics} from '@libs/services/search/useSectionSummariesAnalytics';
import SvgGradientLoader from '@components/loaders/svgGradientLoader';
import {SectionSummariesContext} from '@contexts/sectionSummariesContext';

interface GeneratedAIPanelProps {
  id: string;
  sectionSummary?: string[];
  isLoading: boolean;
}

export default function GeneratedAIPanel({
  id,
  isLoading = false,
  sectionSummary,
}: GeneratedAIPanelProps) {
  const {collapsedStates, toggleCollapsedState} = useContext(
    SectionSummariesContext,
  );
  const {
    trackCollapseSectionSummary,
    trackExpandSectionSummary,
    trackLearnMoreSummary,
  } = useSectionSummariesAnalytics();

  const isCollapsed = collapsedStates[id] ?? false; // Default to open

  const toggleSummary = () => {
    toggleCollapsedState(id);
    isCollapsed ? trackExpandSectionSummary() : trackCollapseSectionSummary();
  };

  return (
    <div className="ai-gradient-border rounded-lg mt-4 mb-5">
      <div className="ai-notice-container rounded-lg p-2 lg:p-6">
        <div className="flex justify-between items-center flex-wrap-reverse">
          <h3 className="!py-0 font-bold grow">Summary</h3>
          <span className="flex gap-3 items-center grow justify-end">
            <AIPanel label="AI-generated" className="p-2 pointer-events-none" />
            <TailoIconButton
              label="Toggle section summary"
              size="small"
              className=""
              handler={toggleSummary}>
              {isCollapsed ? (
                <ExpandIcon className="w-4 h-4 svg-default" />
              ) : (
                <CollapseIcon className="w-4 h-4 svg-default" />
              )}
            </TailoIconButton>
          </span>
        </div>
        {isLoading ? (
          <span className="flex flex-row items-center self-center">
            <SvgGradientLoader size={30} />
            <span className="p-2 italic">Generating Summary</span>
          </span>
        ) : !isCollapsed && sectionSummary ? (
          <ul className="pb-4">
            {sectionSummary.map((summary, index) => (
              <li
                key={index}
                className="pt-2"
                dangerouslySetInnerHTML={{
                  __html: summary.replace(
                    /\*\*(.*?)\*\*/g,
                    '<strong>$1</strong>',
                  ),
                }}
              />
            ))}
          </ul>
        ) : null}
        <span className="text-sm doc-text-subdued pt-4">
          Generated by AI, based on the contents of this section. AI can make
          mistakes.{` `}
          <a
            href="https://tailoapp.com/2024/09/03/embracing-the-future-of-reading-with-tailo/"
            target="_blank"
            className="ai-underline"
            onClick={trackLearnMoreSummary}>
            Learn More
          </a>
        </span>
      </div>
    </div>
  );
}
