'use client';
import {useState} from 'react';
import {IUseDeleteDocument} from './types';
import documentClientServices from '@libs/services/document/documentClinetServices';
import useDocuments from '@libs/services/document/useDocuments';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';

export default function useDeleteDocument(
  id: string,
  closeModal: () => void,
): IUseDeleteDocument {
  const {getDocumentById} = useDocuments();
  const {trackDeleteDocument} = useDocumentAnalytics();
  const document = getDocumentById(id);
  const [disabled, setDisabled] = useState(false);

  const handleDelete = async () => {
    closeModal();
    setDisabled(true);
    documentClientServices.deleteDocument(id).finally(() => {
      if (document) {
        trackDeleteDocument(document);
      }
      setDisabled(false);
    });
  };

  return {
    handleDelete,
    documentName: document?.name || '',
    disabled,
  };
}
