'use client';

import Button from '@components/buttons/button';
import BaseModal from '@components/modals/BaseModal';
import useDeleteDocument from './useDeleteDocument';
import {Trash, Warning2} from 'iconsax-react';
import ModalButtonsContainer from '../ModalButtonsContainer';

const DeleteModal = ({
  id,
  isOpen,
  closeModal,
}: {
  id: string;
  isOpen: boolean;
  closeModal: () => void;
}) => {
  const {handleDelete, documentName, disabled} = useDeleteDocument(
    id,
    closeModal,
  );
  return (
    <BaseModal
      isOpen={isOpen}
      onClose={closeModal}
      accessibilityLabel="Confirm document deletion">
      <div className="flex-col">
        <div className="text-center flex justify-center items-center pb-1">
          <Warning2 variant="Bulk" className={`w-12 h-12 theme-warning`} />
        </div>
        <h3 className=" text-center pb-4 text-2xl font-bold leading-relaxed">
          Confirm document deletion
        </h3>
        <p className="mx-4 text-center pb-4 text-xl font-normal leading-loose">
          {documentName}
        </p>
        <hr className="theme-border-t pb-4 -mx-4" />
        <ModalButtonsContainer>
          <Button
            type="submit"
            variant="secondary"
            onClick={closeModal}
            className="btn-full">
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={disabled}
            className="btn-full">
            <Trash variant="Bold" /> Delete document
          </Button>
        </ModalButtonsContainer>
      </div>
    </BaseModal>
  );
};

export default DeleteModal;
