'use client';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import {FC, ReactNode} from 'react';
import Drawer from 'react-bottom-drawer';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';

export interface BottomSheetProps {
  drawerId: DRAWER_ID;
  onClose?: () => void;
  accessibilityLabel?: string;
  children?: ReactNode;
  fullHeight?: boolean; // Take up the full height of the screen and remove the drag handle
  hideBackdrop?: boolean; // Hides the overlay backdrop when the drawer is open
}

const BottomSheet: FC<BottomSheetProps> = ({
  drawerId,
  onClose,
  accessibilityLabel = '',
  children,
  fullHeight = false,
  hideBackdrop = false,
}) => {
  const {drawStateById, closeDrawer} = useDrawer();
  const isVisible = drawStateById(drawerId) === 'open';
  const handleClose = () => (onClose && onClose()) || closeDrawer(drawerId);
  // The library's classes are dynamically generated based on the drawerClass value
  // See the base.css for actual styling
  const drawerClass = fullHeight ? 'custom-full-drawer' : 'custom-drawer';
  const backdropStyling = hideBackdrop ? 'remove-custom-drawer__backdrop' : '';
  const contentPadding = fullHeight ? '' : 'p-4' + ' pb-8';
  const {isLargeTablet} = useIsMobile();

  if (isLargeTablet)
    return (
      <Drawer
        className={`${backdropStyling} ${drawerClass}`}
        isVisible={isVisible}
        onClose={handleClose}>
        <div
          className="custom-drawer__handle" // This makes sure the drag handle keeps its colour as it lost it with the changes above
        />
        <div aria-label={accessibilityLabel} className={contentPadding}>
          {children}
        </div>
      </Drawer>
    );
};

export default BottomSheet;
