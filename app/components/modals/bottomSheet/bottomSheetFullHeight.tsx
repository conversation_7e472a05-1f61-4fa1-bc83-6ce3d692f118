import React from 'react';
import BottomSheet, {BottomSheetProps} from './bottomSheet';
import Button from '@components/buttons/button';
import useDrawer from '@libs/store/drawer';

export interface IBottomSheetFullHeightProps
  extends Omit<BottomSheetProps, 'fullHeight'> {
  header: string;
  onClose?: () => void;
  closeButtonLabel?: string;
}

export default function BottomSheetFullHeight(
  props: IBottomSheetFullHeightProps,
) {
  const {
    drawerId,
    children,
    header,
    onClose,
    accessibilityLabel,
    closeButtonLabel = 'DONE',
  } = props;
  const {closeDrawer} = useDrawer();

  const closeMenu = () => {
    closeDrawer(drawerId);
    if (onClose) onClose();
  };
  return (
    <BottomSheet
      accessibilityLabel={accessibilityLabel}
      drawerId={drawerId}
      fullHeight>
      <div className="absolute flex flex-row left-0 w-screen h-14 py-3 bottom-sheet-top-nav justify-center items-center z-max">
        <h2 className="font-bold">{header}</h2>
        <Button
          variant="tertiary"
          size="sm"
          className="absolute right-0 underline font-semibold"
          onClick={closeMenu}>
          {closeButtonLabel}
        </Button>
      </div>
      <div className="overflow-auto pt-16">{children}</div>
    </BottomSheet>
  );
}
