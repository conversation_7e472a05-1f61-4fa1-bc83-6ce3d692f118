import React from 'react';
import {Meta, StoryObj} from '@storybook/react';
import BottomSheet from '@components/modals/bottomSheet/bottomSheet';

const meta: Meta = {
  title: 'Modals/BottomSheet',
  component: BottomSheet,
  parameters: {
    controls: {expanded: true},
  },
};
export default meta;

type Story = StoryObj<typeof BottomSheet>;

export const BottomSheetStory: Story = {
  render: args => {
    return (
      <div data-theme="dark">
        <BottomSheet {...args}>
          <div>
            <h1 className="text-white">TEST contents</h1>
          </div>
        </BottomSheet>
      </div>
    );
  },
};

BottomSheetStory.args = {};
