import {ThemeColour} from '@libs/store/settings/types';

export const modalOverlayColour = (theme: string, opacity: number = 0.8) => {
  let overlayColour = '';
  switch (theme) {
    case ThemeColour.Dark:
      overlayColour = `rgba(2, 6, 23, ${opacity})`;
      break;
    case ThemeColour.Light:
      overlayColour = `rgba(2, 6, 23, ${opacity})`;
      break;

    case ThemeColour.Yellow:
      overlayColour = `rgba(0, 0, 0, ${opacity})`;
      break;

    case ThemeColour.PaleGreen:
      overlayColour = `rgba(26, 46, 5, ${opacity})`;
      break;

    case ThemeColour.PaleBlue:
      overlayColour = `rgba(23, 37, 84, ${opacity})`;
      break;

    case ThemeColour.Peach:
      overlayColour = `rgba(27, 26, 24, ${opacity})`;
      break;

    case ThemeColour.PalePink:
      overlayColour = `rgba(76, 5, 25, ${opacity})`;
      break;

    default:
      overlayColour = `rgba(0, 0, 0, ${opacity})`;
      break;
  }

  return overlayColour;
};
