import GenerateSummaryButton from '@components/buttons/generateSummaryButton';
import {useStoreSummaryForm} from '@libs/store/summarys/useStoreSummaryForm';
import React from 'react';

const CustomPromptForm = ({
  onSummarySubmitted,
}: {
  onSummarySubmitted: () => void;
}) => {
  const {customPrompt, updateCustomPrompt} = useStoreSummaryForm();

  // This runs every time there is a change in the text area, not sure if we may need to eventually limit this but seems to be okay for now!
  const updatePromptStore = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateCustomPrompt(event.currentTarget.value);
  };

  return (
    <div
      id="tab-1"
      aria-labelledby="tab-1"
      className=" w-full lg:w-[500px] m-auto">
      <label className="label font-semibold leading-normal">
        <span className="label-text doc-text-default text-left">
          Write your own prompt below
        </span>
      </label>
      <textarea
        className="textarea textarea-bordered w-full bg-slate-700 border-slate-50 text-slate-50 min-h-full h-60"
        value={customPrompt}
        onChange={updatePromptStore}
      />
      <GenerateSummaryButton
        onSummarySubmitted={onSummarySubmitted}
        promptType="custom"
      />
    </div>
  );
};

export default CustomPromptForm;
