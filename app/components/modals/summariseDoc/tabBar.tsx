import React, {<PERSON>} from 'react';
import TabButton from './tabButton';
import {PromptViewWindow} from './types';

interface TabBarProps {
  activeView: PromptViewWindow;
  updateView: (view: PromptViewWindow) => void;
}

const TabBar: FC<TabBarProps> = ({activeView, updateView}) => {
  const toggleActiveView = () => {
    if (activeView === PromptViewWindow.PROMPT_BUILDER) {
      updateView(PromptViewWindow.CUSTOM_PROMPT);
    } else {
      updateView(PromptViewWindow.PROMPT_BUILDER);
    }
  };

  return (
    <div className="flex flex-row sticky top-0 z-10 pb-4 items-center justify-center">
      <TabButton
        label="Prompt Builder"
        active={activeView === PromptViewWindow.PROMPT_BUILDER}
        onPress={toggleActiveView}
        tabId="tab-1"
      />
      <TabButton
        label="Create your own"
        active={activeView === PromptViewWindow.CUSTOM_PROMPT}
        onPress={toggleActiveView}
        tabId="tab-2"
      />
    </div>
  );
};

export default TabBar;
