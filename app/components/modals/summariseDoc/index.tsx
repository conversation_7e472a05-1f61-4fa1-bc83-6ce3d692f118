'use client';

import {PromptViewWindow} from '@components/modals/summariseDoc/types';
import BaseModal, {ModalProps} from '@components/modals/BaseModal';
import React, {useState} from 'react';
import PromptBuilder from './promptBuilder/promptBuilder';
import ModalHeader from './modalHeader';
import CustomPromptForm from './customPropmt/customPromptForm';
import {defaultSettingsString} from '@libs/store/settings/values';

const SummariseDocumentModal = (props: Omit<ModalProps, 'children'>) => {
  const [activeView, setActiveView] = useState<PromptViewWindow>(
    PromptViewWindow.PROMPT_BUILDER,
  );

  const updateActiveView = (view: PromptViewWindow) => {
    setActiveView(view);
  };

  const closeModal = () => props.onClose();

  const getView = () => {
    switch (activeView) {
      case PromptViewWindow.CUSTOM_PROMPT:
        return <CustomPromptForm onSummarySubmitted={closeModal} />;
      case PromptViewWindow.PROMPT_BUILDER:
      default:
        return (
          <div
            className="justify-center flex"
            id="tab-2"
            aria-labelledby="tab-2">
            <PromptBuilder onSummarySubmitted={closeModal} />
          </div>
        );
    }
  };

  return (
    <BaseModal
      {...props}
      width="w-full h-full"
      customTheme={defaultSettingsString}>
      <ModalHeader
        closeModal={closeModal}
        activeView={activeView}
        updateView={updateActiveView}
      />
      <div className="w-full h-full overflow-scroll">{getView()}</div>
    </BaseModal>
  );
};

export default SummariseDocumentModal;
