import React, { <PERSON> } from 'react'

interface TabButtonProps {
  label: string;
  active: boolean;
  onPress: () => void;
  tabId: string;
}

const TabButton: FC<TabButtonProps> = ({label, active, onPress, tabId}) => {
  return (
    <button 
      id={tabId}
      className={`px-4 pb-4 text-slate-50 text-xl font-normal leading-tight ${active ? 'border-b-2 border-bottom-opposite' : ''}`}
      onClick={onPress}
      role='tab'
      aria-selected={active ? true : false}
      aria-controls={tabId}
      tabIndex={active ? -1 : 0}> {/* Setting Tab Index to -1 when selected so its removed from the tab sequence */}
      <h3>{label}</h3>
    </button>
  )
}

export default TabButton
