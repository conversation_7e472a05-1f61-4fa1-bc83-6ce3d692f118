'use client';
import {useStoreSummaryForm} from '@libs/store/summarys/useStoreSummaryForm';
import React, {FC} from 'react';
import PromptInput from './promptInput';

interface SomeoneElseFlowProps {
  onInputBlur: () => void;
}

// This will show if user selects 'Someone Else' on the first set of radio buttons
const SomeoneElseFlow: FC<SomeoneElseFlowProps> = ({onInputBlur}) => {
  const {formData, updateForm} = useStoreSummaryForm();

  return (
    <div className="pt-4">
      <PromptInput
        onChange={e => {
          updateForm({whoFor: e.currentTarget.value});
        }}
        onBlur={onInputBlur}
        value={formData.whoFor}
        label="Who is the summary for?"
        id={'summaryFor'}
        helpText="E.g. My elderly father, an engineering manager"
      />
    </div>
  );
};

export default SomeoneElseFlow;
