'use client';

import {useStoreSummaryForm} from '@libs/store/summarys/useStoreSummaryForm';
import PromptRadioInput from './promptRadioInput';
import PromptInput from './promptInput';
import {IPromptFormData} from '@libs/store/summarys/types';
import {FC} from 'react';

interface ForMeFlowProps {
  onRadioSelect: (data: Partial<IPromptFormData>) => void;
  onInputBlur: () => void;
}

/**
 * when user selects ForMe:true, these fields show
 */
const ForMeFlow: FC<ForMeFlowProps> = ({onRadioSelect, onInputBlur}) => {
  const {formData, updateForm} = useStoreSummaryForm();

  return (
    <div className="pt-6">
      <p className="text-base font-semibold">
        Are you primarily a student or working?
      </p>
      <PromptRadioInput
        id="student-true"
        label="I am a student"
        checked={formData.student === true}
        onSelect={() => {
          onRadioSelect({student: true});
        }}
      />
      <PromptRadioInput
        id="student-false"
        label="I am working"
        checked={formData.student === false && formData.student !== undefined}
        onSelect={() => {
          onRadioSelect({student: false, field: ''});
        }}
      />
      {formData.student && (
        <PromptInput
          onChange={e => {
            updateForm({field: e.currentTarget.value});
          }}
          onBlur={onInputBlur}
          value={formData.field}
          label={'What are you studying?'}
          id={'studying'}
        />
      )}
      {formData.student === false && (
        <PromptInput
          onChange={e => {
            updateForm({field: e.currentTarget.value});
          }}
          onBlur={onInputBlur}
          value={formData.field}
          label={'What is your job field?'}
          id={'job-title'}
        />
      )}
    </div>
  );
};

export default ForMeFlow;
