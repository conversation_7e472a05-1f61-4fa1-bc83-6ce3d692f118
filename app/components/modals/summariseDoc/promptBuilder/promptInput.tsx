import {ChangeEvent, FC, HTMLInputTypeAttribute} from 'react';

interface PromptInputProps {
  label: string;
  helpText?: string;
  value: string | number;
  id: string;
  onBlur: () => void;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  type?: HTMLInputTypeAttribute;
  className?: string;
  suffix?: string | React.ReactNode;
}

const PromptInput: FC<PromptInputProps> = ({
  label,
  helpText,
  value,
  id,
  onBlur,
  onChange,
  type = 'text',
  className = '',
  suffix,
}) => {
  const controlStyle = `ring-4 focus:ring-4 focus:ring-tailo-green-500  border-2 dropdown-input block w-full h-12 px-4 rounded-lg justify-center items-center text-base font-normal leading-tight outline-none ring-4 ring-transparent`;

  return (
    <div className="form-control w-full">
      <label htmlFor={id} className="label">
        <span className="label-text text-base font-semibold">{label}</span>
      </label>
      <span className="relative">
        <input
          type={type}
          placeholder="Type here"
          className={`input w-full ${controlStyle} ${className}`}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          id={id}
          name={id}
          aria-describedby={helpText ? `help-text-${id}` : undefined}
          min="0"
          step="1"
        />
        {suffix && (
          <span className="absolute right-3 top-0 h-full z-10 items-center flex px-2">
            {suffix}
          </span>
        )}
      </span>
      {helpText && (
        <label id={`help-text-${id}`} className="label">
          <span className="label-text-alt">{helpText}</span>
        </label>
      )}
    </div>
  );
};

export default PromptInput;
