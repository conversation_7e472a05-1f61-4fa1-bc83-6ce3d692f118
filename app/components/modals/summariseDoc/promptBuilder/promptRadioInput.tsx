import React, { <PERSON> } from 'react'

interface PromptRadioInputProps {
    id: string;
    label: string;
    checked: boolean;
    onSelect: () => void;
}

const PromptRadioInput: FC<PromptRadioInputProps> = ({id, label, checked, onSelect}) => {
  return (
    <div className="flex flex-row p-4 my-2 rounded-lg border">
        <input type="radio" id={id} name={id} className="radio border-2 bg-slate-700 border-slate-500 checked:border-4 checked:border-slate-50 checked:bg-slate-950" checked={checked} onChange={onSelect} />
        <label htmlFor={id} className="pl-4 text-slate-50 text-base font-semibold leading-normal">{label}</label>
    </div>
  )
}

export default PromptRadioInput
