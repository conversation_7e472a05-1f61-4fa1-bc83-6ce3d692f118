'use client';
import PromptViewer from './promptViewer';
import PromptBuilderForm from './promptBuilderForm';
import {FC} from 'react';
import GenerateSummaryButton from '@components/buttons/generateSummaryButton';

interface PromptBuilderProps {
  onSummarySubmitted: () => void;
}

const PromptBuilder: FC<PromptBuilderProps> = ({onSummarySubmitted}) => {
  return (
    <div className="w-full h-full text-slate-50">
      <div className="lg:flex gap-8 pt-4">
        <div className="lg:flex-1 lg:flex justify-end w-full">
          <div className="xl:w-1/2 w-full">
            <PromptViewer />
            <GenerateSummaryButton
              className="hidden lg:flex"
              onSummarySubmitted={onSummarySubmitted}
              promptType="builder"
            />
          </div>
        </div>

        <div className="w-full lg:flex-1 lg:flex pb-8 lg:h-[calc(100vh-5.75rem)] lg:overflow-scroll">
          <div className="xl:w-1/2 w-full mb-3">
            <PromptBuilderForm />
            <GenerateSummaryButton
              className="mb-5 lg:hidden"
              onSummarySubmitted={onSummarySubmitted}
              promptType="builder"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptBuilder;
