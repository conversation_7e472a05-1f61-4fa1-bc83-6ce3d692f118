import {useStoreSummaryForm} from '@libs/store/summarys/useStoreSummaryForm';
import React from 'react';

const PromptViewer = () => {
  const {prompt} = useStoreSummaryForm();
  const promptSections = Object.values(prompt);

  return (
    <div data-theme="dark" className="border border-slate-50 p-4 rounded-lg">
      <p className="text-base">
        {promptSections.map((section, index) => {
          return (
            <span key={index.toString()}>
              {section.map((segment, index) => {
                let elem = <></>;
                if (segment.type === 'argument') {
                  elem = (
                    <span className="underline text-base">{segment.text}</span>
                  );
                } else {
                  elem = <>{segment.text}</>;
                }
                return (
                  <React.Fragment key={index.toString()}>{elem}</React.Fragment>
                );
              })}
            </span>
          );
        })}
      </p>
    </div>
  );
};

export default PromptViewer;
