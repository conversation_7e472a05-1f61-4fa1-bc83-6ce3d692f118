'use client';
import DropdownSelect from '@components/forms/dropdown/dropdownSelect';
import PromptDivider from './promptDivider';
import PromptInput from './promptInput';
import {useStoreSummaryForm} from '@libs/store/summarys/useStoreSummaryForm';
import PromptRadioInput from './promptRadioInput';
import ForMeFlow from './forMeFlow';
import {IPromptFormData} from '@libs/store/summarys/types';
import SomeoneElseFlow from './someoneElseFlow';

const PromptBuilderForm = () => {
  const {formData, updateForm, submitNewFormPrompt} = useStoreSummaryForm();

  const handleDropdownSelect = (option: string) => {
    updateForm({
      specificSummary: option,
    });
    submitNewFormPrompt();
  };

  const handleRadioSelect = (data: Partial<IPromptFormData>) => {
    updateForm(data);
    submitNewFormPrompt();
  };

  const handleInputBlur = () => {
    submitNewFormPrompt();
  };

  return (
    <div>
      <form>
        <PromptInput
          onChange={e => {
            updateForm({tryingToDo: e.currentTarget.value});
          }}
          onBlur={handleInputBlur}
          value={formData.tryingToDo}
          label="What are you trying to do?"
          id={'tryingToDo'}
          helpText="E.g. I don't have time to read it all"
        />
        <PromptDivider />
        <div className="pt-2">
          <p className="text-base font-semibold">
            Is this for you or someone else?
          </p>
          <PromptRadioInput
            id="its-for-me"
            label="It's for me"
            checked={formData.forMe === true}
            onSelect={() => {
              handleRadioSelect({
                forMe: true,
                student: undefined,
                field: '',
                learningDifferences: '',
                whoFor: '',
              });
            }}
          />
          <PromptRadioInput
            id="someone-else"
            label="Someone Else"
            checked={formData.forMe === false}
            onSelect={() => {
              handleRadioSelect({
                forMe: false,
                student: undefined,
                field: '',
                learningDifferences: '',
                whoFor: '',
              });
            }}
          />
        </div>
        {formData.forMe && (
          <ForMeFlow
            onRadioSelect={handleRadioSelect}
            onInputBlur={handleInputBlur}
          />
        )}
        {formData.forMe === false && (
          <SomeoneElseFlow onInputBlur={handleInputBlur} />
        )}
        {formData.forMe !== undefined && (
          <div className="pt-4">
            <PromptInput
              onChange={e => {
                updateForm({learningDifferences: e.currentTarget.value});
              }}
              onBlur={handleInputBlur}
              value={formData.learningDifferences}
              label={
                formData.forMe
                  ? 'Do you have any learning differences?'
                  : 'Do they have any learning differences?'
              }
              id={'learning-difference'}
              helpText="E.g. Dyslexia, ADHD, a reading age of 12"
            />
          </div>
        )}

        <PromptDivider />
        <div className="pb-4">
          <p className="text-base font-semibold">
            What format would you prefer?
          </p>
          <PromptRadioInput
            id="bullet-points"
            label="Bullet Points"
            checked={formData.bulletPoints === true}
            onSelect={() => {
              handleRadioSelect({bulletPoints: true});
            }}
          />
          <PromptRadioInput
            id="paragraphs"
            label={'Paragraphs'}
            checked={formData.bulletPoints === false}
            onSelect={() => {
              handleRadioSelect({bulletPoints: false});
            }}
          />
        </div>
        <PromptInput
          onChange={e => {
            updateForm({focusingOn: e.currentTarget.value});
          }}
          onBlur={handleInputBlur}
          value={formData.focusingOn}
          label="Are you focusing on anything"
          id={'focusingOn'}
          helpText="E.g. Particular theme, keyword or question"
        />
        <PromptDivider />
        <div className="form-control w-full pb-4">
          <label htmlFor={'specificSummary'} className="label">
            <span className="label-text text-base font-semibold">
              Do you want a specific summary?
            </span>
          </label>
          <DropdownSelect
            id={'specificSummary'}
            defaultOption=""
            onSelect={handleDropdownSelect}
            options={[
              '',
              'abstractive',
              'executive',
              'descriptive',
              'extractive',
              'other',
            ]}
          />
        </div>
        <PromptInput
          onChange={e => {
            updateForm({tone: e.currentTarget.value});
          }}
          onBlur={handleInputBlur}
          value={formData.tone}
          label="Do you want a certain tone?"
          id={'tone'}
          helpText="E.g. Particular theme, keyword or question"
        />
        <div className="pt-4">
          <PromptInput
            onChange={e => {
              updateForm({
                //TODO better logic needs implementing for decimals and '-'
                wordLimit: Number(e.currentTarget.value.replace(/\D/g, '')),
              });
            }}
            onBlur={handleInputBlur}
            value={formData.wordLimit === 0 ? '' : formData.wordLimit}
            label="Do you want an exact word limit?"
            id={'wordLimit'}
            helpText="E.g. 200"
            type="number"
            className="pr-24 textfield"
            suffix="WORDS"
          />
        </div>
      </form>
    </div>
  );
};

export default PromptBuilderForm;
