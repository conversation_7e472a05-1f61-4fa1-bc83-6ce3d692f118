import TailoIconButton from '@components/buttons/tailoIconButton';
import TabBar from '@components/modals/summariseDoc/tabBar';
import {PromptViewWindow} from '@components/modals/summariseDoc/types';
import {CloseCircle} from 'iconsax-react';
import React, {FC} from 'react';

interface ModalHeaderProps {
  closeModal: () => void;
  activeView: PromptViewWindow;
  updateView: (view: PromptViewWindow) => void;
}

const ModalHeader: FC<ModalHeaderProps> = ({
  closeModal,
  activeView,
  updateView,
}) => {
  return (
    <div>
      <TabBar updateView={updateView} activeView={activeView} />
      <TailoIconButton handler={closeModal} label="Close" size="small">
        <CloseCircle variant="Bold" className="w-10 h-10 md:w-8 md:h-8" />
      </TailoIconButton>
    </div>
  );
};

export default ModalHeader;
