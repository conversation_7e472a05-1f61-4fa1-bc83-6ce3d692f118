import React from 'react';
import BaseModal from '../BaseModal';
import Lottie from 'lottie-react';
import Woohoo from 'public/static/animations/woohoo.json';
import Button from '@components/buttons/button';
import useReducedAnimation from '@libs/utils/animation/useReducedAnimation';
import useNewUserRegistration from './useNewUserRegistration';

const NewUserModal = () => {
  const {reducedMotionEnabled} = useReducedAnimation();
  const {newUser, closeModal} = useNewUserRegistration();

  return (
    <BaseModal
      isOpen={newUser}
      onClose={closeModal}
      width="w-11/12 sm:w-96 p-6 flex flex-col"
      accessibilityLabel="Welcome to Tailo!">
      <h1 className="text-center doc-text-default text-2xl font-bold">
        Welcome to Tailo!
      </h1>
      <Lottie
        autoplay={!reducedMotionEnabled}
        animationData={Woohoo}
        loop={2}
        onComplete={closeModal}
        className="w-64 h-64 self-center"
        role="img"
        initialSegment={reducedMotionEnabled ? [15, 15] : undefined}
        aria-label="An animation of a confetti cannon"
      />
      <Button variant="primary" onClick={closeModal} className="w-full btn-md">
        Jump in
      </Button>
    </BaseModal>
  );
};

export default NewUserModal;
