'use client';
import { AppPrivateRoutes } from '@libs/services/auth/routes';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react'

function useNewUserRegistration() {

  const searchParams = useSearchParams()
  const router = useRouter();

  const [newUser, setNewUser] = useState(false);

  useEffect(() => {
    if (searchParams.has('new')) {
      setNewUser(true);
    }
  }, [searchParams]);

  const closeModal = () => {
    setNewUser(false);
    router.replace(AppPrivateRoutes.Dashboard);
  }

  return {newUser, closeModal}
}

export default useNewUserRegistration;
