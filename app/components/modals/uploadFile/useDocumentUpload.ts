import {
  DocUploadView,
  MAX_FILE_SIZE,
  UploadSource,
} from '@components/modals/uploadFile/types';
import {ChangeEvent} from 'react';
import useUploadDocument from '@libs/store/uploadDocument';
import {mutateDocumentsOnDocumentUpload} from '@libs/services/document/useDocuments';
import documentClientServices from '@libs/services/document/documentClinetServices';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';
export default function useDocumentUpload() {
  const {
    uploadProgress,
    updateUploadProgress,
    uploadError,
    updateUploadError,
    file,
    updateFile,
    activeView,
    updateActiveView,
    isOpen,
    updateIsOpen,
    fileUploadSource,
    updateFileUploadSource,
  } = useUploadDocument();

  const {trackUploadDocument} = useDocumentAnalytics();

  const openModal = () => updateIsOpen(true);

  const closeModal = () => {
    updateIsOpen(false);
    updateUploadError(false);
    updateFile(null);
    updateActiveView(DocUploadView.SELECT_DOC);
    updateUploadProgress(0);
  };

  const uploadToClient = async (
    event?: ChangeEvent<HTMLInputElement>,
    file?: File,
  ) => {
    if (file) {
      updateFile(file);
      updateFileUploadSource(UploadSource.DRAG_AND_DROP);
      updateActiveView(DocUploadView.FILE_SELECTED);
    } else if (
      event &&
      event.target.files &&
      event.target.files[0] &&
      event.target.files[0].size < MAX_FILE_SIZE
    ) {
      const i = event.target.files[0];
      updateFile(i);
      updateFileUploadSource(UploadSource.DOCUMENT_EXPLORER);
      updateActiveView(DocUploadView.FILE_SELECTED);
    }
  };

  const storeFile = async (placeholder?: boolean) => {
    if (!file) return;
    updateActiveView(DocUploadView.FILE_UPLOAD);
    updateUploadProgress(0);

    const uploadData = await documentClientServices.getDocumentUploadData(file);

    if (uploadData) {
      await documentClientServices
        .storeUploadedDocument({
          uploadData,
          file,
          onProgress: updateUploadProgress,
          onError: updateUploadError,
        })
        .then(() => {
          trackUploadDocument(file.size, file.type, fileUploadSource);
        });
      if (placeholder) {
        updateActiveView(DocUploadView.SELECT_DOC);
      }
    }
  };

  const removeSelectedFile = async () => {
    if (!file) return;
    updateFile(null);
    updateActiveView(DocUploadView.SELECT_DOC);
  };

  return {
    uploadToClient,
    removeSelectedFile,
    storeFile,
    closeModal,
    activeView,
    file,
    isOpen,
    openModal,
    uploadError,
    uploadProgress,
  };
}
