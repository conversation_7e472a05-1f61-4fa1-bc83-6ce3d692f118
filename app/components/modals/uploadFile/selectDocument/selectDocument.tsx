import React from 'react';
import DropZone from '../dropZone';
import Button from '@components/buttons/button';
import useDocumentUpload from '../useDocumentUpload';

const SelectDocument = ({
  placeholder,
  isStorybook,
}: {
  placeholder?: boolean;
  isStorybook?: boolean;
}) => {
  const {closeModal, uploadToClient} = useDocumentUpload();

  return (
    <>
      <div className="flex-col text-xl font-bold doc-text-default">
        <h3>{placeholder ? '' : 'Select document'}</h3>
      </div>
      <DropZone uploadToClient={uploadToClient} isStorybook={isStorybook} />
      {placeholder ? null : (
        <div className="flex-col">
          <Button
            variant="secondary"
            onClick={closeModal}
            className={'btn-full'}>
            Cancel
          </Button>
        </div>
      )}
    </>
  );
};

export default SelectDocument;
