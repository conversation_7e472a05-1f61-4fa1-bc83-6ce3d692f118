import type {Meta, StoryObj} from '@storybook/react';
import DashboardPlaceholder from '@components/dashboard/placeholder/dashboardPlaceholder';
import FileSelected from './fileSelected/fileSelected';
import FileUploaded from './fileUploaded/fileUploaded';
import SelectDocument from './selectDocument/selectDocument';

const meta: Meta<typeof DashboardPlaceholder> = {
  title: 'Document Upload/DashboardPlaceholder',
  component: DashboardPlaceholder,
  parameters: {
    controls: {expanded: true},
  },
};

export default meta;
type Story = StoryObj<typeof DashboardPlaceholder>;

export const DashboardPlaceholderStory: Story = {
  render: () => {
    return (
      <>
        <div data-theme="dark">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="light">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="yellow">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="pale-green">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="pale-pink">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="pale-blue">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="peach">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={true} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
      </>
    );
  },
};
export const UploadModalStory: Story = {
  render: () => {
    return (
      <>
        <div data-theme="dark">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="light">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="yellow">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="pale-green">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="pale-pink">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="pale-blue">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
        <div data-theme="peach">
          <div className='flex items-center app-bg p-8 space-x-14'>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <SelectDocument placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileSelected placeholder={false} isStorybook />
            </div>
            <div className="modal-bg p-4 w-1/3 rounded-lg shadow-2xl-d">
              <FileUploaded isStorybook />
            </div>
          </div>
        </div>
      </>
    );
  },
};
