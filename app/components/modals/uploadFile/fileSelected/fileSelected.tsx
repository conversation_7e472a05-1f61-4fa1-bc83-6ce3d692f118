import React from 'react';
import Button from '@components/buttons/button';
import FileSelectedPanel from './fileSelectedPanel';
import useDocumentUpload from '../useDocumentUpload';

const FileSelected = ({
  placeholder,
  isStorybook = false,
}: {
  placeholder?: boolean;
  isStorybook?: boolean;
}) => {
  const {storeFile, closeModal, file, removeSelectedFile} = useDocumentUpload();

  const processUpload = () => {
    if (isStorybook) {
      return;
    }
    storeFile(placeholder);
  };

  return (
    <>
      <div className="flex-col text-xl font-bold">
        <h3>Upload Document</h3>
      </div>
      <div className="py-4">
        <FileSelectedPanel
          selectedFile={file}
          removeSelectedFile={removeSelectedFile}
          isStorybook={isStorybook}
        />
      </div>
      <div className="flex-col">
        <Button
          variant="primary"
          onClick={processUpload}
          className={'btn-full mb-2'}>
          Upload
        </Button>
        <Button
          variant="secondary"
          onClick={closeModal}
          className={'btn-full'}>
          Cancel
        </Button>
      </div>
    </>
  );
};

export default FileSelected;
