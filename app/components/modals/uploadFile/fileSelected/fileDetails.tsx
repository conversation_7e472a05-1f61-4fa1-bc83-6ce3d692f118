import React, { <PERSON> } from 'react'

interface FileDetailsProps {
    file: File | null;
    isStorybook?: boolean;
}

const FileDetails: FC<FileDetailsProps> = ({file, isStorybook = false}) => {
  return (
    <div className='flex-col'>
        <p className="text-sm font-normal doc-text-default">{file ? file.name : isStorybook ? 'file-name.pdf' : ''}</p>
        <p className="text-[10px] font-semibold uppercase doc-text-subdued">{`${file ? (file.size / 1024 / 1000).toFixed(1) : isStorybook ? '2.6' : ''} MB`}</p>
    </div>
  )
}

export default FileDetails
