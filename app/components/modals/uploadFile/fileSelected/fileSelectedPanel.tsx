import React, {FC} from 'react';
import {Trash} from 'iconsax-react';
import FileDetails from './fileDetails';
import {Document} from 'iconsax-react';
import TailoIconButton from '@components/buttons/tailoIconButton';

interface FileSelectedPanelProps {
  selectedFile: File | null;
  removeSelectedFile?: () => void;
  uploading?: boolean;
  isStorybook?: boolean;
}

const FileSelectedPanel: FC<FileSelectedPanelProps> = ({
  selectedFile,
  removeSelectedFile,
  uploading,
  isStorybook = false,
}) => {
  return (
    <div className="flex flex-row items-center justify-between border default-progress-bar rounded-lg p-4">
      <div className="flex flex-row items-center z-20">
        <Document
          variant="Bold"
          className="w-8 h-8 mr-4 p-2 border rounded-md default-progress-bar default-progress-bar-icon"
        />
        <FileDetails file={selectedFile} isStorybook={isStorybook} />
      </div>
      {!uploading && (
        <TailoIconButton
          handler={removeSelectedFile}
          size="small"
          label="Remove selected file">
          <Trash variant="Bold" />
        </TailoIconButton>
      )}
    </div>
  );
};

export default FileSelectedPanel;
