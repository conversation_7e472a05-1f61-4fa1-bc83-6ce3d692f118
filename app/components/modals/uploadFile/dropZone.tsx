'use client';
import React, {ChangeEvent, FC, useRef, useState} from 'react';
import {MAX_FILE_SIZE} from './types';
import {AiOutlinePlus} from 'react-icons/ai';

interface DropZoneProps {
  uploadToClient: (event?: ChangeEvent<HTMLInputElement>, file?: File) => void;
  isStorybook?: boolean;
}

const DropZone: FC<DropZoneProps> = ({uploadToClient, isStorybook = false}) => {
  const inputFile = useRef<HTMLInputElement | null>(null);
  const [dragActive, setDragActive] = useState(false);
  
  const acceptedMimeTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  const handleDrop = (e: React.DragEvent<HTMLFormElement>) => {
    if (isStorybook) {
      return;
    }
    e.preventDefault();
    setDragActive(false);
    if (
      e.dataTransfer.files &&
      e.dataTransfer.files[0] &&
      e.dataTransfer.files[0].size < MAX_FILE_SIZE
    ) {
      uploadToClient(undefined, e.dataTransfer.files[0]);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLFormElement>) => {
    if (isStorybook) {
      return;
    }
    e.preventDefault();
    setDragActive(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLFormElement>) => {
    if (isStorybook) {
      return;
    }
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragEnter = (e: React.DragEvent<HTMLFormElement>) => {
    if (isStorybook) {
      return;
    }
    e.preventDefault();
    setDragActive(true);
  };

  const onAddButtonPressed = () => {
    if (isStorybook) {
      return;
    }
    if (inputFile.current) {
      inputFile.current.click();
    }
  };

  const preventDefault = (e: React.DragEvent<HTMLFormElement>) => {
    if (isStorybook) {
      return;
    }
    e.preventDefault();
  };

  return (
    <div onClick={onAddButtonPressed}>
      <form
        tabIndex={0}
        className={`${
          dragActive ? 'upload-area-drag' : ''
        } flex flex-col p-8 my-4 w-full border items-center rounded-md text-center cursor-pointer panel-bg upload-area`}
        onDragEnter={handleDragEnter}
        onSubmit={preventDefault}
        onDrop={handleDrop}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}>
        <input
          id="file"
          type="file"
          ref={inputFile}
          accept={acceptedMimeTypes.join(',')}
          onChange={uploadToClient}
          className="hidden"
        />
        <AiOutlinePlus className="w-16 h-16 mb-4 border-2 opposite-border rounded-2xl p-3 doc-text-default" />
        <div className="flex-col">
          <h3 className="doc-text-default text-base font-semibold leading-normal">
            <span className="hidden xl:block">
              Drop your file here or click/tap to browse
            </span>
            <span className="xl:hidden">Tap to add file</span>
          </h3>

          <span className="doc-text-subdued text-[10px] font-semibold uppercase leading-none tracking-widest">
            Max File Size 20MB. PDF and DOCX Only
          </span>
        </div>
      </form>
    </div>
  );
};

export default DropZone;
