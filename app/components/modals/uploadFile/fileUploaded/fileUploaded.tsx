import Button from '@components/buttons/button';
import React from 'react';
import FileSelectedPanel from '../fileSelected/fileSelectedPanel';
import useDocumentUpload from '../useDocumentUpload';

const FileUploaded = ({isStorybook = false}: {isStorybook?: boolean}) => {
  const {uploadProgress, uploadError, file, closeModal} = useDocumentUpload();

  return (
    <>
      <div className="flex-col text-xl font-bold doc-text-default">
        {uploadProgress < 100 ? (
          <h3>Uploading...</h3>
        ) : (
          <h3>Upload complete</h3>
        )}
      </div>

      <div className="rounded-lg overflow-hidden relative my-4">
        <div
          className="absolute h-full top-0 left-0 default-progress-bar-upload"
          style={{
            width: `${isStorybook ? '70' : uploadProgress}%`,
          }}
        />
        <FileSelectedPanel
          selectedFile={file}
          uploading={true}
          isStorybook={isStorybook}
        />
      </div>
      <Button
        variant={uploadProgress < 100 && !uploadError ? 'secondary' : 'primary'}
        onClick={closeModal}
        className={'btn-full'}>
        {uploadProgress < 100 && !uploadError ? 'Cancel' : 'Close'}
      </Button>

      {uploadError && (
        <p className="p-3 mt-2 bg-red-600 rounded-md text-white">
          {'There has been an error uploading'}
        </p>
      )}
    </>
  );
};

export default FileUploaded;
