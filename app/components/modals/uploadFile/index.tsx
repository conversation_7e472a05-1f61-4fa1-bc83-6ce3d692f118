'use client';

import BaseModal from '@components/modals/BaseModal';
import useDocumentUpload from './useDocumentUpload';
import UploadView from '@components/dashboard/placeholder/uploadView';

const UploadModal = () => {
  const {closeModal, isOpen} = useDocumentUpload();

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={closeModal}
      bgColour="modal-bg"
      accessibilityLabel="Upload file">
      <UploadView placeholder={false} />
    </BaseModal>
  );
};

export default UploadModal;
