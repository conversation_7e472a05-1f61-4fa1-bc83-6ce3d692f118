'use client';
import Button from '@components/buttons/button';
import BaseModal from '@components/modals/BaseModal';
import {Warning2} from 'iconsax-react';
import ModalButtonsContainer from '../../ModalButtonsContainer';
import useReadAloud, {SpeakAloudState} from '@libs/store/speakAloud';
import {useGlobalAudioPlayer} from 'react-use-audio-player';

const SpeakAloudCloseModal = ({
  isOpen,
  closeModal,
}: {
  isOpen: boolean;
  closeModal: () => void;
}) => {
  const {updateSpeakAloudState,clearCurrentParagraph} = useReadAloud();
  const {stop} = useGlobalAudioPlayer();

  const handleClose = () => {
    clearCurrentParagraph();
    updateSpeakAloudState(SpeakAloudState.OFF);
    stop();
    closeModal();
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={closeModal}
      accessibilityLabel="Quit read aloud?">
      <div className="flex-col">
        <div className="text-center flex justify-center items-center pb-1">
          <Warning2 variant="Bulk" className={`w-12 h-12`} />
        </div>
        <h3 className=" text-center pb-4 text-2xl font-bold leading-relaxed">
          Quit read aloud?
        </h3>
        <p className="mx-4 text-center pb-4 text-xl font-normal leading-loose">
          Your current listening progress will be lost.
        </p>
        <ModalButtonsContainer>
          <Button
            type="submit"
            size="sm"
            variant="secondary"
            onClick={closeModal}
            className="btn-full">
            Keep listening
          </Button>
          <Button 
            variant="primary" 
            size="sm"
            onClick={handleClose} 
            className="btn-full">
            Yes, quit
          </Button>
        </ModalButtonsContainer>
      </div>
    </BaseModal>
  );
};

export default SpeakAloudCloseModal;
