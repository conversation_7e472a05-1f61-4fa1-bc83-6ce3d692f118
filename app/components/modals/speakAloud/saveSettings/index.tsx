import Button from '@components/buttons/button';
import BaseModal from '@components/modals/BaseModal';
import ModalButtonsContainer from '@components/modals/ModalButtonsContainer';
import {Warning2} from 'iconsax-react';

const ReadAloudSettingsModal = ({
  isOpen,
  closeModal,
}: {
  isOpen: boolean;
  closeModal: () => void;
}) => {
  return (
    <BaseModal
      isOpen={isOpen}
      onClose={closeModal}
      accessibilityLabel="Read aloud settings">
      <div className="flex-col">
        <div className="text-center flex justify-center items-center pb-1">
          <Warning2 variant="Bulk" className={`w-12 h-12 theme-warning`} />
        </div>
        <h3 className=" text-center pt-3 text-2xl font-bold leading-relaxed">
          Confirm settings change
        </h3>
        <p className="mx-4 text-center pt-4 text-xl font-normal leading-7">
          Changing read aloud settings will cause any already processed sections
          to be re-created.
        </p>
        <div className="flex justify-evenly text-xl p-6">
          <div className="flex-col font-bold ">
            <p>Estimated cost:</p>
            <p>Processing minutes remaining:</p>
          </div>
          <div className="flex-col">
            <p>12 minutes</p>
            <p>9598 minutes</p>
          </div>
        </div>
        <ModalButtonsContainer>
          <Button
            type="submit"
            size="sm"
            variant="secondary"
            onClick={closeModal}
            className="flex-1">
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            size="sm"
            onClick={closeModal} 
            className="flex-1">
            Confirm and save
          </Button>
        </ModalButtonsContainer>
      </div>
    </BaseModal>
  );
};

export default ReadAloudSettingsModal;
