'use client';

import React from 'react';
import Modal from 'react-modal';
import {modalOverlayColour} from './helper';
import { ThemeColour } from '@libs/store/settings/types';
import {useCustomisation} from '@components/customisation/useCustomisation';

// Modal.setAppElement('#appRoot');
//TODO look into replacing with daisy modal
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  width?: string; // should be a tw string eg: sm:w-[600px]
  customTheme?: string;
  bgColour?: string;
  accessibilityLabel?: string;
}

export default function BaseModal({
  isOpen,
  onClose,
  children,
  width = 'sm:w-[600px]',
  customTheme = '',
  bgColour = 'modal-bg',
  accessibilityLabel = 'Confirmation modal',
}: ModalProps) {
  //TODO might be theme depended; Leave it for now as it might be switched to daisy modal;
  const {selectedCategory: selectedTheme} = useCustomisation('Theme');
  const overlayColor = modalOverlayColour(selectedTheme as ThemeColour);
  const modalWidth = `w-11/12 ${width}`;

  const [appElement, setAppElement] = React.useState<
    HTMLElement | HTMLElement[] | HTMLCollection | NodeList | undefined
  >();
  React.useEffect(() => {
    const modalId = document.getElementById('appRoot');
    if (modalId) setAppElement(modalId);
  }, []);
  return (
    <Modal
      appElement={appElement}
      onRequestClose={onClose}
      isOpen={isOpen}
      style={{
        overlay: {backgroundColor: overlayColor, zIndex: 9999},
        content: {
          // the underlying react-modal container which we don't want to see
          position: 'unset',
          border: 'none',
          inset: 0,
          padding: 0,
        },
      }}>
      <div data-theme={customTheme || selectedTheme}>
        <div
          className={`${modalWidth} ${bgColour} shadow-2xl-d rounded-lg p-4 top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 absolute doc-text-default`}
          aria-modal="true"
          aria-label={accessibilityLabel}
          role="dialog"
          tabIndex={0}>
          {children}
        </div>
      </div>
    </Modal>
  );
}
