/* eslint-disable react-hooks/exhaustive-deps */

import {CloseCircle} from 'iconsax-react';
import {useEffect, useState} from 'react';
import {useSettings} from '@contexts/settings';
import {Voice} from '@libs/store/settings/types';
import BaseModal from '@components/modals/BaseModal';
import useSettingsStorage from '@libs/store/settings';
import UserService from '@libs/services/user/service';
import ToggleButton from '@components/buttons/toggleButton';
import TailoIconButton from '../../buttons/tailoIconButton';
import Button from '@components/buttons/button';

export default function Component() {
  const {open, setOpen} = useSettings();

  const {settings, updateSettings} = useSettingsStorage();

  const [success, setSuccess] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>();
  const [doingRefresh, setDoingRefresh] = useState(false);

  const [state, setState] = useState({
    changed: false,
    voice: settings.Voice ?? 'Brian',
    hideSmallImages: settings.HideSmallImages ?? true,
    refreshRequired: false,
  });

  const userService = new UserService();

  const updateInSettings = (
    changes: {[key: string]: any},
    refresh: boolean = false,
  ) => {
    setLoading(true);

    // Take a cut of the current settings
    const previousSettings = settings;

    // Update the value locally.
    updateSettings({
      ...settings,
      ...changes,
    });

    // Update the settings on the server
    userService
      .updateUserSettings({...settings, ...changes})
      .then(() => {
        setDoingRefresh(refresh);
        setLoading(false);
        setSuccess(true);
        if (!refresh) {
          // Close the dialog if we aren't refreshing
          setOpen(false);
        }
      })
      .catch(() => {
        setLoading(false);

        setErrorMessage(
          'Failed to update settings, changes have been reverted.',
        );

        // Revert what we tried to change
        updateSettings({
          ...settings,
          ...previousSettings,
        });
      });
  };

  useEffect(() => {
    if (doingRefresh) {
      window.location.reload();
    }
  }, [doingRefresh]);

  const applyChanges = () => {
    const changes = {
      Voice: state.voice,
      HideSmallImages: state.hideSmallImages,
    };

    updateInSettings(changes, state.refreshRequired);
  };

  return (
    <BaseModal
      isOpen={open}
      onClose={() => setOpen(false)}
      bgColour="modal-bg"
      accessibilityLabel="Settings">
      <div className="mb-2 flex items-center justify-between">
        <h1 className="doc-text-default text-2xl font-bold leading-9 mb-0">
          Settings
        </h1>

        <TailoIconButton
          size="small"
          handler={() => setOpen(false)}
          label="Close settings">
          <CloseCircle />
        </TailoIconButton>
      </div>

      <form
        onChange={() => setState(prev => ({...prev, changed: true}))}
        onSubmit={ev => {
          ev.preventDefault();
          applyChanges();
        }}>
        <div className="field-group mb-4">
          <h2 className="text-lg font-semibold leading-[27px] doc-text-default">
            Layout
          </h2>

          <div className="h-[1px] divider mt-2 mb-4"></div>

          <div className="field self-stretch justify-between items-center flex relative">
            <label htmlFor="" className="block pr-6">
              <span className="text-base font-semibold">Hide small images</span>

              <p className="text-sm font-normal">
                Images smaller than 79 x 79px (often logos, icons, or decorative
                images), will be hidden.
              </p>
            </label>

            <ToggleButton
              checked={state.hideSmallImages}
              handleToggle={() =>
                setState(prev => ({
                  ...prev,
                  hideSmallImages: !state.hideSmallImages,
                }))
              }
            />
          </div>
        </div>

        <div className="field-group">
          <h2 className="text-lg font-semibold leading-[27px] doc-text-default">
            Read aloud
          </h2>

          <div className="h-[1px] divider mt-2 mb-4"></div>

          <div className="field self-stretch justify-between items-center flex">
            <label
              htmlFor=""
              className="w-[400px] flex-col justify-center items-start gap-0.5 flex">
              <span className="text-base font-semibold">Voice</span>

              <p className="text-sm font-normal">
                Customise the voice used for read aloud
              </p>
            </label>

            
              <select
                name="voice"
                onChange={event =>
                  setState(prev => ({
                    ...prev,
                    voice: event.target.value as Voice,
                    refreshRequired: true,
                  }))
                }
                defaultValue={state.voice}
                aria-label="Project status"
                className="input input-md max-w-[160px]">
                <optgroup label="UK">
                  <option value="Brian">Brian</option>
                  <option value="Amy">Amy</option>
                  <option value="Emma">Emma</option>
                </optgroup>

                <optgroup label="US">
                  <option value="Joanna">Joanna</option>
                  <option value="Matthew">Matthew</option>
                  <option value="Salli">Salli</option>
                </optgroup>
              </select>

          </div>

          <div className="h-[1px] divider my-4"></div>

          <div>
            <Button
              variant={'primary'}
              size="md"
              className="btn-full"
              loading={loading || doingRefresh}
              disabled={!state.changed}
              onClick={applyChanges}>
              Apply Changes
            </Button>
          </div>
        </div>
      </form>

      {errorMessage ? <p className="text-sm pt-4">{errorMessage}</p> : null}

      {((loading && state.refreshRequired) || doingRefresh) && (
        <p className="text-sm pt-4">
          To apply these settings the page will refresh automatically
        </p>
      )}
    </BaseModal>
  );
}
