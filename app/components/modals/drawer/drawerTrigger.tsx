import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import {FC, ReactNode} from 'react';

interface DrawerTriggerProps {
  children: ReactNode;
  drawerId: DRAWER_ID; // must match the id passed to the drawer
  ariaLabel: string;
  className?: string;
  onClick?: () => void; // to pass any extra functionality to the click. ie tracking
  disableButton?: boolean; // disables the button if its the its the active view
  isActive?: boolean; // to set the active state of the button
}

/**
 *  drawer trigger uses label to check/uncheck hidden checkbox using the drawerId - see DaisyUI Drawer component
 */
const DrawerTrigger: FC<DrawerTriggerProps> = ({
  children,
  drawerId,
  ariaLabel,
  className,
  onClick,
  disableButton = false,
  isActive,
}) => {
  const {drawStateById, updateDrawer} = useDrawer();
  const isOpen = drawStateById(drawerId) === 'open';

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      updateDrawer(drawerId, isOpen ? 'closed' : 'open');
    }
  };

  return (
    <button
      onClick={handleClick}
      aria-label={ariaLabel}
      title={ariaLabel}
      className={`${
        isActive ? 'tailo-icon-button-active' : ''
      } ${className} doc-text-default ${disableButton ? 'app-bg' : ''}`}
      disabled={disableButton}>
      {children}
    </button>
  );
};

export default DrawerTrigger;
