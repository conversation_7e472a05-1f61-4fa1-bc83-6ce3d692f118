'use client';
import {FC, ReactNode} from 'react';
import useDrawer from '@libs/store/drawer';
import {DRAWER_ID} from '@libs/store/drawer/types';
import dynamic from 'next/dynamic';
import 'react-modern-drawer/dist/index.css';
import useIsMobile from '@libs/utils/windowSize/useIsMobile';
const Drawer = dynamic(() => import('react-modern-drawer'), {ssr: false});

interface DrawerProps {
  drawerContent: ReactNode;
  drawerID: DRAWER_ID;
  drawerTitle: string;
  direction?: 'left' | 'right';
  hideTitleHeader?: boolean;
  drawerBgColour?: string;
  boxShadow?: boolean;
  right?: number;
  height?: string | number;
  width?: number;
  zIndex?: number;
}

// This value ensures that the layover doesn't cover the drawer
const DEFAULT_DRAWER_Z_INDEX = 101;
const DEFAULT_DRAWER_WIDTH = 384;

const AppDrawer: FC<DrawerProps> = ({
  drawerContent,
  drawerID,
  drawerTitle,
  direction,
  hideTitleHeader = false,
  drawerBgColour = 'panel-bg theme-border', //TODO: Change this theme app-navbar-bg
  boxShadow = true,
  right,
  height = '100vh',
  width = DEFAULT_DRAWER_WIDTH,
  zIndex = DEFAULT_DRAWER_Z_INDEX,
}) => {
  const {isLargeTablet} = useIsMobile();
  const {updateDrawer, drawStateById} = useDrawer();
  const isOpen = drawStateById(drawerID) === 'open';
  const closeDrawer = () => {
    updateDrawer(drawerID, 'closed');
  };

  if (isLargeTablet) return null;

  return (
    <Drawer
      overlayOpacity={0}
      size={width}
      open={isOpen}
      style={{
        height,
        right,
        background: 'none',
        boxShadow: boxShadow ? '' : 'none',
        transition: '0.2s linear',
        zIndex,
      }}
      onClose={closeDrawer}
      direction={direction || 'right'}>
      <div
        className={`w-full h-full flex flex-col overflow-hidden ${drawerBgColour}`}>
        {!hideTitleHeader && (
          <div className="flex flex-row items-center justify-between p-4">
            <h2 className="text-2xl font-bold leading-relaxed">
              {drawerTitle}
            </h2>
          </div>
        )}
        <div className="flex-grow overflow-y-auto p-4">{drawerContent}</div>
      </div>
    </Drawer>
  );
};

export default AppDrawer;
