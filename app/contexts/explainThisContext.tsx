'use client';
import {createContext, useContext, useEffect, useReducer} from 'react';
import {
  ExplainPromptConfigLevelOfDetail,
  ExplainPromptConfigLanguageComplexity,
  ExplainPromptConfigLevelOfKnowledge,
} from '@libs/services/explain/types';
import {useParams, usePathname} from 'next/navigation';
import explainService from '@libs/services/explain/service';
import useDrawer from '@libs/store/drawer';
import {useExplainThisAnalytics} from '@libs/services/explain/useExplainThisAnalytics';

type ExplainThisState = {
  loading: boolean;
  error: string | null;
  text: string;
  generatedText: string;
  levelOfDetail: ExplainPromptConfigLevelOfDetail;
  languageComplexity: ExplainPromptConfigLanguageComplexity;
  levelOfKnowledge: ExplainPromptConfigLevelOfKnowledge;
};

type ExplainThisContextType = {
  state: ExplainThisState;
  fetchExplainThisText: () => Promise<void>;
  resetText: () => void;
  dispatch: React.Dispatch<any>;
};

const ExplainThisContextDefaults: ExplainThisContextType = {
  state: {
    loading: false,
    error: null,
    text: '',
    generatedText: '',
    levelOfDetail: ExplainPromptConfigLevelOfDetail.BRIEF,
    languageComplexity: ExplainPromptConfigLanguageComplexity.MATCH_SOURCE,
    levelOfKnowledge: ExplainPromptConfigLevelOfKnowledge.INTERMEDIATE,
  },

  fetchExplainThisText: async () => {},
  resetText: () => {},
  dispatch: () => {},
};

const ExplainThisContext = createContext<ExplainThisContextType>(
  ExplainThisContextDefaults,
);

export function useExplainThisContext() {
  return useContext(ExplainThisContext);
}

export function explainThisReducer(state: ExplainThisState, action: any) {
  switch (action.type) {
    case 'resetText':
      return {...state, text: '', generatedText: '', error: null};
    case 'startFetchExplainThisText':
      return {...state, loading: true, error: null};
    case 'setLoading':
      return {...state, loading: action.loading};
    case 'setError':
      return {...state, error: action.error};
    case 'setText':
      return {...state, text: action.text};
    case 'setGeneratedText':
      return {...state, generatedText: action.generatedText};
    case 'setLevelOfDetail':
      return {...state, levelOfDetail: action.levelOfDetail};
    case 'setLanguageComplexity':
      return {...state, languageComplexity: action.languageComplexity};
    case 'setLevelOfKnowledge':
      return {...state, levelOfKnowledge: action.levelOfKnowledge};
    default:
      return state;
  }
}

export function ExplainThisProvider({children}: {children: React.ReactNode}) {
  const params = useParams();
  const documentId = params.id as string;
  const pathname = usePathname();
  const {isExplainThisDrawerActive} = useDrawer();
  const [state, dispatch] = useReducer(
    explainThisReducer,
    ExplainThisContextDefaults.state,
  );
  const {generateExplainThis, resetExplainThis} = useExplainThisAnalytics();

  const fetchExplainThisText = async () => {
    dispatch({type: 'startFetchExplainThisText'});
    generateExplainThis(state.text.length);
    try {
      const response = await explainService.sendExplainPromptConfig(
        documentId,
        {
          input_text: state.text,
          level_of_detail: state.levelOfDetail,
          language_complexity: state.languageComplexity,
          level_of_knowledge: state.levelOfKnowledge,
        },
      );
      if ('content' in response) {
        dispatch({type: 'setGeneratedText', generatedText: response.content});
      }
    } catch (error) {
      if (error instanceof Error) {
        dispatch({type: 'setError', error: error.message});
      } else {
        dispatch({type: 'setError', error: String(error)});
      }
    } finally {
      dispatch({type: 'setLoading', loading: false});
    }
  };

  const resetText = () => {
    resetExplainThis();
    dispatch({type: 'resetText'});
  };

  useEffect(() => {
    if (!pathname.includes('/dashboard')) return;
    resetText();
  }, [pathname]);

  useEffect(() => {
    if (isExplainThisDrawerActive) return;
    resetText();
  }, [isExplainThisDrawerActive]);

  useEffect(() => {
    const readingPreferences = localStorage.getItem('readingPreferences');
    if (!readingPreferences) return;

    const preferences = JSON.parse(readingPreferences);
    dispatch({
      type: 'setLevelOfKnowledge',
      levelOfKnowledge: preferences.levelOfKnowledge,
    });
    dispatch({
      type: 'setOutputFormat',
      outputFormat: preferences.outputFormat,
    });
    dispatch({
      type: 'setLanguageComplexity',
      languageComplexity: preferences.languageComplexity,
    });
    dispatch({
      type: 'setLevelOfDetail',
      levelOfDetail: preferences.levelOfDetail,
    });
  }, []);

  //Stops the user from selecting language complexity if they are an expert
  useEffect(() => {
    if (state.levelOfKnowledge != 'expert') return;
    //set the state of language complexity to match source
    dispatch({
      type: 'setLanguageComplexity',
      languageComplexity: 'match_source',
    });
  }, [state.levelOfKnowledge]);

  return (
    <ExplainThisContext.Provider
      value={{
        fetchExplainThisText,
        resetText,
        state,
        dispatch,
      }}>
      {children}
    </ExplainThisContext.Provider>
  );
}
