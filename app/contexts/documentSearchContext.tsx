'use client';

import {usePathname} from 'next/navigation';
import {ISearchResult} from '@components/document/search/types';
import documentClientServices from '@libs/services/document/documentClinetServices';
import useProcessedDocument from '@libs/services/document/useProcessedDocument';

import React, {
  createContext,
  ReactNode,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {useSearchAnalytics} from '@libs/services/search/useSearchAnalytics';

interface DocumentSearchContextProps {
  loading: boolean;
  searchedWord: string;
  words: string[];
  suggestions: [];
  results: ISearchResult[];
  errorSearching: boolean;
  setErrorSearching: (error: boolean) => void;
  setLoading: (loading: boolean) => void;
  triggerSearch: (id: string, text?: string) => void;
  setSearchWord: (word: string) => void;
  recentSearch: string[];
  setRecentSearch: (results: string[]) => void;
  addWordToRecentSearch: (word: string) => void;
  clearSearchedWord: () => void;
  clearRecentSearch: () => void;
  wordSubmitted: boolean; //After the users submits a word, this will be set to true
  setWordSubmitted: (submitted: boolean) => void;
}

export const DocumentSearchContext = createContext<DocumentSearchContextProps>({
  loading: false,
  searchedWord: '',
  results: [],
  words: [],
  suggestions: [],
  errorSearching: false,
  setErrorSearching: () => {},
  setLoading: () => {},
  triggerSearch: () => {},
  setSearchWord: () => {},
  recentSearch: [],
  setRecentSearch: () => {},
  addWordToRecentSearch: () => {},
  clearSearchedWord: () => {},
  clearRecentSearch: () => {},
  wordSubmitted: false,
  setWordSubmitted: () => {},
});

export const DocumentSearchProvider = ({children}: {children: ReactNode}) => {
  const {id} = useProcessedDocument();
  const {
    trackGenerateSearch,
    trackClearRecentSearches,
    trackClearSelectedWord,
  } = useSearchAnalytics();

  const [searchedWord, setSearchedWord] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<ISearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<[]>([]);
  const [errorSearching, setErrorSearching] = useState(false);
  const [recentSearch, setRecentSearch] = useState<string[]>([]);
  const [wordSubmitted, setWordSubmitted] = useState(false);
  const pathname = usePathname();

  const words = useMemo(() => {
    if (!results || !results.length) return [];

    const found = results
      .map((result: {content: string[]}) => {
        const content = result.content.join() as string;
        const matches = content.match(/<em>(.*?)<\/em>/g);

        if (!matches) return null;

        return matches.map(match => match.replace(/<\/?em>/g, ''));
      })
      .flat();

    if (!found) return [];

    return [...new Set(found.filter((word): word is string => word !== null))];
  }, [results]);

  const setSearchWord = (word: string) => {
    setSearchedWord(word);
  };

  const triggerSearch = (id: string, text?: string) => {
    setLoading(true);
    setErrorSearching(false);
    setResults([]);

    documentClientServices
      .searchDocuments(id, text ?? searchedWord)
      .then(data => {
        trackGenerateSearch(searchedWord.length, data.hits.length, 'Document');
        setResults(data.hits);
        setSuggestions(data.suggestions);
        setLoading(false);
      });
  };

  const addWordToRecentSearch = (word: string) => {
    if (recentSearch.includes(word)) return;

    let items = recentSearch;

    if (items.length === 5) {
      items = recentSearch.slice(0, -1);
    }

    setRecentSearch([word, ...items]);
  };

  const clearSearchedWord = () => {
    trackClearSelectedWord();
    setSearchedWord('');
    setResults([]);
  };

  const clearRecentSearch = () => {
    localStorage.removeItem('recentSearches');
    trackClearRecentSearches();

    setRecentSearch([]);
  };

  useEffect(() => {
    if (recentSearch.length === 0) return;

    localStorage.setItem('recentSearches', JSON.stringify(recentSearch));
  }, [recentSearch]);

  useEffect(() => {
    const savedSearches = JSON.parse(
      localStorage.getItem('recentSearches') || '[]',
    );

    setRecentSearch(savedSearches);
  }, []);

  useEffect(() => {
    if (wordSubmitted && searchedWord === '') {
      setWordSubmitted(false);
    }
  }, [wordSubmitted, searchedWord]);

  useEffect(() => {
    if (!pathname) return;

    clearSearchedWord();
  }, [pathname]);

  return (
    <DocumentSearchContext.Provider
      value={{
        loading,
        searchedWord,
        results,
        words,
        suggestions,
        errorSearching,
        setErrorSearching,
        setLoading,
        triggerSearch,
        setSearchWord,
        recentSearch,
        setRecentSearch,
        addWordToRecentSearch,
        clearSearchedWord,
        clearRecentSearch,
        wordSubmitted,
        setWordSubmitted,
      }}>
      {children}
    </DocumentSearchContext.Provider>
  );
};
