'use client';

import useDrawer from '@libs/store/drawer';
import {DRAWER_ID, DrawerTab} from '@libs/store/drawer/types';
import React, {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
} from 'react';
import {authenticatedClientApi} from '@libs/services/axiosInstance';
import {WordDefinitionResponse} from '@libs/store/document/wordDefinition/types';
import {startTimer} from '@libs/utils/wordDefinition/helper';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';
import {usePathname, useSearchParams} from 'next/navigation';
import debounce from 'lodash/debounce';

type WordDefinitionContextType = {
  loading: boolean;
  results: WordDefinitionResponse | undefined;
  error: string | null;
  currentlySearchedWord: string;
  fetchSelectedWordDefinition: (e?: React.FormEvent) => Promise<void>;
  setCurrentlySearchedWord: (word: string) => void;
  setResults: (results: WordDefinitionResponse | undefined) => void;
  setError: (error: string | null) => void;
  clearWordDefinition: () => void;
};

const WordDefinitionContextDefaults: WordDefinitionContextType = {
  loading: false,
  results: undefined,
  error: null,
  currentlySearchedWord: '',
  fetchSelectedWordDefinition: async () => {},
  setCurrentlySearchedWord: () => {},
  setResults: () => {},
  setError: () => {},
  clearWordDefinition: () => {},
};

const WordDefinitionContext = createContext<WordDefinitionContextType>(
  WordDefinitionContextDefaults,
);

export function useWordDefinitionContext() {
  return useContext(WordDefinitionContext);
}

export function WordDefinitionProvider({children}: {children: ReactNode}) {
  const {closeDrawer, drawStateById, docToolsDrawerContent} = useDrawer();
  const {trackDefineProcess} = useDocumentAnalytics();

  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<WordDefinitionResponse | undefined>(
    undefined,
  );
  const [error, setError] = useState<string | null>(null);
  const [currentlySearchedWord, setCurrentlySearchedWord] = useState('');

  const pathname = usePathname();
  const queryParams = useSearchParams();

  useEffect(() => {
    closeDrawer(DRAWER_ID.DOCUMENT_TOOLS);
  }, [pathname, queryParams]);

  const clearWordDefinition = () => {
    setResults(undefined);
    setCurrentlySearchedWord('');
    setError(null);
  };

  const validateSearchedWord = (value: string) => {
    return (
      value.replace(/[^a-z]/gi, '') === currentlySearchedWord.trim() &&
      currentlySearchedWord.trim() !== ''
    );
  };

  const handleError = (msg: string) => {
    setError(msg);
    setResults(undefined);
  };

  const fetchSelectedWordDefinition = debounce(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    if (!currentlySearchedWord || currentlySearchedWord.split(' ').length > 1) {
      return;
    }

    if (
      drawStateById(DRAWER_ID.DOCUMENT_TOOLS) === 'closed' ||
      docToolsDrawerContent.content !== DrawerTab.WORD_DEFINITION
    )
      return;

    const isValidWord = validateSearchedWord(currentlySearchedWord);
    if (!isValidWord) {
      setError(
        "Define isn't able to find words containing numbers and symbols. Please try a different word.",
      );
      setResults(undefined);
      return;
    }

    const stopTimer = startTimer();

    setLoading(true);
    setError(null);

    try {
      const response = await authenticatedClientApi().get('/dictionary', {
        params: {word: currentlySearchedWord},
      });
      const {message, code} = response.data;
      if (code === 0 && Array.isArray(message)) {
        setResults(message[message.length - 1]);
        setError(null);
      } else {
        handleError(
          'Error occurred while fetching definition. Please try again.',
        );
      }
    } catch (error) {
      handleError(
        'Error occurred while fetching definition. Please try again.',
      );
    } finally {
      const timeTaken = stopTimer();
      trackDefineProcess(timeTaken, true, currentlySearchedWord);
      setLoading(false);
    }
  }, 250);

  const resolvedFetchSelectedWordDefinition = (
    e?: React.FormEvent,
  ): Promise<void> => {
    return new Promise(resolve => {
      fetchSelectedWordDefinition(e);
      resolve();
    });
  };

  return (
    <WordDefinitionContext.Provider
      value={{
        loading,
        results,
        error,
        currentlySearchedWord,
        fetchSelectedWordDefinition: resolvedFetchSelectedWordDefinition,
        setCurrentlySearchedWord,
        setResults,
        setError,
        clearWordDefinition,
      }}>
      {children}
    </WordDefinitionContext.Provider>
  );
}
