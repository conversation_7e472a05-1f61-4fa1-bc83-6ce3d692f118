'use client';
import {createContext, useState} from 'react';
import SectionSummaryService from '@libs/services/sectionSummaries/service';
import {useSectionSummariesAnalytics} from '@libs/services/search/useSectionSummariesAnalytics';

interface SectionSummary {
  section_id: string;
  text: string[];
}

type CollapsedState = {
  [sectionId: string]: boolean;
};

type SummariesContextType = {
  generatedSummaries: SectionSummary[];
  collapsedStates: CollapsedState;
  error: string | null;
  loading: boolean;
  toggleCollapsedState: (sectionId: string) => void;
  fetchSectionSummary: (
    document_id: string,
    section_id: string,
    inputLength: number,
  ) => Promise<void>;
};

export const SectionSummariesContext = createContext<SummariesContextType>({
  generatedSummaries: [],
  collapsedStates: {},
  error: null,
  loading: false,
  toggleCollapsedState: () => {},
  fetchSectionSummary: async () => {},
});

export const SectionSummariesProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [generatedSummaries, setGeneratedSummaries] = useState<
    SectionSummary[]
  >([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [collapsedStates, setCollapsedStates] = useState<CollapsedState>({});
  const {trackGenerateSectionSummary} = useSectionSummariesAnalytics();

  const fetchSectionSummary = async (
    document_id: string,
    section_id: string,
    inputLength: number,
  ) => {
    setLoading(true);

    try {
      const response = await SectionSummaryService.sendSectionSummaryConfig(
        document_id,
        section_id,
      );

      if ('data' in response) {
        // Ensure response.data is transformed into a string[] as required by SectionSummary
        if (Array.isArray(response.data)) {
          const summaryText = response.data.map(item => String(item)); // Convert to string[] if necessary

          setGeneratedSummaries(prevSummaries => {
            const updatedSummaries = [
              ...prevSummaries,
              {
                section_id,
                text: summaryText,
              },
            ];

            const outputLength = summaryText.join(' ').split(/\s+/).length;
            fetchSectionDetails(inputLength, outputLength);

            return updatedSummaries;
          });
        } else {
          setError('Invalid data format');
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchSectionDetails = (
    sectionWordCount: number,
    outputLength: number,
  ) => {
    trackGenerateSectionSummary(sectionWordCount, outputLength);
  };

  const toggleCollapsedState = (sectionId: string) => {
    setCollapsedStates(prevState => ({
      ...prevState,
      [sectionId]: !prevState[sectionId],
    }));
  };

  return (
    <SectionSummariesContext.Provider
      value={{
        generatedSummaries,
        error,
        loading,
        collapsedStates,
        toggleCollapsedState,
        fetchSectionSummary,
      }}>
      {children}
    </SectionSummariesContext.Provider>
  );
};
