/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import React, {
  useMemo,
  useState,
  ReactNode,
  useContext,
  createContext,
} from 'react';

import Modal from '@components/modals/settings';

type SettingsContextType = {
  open: boolean;
  setOpen: CallableFunction;
};

const SettingsContextDefaults: SettingsContextType = {
  open: false,
  setOpen: () => {},
};

const SettingsContext = createContext<SettingsContextType>(
  SettingsContextDefaults,
);

export function useSettings() {
  return useContext(SettingsContext);
};

export function SettingsProvider({children}: {children: ReactNode}) {
  const [open, setOpen] = useState<boolean>(false);

  const currentValue = {
    open,
    setOpen,
  };

  const value = useMemo(() => currentValue, [currentValue]);

  return (
    <SettingsContext.Provider value={value}>
      {children}

      {open && (
        <Modal />
      )}
    </SettingsContext.Provider>
  );
};
