'use client';

import {usePathname} from 'next/navigation';
import {useSearchParams} from 'next/navigation';
import useSpeakAloud, {SpeakAloudState} from '@libs/store/speakAloud';
import ControlContainer from '@components/document/speakAloud/controlContainer';

import React, {
  createContext,
  useContext,
  ReactNode,
  useMemo,
  useEffect,
  useState,
} from 'react';

import {useGlobalAudioPlayer} from 'react-use-audio-player';

type TextToSpeechContextType = {
  sectionToAutoplay: string | null;
  setSectionToAutoplay: (id: string) => void;
};

const TextToSpeechContextDefaults: TextToSpeechContextType = {
  sectionToAutoplay: null,
  setSectionToAutoplay: id => null,
};

const TextToSpeechContext = createContext<TextToSpeechContextType>(
  TextToSpeechContextDefaults,
);

export function useTextToSpeech() {
  return useContext(TextToSpeechContext);
}

export function TextToSpeechProvider({children}: {children: ReactNode}) {
  const pathname = usePathname();
  const queryParams = useSearchParams();

  const {speakAloudState, updateSpeakAloudState, clearCurrentParagraph} =
    useSpeakAloud();

  const {stop} = useGlobalAudioPlayer();

  const [sectionToAutoplay, setSectionToAutoplay] = useState<string | null>(
    null,
  );

  const currentValue = {
    sectionToAutoplay,
    setSectionToAutoplay,
  };

  const value = useMemo(() => currentValue, [currentValue]);

  const stopAudio = () => {
    clearCurrentParagraph();
    updateSpeakAloudState(SpeakAloudState.OFF);
    stop();
  };

  useEffect(() => {
    // Set autoplay to null on route change.
    setSectionToAutoplay(null);

    // Stop audio on route change.
    stopAudio();
  }, [pathname, queryParams]);

  return (
    <TextToSpeechContext.Provider value={value}>
      {children}

      {speakAloudState !== SpeakAloudState.OFF && <ControlContainer />}
    </TextToSpeechContext.Provider>
  );
}
