'use client';

import {Footnote} from '@components/footnotes/types';
import React, {createContext, useContext, useState} from 'react';

interface FootnotesContextType {
  footnotes: Footnote[];
  setFootnotes: (footnotes: Footnote[]) => void;
}

const FootnotesContext = createContext<FootnotesContextType | undefined>(
  undefined,
);

export const FootnotesProvider: React.FC<{children: React.ReactNode}> = ({
  children,
}) => {
  const [footnotes, setFootnotes] = useState<Footnote[]>([]);

  return (
    <FootnotesContext.Provider value={{footnotes, setFootnotes}}>
      {children}
    </FootnotesContext.Provider>
  );
};

export const useFootnotes = () => {
  const context = useContext(FootnotesContext);
  if (!context) {
    throw new Error('useFootnotes must be used within a FootnotesProvider');
  }
  return context;
};
