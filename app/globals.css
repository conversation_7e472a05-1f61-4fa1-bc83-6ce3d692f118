@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import './styles/index.css';

/* These styles are being dynamically generated based on class passed to the library's Drawer element hence need to be defined after the TW imports */
/* Standard bottom drawer with handle */
.custom-drawer__backdrop {
  /* z-max reserved for drawers */
  /* This styling along with .custom-drawer ensures that the bottom sheet stays on top of other elements   */
  @apply !z-max;
}
.custom-drawer {
  @apply app-bg theme-border flex-col !z-max fixed;
}
/* Do not apply max height as it will hide the drawer handle on small screens making it impossible to close the drawer */
.custom-drawer__content {
  @apply bottom-sheet-scrollbar;
}
.custom-drawer__handle {
  @apply !app-bg-tertiary;
}
.custom-drawer__backdrop.remove-custom-drawer__backdrop {
  @apply !opacity-0;
}
.remove-custom-drawer__handle-wrapper .custom-drawer__handle-wrapper {
  @apply !hidden;
}
/* Full height bottom drawer */
.custom-full-drawer {
  @apply drawer-bg theme-border flex-col !z-max fixed;
}

.is-mobile .custom-full-drawer {
  height: calc(100vh - 100px); /* Tested with android and iOS */
}

.custom-full-drawer__content {
  @apply !h-screen !max-h-screen bottom-sheet-scrollbar;
}
.custom-full-drawer__content-wrapper {
  @apply !h-screen !max-h-screen;
}

.collapse-title,
:where(.collapse > input[type='checkbox']) {
  width: 100%;
  padding: 1rem /* 16px */;
  min-height: min-content;
  transition: background-color 0.2s ease-out;
}
.accordion:focus-visible,
.accordion:has(.collapse-title:focus-visible),
.accordion:has(> input[type='checkbox']:focus-visible) {
  @apply focus-outline;
}
.rc-slider-handle-dragging {
  @apply !slider-handle-dragging !shadow-none;
}
.rc-slider-handle:focus-visible {
  @apply !slider-handle-focus !shadow-none !border-none;
}
.rc-slider-handle:hover {
  @apply !border-none;
}
.rc-slider-handle {
  @apply !slider-handle;
}
.rc-slider-rail {
  @apply !slider-rail;
}
.rc-slider-dot {
  @apply !slider-dot !border-none;
}
.rc-slider-dot-active {
  @apply !slider-dot-active;
}
.rc-slider-track {
  @apply !slider-track;
}

::selection {
  @apply !text-highlight-word-definition;
}

#cookiebanner {
  font-family: Poppins, sans-serif;
  @apply absolute text-slate-50 bg-slate-900 p-6 w-full lg:left-1/2 lg:-translate-x-1/2 lg:w-1/2 lg:max-w-[600px] h-min max-h-screen overflow-y-scroll opacity-95 z-[2147483645];
}
#c-left {
  @apply flex flex-col justify-center items-center text-center;
}
#c-left .c-header {
  @apply text-2xl font-bold mb-4;
}
#c-left .c-message {
  @apply text-xl;
}
#c-right {
  @apply flex items-center my-4 gap-6;
}
#c-right .c-button {
  @apply bg-slate-50 text-slate-900 hover:bg-slate-300 active:bg-slate-400 disabled:bg-slate-500 disabled:text-slate-950 disabled:outline-none text-center rounded-lg px-6 py-3 text-lg font-bold shadow-md w-full;
}
#c-right .c-button-essential {
  @apply bg-slate-900 border-2 border-slate-500 hover:border-slate-300 text-slate-50 focus-visible:border-transparent active:border-slate-50 active:bg-slate-900 disabled:border-slate-700 disabled:text-slate-700 disabled:outline-none text-center rounded-lg px-6 py-3 text-lg font-bold shadow-md w-full;
}
#cookiebanner hr {
  @apply border-slate-800 my-6;
}
.c-header {
  @apply text-xl font-bold;
}
.c-message {
  @apply text-base;
}
#preference,
#statistics {
  @apply flex justify-between items-center gap-6;
}
.preference-toggle,
.statistics-toggle {
  @apply toggle;
}
#privacy-policy-text {
  @apply text-base font-normal text-center;
}
#privacy-policy {
  @apply text-base font-semibold underline;
}
*:focus-visible {
  outline: none;
}
