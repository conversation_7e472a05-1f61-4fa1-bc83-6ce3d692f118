import '../globals.css';
import Metrics from '@libs/metrics';
import {Providers} from '@components/providers/providers';
import HtmlWrapper from '@components/wrappers/htmlWrapper';
import TailoMetadata from '@libs/utils/metadata/TailoMetadata';

export const metadata = TailoMetadata;

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <HtmlWrapper>
      <Metrics />
      <Providers>{children}</Providers>
    </HtmlWrapper>
  );
}
