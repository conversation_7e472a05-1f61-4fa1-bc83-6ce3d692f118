import {VerifyAccountMetadata} from '@libs/utils/metadata/TailoMetadata';

export const metadata = VerifyAccountMetadata;
// We need the layout file as long as we keep the logic for access-only route in page file. When that gets removed, please move the metadata to the page file and remove this file.

export default function VerifyAccountLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
