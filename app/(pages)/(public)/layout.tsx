import {disablePublicRoutesForAuthUser} from '@libs/services/auth/routes';
import GuestThemedWrapper from '@components/wrappers/themed/guestPagesThemedWrapper';
import {FootnotesProvider} from '@contexts/footnotesContext';

export default async function PublicPagesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  await disablePublicRoutesForAuthUser();
  return (
    <GuestThemedWrapper as="div">
      {/* <FootnotesProvider> */}
      {children}
      {/* </FootnotesProvider> */}
    </GuestThemedWrapper>
  );
}
