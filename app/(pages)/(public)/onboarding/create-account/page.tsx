import OnboardingNavBar from '@components/onboarding/navbar';
import RegisterPanel from '@components/onboarding/register/registerPanel';
import ResultsPanel from '@components/onboarding/results/resultsPanel';
import React from 'react'

export default function CreateAccountPage() {

  return (
    <>
      <OnboardingNavBar finalPage />
      <div className='flex flex-col-reverse lg:flex-row justify-center w-full'>
        {/* Create Form */}
        <div className='flex flex-1 justify-center lg:pl-8 2xl:justify-end items-center 2xl:pr-14'>
          <RegisterPanel />
        </div>
        {/* Results */}
        <div className='flex flex-1 py-0 justify-center items-center lg:pr-8 lg:py-6 2xl:pl-14 2xl:justify-start'>
          <ResultsPanel />
        </div>
      </div>
    </>
  )
}
