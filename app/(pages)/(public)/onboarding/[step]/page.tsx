import OnboardingDynamicPageTemplate from '@components/onboarding/onboardingDynamicPageTemplate';
import {isValidOnboardingStep} from '@libs/services/auth/routes';
import {OnboardingDynamicPagesMetadata} from '@libs/utils/metadata/TailoMetadata';

export function generateMetadata({params}: {params: {step: string}}) {
  const {step} = params;
  return {
    title: OnboardingDynamicPagesMetadata[step].title,
  };
}

export default function Page({params}: {params: {step: string}}) {
  const {step} = params;
  return (
    isValidOnboardingStep(step) && <OnboardingDynamicPageTemplate step={step} />
  );
}
