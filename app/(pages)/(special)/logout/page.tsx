'use client';

import React, { useEffect } from 'react';
import { signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import useSettings from '@libs/store/settings';
import TailoFullLogo from '@components/logos/tailoFullLogo';
import SvgGradientLoader from '@components/loaders/svgGradientLoader';
import {DEFAULT_SETTINGS_ATTRIBUTE_VALUE} from '@libs/utils/settings/helper';

const theme = DEFAULT_SETTINGS_ATTRIBUTE_VALUE;

// This page exists because next-auth hijacks the logout on pages.
export default function Page() {
  const router = useRouter();

  const { resetSettings } = useSettings();

  const reloadPage = () => {
    window.location.reload();
  };

  useEffect(() => {
    resetSettings();

    // Wait two seconds then trigger logout.
    setTimeout(() => {
      signOut({ redirect: false }).then(() => {
        // Redirect to the login page.
        router.push('/');
      });
    }, 2000);
  }, []);

  return (
    <div data-theme={theme}>
      <div className="login-app-bg doc-text-default min-w-full min-h-screen">
        <div className="min-h-screen flex justify-center pt-10 sm:pt-16">
          <div className="space-y-8">
            <TailoFullLogo className="m-auto w-44 sm:h-9" />
            <div className="w-full p-4 sm:w-96 sm:p-6 login-panel border flex-col gap-4 inline-flex  justify-center text-center">
              <div className="inline-block m-auto">
                <SvgGradientLoader />
              </div>

              <h1 className="text-xl font-bold sm:text-2xl ">Signing out...</h1>

              <p className="whitespace-pre-wrap">
                This page will automatically reload.
              </p>
            </div>
            <div className="w-72 sm:w-96 pl-4 pr-2 sm:px-6 space-y-2">
              <p>
                {'If the page doesn’t automatically reload after 5 seconds, '}
                <span role="button" onClick={reloadPage} className="link">
                  click here to manually refresh the page.
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
