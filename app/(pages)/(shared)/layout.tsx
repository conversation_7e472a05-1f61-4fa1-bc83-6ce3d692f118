import SharedPagesThemedWrapper from '@components/wrappers/themed/sharedPagesThemedWrapper';
import PermissionsWrapper from '@components/wrappers/permissionsWrapper';
import SharedScreenNavigation from '@components/nav/sharedScreenNavigation';
import {FootnotesProvider} from '@contexts/footnotesContext';

export default async function SharedPagesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <PermissionsWrapper>
      <SharedPagesThemedWrapper>
        <SharedScreenNavigation />
        {/* <FootnotesProvider> */}
        {children}
        {/* </FootnotesProvider> */}
      </SharedPagesThemedWrapper>
    </PermissionsWrapper>
  );
}
