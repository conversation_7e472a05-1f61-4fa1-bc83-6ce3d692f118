import React from 'react';
import {AdminMetadata} from '@libs/utils/metadata/TailoMetadata';
import {
  AppPrivateRoutes,
  protectExplicitRoute,
} from '@libs/services/auth/routes';
import DashboardApplicationNavigation from '@components/nav/dashboard';

export const metadata = AdminMetadata;

export default async function AdminPagesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  await protectExplicitRoute(AppPrivateRoutes.Admin);

  return (
    <div className="lg:flex" data-clarity-mask="True">
      {children}
    </div>
  );
}
