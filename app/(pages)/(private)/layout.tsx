import React from 'react';
import {TextToSpeechProvider} from '@contexts/tts';
import {Auth} from '@components/wrappers/authWrapper';
import {SettingsProvider} from '@contexts/settings';
import {protectPrivateRoutes} from '@libs/services/auth/routes';
import {ExplainThisProvider} from '@contexts/explainThisContext';
import WebSocketWrapper from '@components/wrappers/webSocketWrapper';
import {WordDefinitionProvider} from '@contexts/wordDefinitionContext';
import {DocumentSearchProvider} from '@contexts/documentSearchContext';
import AppNavBar from '@components/nav/applicationNavigation/appNavBar';
import PermissionsWrapper from '@components/wrappers/permissionsWrapper';
import PrivatePagesServerThemeWrapper from '@components/wrappers/themed/privatePagesServerThemeWrapper';
import {FootnotesProvider} from '@contexts/footnotesContext';
import {SectionSummariesProvider} from '@contexts/sectionSummariesContext';

export default async function PrivatePagesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  await protectPrivateRoutes();

  return (
    <Auth>
      <PermissionsWrapper>
        <PrivatePagesServerThemeWrapper>
          <SettingsProvider>
            <TextToSpeechProvider>
              <WordDefinitionProvider>
                <ExplainThisProvider>
                  <DocumentSearchProvider>
                    <SectionSummariesProvider>
                      {/* <FootnotesProvider> */}
                      <AppNavBar />
                      <WebSocketWrapper>{children}</WebSocketWrapper>
                      {/* </FootnotesProvider> */}
                    </SectionSummariesProvider>
                  </DocumentSearchProvider>
                </ExplainThisProvider>
              </WordDefinitionProvider>
            </TextToSpeechProvider>
          </SettingsProvider>
        </PrivatePagesServerThemeWrapper>
      </PermissionsWrapper>
    </Auth>
  );
}
