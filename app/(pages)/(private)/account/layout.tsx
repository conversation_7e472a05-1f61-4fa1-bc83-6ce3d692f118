import DashboardApplicationNavigation from '@components/nav/dashboard';
import {
  AppPrivateRoutes,
  protectExplicitRoute,
} from '@libs/services/auth/routes';
import {AccountMetadata} from '@libs/utils/metadata/TailoMetadata';
import React from 'react';

export const metadata = AccountMetadata;

export default async function NavLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  await protectExplicitRoute(AppPrivateRoutes.Account);
  return <div className="lg:flex">{children}</div>;
}
