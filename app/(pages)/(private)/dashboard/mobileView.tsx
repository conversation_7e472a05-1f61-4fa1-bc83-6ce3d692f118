import React from 'react';
import Panel from '@components/panel/Panel';
import WelcomeBanner from '@components/banners/welcome';
import DocumentsList from '@components/dashboard/documentsList';
import UploadDocumentButton from '@components/dashboard/uploadDocumentButton';
import DashboardPlaceholder from '@components/dashboard/placeholder/dashboardPlaceholder';
import useDocuments from '@libs/services/document/useDocuments';
import ExtractionWarningMessage from '@components/dashboard/ExtractionWarningMessage';

export default function MobileView() {
  const {documents} = useDocuments();
  return (
    <>
      <div className="pt-20 pb-6 px-4 lg:px-0 lg:hidden">
        <Panel className="mb-8">
            <WelcomeBanner defaultVisibility={false} />
        </Panel>

        <div className="mobile-document-body lg:hidden">
          <h2 className="text-lg font-bold leading-snug">My documents</h2>
          <div className="pt-2 mb-6">
            {documents.length === 0 ? (
              <DashboardPlaceholder className="w-full" />
            ) : (
              <DocumentsList />
            )}
            <UploadDocumentButton />
          </div>

          <div className="mobile-extraction-message rounded-lg">
            <ExtractionWarningMessage />
          </div>

        </div>
      </div>
    </>
  );
}
