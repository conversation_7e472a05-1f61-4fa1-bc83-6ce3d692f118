import React from 'react';
import Panel from '@components/panel/Panel';
import WelcomeBanner from '@components/banners/welcome';
import DocumentsList from '@components/dashboard/documentsList';
import UploadDocumentButton from '@components/dashboard/uploadDocumentButton';
import ExtractionWarningMessage from '@components/dashboard/ExtractionWarningMessage';
import CustomisationDrawer from '@components/customisation/customisationDrawer';
import DocToolsDrawer from '@components/feedback/feedbackDrawer';
import useDocuments from '@libs/services/document/useDocuments';
import DashboardPlaceholder from '@components/dashboard/placeholder/dashboardPlaceholder';

export default function DesktopView() {
  const {documents} = useDocuments();
  const innerPanelClasses =
    'w-full h-full py-4 inline-flex flex-row self-center justify-between items-center';

  return (
    <div
      id="document-wrapper"
      className="p-6 items-center hidden lg:flex lg:flex-col">
      <div className="max-w-full xl:w-3/4 mt-14">
      
        <Panel className="mb-8">
            <WelcomeBanner defaultVisibility={true} />
        </Panel>

        <Panel className="mt-4 mb-6" isDocumentsPanel={true}>
          <div className={innerPanelClasses}>
            <h2 className="text-3xl font-bold">My documents</h2>
            <UploadDocumentButton />
          </div>
          <div>
            {documents.length === 0 ? (
              <DashboardPlaceholder className="w-full mb-6" />
            ) : (
              <DocumentsList />
            )}
          </div>
        </Panel>

        <Panel>
          <ExtractionWarningMessage />
        </Panel>
        <CustomisationDrawer />
        <DocToolsDrawer />
      </div>
    </div>
  );
}
