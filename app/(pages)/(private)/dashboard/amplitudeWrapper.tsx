'use client';

import useUserDataFromSession, {
  UserType,
} from '@libs/utils/user/useUserDataFromSession';
import {useEffect} from 'react';
import {useAnalytics} from 'use-analytics';

function DashboardAmplitudeWrapper({children}: {children: React.ReactNode}) {
  const {identify, user} = useAnalytics();
  const {userType, license, trackingId} = useUserDataFromSession();

  useEffect(() => {
    if (license) {
      identify(trackingId ?? '', {
        AccountName: license.accountName,
        LicenceCategory: license.category,
      });
    }
  }, [license]);

  useEffect(() => {
    identify(trackingId ?? '', {
      UserType:
        userType === UserType.AlphaUser ? 'Alpha User' : 'Internal User',
    });
  }, [userType]);

  return <>{children}</>;
}

export default DashboardAmplitudeWrapper;
