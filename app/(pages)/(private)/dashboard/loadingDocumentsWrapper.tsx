'use client';

import {useEffect} from 'react';
import MobileView from './mobileView';
import DesktopView from './desktopView';
import {useAnalytics} from 'use-analytics';
import PageLoader from '@components/loaders/pageLoader';
import useDocuments from '@libs/services/document/useDocuments';
import {useDocumentAnalytics} from '@libs/services/document/useDocumentAnalytics';
import useUserDataFromSession from '@libs/utils/user/useUserDataFromSession';

export default function LoadingDocumentsWrapper() {
  const {isLoading, documents} = useDocuments();
  const {identify, user} = useAnalytics();
  const {trackDashboardPageView} = useDocumentAnalytics();
  const {trackingId} = useUserDataFromSession();

  useEffect(() => {
    identify(trackingId ?? '', {
      NumberOfDocsUploaded: documents.length,
      TotalTimeInDocuments: 0, // TODO: Functionality for this still needs to be implemented
    });
  }, [documents.length]);

  useEffect(() => {
    trackDashboardPageView();
  }, [trackDashboardPageView]);

  if (isLoading) return <PageLoader />;
  return (
    <>
      <DesktopView />
      <MobileView />
    </>
  );
}
