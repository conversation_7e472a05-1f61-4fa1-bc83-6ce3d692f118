'use client';

import React, {useEffect, useMemo} from 'react';
import useSpeakAloud from '@libs/store/speakAloud';
import TabContentWrapper from '@components/document/tabContentWrapper';
import useSummaryTab from '@components/document/summaryTab/useSummaryTab';
import DocumentWrapper from '@components/document/documentWrapper/documentWrapper';

export default function Page() {
  const {documentData, id} = useSummaryTab();

  const {convertDocumentToSkeleton, setInitialSkeletonState} = useSpeakAloud();

  // Transform the document into a skeleton state for TTS.
  const skeleton = useMemo(() => {
    if (!documentData) return;

    return convertDocumentToSkeleton(id, documentData);
  }, [documentData]);

  // Set the skeleton state of the document globally.
  useEffect(() => {
    if (!skeleton) return;

    setInitialSkeletonState(skeleton);
  }, [skeleton]);

  return (
    <div className="m-auto max-w-desktop">
      <DocumentWrapper>
        <TabContentWrapper />
      </DocumentWrapper>
    </div>
  );
}
