{
  "cSpell.ignoreWords": [
    "INVALIDFILETYPE",
    "SUMMARISE",
    "summarisation",
    "summariser"
  ],
  // Pylance settings
  "python.languageServer": "Pylance",
  "python.analysis.typeCheckingMode": "basic",
  "python.analysis.diagnosticMode": "workspace",
  "python.analysis.autoImportCompletions": true,
  "python.analysis.inlayHints.variableTypes": true,
  "python.analysis.inlayHints.functionReturnTypes": true,
  "python.analysis.extraPaths": ["${workspaceFolder}"],
  "python.analysis.autoSearchPaths": true,
  "python.analysis.indexing": true,
  "python.analysis.packageIndexDepths": [
    {
      "name": "tailo",
      "depth": 5
    }
  ],
  // Critical setting: Set Python version to 3.9 to catch compatibility issues
  "python.analysis.pythonVersion": "3.9",
  "python.analysis.useLibraryCodeForTypes": true,
  // Increase severity of type checking issues to catch Python 3.10 features
  "python.analysis.diagnosticSeverityOverrides": {
    "reportGeneralTypeIssues": "error",
    "reportOptionalMemberAccess": "error",
    "reportUnknownVariableType": "warning",
    "reportUnknownMemberType": "warning",
    "reportMissingTypeStubs": "information",
    "reportUntypedFunctionDecorator": "warning",
    "reportUntypedClassDecorator": "warning",
    "reportUntypedBaseClass": "warning",
    "reportUntypedNamedTuple": "warning",
    "reportPrivateUsage": "warning",
    "reportIncompatibleMethodOverride": "error",
    "reportInvalidTypeVarUse": "error",
    "reportUnnecessaryIsInstance": "information",
    "reportUnnecessaryCast": "information",
    "reportUnsupportedDunderAll": "warning",
    "reportUnusedClass": "warning",
    "reportUnusedImport": "warning",
    "reportUnusedFunction": "warning",
    "reportUnusedVariable": "warning",
    "reportDuplicateImport": "warning",
    "reportWildcardImportFromLibrary": "warning",
    "reportConstantRedefinition": "warning",
    "reportIncompatibleVariableOverride": "error",
    "reportUninitializedInstanceVariable": "warning"
  },
  // Ruff settings
  "ruff.enable": true,
  "ruff.lint.run": "onSave",
  "ruff.organizeImports": true,
  "ruff.fixAll": true,
  "ruff.lint.args": ["--target-version=py39"],
  "ruff.lint.pycodestyle.maxLineLength": 100,
  "editor.formatOnSave": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.ruff": "explicit",
    "source.organizeImports.ruff": "explicit"
  }
}
