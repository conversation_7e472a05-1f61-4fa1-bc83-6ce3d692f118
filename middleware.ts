import {NextResponse} from 'next/server';
import type {NextRequest} from 'next/server';
import jwt, {JwtPayload} from 'jsonwebtoken';
import {decode} from 'next-auth/jwt';
import {isTokenInvalid} from '@libs/services/auth/token';

enum AppPublicRoutes {
  SignIn = '/',
  VerifyAccount = '/verify-account',
  VerifyEmail = '/verify-email',
  CheckEmail = '/check-email',
  OnboardingStart = '/onboarding',
  OnboardingEnd = '/onboarding/create-account',
  PrivacyStatement = '/privacy-statement',
  TermsOfService = '/terms-of-service',
}

function navigateLogout(request: NextRequest) {
  return NextResponse.redirect(new URL('/logout', request.url));
}

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
  // Check for maintenance mode.
  if (process.env.MAINTENANCE_MODE === 'true') {
    return NextResponse.redirect('https://tailoapp.com/maintenance')
  }

  const isPublicPath = Object.values(AppPublicRoutes).find(path => {
    return path.startsWith('/onboarding') || path == request.nextUrl.pathname;
  });

  if (isPublicPath) {
    return;
  }

  if (!process.env.SESSION_COOKIE_NAME) {
    console.error('Logging out: No SESSION_COOKIE_NAME in env');
    return navigateLogout(request);
  }
  const token = request.cookies.get(process.env.SESSION_COOKIE_NAME);

  // If no token then nothing to clear from cookies so navigate home
  if (!token) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  const decoded = await decode({
    token: token?.value,
    secret: process.env.NEXTAUTH_SECRET as string,
  });

  // If cannot decode then the cookie is invalid, navigate to logout to clear cookies
  if (!decoded) {
    console.error('Logging out: Unable to decode session token');
    return navigateLogout(request);
  }

  const decodedAccessToken = jwt.decode(decoded?.accessToken as string);

  // Is token invalid?
  if (isTokenInvalid(decodedAccessToken as JwtPayload)) {
    console.error('Logging out: JWT is invalid');
    return navigateLogout(request);
  }
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: '/((?!api|_next/static|_next/image|logout|favicon.ico|images/*).*)',
};
