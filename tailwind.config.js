/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      lineHeight: {
        '3.5': '14px',
      },
      ringWidth: {
        '3': '3px',
      },
      spacing: {
        '1.5': '6px',
      },
      zIndex: {
        nav: '999',
        'nav-app': '1000', // app navigation needs to be above other navs so that drawers aren't overlapped
        max: '9999', // drawers and modals; also used for the app navigation to allow for the drawer ( z-index: 9998) to slide underneath it
      },
      fontFamily: {
        arial: ['Arial'], // the other fonts are google fonts handled in Settings, this one is a browser font
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      dropShadow: {
        tailo: {
          button: '0 0px 10px rgba(0, 0, 0, 0.25)',
        },
      },

      colors: {
        tailo: {
          blue: {
            50: '#ECF4F8',
            100: '#D6E6F0',
            200: '#B1CFE2',
            300: '#88B6D3',
            400: '#5F9DC4',
            500: '#5F9DC4',
            600: '#33698A',
            700: '#264F68',
            800: '#1A3647',
            900: '#0C1922',
            950: '#070E13',
          },
          orange: {
            50: '#FBEEE9',
            100: '#F9DCD2',
            200: '#F6B59D',
            300: '#F68F6A',
            400: '#FC642D',
            500: '#EB4A0F',
            600: '#B63D11',
            700: '#823012',
            800: '#52210F',
            900: '#29120A',
            950: '#130A06',
          },
          green: {
            50: '#F1F8F1',
            100: '#DFF1DF',
            200: '#BBE7BB',
            300: '#98E198',
            400: '#70DB70',
            500: '#42DC42',
            600: '#2EB72E',
            700: '#278627',
            800: '#1D531D',
            900: '#112711',
            950: '#0A140A',
          },
          purple: {
            50: '#F2F1F3',
            100: '#E6E1EA',
            200: '#CEBFD9',
            300: '#B699CC',
            400: '#9E71C1',
            500: '#8745BA',
            600: '#6D329A',
            700: '#522277',
            800: '#34144D',
            900: '#1A0828',
            950: '#0E0514',
          },
          peach: {
            50: '#FCFAF8',
            100: '#FAF5EF',
            200: '#F9ECDD',
            300: '#F9E2C8',
            400: '#FED7AA',
            500: '#F2C38C',
            600: '#E4AC6D',
            700: '#CB8E48',
            800: '#9F7341',
            900: '#5A4935',
            950: '#1B1A18',
          },
        },
      },
      maxWidth: {
        desktop: '1920px',
      },
    },
  },
  plugins: [
    require('tailwind-scrollbar')({nocompatible: true}),
  ],
};
