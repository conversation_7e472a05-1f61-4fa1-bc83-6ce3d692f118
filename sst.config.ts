import {SSTConfig} from 'sst';
import {stages} from 'aws/stacks/constants/stages';
import {RdsStack} from 'aws/stacks/RdsStack';
import {ApiStack} from './aws/stacks/ApiStack';
import {App} from 'sst/constructs';
import {isProduction} from 'aws/stacks/helpers/isProduction';
import {EventBusStack} from 'aws/stacks/EventBusStack';
import {WebsocketStack} from 'aws/stacks/WebsocketStack';
import {ExtractorStack} from 'aws/stacks/ExtractorStack';
import {QueueStack} from 'aws/stacks/QueueStack';
import {isStaging} from 'aws/stacks/helpers/isStaging';
import {DocumentsApiStack} from 'aws/stacks/DocumentsAPIStack';
import {CommentsApiStack} from 'aws/stacks/CommentsAPIStack';
import {QuotasApiStack} from 'aws/stacks/QuotasAPIStack';
import {TagsApiStack} from 'aws/stacks/TagsAPIStack';
import {AuthApiStack} from 'aws/stacks/AuthAPIStack';

function isRunningLocal(app: App) {
  let res = false;

  if (app.mode === 'dev') {
    return true;
  } else if (app.mode === 'remove') {
    // if the mode is remove we need to check if the stack we are removing matches a stage
    if (!Object.values(stages).includes(app.stage)) {
      res = true;
    }
  }

  return res;
}

export default {
  config(_input) {
    return {
      name: 'tailo',
      region: 'eu-west-2',
    };
  },
  stacks(app) {
    if (!isRunningLocal(app)) {
      app.stack(RdsStack);
    }

    app.stack(EventBusStack);
    app.stack(WebsocketStack);
    app.stack(QueueStack);
    app.stack(ApiStack);
    app.stack(DocumentsApiStack);
    app.stack(CommentsApiStack);
    app.stack(QuotasApiStack);
    app.stack(TagsApiStack);
    app.stack(AuthApiStack);
    app.stack(ExtractorStack);

    app.setDefaultRemovalPolicy(
      isProduction(app) || isStaging(app) ? 'retain' : 'destroy',
    );
  },
} satisfies SSTConfig;
