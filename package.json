{"name": "tailo", "version": "0.1.0", "private": true, "scripts": {"dev": "sst bind next dev --turbo", "build": " next build", "start": "next start", "lint": "next lint", "database:start": "docker-compose -f db/local/docker-compose.yml up -d", "database:stop": "docker-compose -f db/local/docker-compose.yml stop", "migrate:create": "ts-node --skip-project db/create-migration.ts", "migrate:up": "ts-node --skip-project db/run-local.ts up", "migrate:down": "ts-node --skip-project db/run-local.ts down", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "deploy:dev": "sst deploy --stage development", "test": "sst bind vitest run", "chromatic": "npx chromatic --project-token=chpt_3d4b28a2acfb1bf", "jest": "jest", "jest:watch": "jest --watchAll --coverage", "postinstall": "patch-package", "prepare": "husky"}, "dependencies": {"@analytics/amplitude": "^0.1.3", "@aws-sdk/client-dynamodb": "^3.465.0", "@aws-sdk/client-s3": "^3.509.0", "@aws-sdk/client-secrets-manager": "^3.714.0", "@aws-sdk/lib-dynamodb": "^3.465.0", "@casl/ability": "^6.5.0", "@casl/react": "^3.1.0", "@panzoom/panzoom": "^4.5.1", "@sentry/nextjs": "^7.64.0", "@types/jsonwebtoken": "^9.0.7", "add": "^2.0.6", "analytics": "^0.8.9", "axios": "^1.5.0", "classnames": "^2.5.1", "dayjs": "^1.11.9", "eslint": "8.48.0", "format-duration": "^3.0.2", "iconsax-react": "^0.0.8", "immer": "^10.0.2", "jotai": "^2.3.1", "js-md5": "^0.8.3", "jsonwebtoken": "^9.0.2", "kysely": "^0.27.5", "loadsh": "^0.0.4", "lottie-react": "^2.4.0", "next": "13.4.19", "next-auth": "^4.23.1", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "pusher-js": "^8.4.0-rc2", "rc-slider": "^10.6.1", "react": "18.2.0", "react-bottom-drawer": "^0.1.1", "react-dom": "18.2.0", "react-icons": "^4.10.1", "react-intersection-observer": "^9.5.2", "react-modal": "^3.16.1", "react-modern-drawer": "^1.2.2", "react-multi-email": "^1.0.19", "react-select": "^5.7.4", "react-use-audio-player": "^2.2.0", "react-use-websocket": "^4.4.0", "storybook-addon-pseudo-states": "^2.1.2", "swr": "^2.2.2", "use-analytics": "^1.1.0", "usehooks-ts": "^2.9.1", "yarn": "^1.22.22"}, "devDependencies": {"@axe-core/react": "^4.8.3", "@storybook/addon-essentials": "^7.2.3", "@storybook/addon-interactions": "^7.2.3", "@storybook/addon-links": "^7.2.3", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.2.3", "@storybook/nextjs": "^7.2.3", "@storybook/react": "^7.2.3", "@storybook/testing-library": "^0.2.0", "@testing-library/jest-dom": "6.1.5", "@testing-library/react": "14.1.2", "@types/bcrypt": "^5.0.0", "@types/jest": "29.5.11", "@types/node": "20.5.9", "@types/pg": "^8.10.9", "@types/react": "18.2.25", "@types/react-dom": "18.2.7", "@types/react-modal": "^3.16.0", "@types/use-analytics": "^0.0.1", "autoprefixer": "10.4.15", "aws-cdk-lib": "2.124.0", "chromatic": "^10.9.4", "constructs": "10.3.0", "eslint-config-next": "13.4.19", "eslint-plugin-storybook": "^0.6.13", "husky": "^9.1.4", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "mysql2": "^3.5.2", "pg": "^8.11.3", "postcss": "8.4.29", "postcss-import": "^15.1.0", "sst": "2.40.6", "storybook": "^7.2.3", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "3.3.3", "ts-node": "10.9.1", "typescript": "5.2.2", "vitest": "^0.34.6"}, "resolutions": {"string-width": "4.2.3", "wrap-ansi": "7.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}