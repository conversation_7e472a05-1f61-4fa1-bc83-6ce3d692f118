#!/bin/sh

LAUNCH_DIR=$(pwd)
CLI_ENV="$LAUNCH_DIR/.cli-env"

if [ ! -f "$CLI_ENV" ]; then
    echo "File not found! $CLI_ENV"
    echo "Expected AWS_PROFILE to be set in $CLI_ENV"
    exit 1
fi

source "$CLI_ENV"

if [ -z "${AWS_PROFILE}" ]; then
    echo "Environment variable not set in $CLI_ENV"
    exit 1
fi

SCRIPT_DIR=$(dirname "$0")

sh "${SCRIPT_DIR}/lib/invenv.sh"

if [ ! $? == 0 ]; then
    # Not in a venv
    exit 1
fi

sh "${SCRIPT_DIR}/lib/loggedin.sh"

if [ ! $? == 0 ]; then
    # Log in failed
    exit 1
fi

read -p "Press enter to continue"

npx sst dev
