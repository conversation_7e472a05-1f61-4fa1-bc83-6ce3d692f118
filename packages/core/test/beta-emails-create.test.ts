// import { expect, test } from "vitest";
// import axios, {AxiosError} from 'axios';
// import {getTestAdminAccessToken } from "./helpers";


// const postBetaEmails = async (emails: string[], accessToken: string) => {
//   return await axios({
//     method: 'post',
//     headers: {
//       'Authorization': 'Bearer ' + accessToken,
//     },
//     url: process.env.NEXT_PUBLIC_API_URL + '/admin/beta-emails',
//     data: {
//       emails,
//     },
//   })
//     .then((res) => {
      
//       return res.data
//     })
//     .catch((e: Error | AxiosError) => {
//       console.log('Failed to gain access:', e);
//       throw new Error('Failed to gain access');
//     });
// }

// const deleteEmail = async (email: string, accessToken: string) => {
//   return await axios({
//     method: 'delete',
//     headers: {
//       'Authorization': 'Bearer ' + accessToken,
//     },
//     url: process.env.NEXT_PUBLIC_API_URL + '/admin/beta-emails',
//     data: {
//       email,
//     },
//   })
//     .then((res) => {
//       console.log(`deleted ${email}`)
//       console.log(res)
//       return res.data
//     })
//     .catch((e: Error | AxiosError) => {
//       console.log('Failed to gain access:', e);
//       throw new Error('Failed to gain access');
//     });
// }

// test("Adding just brand new valid beta emails emails", async () => {
//   const accessToken = await getTestAdminAccessToken();
//   const emails = ['<EMAIL>', '<EMAIL>'];
//   const res = await postBetaEmails(emails, accessToken);
//   expect(res.message).toEqual('OK');
//   expect(res.data.added_emails).toEqual(emails);

//   // Cleanup
//   /**
//    * ISSUE, beta emails are soft deleted, how do we clean up? Param to delete handler to force hard delete?
//    */
//   // for (const email of emails) {
//   //   await deleteEmail(email, accessToken);
//   // }
// });

// test("Adding a mix of new valid and invalid beta emails", async () => {
//   const accessToken = await getTestAdminAccessToken();
//   const emails = [
//     '<EMAIL>',
//     '<EMAIL>',
//     'notanemail'
//   ];
//   const res = await postBetaEmails(emails, accessToken);
//   expect(res.message).toEqual('OK');
//   expect(res.data.invalid_emails).toEqual(['notanemail']);

//   // Cleanup

// });

// test("Adding a mix of new valid and duplicate valid beta emails", async () => {
//   const accessToken = await getTestAdminAccessToken();
//   const emails = ['<EMAIL>', '<EMAIL>'];
//   await postBetaEmails(emails, accessToken);

//   // send in mix of dupe and new valid emails
//   const emails2 = [
//     '<EMAIL>',
//     '<EMAIL>',
//     '<EMAIL>'
//   ];
//   const res = await postBetaEmails(emails2, accessToken);
//   expect(res.message).toEqual('OK');
//   expect(res.data.duplicate_emails).toEqual(['<EMAIL>', '<EMAIL>']);

//   // Cleanup

// });

// test("Try to send beta emails not as type array", async () => {
//   const accessToken = await getTestAdminAccessToken();
//   const emails = '<EMAIL>';
//   const res = await postBetaEmails(emails, accessToken);
//   expect(res.message).toEqual('Emails must be an array');
//   // Cleanup

// });

// test("Try to send beta emails as empty array", async () => {
//   const accessToken = await getTestAdminAccessToken();
//   const emails = [];
//   const res = await postBetaEmails(emails, accessToken);
//   expect(res.message).toEqual('Emails array must contain at least 1 email');
//   // Cleanup

// });

// test("All beta emails in array are duplicates", async () => {
//   const accessToken = await getTestAdminAccessToken();
//   const emails = ['<EMAIL>', '<EMAIL>'];
//   await postBetaEmails(emails, accessToken);

//   // send in all dupes
//   const res = await postBetaEmails(emails, accessToken);
//   expect(res.message).toEqual('All emails in array are duplicates');
//   // Cleanup
  
// });

// test("All beta emails in array are invalid", async () => {
//   const accessToken = await getTestAdminAccessToken();
//   const emails = ['imnotanemail', 'testytestington.gmail.com'];
//   const res = await postBetaEmails(emails, accessToken);
//   expect(res.message).toEqual('All emails in array are invalid');
//   // Cleanup

// });
