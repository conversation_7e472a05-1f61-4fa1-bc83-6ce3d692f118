// import axios, {AxiosError, AxiosResponse} from 'axios';
// import { IUserSignInData, IUserData } from "@libs/services/user/types";

// export const testUserRegister = async () => {
//   return axios({
//     method: 'post',
//     url: process.env.NEXT_PUBLIC_API_URL + '/auth/magic-link/email',
//     data: {
//       email: "<EMAIL>",
//       tos_accepted: true,
//       registration: true
//     },
//   })
//     .then((res: AxiosResponse<IUserSignInData>) => {
//       return res.data
//     })
//     .catch((e: Error | AxiosError) => {
//       console.log('Failed to gain access:', e);
//       throw new Error('Failed to gain access');
//     });
// }

// export const userAuthenticate = async (ticket: String) => {
//   return axios({
//     method: 'post',
//     url: process.env.NEXT_PUBLIC_API_URL + '/auth/magic-link/auth',
//     data: {
//       ticket,
//     },
//   })
//     .then((res: AxiosResponse<IUserData>) => {
//         return res.data
//     })
//     .catch((e: Error | AxiosError) => {
//       console.log('Failed to gain access:', e);
//       throw new Error('Failed to gain access');
//     });
// }

// export const getTestAdminAccessToken = async () => {
//   const registerRes = await testUserRegister();
//   const magic_link = registerRes.data.magic_link_url;
//   const magicLinkTicket = magic_link.split('ticket=')[1].split('&')[0];
//   const authRes = await userAuthenticate(magicLinkTicket);
//   return authRes.data.auth.access_token;
// }