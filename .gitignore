# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
/tests/headings/pdfs/*.pdf
/tests/headings/pdfs/*/
/tests/headings/test_data/*/
/tests/headings/test_data/*.xlsx

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
*.pub

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# sst
.sst
.open-next

# python
venv/
__pycache__/

.env
# Sentry Auth Token
.sentryclirc
/.venv
.cli-env
/.yarn
