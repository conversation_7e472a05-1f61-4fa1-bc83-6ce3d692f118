import os
import shutil
import json
import time
import concurrent.futures
from typing import List
from aws_controller.validate_bucket import update_bucket  # type: ignore
from aws_controller.textract import (
    get_job_status,
    start_textract_process,
    get_textract_blocks,
)

HEADINGS_FILE_DIR = os.path.dirname(os.path.realpath(__file__)) + "/headings/"
PDF_PATH = f"{HEADINGS_FILE_DIR}/pdfs/"
TEXTRACT_PATH = f"{HEADINGS_FILE_DIR}/textract/"
TESTDATA_PATH = f"{HEADINGS_FILE_DIR}/test_data/"
STATUS_MESSAGE = "Extracted {0} out of {1} documents"

processed_count = 0
doc_count = 0


def need_textract(all_pdfs: List[str]):
    test_data_folders = list(
        filter(
            lambda item: os.path.isdir(f"{TESTDATA_PATH}/{item}"),
            os.listdir(TESTDATA_PATH),
        )
    )
    return list(
        filter(lambda item: item.replace(".pdf", "") not in test_data_folders, all_pdfs)
    )


def textract_document(pdf: str):
    job_id = start_textract_process(pdf)
    job_status = get_job_status(job_id)

    while job_status == "IN_PROGRESS":
        time.sleep(2)
        job_status = get_job_status(job_id)

    blocks = get_textract_blocks(job_id)

    test_data_path = f"{TESTDATA_PATH}/{pdf.replace('.pdf', '')}"
    if not os.path.exists(test_data_path):
        os.makedirs(test_data_path)
        shutil.copy(f"{PDF_PATH}/{pdf}", f"{test_data_path}/file.pdf")

    with open(f"{test_data_path}/textract.json", "w") as w:
        w.write(json.dumps(blocks))

    print(f"Completed: {pdf}")


def start_textract(all_pdfs: List[str]):
    if len(all_pdfs) == 0:
        print("Nothing to run textract on")
        return

    print("Running textract on:")
    for pdf in all_pdfs:
        print(f"\t{pdf}")
        pass

    pool = concurrent.futures.ThreadPoolExecutor(max_workers=len(all_pdfs))

    for pdf in all_pdfs:
        pool.submit(textract_document, pdf)

    pool.shutdown(wait=True)


def main():
    if not os.path.exists(TESTDATA_PATH):
        os.makedirs(TESTDATA_PATH)

    bucket_pdfs = update_bucket(PDF_PATH)

    files_to_textract = need_textract(bucket_pdfs)

    start_textract(files_to_textract)


if __name__ == "__main__":
    main()
