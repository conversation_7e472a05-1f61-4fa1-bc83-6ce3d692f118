import os
from openpyxl.worksheet.datavalidation import DataValidation  # type: ignore
from openpyxl import Workbook  # type: ignore
from openpyxl.styles import Font  # type: ignore

PDF_FOLDER = os.path.dirname(os.path.realpath(__file__)) + "/headings/pdfs"
TESTDATA_FOLDER = os.path.dirname(os.path.realpath(__file__)) + "/headings/test_data"

title_font = Font(size="20", bold=True)
tester_heading_font = Font(size="16", bold=True)
tester_font = Font(size="16")
table_heading_font = Font(size="14", bold=True)


def generate_workbook(sub_folder: str):
    path = f"{PDF_FOLDER}/{sub_folder}/"
    workbook = Workbook()
    all_files = os.listdir(path)
    all_pdfs = list(filter(lambda file: file.endswith(".pdf"), all_files))

    # Create datavaliudation
    tag_sheet = workbook.create_sheet(title="Tags")
    for number in range(1, 7):
        tag_sheet[f"A{number}"] = f"H{number}"

    status_sheet = workbook.create_sheet(title="Statuses")
    status_sheet.cell(1, 1, "To Do")
    status_sheet.cell(2, 1, "In Progress")
    status_sheet.cell(3, 1, "Completed")

    for filename in all_pdfs:
        sheet = workbook.create_sheet(filename[0:30])
        tag_data_validation = DataValidation(
            type="list",
            formula1="=Tags!$A$1:$A$6",
        )
        sheet.add_data_validation(tag_data_validation)

        status_data_validation = DataValidation(
            type="list", formula1="=Statuses!$A$1:$A$3"
        )
        sheet.add_data_validation(status_data_validation)

        # Doc title
        sheet.cell(1, 1, filename).font = title_font

        # Tester info
        sheet.cell(2, 1, "Tester").font = tester_heading_font
        sheet.cell(3, 1, "Status").font = tester_heading_font

        sheet.cell(2, 2).font = tester_font
        sheet.cell(3, 2, "To Do").font = tester_font

        status_data_validation.add(sheet.cell(3, 2))

        # Results Table
        sheet.cell(5, 1, "Title").font = table_heading_font
        sheet.cell(5, 2, "Tag").font = table_heading_font

        for i in range(6, 50):  # Why 50? No idea! Banter
            tag_data_validation.add(sheet.cell(i, 2))

    workbook.remove(workbook.worksheets[0])
    workbook.move_sheet(workbook.worksheets[0], len(workbook.worksheets))
    workbook.move_sheet(workbook.worksheets[0], len(workbook.worksheets))
    workbook.save(f"{PDF_FOLDER}/{sub_folder}/{sub_folder.lower()}.xlsx")
    print(f"Written to {PDF_FOLDER}/{sub_folder}/{sub_folder.lower()}.xlsx")


def main():
    sub_folders = list(
        filter(
            lambda item: os.path.isdir(f"{PDF_FOLDER}/{item}"), os.listdir(PDF_FOLDER)
        )
    )

    for sub_folder in sub_folders:
        generate_workbook(sub_folder)


if __name__ == "__main__":
    main()
