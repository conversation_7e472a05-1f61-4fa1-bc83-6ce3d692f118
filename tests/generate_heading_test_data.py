import os
import shutil
import random
import json
import time
import concurrent.futures
from typing import List
from aws_controller.s3 import upload_to_bucket
from aws_controller.textract import (
    get_job_status,
    start_textract_process,
    get_textract_blocks,
)

HEADINGS_FILE_DIR = os.path.dirname(os.path.realpath(__file__)) + "/headings/"
PDF_PATH = f"{HEADINGS_FILE_DIR}/pdfs/"
TEXTRACT_PATH = f"{HEADINGS_FILE_DIR}/textract/"
TESTDATA_PATH = f"{HEADINGS_FILE_DIR}/test_data/"
STATUS_MESSAGE = "Extracted {0} out of {1} documents"

processed_count = 0
doc_count = 0


def need_textract(all_pdfs: List[str], sub_folder: str):
    test_data_folders = list(
        filter(
            lambda item: os.path.isdir(f"{TESTDATA_PATH}/{sub_folder}/{item}"),
            os.listdir(f"{TESTDATA_PATH}/{sub_folder}"),
        )
    )
    return list(
        filter(lambda item: item.replace(".pdf", "") not in test_data_folders, all_pdfs)
    )


def textract_document(pdf: str, sub_folder: str):
    test_data_path = f"{TESTDATA_PATH}/{sub_folder}/{pdf.replace('.pdf', '')}"

    job_id = start_textract_process(pdf)
    job_status = get_job_status(job_id)

    while job_status == "IN_PROGRESS":
        time.sleep(2)
        job_status = get_job_status(job_id)

    blocks = get_textract_blocks(job_id)

    if not os.path.exists(test_data_path):
        os.makedirs(test_data_path)

    shutil.copy(f"{PDF_PATH}/{sub_folder}/{pdf}", f"{test_data_path}/file.pdf")

    with open(f"{test_data_path}/textract.json", "w") as w:
        w.write(json.dumps(blocks))

    print(f"Completed: {pdf}")


def start_textract(all_pdfs: List[str], sub_folder: str):
    if not os.path.exists(f"{PDF_PATH}/{sub_folder}"):
        os.makedirs(f"{PDF_PATH}/{sub_folder}")

    if len(all_pdfs) == 0:
        print(f"Nothing to run in '{sub_folder}'")
        return

    print("Running textract on:")
    for pdf in all_pdfs:
        print(f"\t{pdf}")

    with concurrent.futures.ThreadPoolExecutor(max_workers=len(all_pdfs)) as pool:
        futures = [pool.submit(textract_document, pdf, sub_folder) for pdf in all_pdfs]

    concurrent.futures.wait(futures)


def main():
    if not os.path.exists(TESTDATA_PATH):
        os.makedirs(TESTDATA_PATH)

    sub_folders = list(
        filter(lambda item: os.path.isdir(f"{PDF_PATH}/{item}"), os.listdir(PDF_PATH))
    )

    for sub_folder in sub_folders:
        if not os.path.exists(f"{TESTDATA_PATH}/{sub_folder}"):
            os.makedirs(f"{TESTDATA_PATH}/{sub_folder}")
        bucket_pdfs = upload_to_bucket(f"{PDF_PATH}/{sub_folder}")

        files_to_textract = need_textract(bucket_pdfs, sub_folder)

        start_textract(files_to_textract, sub_folder)


if __name__ == "__main__":
    main()
