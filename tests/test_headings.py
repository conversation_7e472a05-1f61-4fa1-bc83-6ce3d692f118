import pytest  # type: ignore
import json
import os
from typing import Dict, List
from openpyxl import load_workbook  # type: ignore
from extractor import PDFExtractor
from dataclasses import dataclass
import warnings

warnings.simplefilter(action="ignore", category=UserWarning)

DIR_PATH = os.path.dirname(os.path.realpath(__file__))
DATA_PATH = f"{DIR_PATH}/headings/test_data/"


@dataclass
class HeadingItem:
    text: str
    tag: str


all_xlsx_files = [
    f"{DATA_PATH}/{sub_folder}/{sub_folder.lower()}.xlsx"
    for sub_folder in os.listdir(DATA_PATH)
    if os.path.isdir(f"{DATA_PATH}/{sub_folder}")
    and os.path.exists(f"{DATA_PATH}/{sub_folder}/{sub_folder.lower()}.xlsx")
]


def get_xlsx_data(path: str) -> Dict[str, List[HeadingItem]]:
    ret_val: Dict[str, List[HeadingItem]] = {}
    wb = load_workbook(path, read_only=True)

    first_title_idx = 6
    for sheet in wb.worksheets:
        if sheet.title == "Tags" or sheet.cell(first_title_idx, 1).value is None:
            continue

        running = True
        idx = first_title_idx

        filename = sheet["A1"].value
        data: List[HeadingItem] = []

        while running:
            if sheet.cell(idx, 1).value is None:
                running = False
                break
            data.append(
                HeadingItem(
                    text=sheet[f"A{idx}"].value,
                    tag=sheet[f"B{idx}"].value,
                )
            )
            idx += 1

        ret_val[filename] = data

    return ret_val


test_data = {
    "/".join(path.split("/")[:-1]): get_xlsx_data(path) for path in all_xlsx_files
}


def get_textract_json(path: str) -> Dict:
    return json.loads(open(path).read())


def extract_toc(items: List) -> List[HeadingItem]:
    ret_list: List[HeadingItem] = []
    for item in items:
        heading_item = HeadingItem(item.title, item.tag.upper())
        ret_list.append(heading_item)

        if len(item.sections) > 0:
            sub_headings = extract_toc(item.sections)
            ret_list.extend(sub_headings)

    return ret_list


@pytest.mark.parametrize(
    "key",
    (key for key in test_data.keys()),
    ids=[sub_folder for sub_folder in test_data.keys()],
)
def test_toc(key: str):
    files = test_data[key]
    for file in files:
        filename = file.replace(".pdf", "")
        textract_data = get_textract_json(f"{key}/{filename}/textract.json")
        expected_headings = test_data[key][file]

        extractor = PDFExtractor(
            stream=open(f"{key}/{filename}/file.pdf", "rb").read(),
            textract=textract_data,
        )

        extracted_data = extractor.get_extracted_data()

        extracted_headings = extract_toc(extracted_data.details.toc)

        # Object comparitor
        assert extracted_headings == expected_headings
