import time
from . import BUCKET_NAME, TEXTRACT_CLIENT as CLIENT


def get_textract_blocks(job_id: str):
    textract_response = CLIENT.get_document_analysis(JobId=job_id)

    blocks = []

    blocks.extend(textract_response["Blocks"])

    next_token = (
        textract_response["NextToken"] if "NextToken" in textract_response else None
    )

    while next_token:
        next_response = CLIENT.get_document_analysis(JobId=job_id, NextToken=next_token)
        blocks.extend(next_response["Blocks"])
        next_token = (
            next_response["NextToken"] if "NextToken" in next_response else None
        )

    return blocks


def get_job_status(job_id: str):
    textract_response = CLIENT.get_document_analysis(JobId=job_id)

    return textract_response["JobStatus"]


def start_textract_process(pdf: str):
    result = CLIENT.start_document_analysis(
        DocumentLocation={
            "S3Object": {
                "Bucket": BUCKET_NAME,
                "Name": pdf,
            }
        },
        FeatureTypes=["LAYOUT"],
    )

    job_id = result["JobId"]

    return job_id
