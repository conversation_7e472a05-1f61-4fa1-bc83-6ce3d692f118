import os
from typing import List
from . import BUCKET_NAME, S3_CLIENT as <PERSON>LIENT


def get_s3_pdfs() -> List[str]:
    s3_data = CLIENT.list_objects(Bucket=BUCKET_NAME)

    s3_pdfs = []
    if "Contents" in s3_data:
        for item in s3_data["Contents"]:
            s3_pdfs.append(item["Key"])

    return s3_pdfs


def upload_to_bucket(pdf_path: str) -> List[str]:
    all_pdfs = list(filter(lambda item: item.endswith(".pdf"), os.listdir(pdf_path)))

    s3_pdfs = get_s3_pdfs()

    upload_pdfs = list(filter(lambda item: item not in s3_pdfs, all_pdfs))

    if len(upload_pdfs) == 0:
        return all_pdfs

    for pdf in upload_pdfs:
        CLIENT.put_object(
            Bucket=BUCKET_NAME,
            Key=pdf,
            Body=open(f"{pdf_path}/{pdf}", "rb").read(),
        )

    return all_pdfs
