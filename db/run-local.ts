import * as path from 'path';
import {promises as fs} from 'fs';
import {createPool} from 'mysql2';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  MysqlDialect,
  PostgresDialect,
  FileMigrationProvider,
  MigrationResult,
} from 'kysely';
import {Pool} from 'pg';
import * as dotenv from 'dotenv';

async function runMigrate(down: boolean = false, count: number) {
  const migrationFolder = path.join(__dirname, 'migrations');

  // Load in the .env file
  dotenv.config();
  if (
    !process.env.DATABASE_HOST ||
    !process.env.DATABASE_PORT ||
    !process.env.DATABASE_USER ||
    !process.env.DATABASE_NAME
  ) {
    console.error(
      'Error: DATABASE_HOST, DATABASE_PORT, DATABASE_USER, and DATABASE_NAME need to be defined in .env',
    );
    return;
  }

  const db = new Kysely<any>({
    dialect: new PostgresDialect({
      pool: new Pool({
        host: process.env.DATABASE_HOST,
        port: parseInt(process.env.DATABASE_PORT),
        database: process.env.DATABASE_NAME,
        user: process.env.DATABASE_USER,
      }),
    }),
  });

  const migrator = new Migrator({
    db,
    provider: new FileMigrationProvider({
      fs,
      path,
      migrationFolder: migrationFolder,
    }),
  });

  const migrate_count = count
    ? count
    : (await migrator.getMigrations()).filter(
        migration => !migration.executedAt,
      ).length;

  console.log(migrate_count);

  if (down) {
    for (let i = 0; i < migrate_count; i++) {
      const {error, results} = await migrator.migrateDown();

      if (error || !results) {
        console.error('failed to rollback');
        console.error(error);
        process.exit(1);
      }

      results.forEach(it => {
        if (it.status === 'Success') {
          console.log(
            `rollback "${it.migrationName}" was executed successfully`,
          );
        } else if (it.status === 'Error') {
          console.error(`failed to execute rollback "${it.migrationName}"`);
        }
      });
    }
  } else {
    for (let i = 0; i < migrate_count; i++) {
      const {error, results} = await migrator.migrateUp();

      if (error || !results) {
        console.error('failed to migrate');
        console.error(error);
        process.exit(1);
      }

      results.forEach(it => {
        if (it.status === 'Success') {
          console.log(
            `migration "${it.migrationName}" was executed successfully`,
          );
        } else if (it.status === 'Error') {
          console.error(`failed to execute migration "${it.migrationName}"`);
        }
      });
    }
  }

  await db.destroy();
}

runMigrate(process.argv[2] === 'down', Number(process.argv[3]));
