import {promises as fs} from 'fs';

async function createValidName(name: string) {
  return name
    .replace(/[A-Z][a-z]*/g, str => '_' + str.toLowerCase() + '_')
    .replace(/-/g, '_')
    .replace(/__/g, '_')
    .replace(/(^_)|(_$)/g, '');
}

async function getTimestamp() {
  const time = new Date();

  const month = (time.getMonth() + 1).toLocaleString('en-GB', {
    minimumIntegerDigits: 2,
    useGrouping: false,
  });

  const date = time
    .getDate()
    .toLocaleString('en-GB', {minimumIntegerDigits: 2, useGrouping: false});

  const hour = time
    .getHours()
    .toLocaleString('en-GB', {minimumIntegerDigits: 2, useGrouping: false});

  const minute = time
    .getMinutes()
    .toLocaleString('en-GB', {minimumIntegerDigits: 2, useGrouping: false});

  return `${time.getFullYear()}${month}${date}${hour}${minute}`;
}

async function createMigration(name: string | undefined) {
  if (!name || name.trim() == '') {
    console.error('ERROR: No name specified');
    return;
  }

  const valid_name = await createValidName(name);
  const timestamp = await getTimestamp();
  const filename = `${timestamp}_${valid_name}.js`;

  const sampleText = (await fs.readFile('db/sample_migration.js')).toString();

  const fullPath = `db/migrations/${filename}`;

  fs.writeFile(fullPath, sampleText);

  console.log(`File created: ${fullPath}`);
}
createMigration(process.argv[2]);
