const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  await db.schema
    .alterTable('documents')
    .addColumn('hash', 'varchar(255)')
    .addColumn('filesize', 'bigint') // Using bigint for future proofing, was originally integer but that only allowed up to 2 gig
    .execute();

  await db.schema
    .createIndex('document_hash_filesize_index')
    .on('documents')
    .columns(['hash', 'filesize'])
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  await db.schema
    .dropIndex('document_hash_filesize_index')
    .ifExists()
    .execute();

  await db.schema
    .alterTable('documents')
    .dropColumn('hash')
    .dropColumn('filesize')
    .execute();
}

module.exports = {up, down};
