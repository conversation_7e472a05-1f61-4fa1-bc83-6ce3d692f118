const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  await db.schema
    .alterTable('taggables')
    .addUniqueConstraint('unique_taggable_item', [
      'tag_id',
      'taggable_id',
      'taggable_type',
    ])
    .execute();

  await db.schema
    .alterTable('tags')
    .addUniqueConstraint('unique_user_tag_name', ['owner_user_id', 'name'])
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  await db.schema
    .alterTable('taggables')
    .dropConstraint('unique_taggable_item')
    .ifExists()
    .execute();

  await db.schema
    .alterTable('tags')
    .dropConstraint('unique_user_tag_name')
    .ifExists()
    .execute();
}

module.exports = {up, down};
