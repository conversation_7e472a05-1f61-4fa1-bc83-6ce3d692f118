const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  await db.schema
    .alterTable('documents')
    .addColumn('extracted_title', 'varchar(255)')
    .addColumn('display_name', 'varchar(255)')
    .addColumn('meta', 'json')
    .execute();

  // * Can't do `renameColumn` in same schema as adding columns
  await db.schema
    .alterTable('documents')
    .renameColumn('name', 'filename')
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  await db.schema
    .alterTable('documents')
    .dropColumn('extracted_title')
    .dropColumn('display_name')
    .dropColumn('meta')
    .execute();

  await db.schema
    .alterTable('documents')
    .renameColumn('filename', 'name')
    .execute();
}

module.exports = {up, down};
