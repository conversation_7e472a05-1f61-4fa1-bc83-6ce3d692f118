const uuid = require('uuid');
const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  const voices = [
    {
      name: '<PERSON>',
      external_id: '<PERSON>',
      external_provider: 'aws_polly',
    },
  ];

  db.insertInto('tts_voices')
    .values(
      voices.map(voice => ({
        uuid: uuid.v4(),
        name: voice.name,
        external_id: voice.external_id,
        external_provider: voice.external_provider,
      })),
    )
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  db.deleteFrom('tts_voices')
    .where('external_id', '=', 'Brian')
    .executeTakeFirst();
}

module.exports = {up, down};
