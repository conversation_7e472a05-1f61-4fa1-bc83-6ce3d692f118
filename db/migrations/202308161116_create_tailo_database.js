const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // users table
  await db.schema
    .createTable('users')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('estendio_id', 'varchar(255)', col => col.unique()) // has to be varchar for uuids, storing as binary is a pain
    .addColumn('role', 'varchar(255)', col => col.defaultTo(null))
    .addColumn('created_at', 'timestamp', col => col.notNull())
    .addColumn('modified_at', 'timestamp')
    .addColumn('deleted_at', 'timestamp')
    .execute();

  await db.schema
    .createIndex('user_estendio_id_index')
    .on('users')
    .column('estendio_id')
    .execute();

  await db.schema
    .createTable('roles')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('name', 'varchar(255)', col => col.notNull().unique())
    .addColumn('definition', 'json', col => col.notNull())
    .execute();

  await db.schema
    .createTable('user_roles')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('user_id', 'bigint', col => col.notNull())
    .addColumn('role_id', 'bigint', col => col.notNull())
    .addForeignKeyConstraint(
      'user_role_user_fkey',
      ['user_id'],
      'users',
      ['id'],
      cf => cf.onDelete('cascade'),
    )
    .addForeignKeyConstraint(
      'user_role_role_fkey',
      ['role_id'],
      'roles',
      ['id'],
      cf => cf.onDelete('cascade'),
    )
    .execute();

  await db.schema
    .createTable('documents')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'varchar(255)', col => col.unique().notNull())
    .addColumn('owner_user_id', 'bigint', col => col.notNull())
    .addColumn('name', 'varchar(255)', col => col.notNull())
    .addColumn('type', 'varchar(255)', col => col.notNull())
    .addColumn('path', 'text', col => col.notNull())
    .addColumn('extracted_path', 'text')
    .addColumn('status', 'varchar(255)', col => col.notNull())
    .addColumn('status_message', 'json', col => col.defaultTo(null)) // values of the form used to make summarisation
    .addColumn('date_created', 'timestamp')
    .addColumn('date_modified', 'timestamp')
    .addColumn('date_deleted', 'timestamp')
    .addForeignKeyConstraint(
      'documents_user_id_fkey',
      ['owner_user_id'],
      'users',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .execute();

  await db.schema
    .createTable('document_users')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('document_id', 'bigint', col => col.notNull())
    .addColumn('user_id', 'bigint', col => col.notNull())
    .addForeignKeyConstraint(
      'documents_users_user_id_fkey',
      ['user_id'],
      'users',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .addForeignKeyConstraint(
      'documents_users_document_id_fkey',
      ['document_id'],
      'documents',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .execute();

  await db.schema
    .createIndex('document_users_document_id_user_id_index')
    .on('document_users')
    .column('user_id')
    .column('document_id')
    .execute();

  await db.schema
    .createTable('summarisations')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'varchar(255)', col => col.unique().notNull())
    .addColumn('name', 'varchar(255)', col => col.notNull())
    .addColumn('document_id', 'bigint', col => col.notNull())
    .addColumn('type', 'varchar(255)')
    .addColumn('path', 'text')
    .addColumn('prompt', 'text', col => col.notNull())
    .addColumn('status', 'varchar(255)', col => col.notNull())
    .addColumn('form_fields', 'text', col => col.notNull()) // values of the form used to make summarisation
    .addColumn('meta', 'text') // to hold information such as cost and token usage
    .addColumn('status_message', 'json', col => col.defaultTo(null))
    .addColumn('date_created', 'timestamp')
    .addColumn('date_modified', 'timestamp')
    .addColumn('date_deleted', 'timestamp')
    .addForeignKeyConstraint(
      'summarisations_documents_id_fkey',
      ['document_id'],
      'documents',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .execute();

  // Seeding
  // JSON has to be inserted with new json function courtesy of kyselys
  // not so great implementation of json and PostgreSQL
  // inspiration https://github.com/kysely-org/kysely/issues/209
  await db
    .insertInto('roles')
    .values([
      {
        name: 'access-only',
        definition: json({permissions: []}),
      },
      {
        name: 'general-user',
        definition: json({
          permissions: [
            {action: 'read', subject: ['dashboard', 'document', 'account']},
          ],
        }),
      },
      {
        name: 'guest-user',
        definition: json({
          permissions: [{action: 'read', subject: ['dashboard', 'document']}],
        }),
      },
      {
        name: 'admin-user',
        definition: json({
          permissions: [
            {
              action: 'read',
              subject: ['dashboard', 'document', 'admin', 'account'],
            },
            {
              action: 'write',
              subject: ['dashboard', 'document', 'admin', 'account'],
            },
          ],
        }),
      },
    ])
    .execute();
}

/**
 * Because Kysely decides it doesn't want to make json insertion into Postgres easy
 * @param {object} obj
 * @returns json cast of object
 */
function json(obj) {
  return kysely.sql`cast (${JSON.stringify(obj)} as json)`;
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  db.schema.dropTable('verification_token').ifExists().execute();
  db.schema.dropTable('user_roles').ifExists().execute();
  db.schema.dropTable('roles').ifExists().execute();
  db.schema.dropTable('document_users').ifExists().execute();
  db.schema.dropTable('summarisations').ifExists().execute();
  db.schema.dropTable('documents').ifExists().execute();
  db.schema.dropTable('users').ifExists().execute();
}

module.exports = {up, down};
