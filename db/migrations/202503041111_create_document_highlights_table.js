const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  db.schema
    .createTable('document_highlights')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'varchar(255)', col => col.unique().notNull())
    .addColumn('document_id', 'bigint', col => col.notNull())
    .addColumn('owner_user_id', 'bigint', col => col.notNull())
    .addColumn('meta', 'json', col => col.notNull())
    .addColumn('date_created', 'timestamp')
    .addColumn('date_modified', 'timestamp')
    .addForeignKeyConstraint(
      'document_highlights_document_id_fkey',
      ['document_id'],
      'documents',
      ['id'],
    )
    .addForeignKeyConstraint(
      'document_highlights_user_id_fkey',
      ['owner_user_id'],
      'users',
      ['id'],
    )
    .execute();

  db.schema
    .createIndex('document_highlights_document_id')
    .on('document_highlights')
    .column('document_id')
    .execute();

  db.schema
    .createIndex('document_highlights_owner_user_id')
    .on('document_highlights')
    .column('owner_user_id')
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  db.schema.dropIndex('document_highlights_document_id_fkey').ifExists().execute();
  db.schema.dropIndex('document_highlights_user_id_fkey').ifExists().execute();
  db.schema.dropTable('document_highlights').ifExists().execute();
}

module.exports = {up, down};
