const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  await db.schema
    .alterTable('documents')
    .addColumn('page_count', 'integer')
    .execute();

  await db.schema
    .createIndex('documents_created_at_index')
    .on('documents')
    .columns(['date_created'])
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  await db.schema.dropIndex('documents_created_at_index').ifExists().execute();

  await db.schema.alterTable('documents').dropColumn('page_count').execute();
}

module.exports = {up, down};
