const uuid = require('uuid');
const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  db.schema
    .createTable('tts_voices')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'varchar(255)', col => col.unique().notNull())
    .addColumn('name', 'varchar(255)', col => col.notNull())
    .addColumn('external_id', 'varchar(255)', col => col.notNull())
    .addColumn('external_provider', 'varchar(255)', col => col.notNull())
    .addColumn('date_created', 'timestamp')
    .addColumn('date_modified', 'timestamp')
    .addColumn('date_deleted', 'timestamp')
    .execute();

  const voices = [
    {
      name: '<PERSON>',
      external_id: '<PERSON>',
      external_provider: 'aws_polly',
    },
  ];

  db.insertInto('tts_voices')
    .values(
      voices.map(voice => ({
        uuid: uuid.v4(),
        name: voice.name,
        external_id: voice.external_id,
        external_provider: voice.external_provider,
      })),
    )
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  db.schema.dropTable('tts_voices').ifExists().execute();
}

module.exports = {up, down};
