const { sql } = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  await db.schema
    .createTable('comment_threads')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'varchar(255)', col => col.notNull().unique())
    .addColumn('threadable_id', 'integer', col => col.notNull())
    .addColumn('threadable_type', 'varchar(255)', col => col.notNull())
    .addColumn('meta', 'json', col => col.notNull())
    .addColumn('created_at', 'timestamp', col => col.notNull().defaultTo(sql`now()`))
    .addColumn('updated_at', 'timestamp', col => col.notNull().defaultTo(sql`now()`))
    .addColumn('deleted_at', 'timestamp')
    .execute();

  await db.schema
    .createIndex('comment_threads_uuid_index')
    .on('comment_threads')
    .column('uuid')
    .execute();

  await db.schema
    .createIndex('comment_threads_threadable_id_index')
    .on('comment_threads')
    .column('threadable_id')
    .execute();

  await db.schema
    .createTable('comments')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'varchar(255)', col => col.notNull().unique())
    .addColumn('thread_id', 'integer', col => col.references('comment_threads.id').notNull())
    .addColumn('owner_user_id', 'integer', col => col.notNull())
    .addColumn('text', 'text', col => col.notNull())
    .addColumn('created_at', 'timestamp', col => col.notNull().defaultTo(sql`now()`))
    .addColumn('updated_at', 'timestamp', col => col.notNull().defaultTo(sql`now()`))
    .addColumn('deleted_at', 'timestamp')
    .execute();

  await db.schema
    .createIndex('comments_uuid_index')
    .on('comments')
    .column('uuid')
    .execute();

  await db.schema
    .createIndex('comments_thread_id_index')
    .on('comments')
    .column('thread_id')
    .execute();

  await db.schema
    .createIndex('comments_owner_user_id_index')
    .on('comments')
    .column('owner_user_id')
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  await db.schema.dropIndex('comments_owner_user_id_index').execute();
  await db.schema.dropIndex('comments_thread_id_index').execute();
  await db.schema.dropIndex('comments_uuid_index').execute();
  await db.schema.dropTable('comments').execute();
  await db.schema.dropIndex('comment_threads_threadable_id_index').execute();
  await db.schema.dropIndex('comment_threads_uuid_index').execute();
  await db.schema.dropTable('comment_threads').execute();
}

module.exports = { up, down };