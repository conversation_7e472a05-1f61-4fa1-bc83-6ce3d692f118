const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  db.schema
    .createTable('document_embeddings')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('user_id', 'bigint', col => col.notNull())
    .addColumn('document_id', 'bigint', col => col.notNull())
    .addColumn('snippet', 'text')
    .addColumn('embed', kysely.sql('vector(1536)'))
    .addForeignKeyConstraint(
      'vector_user_id_fkey',
      ['user_id'],
      'users',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .addForeignKeyConstraint(
      'vector_document_id_fkey',
      ['document_id'],
      'documents',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .execute();

  db.schema
    .createIndex('document_embeddings_user_document_id_index')
    .on('document_embeddings')
    .column('user_id')
    .column('document_id')
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  db.schema
    .dropIndex('document_embeddings_user_document_id_index')
    .ifExists()
    .execute();
  db.schema.dropTable('document_embeddings').ifExists().execute();
}

module.exports = {up, down};
