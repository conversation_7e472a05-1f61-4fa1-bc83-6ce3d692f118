const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  await db.schema
    .alterTable('documents')
    .addColumn('last_read_at', 'timestamp')
    .execute();

  await db.schema
    .createIndex('document_last_read_at_index')
    .on('documents')
    .columns(['last_read_at'])
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  await db.schema.dropIndex('document_last_read_at_index').ifExists().execute();

  await db.schema.alterTable('documents').dropColumn('last_read_at').execute();
}

module.exports = {up, down};
