const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  await db.schema
    .alterTable('documents')
    .addColumn('indexed', 'boolean', col => col.defaultTo(false))
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  await db.schema
    .alterTable('documents')
    .dropColumn('indexed')
    .execute();
}

module.exports = {up, down};
