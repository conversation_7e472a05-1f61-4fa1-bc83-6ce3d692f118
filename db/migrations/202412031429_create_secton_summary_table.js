const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  await db.schema
    .createTable('section_summary')
    .ifNotExists()
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'varchar(255)', col => col.unique().notNull())
    .addColumn('user_id', 'bigint', col => col.notNull())
    .addColumn('document_id', 'bigint', col => col.notNull())
    .addColumn('section_id', 'varchar(255)', col => col.notNull())
    .addColumn('summary_response', kysely.sql`text[]`)
    .addColumn('date_created', 'timestamp')
    .addColumn('date_modified', 'timestamp')
    .addColumn('date_deleted', 'timestamp')
    .addForeignKeyConstraint(
      'section_summary_document_id_fkey',
      ['document_id'],
      'documents',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .addForeignKeyConstraint(
      'section_summary_user_id_fkey',
      ['user_id'],
      'users',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .execute();

  db.schema
    .createIndex('section_summary_uuid_index')
    .ifNotExists()
    .on('section_summary')
    .column('uuid')
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  db.schema.dropIndex('section_summary_uuid_index').ifExists().execute();
  db.schema.dropTable('section_summary').ifExists().execute();
}

module.exports = {up, down};
