const kysely = require('kysely').Kysely;

/**
 *
 * @param {kysely<any>} db
 */
async function up(db) {
  await db.schema
    .createTable('beta_emails')
    .addColumn('email', 'varchar(255)', col => col.primaryKey())
    .addColumn('added_by', 'bigint', col => col.notNull()) //admin uuid
    .addColumn('deleted_by', 'bigint') //admin uuid
    .addColumn('claimed_by', 'bigint') //user uuid when registered
    .addColumn('date_created', 'timestamp', col => col.notNull())
    .addColumn('date_deleted', 'timestamp', col => col.defaultTo(null))
    .execute();
}

/**
 *
 * @param {kysely<any>} db
 */
async function down(db) {
  db.schema.dropTable('beta_emails').ifExists().execute();
}

module.exports = {up, down};
