const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  db.schema
    .createTable('document_audio')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'varchar(255)', col => col.unique().notNull())
    .addColumn('document_id', 'bigint', col => col.notNull())
    .addColumn('section_id', 'varchar(255)', col => col.notNull())
    .addColumn('paragraph_id', 'varchar(255)', col => col.notNull())
    .addColumn('owner_user_id', 'bigint', col => col.notNull())
    .addColumn('external_task_id', 'varchar(255)', col => col.notNull())
    .addColumn('tts_voice_id', 'bigint', col => col.notNull())
    .addColumn('name', 'varchar(255)')
    .addColumn('type', 'varchar(255)')
    .addColumn('path', 'text')
    .addColumn('status', 'varchar(255)', col => col.notNull())
    .addColumn('date_created', 'timestamp')
    .addColumn('date_modified', 'timestamp')
    .addColumn('date_deleted', 'timestamp')
    .addForeignKeyConstraint(
      'document_audio_document_id_fkey',
      ['document_id'],
      'documents',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .addForeignKeyConstraint(
      'document_audio_user_id_fkey',
      ['owner_user_id'],
      'users',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .addForeignKeyConstraint(
      'document_audio_tts_voice_id_fkey',
      ['tts_voice_id'],
      'tts_voices',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .execute();

  db.schema
    .createIndex('document_audio_document_id')
    .on('document_audio')
    .column('document_id')
    .execute();

  db.schema
    .createIndex('document_audio_owner_user_id')
    .on('document_audio')
    .column('owner_user_id')
    .execute();

  db.schema
    .createIndex('document_audio_external_task_id')
    .on('document_audio')
    .column('external_task_id')
    .execute();

  db.schema
    .createIndex('document_audio_section_id')
    .on('document_audio')
    .column('section_id')
    .execute();

  db.schema
    .createIndex('document_audio_paragraph_id')
    .on('document_audio')
    .column('paragraph_id')
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  db.schema.dropIndex('document_audio_document_id_fkey').ifExists().execute();
  db.schema.dropIndex('document_audio_user_id_fkey').ifExists().execute();
  db.schema.dropTable('document_audio').ifExists().execute();
}

module.exports = {up, down};
