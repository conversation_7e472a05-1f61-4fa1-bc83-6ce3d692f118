const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  db.schema
    .createTable('user_settings')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('user_id', 'bigint', col => col.notNull())
    .addColumn('key', 'varchar', col => col.notNull())
    .addColumn('str_value', 'varchar(255)', col => col.defaultTo(null))
    .addColumn('int_value', 'integer', col => col.defaultTo(null))
    .addColumn('bool_value', 'boolean', col => col.defaultTo(null))
    .execute();

  db.schema
    .createIndex('user_settings_user_id_index')
    .on('user_settings')
    .column('user_id')
    .execute();

  db.schema
    .createIndex('user_settings_user_id_key_index')
    .on('user_settings')
    .column('user_id')
    .column('key')
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  db.schema.dropIndex('user_settings_user_id_key_index').ifExists().execute();
  db.schema.dropIndex('user_settings_user_id_index').ifExists().execute();
  db.schema.dropTable('user_settings').ifExists().execute();
}

module.exports = {up, down};
