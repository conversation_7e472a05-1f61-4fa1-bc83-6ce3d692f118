const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  await db.schema
    .createTable('tags')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'uuid', col =>
      col
        .notNull()
        .unique()
        .defaultTo(kysely.sql`gen_random_uuid()`),
    )
    .addColumn('name', 'varchar(32)', col => col.notNull())
    .addColumn('owner_user_id', 'bigint', col => col.notNull())
    .addColumn('created_at', 'timestamp', col =>
      col.notNull().defaultTo(kysely.sql`now()`),
    )
    .addForeignKeyConstraint(
      'tags_user_id_foreign_key',
      ['owner_user_id'],
      'users',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .execute();

  await db.schema
    .createIndex('tags_uuid_index')
    .on('tags')
    .column('uuid')
    .execute();

  await db.schema
    .createIndex('tags_name_index')
    .on('tags')
    .column('name')
    .execute();

  await db.schema
    .createTable('taggables')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('tag_id', 'bigint', col => col.notNull())
    .addColumn('taggable_type', 'varchar(255)', col => col.notNull())
    .addColumn('taggable_id', 'bigint', col => col.notNull())
    .addColumn('created_at', 'timestamp', col =>
      col.notNull().defaultTo(kysely.sql`now()`),
    )
    .addForeignKeyConstraint(
      'taggables_tag_id_foreign_key',
      ['tag_id'],
      'tags',
      ['id'],
      cb => cb.onDelete('cascade'),
    )
    .execute();

  await db.schema
    .createIndex('taggables_taggable_types_index')
    .on('taggables')
    .columns(['taggable_type', 'taggable_id'])
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  await db.schema.dropIndex('taggables_taggable_types_index').execute();
  await db.schema.dropIndex('tags_name_index').execute();
  await db.schema.dropIndex('tags_uuid_index').execute();

  await db.schema.dropTable('taggables').execute();
  await db.schema.dropTable('tags').execute();
}

module.exports = {up, down};
