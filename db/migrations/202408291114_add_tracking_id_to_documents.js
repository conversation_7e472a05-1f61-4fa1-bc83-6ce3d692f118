const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Up command
  await db.schema
    .alterTable('documents')
    .addColumn('user_tracking_id', 'varchar(255)')
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {
  // Down command
  await db.schema
    .alterTable('documents')
    .dropColumn('user_tracking_id')
    .execute();
}

module.exports = {up, down};
