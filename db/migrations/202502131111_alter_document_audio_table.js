const kysely = require('kysely');

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function up(db) {
  // Rename the old table to legacy_*
  db.schema.alterTable('document_audio').renameTo('legacy_document_audio').execute();

  db.schema
    .createTable('document_audio')
    .addColumn('id', 'serial', col => col.primaryKey())
    .addColumn('uuid', 'varchar(255)', col => col.unique().notNull())
    .addColumn('document_id', 'bigint', col => col.notNull())
    .addColumn('block_id', 'varchar(255)', col => col.notNull())
    .addColumn('owner_user_id', 'bigint', col => col.notNull())
    .addColumn('voice_id', 'varchar(255)', col => col.notNull())
    .addColumn('path', 'text')
    .addColumn('duration', 'bigint')
    .addColumn('date_created', 'timestamp')
    .addColumn('date_modified', 'timestamp')
    .addColumn('date_deleted', 'timestamp')
    .addForeignKeyConstraint(
      'doc_audio_document_id_fkey',
      ['document_id'],
      'documents',
      ['id'],
    )
    .addForeignKeyConstraint(
      'doc_audio_user_id_fkey',
      ['owner_user_id'],
      'users',
      ['id'],
    )
    .execute();

  db.schema
    .createIndex('doc_audio_document_id')
    .on('document_audio')
    .column('document_id')
    .execute();

  db.schema
    .createIndex('doc_audio_owner_user_id')
    .on('document_audio')
    .column('owner_user_id')
    .execute();
}

/**
 *
 * @param {kysely.Kysely<any>} db
 */
async function down(db) {

}

module.exports = {up, down};
