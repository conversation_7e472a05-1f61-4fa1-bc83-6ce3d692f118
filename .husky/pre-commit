test_python=false
test_next=false

echo "Checking files committed for test actions..."
for file in $(git diff --cached --name-only --diff-filter=ACMR | sed 's| |\\ |g'); do
    if [[ "$file" =~ ^aws\/ ]]; then
        test_python=true
    elif [[ "$file" =~ ^app\/ ]]; then
        test_next=true
    fi
done

HUSKY_DIR=$(dirname "$0")
SCRIPT_DIR="$HUSKY_DIR/../scripts"

if $test_python; then
    echo "... testing lambdas"

    # sh "${SCRIPT_DIR}/lib/invenv.sh"

    if [ -z "${VIRTUAL_ENV}" ]; then
        echo "Venv not running"

        VDIR=""

        if [ -d "./.venv" ]; then
            VDIR="./.venv"
        elif [ -d "./venv" ]; then
            VDIR="./venv"
        fi

        echo "Sourcing...'$VDIR/bin/activate'"

        if [ -f "$VDIR/bin/activate" ]; then
            source "$VDIR/bin/activate"
        fi

    fi

    python -m pytest --ignore=tests/test_headings.py

    if [ ! $? == 0 ]; then
        echo "Pytest error detected"
        exit 1
    fi
fi

if $test_next; then
    echo "... testing nextjs"

    sh "${SCRIPT_DIR}/lib/loggedin.sh"

    if [ ! $? == 0 ]; then
        # Log in failed
        exit 1
    fi

    yarn run jest

    if [ ! $? == 0 ]; then
        echo "Jest errors detected"
        exit 1
    fi

    yarn run build

    if [ ! $? == 0 ]; then
        echo "Build error detected"
        exit 1
    fi
fi
